module.exports = {

"[project]/app/auth/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00a78b43259bdfa35946a0918da66b9382dcd7b4dc":"signOutUser"},"",""] */ __turbopack_context__.s({
    "signOutUser": (()=>signOutUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function signOutUser() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { error: _error } = await supabase.auth.signOut();
        // Note: Sign out errors are typically not critical for user experience
        // The user will be redirected to login regardless
        // Explicitly clear auth cookies to ensure logout
        const cookieStore = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i).then((m)=>m.cookies());
        const cookiesToClear = [
            "sb-access-token",
            "sb-refresh-token"
        ];
        for (const cookieName of cookiesToClear){
            try {
                cookieStore.set(cookieName, "", {
                    expires: new Date(0),
                    maxAge: -1
                });
            } catch  {
            // Cookie clearing errors are not critical for sign out
            // Continue with the sign out process
            }
        }
    } catch  {
    // Even if sign out fails, redirect to login for security
    // User will be treated as logged out
    }
    // Redirect to login with a flag to prevent middleware redirect loop
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?logged_out=true");
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    signOutUser
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(signOutUser, "00a78b43259bdfa35946a0918da66b9382dcd7b4dc", null);
}}),
"[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminClient": (()=>createAdminClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-rsc] (ecmascript) <locals>");
;
function createAdminClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co"), process.env.SUPABASE_SERVICE_ROLE_KEY);
}
}}),
"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"401f6bc60c2609b14a366437092383efd0c662356b":"markActivitiesAsRead","404306b831edd693397c4d200cb7cfaf72ef475b59":"getBusinessActivities","40f389eb27483c521497eadb1dbe197d2328544a4a":"getUnreadActivitiesCount"},"",""] */ __turbopack_context__.s({
    "getBusinessActivities": (()=>getBusinessActivities),
    "getUnreadActivitiesCount": (()=>getUnreadActivitiesCount),
    "markActivitiesAsRead": (()=>markActivitiesAsRead)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
;
async function getBusinessActivities({ businessProfileId, page = 1, pageSize = 15, sortBy = "newest", filterBy = "all", autoMarkAsRead = true }) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const supabaseAdmin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return {
            activities: [],
            count: 0,
            error: "Not authenticated"
        };
    }
    // Verify the user is the owner of the business
    if (user.id !== businessProfileId) {
        return {
            activities: [],
            count: 0,
            error: "Unauthorized"
        };
    }
    try {
        // Calculate pagination
        const from = (page - 1) * pageSize;
        const to = from + pageSize - 1;
        // Build the query
        let query = supabase.from("business_activities").select("*", {
            count: "exact"
        }).eq("business_profile_id", businessProfileId);
        // Apply filter
        if (filterBy === "like") {
            query = query.eq("activity_type", "like");
        } else if (filterBy === "subscribe") {
            query = query.eq("activity_type", "subscribe");
        } else if (filterBy === "rating") {
            query = query.eq("activity_type", "rating");
        } else if (filterBy === "unread") {
            query = query.eq("is_read", false);
        }
        // Apply sorting
        switch(sortBy){
            case "oldest":
                query = query.order("created_at", {
                    ascending: true
                });
                break;
            case "unread_first":
                query = query.order("is_read", {
                    ascending: true
                }).order("created_at", {
                    ascending: false
                });
                break;
            case "newest":
            default:
                query = query.order("created_at", {
                    ascending: false
                });
                break;
        }
        // Apply pagination
        query = query.range(from, to);
        // Execute the query
        const { data: activities, error, count } = await query;
        if (error) {
            console.error("Error fetching business activities:", error);
            return {
                activities: [],
                count: 0,
                error: error.message
            };
        }
        // Get user profiles for the activities
        const userIds = activities.map((activity)=>activity.user_id);
        // Fetch both customer and business profiles
        const [customerProfiles, businessProfiles] = await Promise.all([
            supabaseAdmin.from("customer_profiles").select("id, name, avatar_url, email").in("id", userIds),
            supabaseAdmin.from("business_profiles").select("id, business_name, business_slug, logo_url").in("id", userIds)
        ]);
        // Combine the profiles
        const userProfiles = new Map();
        // Add customer profiles to the map
        customerProfiles.data?.forEach((profile)=>{
            userProfiles.set(profile.id, {
                name: profile.name,
                avatar_url: profile.avatar_url,
                email: profile.email,
                is_business: false
            });
        });
        // Add business profiles to the map, overriding customer profiles if both exist
        businessProfiles.data?.forEach((profile)=>{
            const existingProfile = userProfiles.get(profile.id) || {};
            userProfiles.set(profile.id, {
                ...existingProfile,
                business_name: profile.business_name,
                business_slug: profile.business_slug,
                logo_url: profile.logo_url,
                is_business: true
            });
        });
        // Attach user profiles to activities
        const activitiesWithProfiles = activities.map((activity)=>({
                ...activity,
                user_profile: userProfiles.get(activity.user_id) || {}
            }));
        // Auto-mark fetched activities as read if enabled
        if (autoMarkAsRead && activities.length > 0) {
            // Get IDs of unread activities
            const unreadActivityIds = activities.filter((activity)=>!activity.is_read).map((activity)=>activity.id);
            // Only proceed if there are unread activities
            if (unreadActivityIds.length > 0) {
                // Mark these activities as read
                const { error: markError } = await supabase.from("business_activities").update({
                    is_read: true
                }).eq("business_profile_id", businessProfileId).in("id", unreadActivityIds);
                if (markError) {
                    console.error("Error auto-marking activities as read:", markError);
                } else {
                    // Update the activities in our result to reflect they're now read
                    activitiesWithProfiles.forEach((activity)=>{
                        if (unreadActivityIds.includes(activity.id)) {
                            activity.is_read = true;
                        }
                    });
                }
            }
        }
        return {
            activities: activitiesWithProfiles,
            count: count || 0,
            error: null
        };
    } catch (error) {
        console.error("Unexpected error fetching business activities:", error);
        return {
            activities: [],
            count: 0,
            error: "An unexpected error occurred"
        };
    }
}
async function markActivitiesAsRead({ businessProfileId, activityIds }) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return {
            success: false,
            error: "Not authenticated"
        };
    }
    // Verify the user is the owner of the business
    if (user.id !== businessProfileId) {
        return {
            success: false,
            error: "Unauthorized"
        };
    }
    try {
        // If marking specific activities as read
        if (activityIds !== "all") {
            // Handle case where we have specific activity IDs
            const { error } = await supabase.from("business_activities").update({
                is_read: true
            }).eq("business_profile_id", businessProfileId).in("id", activityIds);
            if (error) {
                console.error("Error marking specific activities as read:", error);
                return {
                    success: false,
                    error: error.message
                };
            }
        } else {
            // Handle "mark all as read" with pagination to work around Supabase's 1000 row limit
            const BATCH_SIZE = 1000; // Maximum number of rows to update at once
            let hasMore = true;
            let processedCount = 0;
            while(hasMore){
                // Get a batch of unread activity IDs
                const { data: unreadActivities, error: fetchError } = await supabase.from("business_activities").select("id").eq("business_profile_id", businessProfileId).eq("is_read", false).limit(BATCH_SIZE);
                if (fetchError) {
                    console.error("Error fetching unread activities:", fetchError);
                    return {
                        success: false,
                        error: fetchError.message
                    };
                }
                // If no more unread activities, we're done
                if (!unreadActivities || unreadActivities.length === 0) {
                    hasMore = false;
                    break;
                }
                // Extract IDs from the batch
                const batchIds = unreadActivities.map((activity)=>activity.id);
                // Mark this batch as read
                const { error: updateError } = await supabase.from("business_activities").update({
                    is_read: true
                }).eq("business_profile_id", businessProfileId).in("id", batchIds);
                if (updateError) {
                    console.error("Error marking batch as read:", updateError);
                    return {
                        success: false,
                        error: updateError.message
                    };
                }
                // Update processed count and check if we need to continue
                processedCount += batchIds.length;
                hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more
            }
            console.log(`Marked ${processedCount} activities as read`);
        }
        // Revalidate the activities page
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/activities");
        return {
            success: true,
            error: null
        };
    } catch (error) {
        console.error("Unexpected error marking activities as read:", error);
        return {
            success: false,
            error: "An unexpected error occurred"
        };
    }
}
async function getUnreadActivitiesCount(businessProfileId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return {
            count: 0,
            error: "Not authenticated"
        };
    }
    // Verify the user is the owner of the business
    if (user.id !== businessProfileId) {
        return {
            count: 0,
            error: "Unauthorized"
        };
    }
    try {
        const { count, error } = await supabase.from("business_activities").select("*", {
            count: "exact",
            head: true
        }).eq("business_profile_id", businessProfileId).eq("is_read", false);
        if (error) {
            console.error("Error getting unread activities count:", error);
            return {
                count: 0,
                error: error.message
            };
        }
        return {
            count: count || 0,
            error: null
        };
    } catch (error) {
        console.error("Unexpected error getting unread activities count:", error);
        return {
            count: 0,
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getBusinessActivities,
    markActivitiesAsRead,
    getUnreadActivitiesCount
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessActivities, "404306b831edd693397c4d200cb7cfaf72ef475b59", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(markActivitiesAsRead, "401f6bc60c2609b14a366437092383efd0c662356b", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getUnreadActivitiesCount, "40f389eb27483c521497eadb1dbe197d2328544a4a", null);
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/activities.ts [app-rsc] (ecmascript)");
;
;
;
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/activities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signOutUser"]),
    "404306b831edd693397c4d200cb7cfaf72ef475b59": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessActivities"]),
    "40f389eb27483c521497eadb1dbe197d2328544a4a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getUnreadActivitiesCount"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/auth/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/activities.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => \"[project]/app/auth/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/activities.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00a78b43259bdfa35946a0918da66b9382dcd7b4dc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00a78b43259bdfa35946a0918da66b9382dcd7b4dc"]),
    "404306b831edd693397c4d200cb7cfaf72ef475b59": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["404306b831edd693397c4d200cb7cfaf72ef475b59"]),
    "40f389eb27483c521497eadb1dbe197d2328544a4a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40f389eb27483c521497eadb1dbe197d2328544a4a"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$overview$2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$app$2f$auth$2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$activities$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/(dashboard)/dashboard/business/overview/page/actions.js { ACTIONS_MODULE0 => "[project]/app/auth/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/activities.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/(dashboard)/dashboard/business/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/lib/config/plans.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Central configuration for all plan-related constants
 * This file serves as the single source of truth for plan information
 */ // Plan types
__turbopack_context__.s({
    "PLANS": (()=>PLANS),
    "getPlanById": (()=>getPlanById),
    "getPlanByRazorpayPlanId": (()=>getPlanByRazorpayPlanId),
    "getPlanId": (()=>getPlanId),
    "getProductLimit": (()=>getProductLimit),
    "getRazorpayPlanId": (()=>getRazorpayPlanId),
    "getSubscriptionRazorpayPlanId": (()=>getSubscriptionRazorpayPlanId),
    "hasFeature": (()=>hasFeature),
    "mapRazorpayPlanToDukancardPlan": (()=>mapRazorpayPlanToDukancardPlan),
    "pricingPlans": (()=>pricingPlans)
});
// Define Razorpay plan IDs
// Different plan IDs for production and development environments
const RAZORPAY_PLAN_IDS = {
    free: {
        monthly: "free-plan-monthly",
        yearly: "free-plan-yearly"
    },
    basic: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgoJF3OfM6mB0",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgr1XzaksqvSZ"
    },
    growth: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMGvYCN7BM0V",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMdSbSeFrykv"
    },
    pro: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnN4mwUu6H2Ho",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnNYfrCExI496"
    },
    enterprise: {
        monthly: "enterprise-plan-monthly-razorpay",
        yearly: "enterprise-plan-yearly-razorpay"
    }
};
// Set payment gateway to Razorpay
const _paymentGateway = "razorpay";
const PLANS = [
    {
        id: "free",
        name: "Free",
        description: "Basic features for individuals and startups",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.free,
        pricing: {
            monthly: 0,
            yearly: 0
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Simple digital business card with contact information"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 5,
                description: "Showcase your products or services (limited to 5)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: false,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: false,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 1,
                description: "Upload and display 1 image in your gallery"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "basic",
        name: "Basic",
        description: "Essential features for small businesses",
        recommended: false,
        razorpayPlanIds: RAZORPAY_PLAN_IDS.basic,
        pricing: {
            monthly: 99,
            yearly: 999
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Basic digital business card with contact information"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 15,
                description: "Showcase your products or services (up to 15)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: false,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 3,
                description: "Upload and display up to 3 images in your gallery"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "growth",
        name: "Growth",
        description: "Advanced features for growing businesses",
        recommended: true,
        razorpayPlanIds: RAZORPAY_PLAN_IDS.growth,
        pricing: {
            monthly: 499,
            yearly: 4990
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Premium digital business card with enhanced features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 50,
                description: "Showcase your products or services (up to 50)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 10,
                description: "Upload and display up to 10 images"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "pro",
        name: "Pro",
        description: "Premium features for established businesses",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.pro,
        pricing: {
            monthly: 1999,
            yearly: 19990
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Elite digital business card with premium features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: "unlimited",
                description: "Showcase unlimited products or services"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: true,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: true,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 50,
                description: "Upload and display up to 50 images"
            },
            {
                name: "Priority Support",
                included: true,
                description: "Priority email and chat support"
            },
            {
                name: "Dukancard Branding",
                included: false,
                description: "No Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "enterprise",
        name: "Enterprise",
        description: "Custom solutions for large businesses",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.enterprise,
        pricing: {
            monthly: 0,
            yearly: 0
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Enterprise-grade digital business card with all premium features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: "unlimited",
                description: "Showcase unlimited products or services"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: true,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: true,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 100,
                description: "Upload and display up to 100 images"
            },
            {
                name: "Dedicated Account Manager",
                included: true,
                description: "Get a dedicated account manager"
            },
            {
                name: "Custom Analytics Dashboard",
                included: true,
                description: "Get a custom analytics dashboard"
            },
            {
                name: "24/7 Priority Support",
                included: true,
                description: "24/7 priority support"
            },
            {
                name: "White-Label Option",
                included: true,
                description: "Use your own branding instead of Dukancard"
            },
            {
                name: "Dukancard Branding",
                included: false,
                description: "No Dukancard branding on your business card"
            }
        ]
    }
];
function getPlanById(planId) {
    return PLANS.find((plan)=>plan.id === planId);
}
function getPlanByRazorpayPlanId(planId) {
    return PLANS.find((plan)=>plan.razorpayPlanIds.monthly === planId || plan.razorpayPlanIds.yearly === planId);
}
function mapRazorpayPlanToDukancardPlan(razorpayPlanId) {
    const plan = getPlanByRazorpayPlanId(razorpayPlanId);
    return plan?.id || "free";
}
function getRazorpayPlanId(planType, planCycle) {
    const plan = getPlanById(planType);
    if (!plan) {
        return null;
    }
    const planId = plan.razorpayPlanIds[planCycle];
    return planId || null;
}
function getSubscriptionRazorpayPlanId(planId, planCycle) {
    if (planId === "free") {
        return planCycle === "monthly" ? "free-plan-monthly" : "free-plan-yearly";
    } else if (planId === "basic") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgoJF3OfM6mB0" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgr1XzaksqvSZ";
    } else if (planId === "growth") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMGvYCN7BM0V" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMdSbSeFrykv";
    } else if (planId === "pro") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnN4mwUu6H2Ho" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnNYfrCExI496";
    } else {
        throw new Error(`Invalid plan selected: ${planId}`);
    }
}
function getPlanId(planType, planCycle) {
    // Use Razorpay plan IDs since we've migrated to Razorpay
    const planId = getRazorpayPlanId(planType, planCycle);
    return planId === null ? undefined : planId;
}
function getProductLimit(planType) {
    if (!planType) return 0;
    const plan = getPlanById(planType);
    if (!plan) return 0;
    const productFeature = plan.features.find((feature)=>feature.name === "Product Listings");
    if (!productFeature || !productFeature.included) return 0;
    return productFeature.limit === "unlimited" ? Infinity : productFeature.limit || 0;
}
function hasFeature(planType, featureName) {
    if (!planType) return false;
    const plan = getPlanById(planType);
    if (!plan) return false;
    const feature = plan.features.find((feature)=>feature.name === featureName);
    return feature?.included || false;
}
function pricingPlans() {
    return PLANS;
}
}}),
"[project]/lib/PricingPlans.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getPlanLimit": (()=>getPlanLimit),
    "onboardingPlans": (()=>onboardingPlans),
    "pricingPlans": (()=>pricingPlans)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$config$2f$plans$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/config/plans.ts [app-rsc] (ecmascript)");
;
// Convert a ConfigPlan to a PricingPlan for backward compatibility
function convertToPricingPlan(plan, billingCycle) {
    // All plans are available now, Enterprise will show "Contact Sales"
    const available = true;
    // Handle Enterprise plan pricing differently
    const isEnterprise = plan.id === "enterprise";
    // Calculate savings for yearly plans (skip for Enterprise)
    const monthlyCost = plan.pricing.monthly;
    const yearlyCost = plan.pricing.yearly;
    const yearlySavings = isEnterprise ? 0 : monthlyCost * 12 - yearlyCost;
    return {
        id: plan.id,
        name: `${plan.name} Plan`,
        razorpayPlanIds: {
            monthly: plan.razorpayPlanIds.monthly || null,
            yearly: plan.razorpayPlanIds.yearly || null
        },
        price: isEnterprise ? "Contact Sales" : billingCycle === "monthly" ? `₹${plan.pricing.monthly.toLocaleString("en-IN")}` : `₹${plan.pricing.yearly.toLocaleString("en-IN")}`,
        yearlyPrice: isEnterprise ? "Contact Sales" : `₹${plan.pricing.yearly.toLocaleString("en-IN")}`,
        period: isEnterprise ? "" : billingCycle === "monthly" ? "/month" : "/year",
        savings: isEnterprise ? undefined : billingCycle === "yearly" ? `Save ₹${yearlySavings.toLocaleString("en-IN")}` : undefined,
        description: plan.description,
        features: plan.features.map((f)=>{
            if (f.name === "Product Listings" && f.included) {
                const limit = f.limit === "unlimited" ? "Unlimited" : f.limit;
                return `Product/Service Listings (up to ${limit})`;
            }
            return f.included ? f.name : `❌ ${f.name}`;
        }),
        button: isEnterprise ? "Contact Sales" : "Subscribe Now",
        available,
        featured: plan.id === "free" || plan.id === "basic" || plan.id === "growth" || plan.id === "pro",
        recommended: plan.recommended || false,
        mostPopular: plan.recommended || false
    };
}
const pricingPlans = (billingCycle)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$config$2f$plans$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLANS"].map((plan)=>convertToPricingPlan(plan, billingCycle));
};
const onboardingPlans = pricingPlans("monthly");
const getPlanLimit = (planId)=>{
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$config$2f$plans$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProductLimit"])(planId);
};
}}),
"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx <module evaluation>", "default");
}}),
"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx", "default");
}}),
"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$BusinessDashboardClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$BusinessDashboardClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$BusinessDashboardClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/lib/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanPhoneFromAuth": (()=>cleanPhoneFromAuth),
    "cn": (()=>cn),
    "formatAddress": (()=>formatAddress),
    "formatCurrency": (()=>formatCurrency),
    "formatDate": (()=>formatDate),
    "formatIndianNumberShort": (()=>formatIndianNumberShort),
    "maskEmail": (()=>maskEmail),
    "maskPhoneNumber": (()=>maskPhoneNumber),
    "toTitleCase": (()=>toTitleCase)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-rsc] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
function cleanPhoneFromAuth(phone) {
    if (!phone) return null;
    let processedPhone = phone.trim();
    // Remove +91 prefix if present
    if (processedPhone.startsWith('+91')) {
        processedPhone = processedPhone.substring(3);
    } else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {
        processedPhone = processedPhone.substring(2);
    }
    // Validate it's a 10-digit number
    if (/^\d{10}$/.test(processedPhone)) {
        return processedPhone;
    }
    return null; // Invalid format
}
function maskPhoneNumber(phone) {
    if (!phone || phone.length < 4) {
        return "Invalid Phone"; // Or return empty string or original if preferred
    }
    const firstTwo = phone.substring(0, 2);
    const lastTwo = phone.substring(phone.length - 2);
    const maskedPart = "*".repeat(phone.length - 4);
    return `${firstTwo}${maskedPart}${lastTwo}`;
}
function maskEmail(email) {
    if (!email || !email.includes("@")) {
        return "Invalid Email"; // Or return empty string or original
    }
    const parts = email.split("@");
    const username = parts[0];
    const domain = parts[1];
    if (username.length <= 2 || domain.length <= 2 || !domain.includes(".")) {
        return "Email Hidden"; // Simple mask for very short/invalid emails
    }
    const maskedUsername = username.substring(0, 2) + "*".repeat(username.length - 2);
    const domainParts = domain.split(".");
    const domainName = domainParts[0];
    const domainTld = domainParts.slice(1).join("."); // Handle multiple parts like .co.uk
    const maskedDomainName = domainName.substring(0, 2) + "*".repeat(domainName.length - 2);
    return `${maskedUsername}@${maskedDomainName}.${domainTld}`;
}
function formatIndianNumberShort(num) {
    if (num === null || num === undefined || isNaN(num)) return "0";
    const absNum = Math.abs(num);
    // Indian units and their values
    const units = [
        {
            value: 1e5,
            symbol: "L"
        },
        {
            value: 1e7,
            symbol: "Cr"
        },
        {
            value: 1e9,
            symbol: "Ar"
        },
        {
            value: 1e11,
            symbol: "Khar"
        },
        {
            value: 1e13,
            symbol: "Neel"
        },
        {
            value: 1e15,
            symbol: "Padma"
        },
        {
            value: 1e17,
            symbol: "Shankh"
        }
    ];
    // For thousands (K), use western style for sub-lakh
    if (absNum < 1e5) {
        if (absNum >= 1e3) {
            return (num / 1e3).toFixed(1).replace(/\.0$/, "") + "K";
        }
        return num.toString();
    }
    // Find the largest unit that fits
    for(let i = units.length - 1; i >= 0; i--){
        if (absNum >= units[i].value) {
            return (num / units[i].value).toFixed(1).replace(/\.0$/, "") + units[i].symbol;
        }
    }
    // Fallback (should not reach here)
    return num.toString();
}
function formatAddress(data) {
    const addressParts = [
        data.address_line,
        data.locality,
        data.city,
        data.state,
        data.pincode
    ].filter(Boolean);
    return addressParts.join(", ") || "Address not available";
}
function formatDate(date, includeTime = false) {
    if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
        return "Invalid date";
    }
    const options = {
        year: "numeric",
        month: "long",
        day: "numeric",
        timeZone: "Asia/Kolkata"
    };
    if (includeTime) {
        options.hour = "2-digit";
        options.minute = "2-digit";
        options.hour12 = true;
    }
    return date.toLocaleString("en-IN", options);
}
function formatCurrency(amount, currency = "INR") {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return "Invalid amount";
    }
    try {
        return new Intl.NumberFormat("en-IN", {
            style: "currency",
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(amount);
    } catch  {
        // Catch any error without using the error variable
        // Fallback in case of invalid currency code
        return `${currency} ${amount.toFixed(2)}`;
    }
}
function toTitleCase(text) {
    if (!text) return "";
    return text.toLowerCase().replace(/\b\w/g, (char)=>char.toUpperCase());
}
}}),
"[project]/components/ui/skeleton.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Skeleton": (()=>Skeleton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-rsc] (ecmascript)");
;
;
function Skeleton({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "skeleton",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])("bg-accent animate-pulse rounded-md", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/skeleton.tsx",
        lineNumber: 5,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/app/(dashboard)/dashboard/business/overview/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BusinessOverviewPage),
    "metadata": (()=>metadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$PricingPlans$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/PricingPlans.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$BusinessDashboardClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/skeleton.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
// Helper function to find plan details by ID and cycle
const getPlanDetails = (planId, planCycle)=>{
    if (!planId) return undefined;
    // Use the provided plan cycle or default to monthly
    const cycle = planCycle === "yearly" ? "yearly" : "monthly";
    const allPlans = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$PricingPlans$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pricingPlans"])(cycle);
    return allPlans.find((plan)=>plan.id === planId);
};
// Helper function to check subscription status using centralized logic
function checkSubscriptionStatus(profile, subscription) {
    if (!profile) return "inactive";
    const now = new Date();
    const trialEndDate = profile.trial_end_date ? new Date(profile.trial_end_date) : null;
    // If we have a subscription status from the database, use centralized logic
    if (subscription?.subscription_status) {
        const status = subscription.subscription_status;
        const planId = subscription.plan_id || 'free';
        // Use centralized logic - only paid subscriptions count as active
        // Trial and free plan users are NOT considered to have active subscription
        if (status === 'active' || status === 'authenticated') {
            // Only paid plans count as active subscription
            if (planId !== 'free') {
                return "active";
            }
        }
        if (status === 'trial') {
            return "trial";
        }
    }
    // Fallback: Check if user is in trial period based on trial_end_date
    if (trialEndDate && trialEndDate > now) {
        return "trial";
    }
    return "inactive"; // Trial ended or never existed, and no active subscription
}
const metadata = {
    title: "Business Dashboard Overview",
    robots: "noindex, nofollow"
};
async function BusinessOverviewPage() {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?message=Authentication required");
    }
    // Fetch the business profile including interaction details and status
    const { data: profileData, error: profileError } = await supabase.from("business_profiles").select("business_name, business_slug, trial_end_date, total_likes, total_subscriptions, average_rating, logo_url, title, status").eq("id", user.id).single();
    // Fetch subscription data from payment_subscriptions
    const { data: subscription, error: subscriptionError } = await supabase.from("payment_subscriptions").select("subscription_status, plan_id, plan_cycle").eq("business_profile_id", user.id).order("created_at", {
        ascending: false
    }).limit(1).maybeSingle();
    if (subscriptionError) {
        console.error("Error fetching subscription data:", subscriptionError);
    }
    if (profileError || !profileData) {
        console.error("Error fetching business profile or profile not found:", profileError?.message);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])("/login?message=Profile fetch error");
    }
    // No need to generate visit data as we've removed the performance metrics component
    // Get subscription status and plan details
    const subscriptionStatus = checkSubscriptionStatus(profileData, subscription);
    // Get the plan ID from the subscription data, even if the subscription is inactive or halted
    // This ensures that users who have paused their subscription still see their actual plan
    const planId = subscription?.plan_id || "free";
    const planDetails = getPlanDetails(planId, subscription?.plan_cycle || null);
    // Create the properly formatted initialProfile object
    const initialProfile = {
        business_name: profileData.business_name || "",
        business_slug: profileData.business_slug || "",
        plan_id: planId,
        plan_cycle: subscription?.plan_cycle || null,
        has_active_subscription: // Use centralized logic: only paid subscriptions count as active
        // Trial and free plan users have has_active_subscription = false
        (subscription?.subscription_status === "active" || subscription?.subscription_status === "authenticated") && planId !== 'free',
        trial_end_date: profileData.trial_end_date,
        total_likes: profileData.total_likes || 0,
        total_subscriptions: profileData.total_subscriptions || 0,
        average_rating: profileData.average_rating || 0,
        logo_url: profileData.logo_url,
        title: profileData.title,
        status: profileData.status || "offline" // Default to offline if status is not set
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Suspense"], {
        fallback: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Skeleton"], {
            className: "h-[600px] w-full"
        }, void 0, false, {
            fileName: "[project]/app/(dashboard)/dashboard/business/overview/page.tsx",
            lineNumber: 147,
            columnNumber: 25
        }, void 0),
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$BusinessDashboardClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
            initialProfile: initialProfile,
            userId: user.id,
            subscriptionStatus: subscriptionStatus,
            planDetails: planDetails,
            subscription: subscription
        }, void 0, false, {
            fileName: "[project]/app/(dashboard)/dashboard/business/overview/page.tsx",
            lineNumber: 148,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/business/overview/page.tsx",
        lineNumber: 147,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/(dashboard)/dashboard/business/overview/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/overview/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=_36ca324c._.js.map