module.exports = {

"[project]/lib/config/plans.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Central configuration for all plan-related constants
 * This file serves as the single source of truth for plan information
 */ // Plan types
__turbopack_context__.s({
    "PLANS": (()=>PLANS),
    "getPlanById": (()=>getPlanById),
    "getPlanByRazorpayPlanId": (()=>getPlanByRazorpayPlanId),
    "getPlanId": (()=>getPlanId),
    "getProductLimit": (()=>getProductLimit),
    "getRazorpayPlanId": (()=>getRazorpayPlanId),
    "getSubscriptionRazorpayPlanId": (()=>getSubscriptionRazorpayPlanId),
    "hasFeature": (()=>hasFeature),
    "mapRazorpayPlanToDukancardPlan": (()=>mapRazorpayPlanToDukancardPlan),
    "pricingPlans": (()=>pricingPlans)
});
// Define Razorpay plan IDs
// Different plan IDs for production and development environments
const RAZORPAY_PLAN_IDS = {
    free: {
        monthly: "free-plan-monthly",
        yearly: "free-plan-yearly"
    },
    basic: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgoJF3OfM6mB0",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgr1XzaksqvSZ"
    },
    growth: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMGvYCN7BM0V",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMdSbSeFrykv"
    },
    pro: {
        monthly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnN4mwUu6H2Ho",
        yearly: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnNYfrCExI496"
    },
    enterprise: {
        monthly: "enterprise-plan-monthly-razorpay",
        yearly: "enterprise-plan-yearly-razorpay"
    }
};
// Set payment gateway to Razorpay
const _paymentGateway = "razorpay";
const PLANS = [
    {
        id: "free",
        name: "Free",
        description: "Basic features for individuals and startups",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.free,
        pricing: {
            monthly: 0,
            yearly: 0
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Simple digital business card with contact information"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 5,
                description: "Showcase your products or services (limited to 5)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: false,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: false,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 1,
                description: "Upload and display 1 image in your gallery"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "basic",
        name: "Basic",
        description: "Essential features for small businesses",
        recommended: false,
        razorpayPlanIds: RAZORPAY_PLAN_IDS.basic,
        pricing: {
            monthly: 99,
            yearly: 999
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Basic digital business card with contact information"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 15,
                description: "Showcase your products or services (up to 15)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: false,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 3,
                description: "Upload and display up to 3 images in your gallery"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "growth",
        name: "Growth",
        description: "Advanced features for growing businesses",
        recommended: true,
        razorpayPlanIds: RAZORPAY_PLAN_IDS.growth,
        pricing: {
            monthly: 499,
            yearly: 4990
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Premium digital business card with enhanced features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: 50,
                description: "Showcase your products or services (up to 50)"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: false,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: false,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 10,
                description: "Upload and display up to 10 images"
            },
            {
                name: "Dukancard Branding",
                included: true,
                description: "Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "pro",
        name: "Pro",
        description: "Premium features for established businesses",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.pro,
        pricing: {
            monthly: 1999,
            yearly: 19990
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Elite digital business card with premium features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: "unlimited",
                description: "Showcase unlimited products or services"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: true,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: true,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 50,
                description: "Upload and display up to 50 images"
            },
            {
                name: "Priority Support",
                included: true,
                description: "Priority email and chat support"
            },
            {
                name: "Dukancard Branding",
                included: false,
                description: "No Dukancard branding on your business card"
            }
        ]
    },
    {
        id: "enterprise",
        name: "Enterprise",
        description: "Custom solutions for large businesses",
        razorpayPlanIds: RAZORPAY_PLAN_IDS.enterprise,
        pricing: {
            monthly: 0,
            yearly: 0
        },
        features: [
            {
                name: "Digital Business Card",
                included: true,
                description: "Enterprise-grade digital business card with all premium features"
            },
            {
                name: "QR Code for Sharing",
                included: true,
                description: "Shareable QR code for your business card"
            },
            {
                name: "Social Media Links",
                included: true,
                description: "Add links to your social media profiles"
            },
            {
                name: "Product Listings",
                included: true,
                limit: "unlimited",
                description: "Showcase unlimited products or services"
            },
            {
                name: "Customer Subscriptions",
                included: true,
                description: "Allow customers to subscribe to your business"
            },
            {
                name: "Ratings & Reviews",
                included: true,
                description: "Collect and display customer reviews"
            },
            {
                name: "Like Feature",
                included: true,
                description: "Let customers like your business card"
            },
            {
                name: "Basic Analytics",
                included: true,
                description: "View basic metrics like views and clicks"
            },
            {
                name: "Default Theme",
                included: true,
                description: "Use the default Dukancard theme"
            },
            {
                name: "Delivery Hours",
                included: true,
                description: "Set and display your delivery hours"
            },
            {
                name: "Business Hours",
                included: true,
                description: "Set and display your business hours"
            },
            {
                name: "Theme Customization",
                included: true,
                description: "Customize your card theme and colors"
            },
            {
                name: "Enhanced Analytics",
                included: true,
                description: "View detailed metrics including product views"
            },
            {
                name: "Advanced Analytics",
                included: true,
                description: "Access comprehensive business insights"
            },
            {
                name: "Photo Gallery",
                included: true,
                limit: 100,
                description: "Upload and display up to 100 images"
            },
            {
                name: "Dedicated Account Manager",
                included: true,
                description: "Get a dedicated account manager"
            },
            {
                name: "Custom Analytics Dashboard",
                included: true,
                description: "Get a custom analytics dashboard"
            },
            {
                name: "24/7 Priority Support",
                included: true,
                description: "24/7 priority support"
            },
            {
                name: "White-Label Option",
                included: true,
                description: "Use your own branding instead of Dukancard"
            },
            {
                name: "Dukancard Branding",
                included: false,
                description: "No Dukancard branding on your business card"
            }
        ]
    }
];
function getPlanById(planId) {
    return PLANS.find((plan)=>plan.id === planId);
}
function getPlanByRazorpayPlanId(planId) {
    return PLANS.find((plan)=>plan.razorpayPlanIds.monthly === planId || plan.razorpayPlanIds.yearly === planId);
}
function mapRazorpayPlanToDukancardPlan(razorpayPlanId) {
    const plan = getPlanByRazorpayPlanId(razorpayPlanId);
    return plan?.id || "free";
}
function getRazorpayPlanId(planType, planCycle) {
    const plan = getPlanById(planType);
    if (!plan) {
        return null;
    }
    const planId = plan.razorpayPlanIds[planCycle];
    return planId || null;
}
function getSubscriptionRazorpayPlanId(planId, planCycle) {
    if (planId === "free") {
        return planCycle === "monthly" ? "free-plan-monthly" : "free-plan-yearly";
    } else if (planId === "basic") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgoJF3OfM6mB0" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QRgr1XzaksqvSZ";
    } else if (planId === "growth") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMGvYCN7BM0V" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnMdSbSeFrykv";
    } else if (planId === "pro") {
        return planCycle === "monthly" ? ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnN4mwUu6H2Ho" : ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : "plan_QbnNYfrCExI496";
    } else {
        throw new Error(`Invalid plan selected: ${planId}`);
    }
}
function getPlanId(planType, planCycle) {
    // Use Razorpay plan IDs since we've migrated to Razorpay
    const planId = getRazorpayPlanId(planType, planCycle);
    return planId === null ? undefined : planId;
}
function getProductLimit(planType) {
    if (!planType) return 0;
    const plan = getPlanById(planType);
    if (!plan) return 0;
    const productFeature = plan.features.find((feature)=>feature.name === "Product Listings");
    if (!productFeature || !productFeature.included) return 0;
    return productFeature.limit === "unlimited" ? Infinity : productFeature.limit || 0;
}
function hasFeature(planType, featureName) {
    if (!planType) return false;
    const plan = getPlanById(planType);
    if (!plan) return false;
    const feature = plan.features.find((feature)=>feature.name === featureName);
    return feature?.included || false;
}
function pricingPlans() {
    return PLANS;
}
}}),

};

//# sourceMappingURL=lib_config_plans_ts_794a5ac4._.js.map