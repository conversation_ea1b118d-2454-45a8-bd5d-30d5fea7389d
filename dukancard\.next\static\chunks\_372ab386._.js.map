{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-pointer items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 594, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAMsB,cAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/MinimalHeader.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\n// Removed unused imports: useRout<PERSON>, createClient\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { LogOut } from \"lucide-react\";\r\n// Removed unused ThemeToggle import\r\nimport { signOutUser } from \"@/app/auth/actions\"; // Import the server action\r\n// Removed unused Sheet import\r\n\r\ninterface MinimalHeaderProps {\r\n  children?: React.ReactNode;\r\n  // Updated props\r\n  businessName?: string | null; // For business context or fallback\r\n  logoUrl?: string | null;\r\n  userName?: string | null; // Added for user's actual name\r\n}\r\n\r\n// Helper to get initials - prioritize userName if available, else businessName\r\nconst getInitials = (userName?: string | null, businessName?: string | null): string => {\r\n  const nameToUse = userName || businessName; // Use user name first for avatar\r\n  if (!nameToUse) return \"?\";\r\n  const names = nameToUse.trim().split(/\\s+/);\r\n  if (names.length === 1) return names[0].charAt(0).toUpperCase();\r\n  return (\r\n    names[0].charAt(0).toUpperCase() +\r\n    names[names.length - 1].charAt(0).toUpperCase()\r\n  );\r\n};\r\n\r\n// Helper to get current page name from pathname\r\nconst getPageName = (pathname: string): string => {\r\n  const pathSegments = pathname.split('/').filter(Boolean);\r\n\r\n  // Handle customer dashboard routes\r\n  if (pathSegments.includes('customer')) {\r\n    const lastSegment = pathSegments[pathSegments.length - 1];\r\n\r\n    switch (lastSegment) {\r\n      case 'customer':\r\n        return 'Feed';\r\n      case 'overview':\r\n        return 'Overview';\r\n      case 'likes':\r\n        return 'My Likes';\r\n      case 'subscriptions':\r\n        return 'Subscriptions';\r\n      case 'reviews':\r\n        return 'My Reviews';\r\n      case 'profile':\r\n        return 'Profile';\r\n      case 'settings':\r\n        return 'Settings';\r\n      default:\r\n        return 'Dashboard';\r\n    }\r\n  }\r\n\r\n  // Handle business dashboard routes\r\n  if (pathSegments.includes('business')) {\r\n    const lastSegment = pathSegments[pathSegments.length - 1];\r\n\r\n    switch (lastSegment) {\r\n      case 'business':\r\n        return 'Feed';\r\n      case 'overview':\r\n        return 'Overview';\r\n      case 'analytics':\r\n        return 'Analytics';\r\n      case 'card':\r\n        return 'Manage Card';\r\n      case 'products':\r\n        return 'Products & Services';\r\n      case 'gallery':\r\n        return 'Gallery';\r\n      case 'subscriptions':\r\n        return 'Subscriptions';\r\n      case 'likes':\r\n        return 'Likes';\r\n      case 'reviews':\r\n        return 'Reviews';\r\n      case 'activities':\r\n        return 'Activities';\r\n      case 'settings':\r\n        return 'Settings';\r\n      case 'plan':\r\n        return 'Plan Management';\r\n      default:\r\n        return 'Business Dashboard';\r\n    }\r\n  }\r\n\r\n  // Default fallback\r\n  return 'Dashboard';\r\n};\r\n\r\nconst MinimalHeader: React.FC<MinimalHeaderProps> = ({\r\n  children,\r\n  businessName: propBusinessName,\r\n  logoUrl: propLogoUrl,\r\n  userName: propUserName, // Added prop\r\n}) => {\r\n  // Use props directly for now - context can be added back later if needed\r\n  const businessName = propBusinessName;\r\n  const logoUrl = propLogoUrl;\r\n  const userName = propUserName;\r\n\r\n  // Get current pathname and page name\r\n  const pathname = usePathname();\r\n  const currentPageName = getPageName(pathname);\r\n\r\n  // Initials logic updated to prioritize userName\r\n  const initials = getInitials(userName, businessName);\r\n\r\n  // Determine display name for the dropdown\r\n  let displayName = \"User\"; // Default fallback\r\n  if (userName && businessName) {\r\n    displayName = `${businessName} (${userName})`; // Business context\r\n  } else if (userName) {\r\n    displayName = userName; // Customer context\r\n  } else if (businessName) {\r\n    displayName = businessName; // Fallback if only business name exists\r\n  }\r\n\r\n\r\n  // Access children assuming specific order from layout: [Sheet, Button, ThemeToggle]\r\n  const childArray = React.Children.toArray(children);\r\n  const mobileSheetTrigger = childArray[0]; // Assumes Sheet is the first child\r\n  const desktopToggleButton = childArray[1]; // Assumes Button is the second child\r\n  const themeToggleElement = childArray[2]; // Assumes ThemeToggle is the third child\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-40 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80\">\r\n      {/* Enhanced container with better spacing */}\r\n      <div className=\"container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8\">\r\n        {/* Left Section: Mobile Toggle -> Logo -> Desktop Toggle */}\r\n        <div className=\"flex items-center space-x-2 md:space-x-4\">\r\n          {/* Render Mobile Sheet Trigger First */}\r\n          {mobileSheetTrigger}\r\n\r\n          {/* Current page name instead of logo */}\r\n          <div className=\"flex items-center\">\r\n            <h1 className=\"text-xl font-semibold text-foreground\">\r\n              {currentPageName}\r\n            </h1>\r\n          </div>\r\n\r\n          {/* Render Desktop Toggle Button after logo */}\r\n          {desktopToggleButton}\r\n        </div>\r\n\r\n        {/* Right Section: User Menu + Theme Toggle */}\r\n        <div className=\"flex items-center space-x-2 md:space-x-4\">\r\n          {/* Enhanced User Profile with Name Display */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"cursor-pointer flex items-center gap-3 h-10 px-3 rounded-lg hover:bg-accent/50 transition-all duration-200 focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n              >\r\n                <Avatar className=\"h-8 w-8 border-2 border-border hover:border-primary/50 transition-all duration-200\">\r\n                  {/* Use logoUrl for avatar image only if it exists */}\r\n                  {logoUrl ? (\r\n                    <AvatarImage\r\n                      src={logoUrl}\r\n                      alt={userName || businessName || \"User\"}\r\n                    />\r\n                  ) : null}\r\n                  <AvatarFallback className=\"bg-gradient-to-br from-primary/10 to-primary/5 text-primary font-semibold text-sm\">\r\n                    {initials}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                {/* Display user name on larger screens */}\r\n                <span className=\"hidden sm:block text-sm font-medium text-foreground max-w-32 truncate\">\r\n                  {userName || businessName || \"User\"}\r\n                </span>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent className=\"w-64\" align=\"end\" forceMount> {/* Increased width slightly */}\r\n              {/* Updated Dropdown Label */}\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <p className=\"text-sm font-medium leading-none truncate py-2\">\r\n                  {displayName} {/* Use the combined/correct display name */}\r\n                </p>\r\n                {/* Optionally add email back if needed, maybe only for customers? */}\r\n                {/* {userName && !businessName && userEmail && (\r\n                  <p className=\"text-xs leading-none text-muted-foreground truncate pt-1\">\r\n                    {userEmail}\r\n                  </p>\r\n                )} */}\r\n              </DropdownMenuLabel>\r\n              {/* Removed email display and extra separator */}\r\n              {/* <DropdownMenuSeparator /> */}\r\n              {/* Add links to profile/settings if needed */}\r\n              {/* <DropdownMenuItem asChild>\r\n                <Link href=\"/dashboard/profile\">\r\n                  <UserIcon className=\"mr-2 h-4 w-4\" />\r\n                  <span>Profile</span>\r\n                </Link>\r\n              </DropdownMenuItem> */}\r\n              <DropdownMenuSeparator />\r\n              {/* Logout Button using Server Action */}\r\n              <form action={signOutUser} className=\"w-full px-2 py-1.5\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"ghost\"\r\n                  className=\"w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer\"\r\n                >\r\n                  <LogOut className=\"mr-2 h-4 w-4\" />\r\n                  <span>Log out</span>\r\n                </Button>\r\n              </form>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n          {themeToggleElement} {/* Render theme toggle last */}\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default MinimalHeader;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,kDAAkD;AAClD;AACA;AAOA;AACA;AACA,oCAAoC;AACpC,oQAAkD,2BAA2B;;;AAhB7E;;;;;;;;AA2BA,+EAA+E;AAC/E,MAAM,cAAc,CAAC,UAA0B;IAC7C,MAAM,YAAY,YAAY,cAAc,iCAAiC;IAC7E,IAAI,CAAC,WAAW,OAAO;IACvB,MAAM,QAAQ,UAAU,IAAI,GAAG,KAAK,CAAC;IACrC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IAC7D,OACE,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAC9B,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;AAEjD;AAEA,gDAAgD;AAChD,MAAM,cAAc,CAAC;IACnB,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAEhD,mCAAmC;IACnC,IAAI,aAAa,QAAQ,CAAC,aAAa;QACrC,MAAM,cAAc,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAEzD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mCAAmC;IACnC,IAAI,aAAa,QAAQ,CAAC,aAAa;QACrC,MAAM,cAAc,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAEzD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mBAAmB;IACnB,OAAO;AACT;AAEA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,EACR,cAAc,gBAAgB,EAC9B,SAAS,WAAW,EACpB,UAAU,YAAY,EACvB;;IACC,yEAAyE;IACzE,MAAM,eAAe;IACrB,MAAM,UAAU;IAChB,MAAM,WAAW;IAEjB,qCAAqC;IACrC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,kBAAkB,YAAY;IAEpC,gDAAgD;IAChD,MAAM,WAAW,YAAY,UAAU;IAEvC,0CAA0C;IAC1C,IAAI,cAAc,QAAQ,mBAAmB;IAC7C,IAAI,YAAY,cAAc;QAC5B,cAAc,GAAG,aAAa,EAAE,EAAE,SAAS,CAAC,CAAC,EAAE,mBAAmB;IACpE,OAAO,IAAI,UAAU;QACnB,cAAc,UAAU,mBAAmB;IAC7C,OAAO,IAAI,cAAc;QACvB,cAAc,cAAc,wCAAwC;IACtE;IAGA,oFAAoF;IACpF,MAAM,aAAa,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC1C,MAAM,qBAAqB,UAAU,CAAC,EAAE,EAAE,mCAAmC;IAC7E,MAAM,sBAAsB,UAAU,CAAC,EAAE,EAAE,qCAAqC;IAChF,MAAM,qBAAqB,UAAU,CAAC,EAAE,EAAE,yCAAyC;IAEnF,qBACE,6LAAC;QAAO,WAAU;kBAEhB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;wBAEZ;sCAGD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;wBAKJ;;;;;;;8BAIH,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,wIAAA,CAAA,eAAY;;8CACX,6LAAC,wIAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,6LAAC,8HAAA,CAAA,SAAM;gDAAC,WAAU;;oDAEf,wBACC,6LAAC,8HAAA,CAAA,cAAW;wDACV,KAAK;wDACL,KAAK,YAAY,gBAAgB;;;;;+DAEjC;kEACJ,6LAAC,8HAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB;;;;;;;;;;;;0DAIL,6LAAC;gDAAK,WAAU;0DACb,YAAY,gBAAgB;;;;;;;;;;;;;;;;;8CAInC,6LAAC,wIAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;wCAAC;sDAE3D,6LAAC,wIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6LAAC;gDAAE,WAAU;;oDACV;oDAAY;;;;;;;;;;;;sDAkBjB,6LAAC,wIAAA,CAAA,wBAAqB;;;;;sDAEtB,6LAAC;4CAAK,QAAQ,sJAAA,CAAA,cAAW;4CAAE,WAAU;sDACnC,cAAA,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAKb;wBAAmB;;;;;;;;;;;;;;;;;;AAK9B;GA3HM;;QAYa,qIAAA,CAAA,cAAW;;;KAZxB;uCA6HS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/hooks/use-mobile.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  // Initialize with undefined to avoid hydration mismatch\r\n  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined);\r\n\r\n  useEffect(() => {\r\n    // Check if window is available (client-side)\r\n    if (typeof window !== \"undefined\") {\r\n      const checkMobile = () => {\r\n        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n      };\r\n\r\n      // Initial check\r\n      checkMobile();\r\n\r\n      // Set up media query listener\r\n      const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n      const onChange = () => {\r\n        checkMobile();\r\n      };\r\n\r\n      mql.addEventListener(\"change\", onChange);\r\n      return () => mql.removeEventListener(\"change\", onChange);\r\n    }\r\n  }, []);\r\n\r\n  // Return false during SSR to avoid hydration issues\r\n  return isMobile ?? false;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,wDAAwD;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,6CAA6C;YAC7C,wCAAmC;gBACjC,MAAM;yDAAc;wBAClB,YAAY,OAAO,UAAU,GAAG;oBAClC;;gBAEA,gBAAgB;gBAChB;gBAEA,8BAA8B;gBAC9B,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;gBACvE,MAAM;sDAAW;wBACf;oBACF;;gBAEA,IAAI,gBAAgB,CAAC,UAAU;gBAC/B;6CAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;YACjD;QACF;gCAAG,EAAE;IAEL,oDAAoD;IACpD,OAAO,YAAY;AACrB;GA3BgB", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ThemeToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Moon, Sun, Monitor } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface ThemeToggleProps {\r\n  variant?: \"default\" | \"dashboard\";\r\n}\r\n\r\nexport function ThemeToggle({ variant = \"default\" }: ThemeToggleProps = {}) {\r\n  const { setTheme, theme } = useTheme();\r\n  const isMobile = useIsMobile();\r\n\r\n  // Mobile version with modern card design (only for default variant, not dashboard)\r\n  if (isMobile && variant === \"default\") {\r\n    return (\r\n      <div className=\"w-full\">\r\n        <div className=\"flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 border border-neutral-200/50 dark:border-neutral-700/50 backdrop-blur-sm\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <div className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-muted text-foreground\">\r\n              {theme === \"light\" ? (\r\n                <Sun className=\"h-5 w-5\" />\r\n              ) : theme === \"dark\" ? (\r\n                <Moon className=\"h-5 w-5\" />\r\n              ) : (\r\n                <Monitor className=\"h-5 w-5\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"font-medium text-foreground\">Theme</span>\r\n              <span className=\"text-xs text-muted-foreground capitalize\">\r\n                {theme || \"system\"}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 px-3\">\r\n                Change\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-40\">\r\n              <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n                <Sun className=\"mr-2 h-4 w-4\" />\r\n                <span>Light</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n                <Moon className=\"mr-2 h-4 w-4\" />\r\n                <span>Dark</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n                <Monitor className=\"mr-2 h-4 w-4\" />\r\n                <span>System</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Dashboard variant - simplified icon-only button matching avatar size\r\n  if (variant === \"dashboard\") {\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"h-9 w-9 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0\">\r\n            <Sun className=\"h-[1.1rem] w-[1.1rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n            <Moon className=\"absolute h-[1.1rem] w-[1.1rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n            <span className=\"sr-only\">Toggle theme</span>\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n            <Sun className=\"mr-2 h-4 w-4\" />\r\n            <span>Light</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n            <Moon className=\"mr-2 h-4 w-4\" />\r\n            <span>Dark</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n            <Monitor className=\"mr-2 h-4 w-4\" />\r\n            <span>System</span>\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n\r\n  // Desktop version (original)\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"outline\" size=\"icon\">\r\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n          <span className=\"sr-only\">Toggle theme</span>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\">\r\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n          <Sun className=\"mr-2 h-4 w-4\" />\r\n          <span>Light</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n          <Moon className=\"mr-2 h-4 w-4\" />\r\n          <span>Dark</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n          <Monitor className=\"mr-2 h-4 w-4\" />\r\n          <span>System</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;AAmBO,SAAS,YAAY,EAAE,UAAU,SAAS,EAAoB,GAAG,CAAC,CAAC;;IACxE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAE3B,mFAAmF;IACnF,IAAI,YAAY,YAAY,WAAW;QACrC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,UAAU,wBACT,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;2CACb,UAAU,uBACZ,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAGvB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,6LAAC;wCAAK,WAAU;kDACb,SAAS;;;;;;;;;;;;;;;;;;kCAIhB,6LAAC,wIAAA,CAAA,eAAY;;0CACX,6LAAC,wIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAIzD,6LAAC,wIAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,wIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOpB;IAEA,uEAAuE;IACvE,IAAI,YAAY,aAAa;QAC3B,qBACE,6LAAC,wIAAA,CAAA,eAAY;;8BACX,6LAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,WAAU;;0CAChC,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAG9B,6LAAC,wIAAA,CAAA,sBAAmB;oBAAC,OAAM;;sCACzB,6LAAC,wIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;sCAER,6LAAC,wIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,6BAA6B;IAC7B,qBACE,6LAAC,wIAAA,CAAA,eAAY;;0BACX,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC,wIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA3GgB;;QACc,mJAAA,CAAA,WAAQ;QACnB,yHAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/BottomNav.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { motion } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Home, Search, User, Store, Users } from \"lucide-react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\ninterface BottomNavItemProps {\r\n  href: string;\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  isActive: boolean;\r\n  isTablet?: boolean;\r\n  badge?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst BottomNavItem = ({\r\n  href,\r\n  icon,\r\n  label,\r\n  isActive,\r\n  isTablet = false,\r\n  badge,\r\n  disabled = false\r\n}: BottomNavItemProps) => {\r\n  const content = (\r\n    <>\r\n      <div className=\"relative mb-1\">\r\n        {icon}\r\n        {badge && (\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]\"\r\n          >\r\n            {badge}\r\n          </Badge>\r\n        )}\r\n      </div>\r\n      <span className={cn(\r\n        \"transition-all\",\r\n        isTablet ? \"text-[9px]\" : \"text-[10px]\"\r\n      )}>{label}</span>\r\n    </>\r\n  );\r\n\r\n  const itemClassName = cn(\r\n    \"flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors\",\r\n    isActive\r\n      ? \"text-[var(--brand-gold)]\"\r\n      : \"text-muted-foreground hover:text-[var(--brand-gold)]\",\r\n    disabled && \"opacity-70 pointer-events-none\"\r\n  );\r\n\r\n  if (disabled) {\r\n    return (\r\n      <div className={itemClassName}>\r\n        {content}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Link href={href} className={itemClassName}>\r\n      {content}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default function BottomNav() {\r\n  const pathname = usePathname();\r\n  const isMobile = useIsMobile();\r\n  const [isTablet, setIsTablet] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (typeof window === \"undefined\") return;\r\n\r\n    // Check if device is a tablet (between 768px and 1024px)\r\n    const checkTablet = () => {\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    // Initial check\r\n    checkTablet();\r\n\r\n    // Add event listener for resize\r\n    window.addEventListener('resize', checkTablet);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkTablet);\r\n  }, []);\r\n\r\n  // Don't render on desktop\r\n  if (!isMobile && !isTablet) {\r\n    return null;\r\n  }\r\n\r\n  // Unified navigation items for all layouts\r\n  // Determine the appropriate account link based on the current path\r\n  let accountLink = \"/login\";\r\n  let accountIsActive = false;\r\n\r\n  // If user is in business dashboard\r\n  if (pathname.startsWith(\"/dashboard/business\")) {\r\n    accountLink = \"/dashboard/business/settings\";\r\n    accountIsActive = pathname.includes(\"/dashboard/business/settings\");\r\n  }\r\n  // If user is in customer dashboard\r\n  else if (pathname.startsWith(\"/dashboard/customer\")) {\r\n    accountLink = \"/dashboard/customer/settings\";\r\n    accountIsActive = pathname.includes(\"/dashboard/customer/settings\");\r\n  }\r\n  // If user is in auth or onboarding flow\r\n  else if (pathname.startsWith(\"/login\") ||\r\n           pathname.startsWith(\"/choose-role\") || pathname.startsWith(\"/onboarding\")) {\r\n    accountLink = pathname; // Keep current page\r\n    accountIsActive = true;\r\n  }\r\n  // For public pages\r\n  else {\r\n    accountLink = \"/login\";\r\n    accountIsActive = pathname === \"/login\";\r\n  }\r\n\r\n  // Determine feed link based on current location and authentication status\r\n  let feedLink = \"/login\"; // Default to login for non-authenticated users\r\n  let feedIsActive = false;\r\n\r\n  // If user is in business dashboard, redirect to business dashboard (main feed page)\r\n  if (pathname.startsWith(\"/dashboard/business\")) {\r\n    feedLink = \"/dashboard/business\";\r\n    feedIsActive = pathname === \"/dashboard/business\";\r\n  }\r\n  // If user is in customer dashboard, redirect to customer dashboard (main feed page)\r\n  else if (pathname.startsWith(\"/dashboard/customer\")) {\r\n    feedLink = \"/dashboard/customer\";\r\n    feedIsActive = pathname === \"/dashboard/customer\";\r\n  }\r\n  // For all other pages (public pages), redirect to login\r\n  else {\r\n    feedLink = \"/login\";\r\n    feedIsActive = false;\r\n  }\r\n\r\n  // Unified navigation items\r\n  const navItems = [\r\n    {\r\n      key: \"home\",\r\n      href: pathname.startsWith(\"/dashboard/business\") ? \"/?view=home\" :\r\n            pathname.startsWith(\"/dashboard/customer\") ? \"/?view=home\" : \"/\",\r\n      icon: <Home size={20} />,\r\n      label: \"Home\",\r\n      isActive: pathname === \"/\" ||\r\n                pathname === \"/dashboard/business/overview\" ||\r\n                pathname === \"/dashboard/customer/overview\"\r\n    },\r\n    {\r\n      key: \"discover\",\r\n      href: \"/discover\",\r\n      icon: <Search size={20} />,\r\n      label: \"Discover\",\r\n      isActive: pathname === \"/discover\"\r\n    },\r\n    {\r\n      key: \"feed\",\r\n      href: feedLink,\r\n      icon: <Users size={20} />,\r\n      label: \"Feed\", // Always show \"Feed\" for consistency\r\n      isActive: feedIsActive\r\n    },\r\n    {\r\n      key: \"dukan-ai\",\r\n      href: \"#\",\r\n      icon: <Store size={20} />,\r\n      label: \"Dukan AI\",\r\n      isActive: false,\r\n      badge: \"Soon\",\r\n      disabled: true\r\n    },\r\n    {\r\n      key: \"account\",\r\n      href: accountLink,\r\n      icon: <User size={20} />,\r\n      label: \"Account\",\r\n      isActive: accountIsActive\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ y: 100 }}\r\n      animate={{ y: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n      className={cn(\r\n        \"fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2\",\r\n        isTablet ? \"h-14\" : \"h-16\"\r\n      )}\r\n    >\r\n      {navItems.map((item) => (\r\n        <BottomNavItem\r\n          key={item.key}\r\n          href={item.href}\r\n          icon={item.icon}\r\n          label={item.label}\r\n          isActive={item.isActive}\r\n          isTablet={isTablet}\r\n          badge={item.badge}\r\n          disabled={item.disabled}\r\n        />\r\n      ))}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;AAsBA,MAAM,gBAAgB,CAAC,EACrB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,KAAK,EACL,WAAW,KAAK,EACG;IACnB,MAAM,wBACJ;;0BACE,6LAAC;gBAAI,WAAU;;oBACZ;oBACA,uBACC,6LAAC,6HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAU;kCAET;;;;;;;;;;;;0BAIP,6LAAC;gBAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAChB,kBACA,WAAW,eAAe;0BACxB;;;;;;;;IAIR,MAAM,gBAAgB,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACrB,mFACA,WACI,6BACA,wDACJ,YAAY;IAGd,IAAI,UAAU;QACZ,qBACE,6LAAC;YAAI,WAAW;sBACb;;;;;;IAGP;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW;kBAC1B;;;;;;AAGP;KAlDM;AAoDS,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,uCAAmC;;YAAM;YAEzC,yDAAyD;YACzD,MAAM;mDAAc;oBAClB,YAAY,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;gBAC9D;;YAEA,gBAAgB;YAChB;YAEA,gCAAgC;YAChC,OAAO,gBAAgB,CAAC,UAAU;YAElC,UAAU;YACV;uCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;8BAAG,EAAE;IAEL,0BAA0B;IAC1B,IAAI,CAAC,YAAY,CAAC,UAAU;QAC1B,OAAO;IACT;IAEA,2CAA2C;IAC3C,mEAAmE;IACnE,IAAI,cAAc;IAClB,IAAI,kBAAkB;IAEtB,mCAAmC;IACnC,IAAI,SAAS,UAAU,CAAC,wBAAwB;QAC9C,cAAc;QACd,kBAAkB,SAAS,QAAQ,CAAC;IACtC,OAEK,IAAI,SAAS,UAAU,CAAC,wBAAwB;QACnD,cAAc;QACd,kBAAkB,SAAS,QAAQ,CAAC;IACtC,OAEK,IAAI,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,mBAAmB,SAAS,UAAU,CAAC,gBAAgB;QAClF,cAAc,UAAU,oBAAoB;QAC5C,kBAAkB;IACpB,OAEK;QACH,cAAc;QACd,kBAAkB,aAAa;IACjC;IAEA,0EAA0E;IAC1E,IAAI,WAAW,UAAU,+CAA+C;IACxE,IAAI,eAAe;IAEnB,oFAAoF;IACpF,IAAI,SAAS,UAAU,CAAC,wBAAwB;QAC9C,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK,IAAI,SAAS,UAAU,CAAC,wBAAwB;QACnD,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK;QACH,WAAW;QACX,eAAe;IACjB;IAEA,2BAA2B;IAC3B,MAAM,WAAW;QACf;YACE,KAAK;YACL,MAAM,SAAS,UAAU,CAAC,yBAAyB,gBAC7C,SAAS,UAAU,CAAC,yBAAyB,gBAAgB;YACnE,oBAAM,6LAAC,sMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU,aAAa,OACb,aAAa,kCACb,aAAa;QACzB;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,OAAO;YACP,UAAU,aAAa;QACzB;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,uMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG;QAAI;QAClB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wIACA,WAAW,SAAS;kBAGrB,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC;gBAEC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;gBACV,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;eAPlB,KAAK,GAAG;;;;;;;;;;AAYvB;GA/IwB;;QACL,qIAAA,CAAA,cAAW;QACX,yHAAA,CAAA,cAAW;;;MAFN", "debugId": null}}, {"offset": {"line": 1767, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/site-config.ts"], "sourcesContent": ["/**\r\n * Site-wide configuration file\r\n *\r\n * This file contains all the site-wide configuration data like:\r\n * - Contact information\r\n * - Site metadata\r\n * - Legal information\r\n *\r\n * Update this file to change information across the entire site\r\n */\r\n\r\nexport const siteConfig = {\r\n  name: \"Dukancard\",\r\n  description:\r\n    \"Create and share digital business cards, showcase products, and connect with customers.\",\r\n  url: \"https://dukancard.in\",\r\n  ogImage: \"https://dukancard.in/opengraph-image.png\",\r\n\r\n  // Contact Information\r\n  contact: {\r\n    email: \"<EMAIL>\",\r\n    phone: \"+91 8458060663\",\r\n    address: {\r\n      street: \"Bisra Road\",\r\n      city: \"Rourkela\",\r\n      state: \"Odisha\",\r\n      postalCode: \"769001\",\r\n      country: \"India\",\r\n      full: \"Bisra Road, Rourkela, Odisha - 769001\",\r\n    },\r\n    // Hours of operation\r\n    hours: \"Monday - Friday: 9:00 AM - 6:00 PM\",\r\n  },\r\n\r\n  // Legal\r\n  legal: {\r\n    privacyPolicy: \"/privacy\",\r\n    termsOfService: \"/terms\",\r\n    refundPolicy: \"/refund\",\r\n  },\r\n\r\n  // Support\r\n  support: {\r\n    email: \"<EMAIL>\",\r\n    phone: \"+91 8458060663\",\r\n    helpCenter: \"/support\",\r\n  },\r\n\r\n  // Advertising\r\n  advertising: {\r\n    email: \"<EMAIL>\",\r\n    phone: \"+************\",\r\n    page: \"/advertise\",\r\n  },\r\n};\r\n\r\nexport type SiteConfig = typeof siteConfig;\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;AAEM,MAAM,aAAa;IACxB,MAAM;IACN,aACE;IACF,KAAK;IACL,SAAS;IAET,sBAAsB;IACtB,SAAS;QACP,OAAO;QACP,OAAO;QACP,SAAS;YACP,QAAQ;YACR,MAAM;YACN,OAAO;YACP,YAAY;YACZ,SAAS;YACT,MAAM;QACR;QACA,qBAAqB;QACrB,OAAO;IACT;IAEA,QAAQ;IACR,OAAO;QACL,eAAe;QACf,gBAAgB;QAChB,cAAc;IAChB;IAEA,UAAU;IACV,SAAS;QACP,OAAO;QACP,OAAO;QACP,YAAY;IACd;IAEA,cAAc;IACd,aAAa;QACX,OAAO;QACP,OAAO;QACP,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/AdvertiseButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Megaphone } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { siteConfig } from \"@/lib/site-config\";\r\nimport { usePathname } from \"next/navigation\";\r\n\r\n// CSS for vertical text\r\nconst verticalTextStyle = {\r\n  writingMode: 'vertical-rl' as const,\r\n  textOrientation: 'mixed' as const,\r\n  transform: 'rotate(180deg)',\r\n  letterSpacing: '0.05em',\r\n  fontSize: '0.8rem',\r\n  fontWeight: 600,\r\n};\r\n\r\nexport default function AdvertiseButton() {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const [isClient, setIsClient] = useState(false);\r\n  const pathname = usePathname();\r\n\r\n  // Use useEffect to detect client-side rendering\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Don't show on dashboard pages or advertise page\r\n  const isDashboardPage = pathname?.includes(\"/dashboard\");\r\n  const isAdvertisePage = pathname === \"/advertise\";\r\n\r\n  if (!isClient || isDashboardPage || isAdvertisePage) {\r\n    return null; // Don't render during SSR, on dashboard pages, or on advertise page\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Desktop and tablet version - side button */}\r\n      <div className=\"fixed right-0 top-1/2 -translate-y-1/2 z-40 hidden sm:block\">\r\n        <div className=\"relative group\">\r\n          {/* Button glow effect */}\r\n          <motion.div\r\n            className=\"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-l-lg blur-md\"\r\n            animate={{ opacity: isHovered ? 0.8 : 0.5 }}\r\n            transition={{ duration: 0.3 }}\r\n          />\r\n\r\n          <Link href={siteConfig.advertising.page}>\r\n            <motion.div\r\n              className=\"relative\"\r\n              onMouseEnter={() => setIsHovered(true)}\r\n              onMouseLeave={() => setIsHovered(false)}\r\n              whileHover={{ scale: 1.03 }}\r\n              whileTap={{ scale: 0.98 }}\r\n            >\r\n              <div\r\n                className=\"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 px-2 py-6 rounded-l-lg font-medium text-xs relative overflow-hidden shadow-lg flex flex-col items-center justify-center w-10 h-32\"\r\n              >\r\n                {/* Button content with animated icon */}\r\n                <div className=\"flex flex-col items-center justify-center gap-2\">\r\n                  <Megaphone className=\"w-4 h-4\" />\r\n                  <span style={verticalTextStyle}>Advertise</span>\r\n                </div>\r\n\r\n                {/* Shimmer effect */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none\"\r\n                  initial={{ x: \"-100%\" }}\r\n                  animate={{ x: \"100%\" }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\",\r\n                    repeatDelay: 1\r\n                  }}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile version - floating button */}\r\n      <div className=\"fixed left-4 bottom-20 z-40 sm:hidden\">\r\n        <div className=\"relative group\">\r\n          {/* Button glow effect */}\r\n          <motion.div\r\n            className=\"absolute -inset-0.5 bg-gradient-to-r from-[var(--brand-gold)]/30 to-[var(--brand-gold)]/50 rounded-full blur-md\"\r\n            animate={{ opacity: 0.6 }}\r\n            transition={{ duration: 0.3 }}\r\n          />\r\n\r\n          <Link href={siteConfig.advertising.page}>\r\n            <motion.div\r\n              className=\"relative\"\r\n              whileTap={{ scale: 0.95 }}\r\n            >\r\n              <div\r\n                className=\"cursor-pointer bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/80 text-black dark:text-neutral-900 p-2 rounded-full font-medium text-xs relative overflow-hidden shadow-md flex items-center justify-center w-10 h-10\"\r\n              >\r\n                <Megaphone className=\"w-4 h-4\" />\r\n\r\n                {/* Shimmer effect */}\r\n                <motion.div\r\n                  className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none\"\r\n                  initial={{ x: \"-100%\" }}\r\n                  animate={{ x: \"100%\" }}\r\n                  transition={{\r\n                    duration: 2,\r\n                    repeat: Infinity,\r\n                    ease: \"linear\",\r\n                    repeatDelay: 1\r\n                  }}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,wBAAwB;AACxB,MAAM,oBAAoB;IACxB,aAAa;IACb,iBAAiB;IACjB,WAAW;IACX,eAAe;IACf,UAAU;IACV,YAAY;AACd;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,YAAY;QACd;oCAAG,EAAE;IAEL,kDAAkD;IAClD,MAAM,kBAAkB,UAAU,SAAS;IAC3C,MAAM,kBAAkB,aAAa;IAErC,IAAI,CAAC,YAAY,mBAAmB,iBAAiB;QACnD,OAAO,MAAM,oEAAoE;IACnF;IAEA,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS,YAAY,MAAM;4BAAI;4BAC1C,YAAY;gCAAE,UAAU;4BAAI;;;;;;sCAG9B,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,wHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,IAAI;sCACrC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,cAAc,IAAM,aAAa;gCACjC,cAAc,IAAM,aAAa;gCACjC,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC;oCACC,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6LAAC;oDAAK,OAAO;8DAAmB;;;;;;;;;;;;sDAIlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;4CAAQ;4CACtB,SAAS;gDAAE,GAAG;4CAAO;4CACrB,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,aAAa;4CACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAI;4BACxB,YAAY;gCAAE,UAAU;4BAAI;;;;;;sCAG9B,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAM,wHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,IAAI;sCACrC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC;oCACC,WAAU;;sDAEV,6LAAC,+MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDAGrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,GAAG;4CAAQ;4CACtB,SAAS;gDAAE,GAAG;4CAAO;4CACrB,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;gDACN,aAAa;4CACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB;GAxGwB;;QAGL,qIAAA,CAAA,cAAW;;;KAHN", "debugId": null}}]}