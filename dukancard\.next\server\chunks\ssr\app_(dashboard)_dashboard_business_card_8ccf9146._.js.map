{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/utils/scrollToError.ts"], "sourcesContent": ["/**\r\n * Scrolls to the first form error with enhanced smooth scrolling behavior\r\n * @param formId The ID of the form containing the errors\r\n */\r\nexport function scrollToFirstError(formId: string = 'business-card-form'): void {\r\n  // Wait for the DOM to update with error messages\r\n  setTimeout(() => {\r\n    // Find the form\r\n    const form = document.getElementById(formId);\r\n    if (!form) return;\r\n\r\n    // Find all error messages within the form\r\n    const errorElements = form.querySelectorAll('[role=\"alert\"]');\r\n    if (!errorElements || errorElements.length === 0) {\r\n      // If no error elements with role=\"alert\", try to find form fields with errors\r\n      const errorFields = form.querySelectorAll('.error-field, [aria-invalid=\"true\"]');\r\n      if (errorFields && errorFields.length > 0) {\r\n        smoothScrollToElement(errorFields[0]);\r\n        return;\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Get the first error element\r\n    const firstError = errorElements[0];\r\n\r\n    // Use custom smooth scroll\r\n    smoothScrollToElement(firstError);\r\n  }, 100); // Small delay to ensure DOM has updated\r\n}\r\n\r\n/**\r\n * Performs a smoother scroll animation to an element\r\n * @param element The element to scroll to\r\n */\r\nfunction smoothScrollToElement(element: Element): void {\r\n  // Get the element's position\r\n  const rect = element.getBoundingClientRect();\r\n  const targetPosition = window.scrollY + rect.top - 150; // 150px offset from top\r\n  const startPosition = window.scrollY;\r\n  const distance = targetPosition - startPosition;\r\n\r\n  // Add a subtle highlight effect to draw attention\r\n  element.classList.add('error-highlight');\r\n  setTimeout(() => {\r\n    element.classList.remove('error-highlight');\r\n  }, 3000); // Longer highlight duration\r\n\r\n  // Use a more sophisticated easing function for smoother animation\r\n  const easeOutCubic = (t: number): number => 1 - Math.pow(1 - t, 3);\r\n\r\n  // Animation parameters\r\n  const duration = 800; // Longer duration for smoother effect\r\n  const startTime = performance.now();\r\n\r\n  // Animation function\r\n  function animateScroll(currentTime: number) {\r\n    const elapsedTime = currentTime - startTime;\r\n    const progress = Math.min(elapsedTime / duration, 1);\r\n    const easedProgress = easeOutCubic(progress);\r\n\r\n    window.scrollTo({\r\n      top: startPosition + distance * easedProgress,\r\n      behavior: 'auto' // We're handling the animation manually\r\n    });\r\n\r\n    if (progress < 1) {\r\n      requestAnimationFrame(animateScroll);\r\n    }\r\n  }\r\n\r\n  // Start the animation\r\n  requestAnimationFrame(animateScroll);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,mBAAmB,SAAiB,oBAAoB;IACtE,iDAAiD;IACjD,WAAW;QACT,gBAAgB;QAChB,MAAM,OAAO,SAAS,cAAc,CAAC;QACrC,IAAI,CAAC,MAAM;QAEX,0CAA0C;QAC1C,MAAM,gBAAgB,KAAK,gBAAgB,CAAC;QAC5C,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;YAChD,8EAA8E;YAC9E,MAAM,cAAc,KAAK,gBAAgB,CAAC;YAC1C,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;gBACzC,sBAAsB,WAAW,CAAC,EAAE;gBACpC;YACF;YACA;QACF;QAEA,8BAA8B;QAC9B,MAAM,aAAa,aAAa,CAAC,EAAE;QAEnC,2BAA2B;QAC3B,sBAAsB;IACxB,GAAG,MAAM,wCAAwC;AACnD;AAEA;;;CAGC,GACD,SAAS,sBAAsB,OAAgB;IAC7C,6BAA6B;IAC7B,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,MAAM,iBAAiB,OAAO,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,OAAO,OAAO;IACpC,MAAM,WAAW,iBAAiB;IAElC,kDAAkD;IAClD,QAAQ,SAAS,CAAC,GAAG,CAAC;IACtB,WAAW;QACT,QAAQ,SAAS,CAAC,MAAM,CAAC;IAC3B,GAAG,OAAO,4BAA4B;IAEtC,kEAAkE;IAClE,MAAM,eAAe,CAAC,IAAsB,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IAEhE,uBAAuB;IACvB,MAAM,WAAW,KAAK,sCAAsC;IAC5D,MAAM,YAAY,YAAY,GAAG;IAEjC,qBAAqB;IACrB,SAAS,cAAc,WAAmB;QACxC,MAAM,cAAc,cAAc;QAClC,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc,UAAU;QAClD,MAAM,gBAAgB,aAAa;QAEnC,OAAO,QAAQ,CAAC;YACd,KAAK,gBAAgB,WAAW;YAChC,UAAU,OAAO,wCAAwC;QAC3D;QAEA,IAAI,WAAW,GAAG;YAChB,sBAAsB;QACxB;IACF;IAEA,sBAAsB;IACtB,sBAAsB;AACxB", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/schema.ts"], "sourcesContent": ["import * as z from \"zod\";\r\nimport { IndianMobileSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Regular expression for validating hex color codes (e.g., #RRGGBB, #RGB)\r\n// const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/; // Removed as theme_color is removed\r\n\r\n// Zod schema for business card data validation (Phase 1)\r\nexport const businessCardSchema = z.object({\r\n  // Optional fields first\r\n  logo_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for logo/profile photo.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .nullable(), // Allow empty string, null, or valid URL\r\n  established_year: z\r\n    .number()\r\n    .int({ message: \"Established year must be a whole number.\" })\r\n    .min(1800, { message: \"Established year must be after 1800.\" })\r\n    .max(new Date().getFullYear(), { message: \"Established year cannot be in the future.\" })\r\n    .optional()\r\n    .nullable(),\r\n  // Address broken down - NOW REQUIRED (from onboarding)\r\n  address_line: z\r\n    .string()\r\n    .min(1, { message: \"Address line is required.\" })\r\n    .max(100, { message: \"Address line cannot exceed 100 characters.\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality/area is required.\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required.\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required.\" }),\r\n  pincode: z\r\n    .string()\r\n    .min(6, { message: \"Pincode must be 6 digits.\" })\r\n    .max(6, { message: \"Pincode must be 6 digits.\" })\r\n    .regex(/^\\d+$/, { message: \"Pincode must contain only digits.\" }),\r\n  phone: IndianMobileSchema, // Primary display phone - NOW REQUIRED\r\n  // timing_info removed\r\n  // delivery_info removed\r\n  // website_url removed\r\n  instagram_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Instagram.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  facebook_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Facebook.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // linkedin_url removed\r\n  // twitter_url removed\r\n  // youtube_url removed\r\n  whatsapp_number: IndianMobileSchema // For wa.me link\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // call_number removed\r\n  about_bio: z\r\n    .string()\r\n    .max(100, { message: \"Bio cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  theme_color: z // Added for Growth plan\r\n    .string()\r\n    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {\r\n      message: \"Invalid hex color format (e.g., #RRGGBB or #RGB).\",\r\n    })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // card_texture field removed as it doesn't exist in the database\r\n  business_hours: z.any().optional().nullable(), // Added for Growth plan - Using z.any() for now, refine if specific structure needed\r\n  delivery_info: z // Added for Growth plan\r\n    .string()\r\n    .max(100, { message: \"Delivery info cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  business_category: z\r\n    .string()\r\n    .min(1, { message: \"Business category is required.\" }),\r\n  google_maps_url: z\r\n    .string()\r\n    .url({ message: \"Please enter a valid Google Maps URL.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .refine((url) => {\r\n      if (!url || url === \"\") return true; // Allow empty\r\n      // Validate Google Maps URL patterns\r\n      const googleMapsPatterns = [\r\n        /^https:\\/\\/maps\\.app\\.goo\\.gl\\/[a-zA-Z0-9]+$/,\r\n        /^https:\\/\\/www\\.google\\.com\\/maps\\//,\r\n        /^https:\\/\\/goo\\.gl\\/maps\\//,\r\n        /^https:\\/\\/maps\\.google\\.com\\//\r\n      ];\r\n      return googleMapsPatterns.some(pattern => pattern.test(url));\r\n    }, {\r\n      message: \"Please enter a valid Google Maps URL (e.g., https://maps.app.goo.gl/... or https://www.google.com/maps/...)\"\r\n    }),\r\n  status: z.enum([\"online\", \"offline\"]).default(\"offline\"),\r\n  // Custom branding fields for Pro/Enterprise users\r\n  custom_branding: z.object({\r\n    custom_header_text: z.string().max(50).optional().or(z.literal(\"\")),\r\n    custom_header_image_url: z.string().url().optional().or(z.literal(\"\")), // Legacy field\r\n    custom_header_image_light_url: z.string().url().optional().or(z.literal(\"\")), // Light theme\r\n    custom_header_image_dark_url: z.string().url().optional().or(z.literal(\"\")), // Dark theme\r\n    hide_dukancard_branding: z.boolean().optional(),\r\n    // File objects for pending uploads (not saved to database)\r\n    pending_light_header_file: z.any().optional(), // File object for light theme\r\n    pending_dark_header_file: z.any().optional(), // File object for dark theme\r\n  }).optional()\r\n  .refine((data) => {\r\n    // Only require custom_header_text OR any header image if hide_dukancard_branding is explicitly true\r\n    if (data?.hide_dukancard_branding === true) {\r\n      const hasText = data?.custom_header_text && data.custom_header_text.trim() !== \"\";\r\n      const hasLegacyImage = data?.custom_header_image_url && data.custom_header_image_url.trim() !== \"\";\r\n      const hasLightImage = data?.custom_header_image_light_url && data.custom_header_image_light_url.trim() !== \"\";\r\n      const hasDarkImage = data?.custom_header_image_dark_url && data.custom_header_image_dark_url.trim() !== \"\";\r\n\r\n      if (!hasText && !hasLegacyImage && !hasLightImage && !hasDarkImage) {\r\n        return false;\r\n      }\r\n    }\r\n    return true;\r\n  }, {\r\n    message: \"Custom header text or image is required when hiding Dukancard branding\",\r\n    path: [\"custom_header_text\"]\r\n  }),\r\n  // Custom ads for Pro/Enterprise users\r\n  custom_ads: z.object({\r\n    enabled: z.boolean().optional(),\r\n    image_url: z.string().url().optional().or(z.literal(\"\")),\r\n    link_url: z.string().url().optional().or(z.literal(\"\")),\r\n    uploaded_at: z.string().optional().or(z.literal(\"\")).nullable(),\r\n  }).optional(),\r\n  business_slug: z\r\n    .string()\r\n    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {\r\n      message:\r\n        \"Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen.\",\r\n    })\r\n    .min(3, { message: \"Slug must be at least 3 characters long.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n\r\n  // Required fields\r\n  member_name: z\r\n    .string()\r\n    .min(1, { message: \"Member name is required.\" })\r\n    .max(50, { message: \"Name cannot exceed 50 characters.\" }),\r\n  title: z\r\n    .string()\r\n    .min(1, { message: \"Title/Designation is required.\" })\r\n    .max(50, { message: \"Title cannot exceed 50 characters.\" }),\r\n  business_name: z\r\n    .string()\r\n    .min(1, { message: \"Business name is required.\" })\r\n    .max(100, { message: \"Business name cannot exceed 100 characters.\" }),\r\n\r\n  // Read-only/managed fields (keep for type safety if needed)\r\n  id: z.string().uuid().optional(),\r\n  contact_email: z.string().email({ message: \"Please enter a valid email address\" }).min(1, { message: \"Contact email is required\" }),\r\n  has_active_subscription: z.boolean().optional(),\r\n  trial_end_date: z.string().optional().nullable(), // Database returns string, not Date\r\n  created_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n  updated_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n\r\n  // Interaction fields (added in Phase 2) - make optional as they might not always be fetched\r\n  total_likes: z.number().int().nonnegative().optional(),\r\n  total_subscriptions: z.number().int().nonnegative().optional(),\r\n  average_rating: z.number().nonnegative().optional(),\r\n  total_visits: z.number().int().nonnegative().optional(),\r\n});\r\n\r\n// TypeScript type inferred from the Zod schema\r\nexport type BusinessCardData = z.infer<typeof businessCardSchema>;\r\n\r\n// Default values for initializing the form or preview (Phase 1)\r\nexport const defaultBusinessCardData: Partial<BusinessCardData> = {\r\n  member_name: \"\",\r\n  title: \"\",\r\n  business_name: \"\",\r\n  logo_url: null,\r\n  established_year: null,\r\n  address_line: \"\",\r\n  locality: \"\",\r\n  city: \"\",\r\n  state: \"\",\r\n  pincode: \"\",\r\n  phone: \"\",\r\n  instagram_url: \"\",\r\n  facebook_url: \"\",\r\n  whatsapp_number: \"\",\r\n  about_bio: \"\",\r\n  theme_color: \"\",\r\n  business_hours: null,\r\n  delivery_info: \"\",\r\n  business_category: \"\",\r\n  google_maps_url: \"\",\r\n  status: \"offline\",\r\n  business_slug: \"\",\r\n  contact_email: \"\", // Added contact_email field\r\n  custom_branding: {\r\n    custom_header_text: \"\",\r\n    custom_header_image_url: \"\", // Legacy field\r\n    custom_header_image_light_url: \"\", // Light theme\r\n    custom_header_image_dark_url: \"\", // Dark theme\r\n    hide_dukancard_branding: false,\r\n    pending_light_header_file: null, // File object for light theme\r\n    pending_dark_header_file: null, // File object for dark theme\r\n  },\r\n  custom_ads: {\r\n    enabled: false,\r\n    image_url: \"\",\r\n    link_url: \"\",\r\n    uploaded_at: null,\r\n  },\r\n};\r\n\r\n// Define which fields are strictly required to go online\r\nexport const requiredFieldsForOnline: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\",\r\n  \"contact_email\", // Added contact_email as required for online status\r\n  \"business_category\" // Added business_category as required for online status\r\n];\r\n\r\n// Define which fields are required for saving regardless of status (all onboarding fields except plan)\r\nexport const requiredFieldsForSaving: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"contact_email\",\r\n  \"business_category\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\"\r\n];\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAMO,MAAM,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACzC,wBAAwB;IACxB,UAAU,CAAA,GAAA,oIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA6C,GAC5D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE,KACb,QAAQ;IACX,kBAAkB,CAAA,GAAA,oIAAA,CAAA,SACT,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA2C,GAC1D,GAAG,CAAC,MAAM;QAAE,SAAS;IAAuC,GAC5D,GAAG,CAAC,IAAI,OAAO,WAAW,IAAI;QAAE,SAAS;IAA4C,GACrF,QAAQ,GACR,QAAQ;IACX,uDAAuD;IACvD,cAAc,CAAA,GAAA,oIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA6C;IACpE,UAAU,CAAA,GAAA,oIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;IAClD,MAAM,CAAA,GAAA,oIAAA,CAAA,SACG,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB;IACzC,OAAO,CAAA,GAAA,oIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAqB;IAC1C,SAAS,CAAA,GAAA,oIAAA,CAAA,SACA,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,KAAK,CAAC,SAAS;QAAE,SAAS;IAAoC;IACjE,OAAO,6HAAA,CAAA,qBAAkB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,sBAAsB;IACtB,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAoC,GACnD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,CAAA,GAAA,oIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAmC,GAClD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,iBAAiB,8HAAmB,iBAAiB;IAApC,CAAA,qBAAkB,CAChC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,sBAAsB;IACtB,WAAW,CAAA,GAAA,oIAAA,CAAA,SACF,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAAoC,GACxD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,aAAa,CAAA,GAAA,oIAAA,CAAA,SACJ,AAAD,IACL,KAAK,CAAC,sCAAsC;QAC3C,SAAS;IACX,GACC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,iEAAiE;IACjE,gBAAgB,CAAA,GAAA,oIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C,GAClE,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,mBAAmB,CAAA,GAAA,oIAAA,CAAA,SACV,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC;IACtD,iBAAiB,CAAA,GAAA,oIAAA,CAAA,SACR,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAwC,GACvD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE,KACb,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,OAAO,QAAQ,IAAI,OAAO,MAAM,cAAc;QACnD,oCAAoC;QACpC,MAAM,qBAAqB;YACzB;YACA;YACA;YACA;SACD;QACD,OAAO,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;IACzD,GAAG;QACD,SAAS;IACX;IACF,QAAQ,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAU;KAAU,EAAE,OAAO,CAAC;IAC9C,kDAAkD;IAClD,iBAAiB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;QACxB,oBAAoB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QAC/D,yBAAyB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QAClE,+BAA+B,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACxE,8BAA8B,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACvE,yBAAyB,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7C,2DAA2D;QAC3D,2BAA2B,CAAA,GAAA,oIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;QAC3C,0BAA0B,CAAA,GAAA,oIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;IAC5C,GAAG,QAAQ,GACV,MAAM,CAAC,CAAC;QACP,oGAAoG;QACpG,IAAI,MAAM,4BAA4B,MAAM;YAC1C,MAAM,UAAU,MAAM,sBAAsB,KAAK,kBAAkB,CAAC,IAAI,OAAO;YAC/E,MAAM,iBAAiB,MAAM,2BAA2B,KAAK,uBAAuB,CAAC,IAAI,OAAO;YAChG,MAAM,gBAAgB,MAAM,iCAAiC,KAAK,6BAA6B,CAAC,IAAI,OAAO;YAC3G,MAAM,eAAe,MAAM,gCAAgC,KAAK,4BAA4B,CAAC,IAAI,OAAO;YAExG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,cAAc;gBAClE,OAAO;YACT;QACF;QACA,OAAO;IACT,GAAG;QACD,SAAS;QACT,MAAM;YAAC;SAAqB;IAC9B;IACA,sCAAsC;IACtC,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;QACnB,SAAS,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7B,WAAW,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACpD,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACnD,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE,KAAK,QAAQ;IAC/D,GAAG,QAAQ;IACX,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,KAAK,CAAC,8BAA8B;QACnC,SACE;IACJ,GACC,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAEhB,kBAAkB;IAClB,aAAa,CAAA,GAAA,oIAAA,CAAA,SACJ,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAC7C,GAAG,CAAC,IAAI;QAAE,SAAS;IAAoC;IAC1D,OAAO,CAAA,GAAA,oIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC,GACnD,GAAG,CAAC,IAAI;QAAE,SAAS;IAAqC;IAC3D,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B,GAC/C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C;IAErE,4DAA4D;IAC5D,IAAI,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,GAAG,QAAQ;IAC9B,eAAe,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAAqC,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B;IACjI,yBAAyB,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;IAC7C,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC9C,YAAY,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IACA,YAAY,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IAEA,4FAA4F;IAC5F,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IACpD,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IAC5D,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,WAAW,GAAG,QAAQ;IACjD,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;AACvD;AAMO,MAAM,0BAAqD;IAChE,aAAa;IACb,OAAO;IACP,eAAe;IACf,UAAU;IACV,kBAAkB;IAClB,cAAc;IACd,UAAU;IACV,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IACnB,iBAAiB;IACjB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,iBAAiB;QACf,oBAAoB;QACpB,yBAAyB;QACzB,+BAA+B;QAC/B,8BAA8B;QAC9B,yBAAyB;QACzB,2BAA2B;QAC3B,0BAA0B;IAC5B;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,aAAa;IACf;AACF;AAGO,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB,wDAAwD;CAC7E;AAGM,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions.ts"], "sourcesContent": ["// Re-export all actions from organized modules\r\nexport * from \"./business-card/updateBusinessCard\";\r\nexport * from \"./business-card/getBusinessCardData\";\r\nexport * from \"./logo/logoActions\";\r\nexport * from \"./slug/slugUtils\";\r\nexport * from \"./public/publicCardActions\";\r\n"], "names": [], "mappings": "AAAA,+CAA+C", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/business-card/updateBusinessCard.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { BusinessCardData } from \"../schema\";\r\nimport { validateBusinessCardData } from \"../validation/businessCardValidation\";\r\nimport { checkSubscriptionStatus } from \"../data/subscriptionChecker\";\r\nimport { generateUniqueSlug } from \"../slug/slugUtils\";\r\nimport { processBusinessHours } from \"../utils/businessHoursProcessor\";\r\nimport { uploadThemeHeaderImage, deleteThemeHeaderImage, cleanupOldThemeHeaderImages } from \"../actions/themeHeaderActions\";\r\n\r\n/**\r\n * Updates business card data with validation and processing\r\n * @param formData - The business card data to update\r\n * @returns Success/error response with updated data\r\n */\r\nexport async function updateBusinessCard(\r\n  formData: BusinessCardData\r\n): Promise<{ success: boolean; error?: string; data?: BusinessCardData }> {\r\n  const supabase = await createClient();\r\n\r\n  // 1. Validate the incoming data\r\n  const validatedFields = validateBusinessCardData(formData);\r\n\r\n  if (!validatedFields.success) {\r\n    console.error(\r\n      \"Validation Error:\",\r\n      validatedFields.error.flatten().fieldErrors\r\n    );\r\n    return {\r\n      success: false,\r\n      error: \"Invalid data provided. Please check the form fields.\",\r\n    };\r\n  }\r\n\r\n  // 2. Get the authenticated user\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    console.error(\"Auth Error:\", authError);\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Get existing profile to compare phone numbers\r\n  const { data: existingProfile, error: profileError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"phone\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (profileError) {\r\n    console.error(\"Profile fetch error:\", profileError);\r\n    return { success: false, error: \"Failed to fetch existing profile.\" };\r\n  }\r\n\r\n  // 3. Check subscription status if going online\r\n  if (validatedFields.data.status === \"online\") {\r\n    const subscriptionCheck = await checkSubscriptionStatus(user.id);\r\n    if (!subscriptionCheck.canGoOnline) {\r\n      return {\r\n        success: false,\r\n        error: subscriptionCheck.error || \"Cannot set card to online status.\"\r\n      };\r\n    }\r\n  }\r\n\r\n  // 4. Handle Slug Logic if going online\r\n  let finalSlug = validatedFields.data.business_slug;\r\n\r\n  if (validatedFields.data.status === \"online\") {\r\n    const slugResult = await generateUniqueSlug(\r\n      validatedFields.data.business_name,\r\n      finalSlug || \"\",\r\n      user.id\r\n    );\r\n\r\n    if (!slugResult.success) {\r\n      return {\r\n        success: false,\r\n        error: slugResult.error || \"Failed to generate unique slug.\"\r\n      };\r\n    }\r\n\r\n    finalSlug = slugResult.slug;\r\n  } else {\r\n    finalSlug = validatedFields.data.business_slug;\r\n  }\r\n\r\n  // 5. Handle theme-specific header image uploads\r\n  const updatedCustomBranding = { ...validatedFields.data.custom_branding };\r\n\r\n  // Handle light theme header upload\r\n  if (validatedFields.data.custom_branding?.pending_light_header_file) {\r\n    const lightFile = validatedFields.data.custom_branding.pending_light_header_file as File;\r\n    const lightUploadResult = await uploadThemeHeaderImage(lightFile, 'light');\r\n\r\n    if (lightUploadResult.success && lightUploadResult.url) {\r\n      // Clean up old light theme images\r\n      if (updatedCustomBranding.custom_header_image_light_url) {\r\n        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_light_url);\r\n      }\r\n      await cleanupOldThemeHeaderImages(user.id, 'light', lightUploadResult.url);\r\n\r\n      updatedCustomBranding.custom_header_image_light_url = lightUploadResult.url;\r\n    } else {\r\n      console.error(\"Light theme header upload failed:\", lightUploadResult.error);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload light theme header: ${lightUploadResult.error}`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // Handle dark theme header upload\r\n  if (validatedFields.data.custom_branding?.pending_dark_header_file) {\r\n    const darkFile = validatedFields.data.custom_branding.pending_dark_header_file as File;\r\n    const darkUploadResult = await uploadThemeHeaderImage(darkFile, 'dark');\r\n\r\n    if (darkUploadResult.success && darkUploadResult.url) {\r\n      // Clean up old dark theme images\r\n      if (updatedCustomBranding.custom_header_image_dark_url) {\r\n        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_dark_url);\r\n      }\r\n      await cleanupOldThemeHeaderImages(user.id, 'dark', darkUploadResult.url);\r\n\r\n      updatedCustomBranding.custom_header_image_dark_url = darkUploadResult.url;\r\n    } else {\r\n      console.error(\"Dark theme header upload failed:\", darkUploadResult.error);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload dark theme header: ${darkUploadResult.error}`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // Handle deletion of theme-specific headers (when URL is empty but no new file)\r\n  if (validatedFields.data.custom_branding?.custom_header_image_light_url === \"\" &&\r\n      !validatedFields.data.custom_branding?.pending_light_header_file) {\r\n    // Get current light URL from database to delete\r\n    const { data: currentProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_branding\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (currentProfile?.custom_branding?.custom_header_image_light_url) {\r\n      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_light_url);\r\n    }\r\n    updatedCustomBranding.custom_header_image_light_url = \"\";\r\n  }\r\n\r\n  if (validatedFields.data.custom_branding?.custom_header_image_dark_url === \"\" &&\r\n      !validatedFields.data.custom_branding?.pending_dark_header_file) {\r\n    // Get current dark URL from database to delete\r\n    const { data: currentProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_branding\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (currentProfile?.custom_branding?.custom_header_image_dark_url) {\r\n      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_dark_url);\r\n    }\r\n    updatedCustomBranding.custom_header_image_dark_url = \"\";\r\n  }\r\n\r\n  // Remove pending file fields from the data to be saved to database\r\n  delete updatedCustomBranding.pending_light_header_file;\r\n  delete updatedCustomBranding.pending_dark_header_file;\r\n\r\n  // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n  // 7. Prepare data for Supabase update\r\n  const businessHoursData = processBusinessHours(validatedFields.data.business_hours);\r\n\r\n  const dataToUpdate: Partial<BusinessCardData> = {\r\n    business_name: validatedFields.data.business_name,\r\n    member_name: validatedFields.data.member_name,\r\n    title: validatedFields.data.title,\r\n    logo_url: validatedFields.data.logo_url,\r\n    established_year: validatedFields.data.established_year,\r\n    address_line: validatedFields.data.address_line,\r\n    city: validatedFields.data.city,\r\n    state: validatedFields.data.state,\r\n    pincode: validatedFields.data.pincode,\r\n    phone: validatedFields.data.phone,\r\n    delivery_info: validatedFields.data.delivery_info,\r\n    google_maps_url: validatedFields.data.google_maps_url,\r\n    instagram_url: validatedFields.data.instagram_url,\r\n    facebook_url: validatedFields.data.facebook_url,\r\n    whatsapp_number: validatedFields.data.whatsapp_number,\r\n    about_bio: validatedFields.data.about_bio,\r\n    locality: validatedFields.data.locality,\r\n    theme_color: validatedFields.data.theme_color,\r\n    business_hours: businessHoursData,\r\n    status: validatedFields.data.status,\r\n    business_slug: finalSlug,\r\n    contact_email: validatedFields.data.contact_email,\r\n    business_category: validatedFields.data.business_category,\r\n    custom_branding: updatedCustomBranding,\r\n    custom_ads: validatedFields.data.custom_ads,\r\n  };\r\n\r\n  // 7. Update the business profile in Supabase\r\n  const { data: updatedProfile, error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update(dataToUpdate)\r\n    .eq(\"id\", user.id)\r\n    .select(\r\n      `\r\n      id, business_name, member_name, title, logo_url, address_line, city, state, pincode, locality,\r\n      phone, instagram_url, facebook_url, whatsapp_number, about_bio, status, business_slug,\r\n      theme_color, delivery_info, business_hours, contact_email, has_active_subscription,\r\n      trial_end_date, created_at, updated_at, total_likes, total_subscriptions, average_rating,\r\n      business_category, custom_branding, custom_ads, google_maps_url, established_year\r\n    `\r\n    )\r\n    .single();\r\n\r\n  if (updateError) {\r\n    console.error(\"Supabase Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  if (!updatedProfile) {\r\n    return {\r\n      success: false,\r\n      error: \"Failed to update profile. Profile not found after update.\",\r\n    };\r\n  }\r\n\r\n  // 8. Update phone in Supabase auth.users table if phone was changed\r\n  if (\r\n    validatedFields.data.phone &&\r\n    validatedFields.data.phone !== existingProfile.phone\r\n  ) {\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${validatedFields.data.phone}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.warn('Failed to update auth phone field:', authUpdateError.message);\r\n      // Don't fail the operation for this, just log the warning\r\n      // The business_profiles table is updated successfully\r\n    }\r\n  }\r\n\r\n  // 9. Revalidate paths\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  if (dataToUpdate.status === \"online\" && dataToUpdate.business_slug) {\r\n    revalidatePath(`/(main)/card/${dataToUpdate.business_slug}`, \"page\");\r\n  }\r\n\r\n  // 10. Return success response with the updated data\r\n  return { success: true, data: updatedProfile as BusinessCardData };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgBsB,qBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/logo/logoActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { getProfileImagePath, getScalableUserPath } from \"@/lib/utils/storage-paths\";\r\nimport {\r\n  LOGO_MAX_SIZE_MB,\r\n  ALLOWED_IMAGE_TYPES,\r\n  STORAGE_BUCKET\r\n} from \"../utils/constants\";\r\n\r\n/**\r\n * Updates only the logo URL in the database\r\n * @param logoUrl - The new logo URL\r\n * @returns Success/error response\r\n */\r\nexport async function updateLogoUrl(\r\n  logoUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Logo URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Deletes logo from storage and updates the database\r\n * @returns Success/error response\r\n */\r\nexport async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // First, get the current logo URL to extract the path\r\n  const { data: profile, error: fetchError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"logo_url\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching profile for logo deletion:\", fetchError);\r\n    return { success: false, error: \"Failed to fetch profile information.\" };\r\n  }\r\n\r\n  // If there's a logo URL, delete the file from storage\r\n  if (profile?.logo_url) {\r\n    try {\r\n      // Extract the file path from the URL\r\n      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');\r\n      if (urlParts.length === 2) {\r\n        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters\r\n\r\n        // Use admin client to delete from storage (required to bypass RLS)\r\n        const adminSupabase = createAdminClient();\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove([filePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting logo from storage:\", deleteError);\r\n          // Continue with database update even if storage deletion fails\r\n        } else {\r\n          console.log(\"Successfully deleted logo from storage:\", filePath);\r\n        }\r\n      } else {\r\n        console.warn(\"Could not parse logo URL for storage deletion:\", profile.logo_url);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing logo URL for deletion:\", error);\r\n      // Continue with database update even if storage deletion fails\r\n    }\r\n  }\r\n\r\n  // Update the database to remove the logo URL\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: null, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Error updating profile after logo deletion:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile after logo deletion: ${updateError.message}`\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Uploads logo file and returns public URL\r\n * @param formData - Form data containing the logo file\r\n * @returns Success/error response with URL\r\n */\r\nexport async function uploadLogoAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"logoFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No logo file provided.\" };\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation\r\n  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {\r\n    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };\r\n  }\r\n\r\n  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);\r\n  const fullPath = getProfileImagePath(userId, timestamp);\r\n\r\n  try {\r\n    // Use admin client for cleanup operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Clean up existing logos in the profile folder\r\n    const userPath = getScalableUserPath(userId);\r\n    const profileFolderPath = `${userPath}/profile/`;\r\n\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .list(profileFolderPath, { limit: 10 });\r\n\r\n    if (!listError && existingFiles && existingFiles.length > 0) {\r\n      const filesToDelete = existingFiles\r\n        .filter(f => f.name.startsWith('logo_'))\r\n        .map(f => `${profileFolderPath}${f.name}`);\r\n\r\n      if (filesToDelete.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove(filesToDelete);\r\n        if (deleteError) {\r\n          console.warn(`Error deleting existing logos:`, deleteError.message);\r\n        }\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Exception during logo deletion check:\", e);\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use the file's original type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Logo Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload logo: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8HsB,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 374, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/logo/logoActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { getProfileImagePath, getScalableUserPath } from \"@/lib/utils/storage-paths\";\r\nimport {\r\n  LOGO_MAX_SIZE_MB,\r\n  ALLOWED_IMAGE_TYPES,\r\n  STORAGE_BUCKET\r\n} from \"../utils/constants\";\r\n\r\n/**\r\n * Updates only the logo URL in the database\r\n * @param logoUrl - The new logo URL\r\n * @returns Success/error response\r\n */\r\nexport async function updateLogoUrl(\r\n  logoUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Logo URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Deletes logo from storage and updates the database\r\n * @returns Success/error response\r\n */\r\nexport async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // First, get the current logo URL to extract the path\r\n  const { data: profile, error: fetchError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"logo_url\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching profile for logo deletion:\", fetchError);\r\n    return { success: false, error: \"Failed to fetch profile information.\" };\r\n  }\r\n\r\n  // If there's a logo URL, delete the file from storage\r\n  if (profile?.logo_url) {\r\n    try {\r\n      // Extract the file path from the URL\r\n      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');\r\n      if (urlParts.length === 2) {\r\n        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters\r\n\r\n        // Use admin client to delete from storage (required to bypass RLS)\r\n        const adminSupabase = createAdminClient();\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove([filePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting logo from storage:\", deleteError);\r\n          // Continue with database update even if storage deletion fails\r\n        } else {\r\n          console.log(\"Successfully deleted logo from storage:\", filePath);\r\n        }\r\n      } else {\r\n        console.warn(\"Could not parse logo URL for storage deletion:\", profile.logo_url);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing logo URL for deletion:\", error);\r\n      // Continue with database update even if storage deletion fails\r\n    }\r\n  }\r\n\r\n  // Update the database to remove the logo URL\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: null, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Error updating profile after logo deletion:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile after logo deletion: ${updateError.message}`\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Uploads logo file and returns public URL\r\n * @param formData - Form data containing the logo file\r\n * @returns Success/error response with URL\r\n */\r\nexport async function uploadLogoAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"logoFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No logo file provided.\" };\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation\r\n  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {\r\n    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };\r\n  }\r\n\r\n  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);\r\n  const fullPath = getProfileImagePath(userId, timestamp);\r\n\r\n  try {\r\n    // Use admin client for cleanup operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Clean up existing logos in the profile folder\r\n    const userPath = getScalableUserPath(userId);\r\n    const profileFolderPath = `${userPath}/profile/`;\r\n\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .list(profileFolderPath, { limit: 10 });\r\n\r\n    if (!listError && existingFiles && existingFiles.length > 0) {\r\n      const filesToDelete = existingFiles\r\n        .filter(f => f.name.startsWith('logo_'))\r\n        .map(f => `${profileFolderPath}${f.name}`);\r\n\r\n      if (filesToDelete.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove(filesToDelete);\r\n        if (deleteError) {\r\n          console.warn(`Error deleting existing logos:`, deleteError.message);\r\n        }\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Exception during logo deletion check:\", e);\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use the file's original type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Logo Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload logo: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAiBsB,gBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/logo/logoActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { getProfileImagePath, getScalableUserPath } from \"@/lib/utils/storage-paths\";\r\nimport {\r\n  LOGO_MAX_SIZE_MB,\r\n  ALLOWED_IMAGE_TYPES,\r\n  STORAGE_BUCKET\r\n} from \"../utils/constants\";\r\n\r\n/**\r\n * Updates only the logo URL in the database\r\n * @param logoUrl - The new logo URL\r\n * @returns Success/error response\r\n */\r\nexport async function updateLogoUrl(\r\n  logoUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Logo URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Deletes logo from storage and updates the database\r\n * @returns Success/error response\r\n */\r\nexport async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // First, get the current logo URL to extract the path\r\n  const { data: profile, error: fetchError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"logo_url\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching profile for logo deletion:\", fetchError);\r\n    return { success: false, error: \"Failed to fetch profile information.\" };\r\n  }\r\n\r\n  // If there's a logo URL, delete the file from storage\r\n  if (profile?.logo_url) {\r\n    try {\r\n      // Extract the file path from the URL\r\n      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');\r\n      if (urlParts.length === 2) {\r\n        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters\r\n\r\n        // Use admin client to delete from storage (required to bypass RLS)\r\n        const adminSupabase = createAdminClient();\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove([filePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting logo from storage:\", deleteError);\r\n          // Continue with database update even if storage deletion fails\r\n        } else {\r\n          console.log(\"Successfully deleted logo from storage:\", filePath);\r\n        }\r\n      } else {\r\n        console.warn(\"Could not parse logo URL for storage deletion:\", profile.logo_url);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing logo URL for deletion:\", error);\r\n      // Continue with database update even if storage deletion fails\r\n    }\r\n  }\r\n\r\n  // Update the database to remove the logo URL\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: null, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Error updating profile after logo deletion:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile after logo deletion: ${updateError.message}`\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Uploads logo file and returns public URL\r\n * @param formData - Form data containing the logo file\r\n * @returns Success/error response with URL\r\n */\r\nexport async function uploadLogoAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"logoFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No logo file provided.\" };\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation\r\n  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {\r\n    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };\r\n  }\r\n\r\n  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);\r\n  const fullPath = getProfileImagePath(userId, timestamp);\r\n\r\n  try {\r\n    // Use admin client for cleanup operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Clean up existing logos in the profile folder\r\n    const userPath = getScalableUserPath(userId);\r\n    const profileFolderPath = `${userPath}/profile/`;\r\n\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .list(profileFolderPath, { limit: 10 });\r\n\r\n    if (!listError && existingFiles && existingFiles.length > 0) {\r\n      const filesToDelete = existingFiles\r\n        .filter(f => f.name.startsWith('logo_'))\r\n        .map(f => `${profileFolderPath}${f.name}`);\r\n\r\n      if (filesToDelete.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove(filesToDelete);\r\n        if (deleteError) {\r\n          console.warn(`Error deleting existing logos:`, deleteError.message);\r\n        }\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Exception during logo deletion check:\", e);\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use the file's original type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Logo Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload logo: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAmDsB,gBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions/customAdUpload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getCustomAdImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface CustomAdUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface CustomAdUpdateResult {\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload custom ad image with compression and auto-save to database\r\n */\r\nexport async function uploadCustomAdImage(\r\n  formData: FormData\r\n): Promise<CustomAdUploadResult> {\r\n  try {\r\n    // Create admin client for storage operations\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Extract the cropped image file from FormData\r\n    const imageFile = formData.get(\"image\") as File;\r\n    if (!imageFile) {\r\n      return {\r\n        success: false,\r\n        error: \"No image file provided\",\r\n      };\r\n    }\r\n\r\n    // Validate file type\r\n    if (!imageFile.type.startsWith(\"image/\")) {\r\n      return {\r\n        success: false,\r\n        error: \"Invalid file type. Please upload an image.\",\r\n      };\r\n    }\r\n\r\n    // Validate file size (max 15MB before compression)\r\n    if (imageFile.size > 15 * 1024 * 1024) {\r\n      return {\r\n        success: false,\r\n        error: \"File too large. Maximum size is 15MB.\",\r\n      };\r\n    }\r\n\r\n    const bucketName = \"business\";\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const imagePath = getCustomAdImagePath(user.id, timestamp);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Custom Ad Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    // Auto-save to database - update custom_ads field\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: true,\r\n          image_url: urlData.publicUrl,\r\n          link_url: \"\", // Will be updated separately\r\n          uploaded_at: new Date().toISOString(),\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\"Database update error:\", updateError);\r\n      // Image uploaded successfully but database update failed\r\n      // We could delete the image here, but let's keep it and return success\r\n      // The user can try again\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad upload error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred during upload.\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update custom ad link URL\r\n */\r\nexport async function updateCustomAdLink(linkUrl: string): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Validate URL if provided\r\n    if (linkUrl && linkUrl.trim()) {\r\n      try {\r\n        new URL(linkUrl);\r\n      } catch {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid URL format\",\r\n        };\r\n      }\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the link_url field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      link_url: linkUrl.trim(),\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to update ad link\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad link update error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Toggle custom ad enabled/disabled state\r\n */\r\nexport async function toggleCustomAd(enabled: boolean): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the enabled field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      enabled,\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to toggle ad state\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad toggle error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete custom ad image and reset data\r\n */\r\nexport async function deleteCustomAd(): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // First, get the current custom ad data to extract the image URL\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    const currentCustomAds = profile?.custom_ads;\r\n    const imageUrl = currentCustomAds?.image_url;\r\n\r\n    // Delete the image from storage if it exists\r\n    if (imageUrl) {\r\n      try {\r\n        // Extract the file path from the URL\r\n        // URL format: https://domain.supabase.co/storage/v1/object/public/business/users/xx/xx/userId/ads/custom_ad_timestamp.webp\r\n        const urlParts = imageUrl.split('/storage/v1/object/public/business/');\r\n        if (urlParts.length === 2) {\r\n          const filePath = urlParts[1];\r\n\r\n          // Use admin client to delete from storage\r\n          const adminSupabase = createAdminClient();\r\n          const { error: deleteError } = await adminSupabase.storage\r\n            .from(\"business\")\r\n            .remove([filePath]);\r\n\r\n          if (deleteError) {\r\n            console.error(\"Storage deletion error:\", deleteError);\r\n            // Continue with database update even if storage deletion fails\r\n          }\r\n        }\r\n      } catch (storageError) {\r\n        console.error(\"Error deleting custom ad from storage:\", storageError);\r\n        // Continue with database update even if storage deletion fails\r\n      }\r\n    }\r\n\r\n    // Reset custom_ads data in database\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: false,\r\n          image_url: \"\",\r\n          link_url: \"\",\r\n          uploaded_at: null,\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to delete custom ad\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad delete error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoBsB,sBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions/customAdUpload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getCustomAdImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface CustomAdUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface CustomAdUpdateResult {\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload custom ad image with compression and auto-save to database\r\n */\r\nexport async function uploadCustomAdImage(\r\n  formData: FormData\r\n): Promise<CustomAdUploadResult> {\r\n  try {\r\n    // Create admin client for storage operations\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Extract the cropped image file from FormData\r\n    const imageFile = formData.get(\"image\") as File;\r\n    if (!imageFile) {\r\n      return {\r\n        success: false,\r\n        error: \"No image file provided\",\r\n      };\r\n    }\r\n\r\n    // Validate file type\r\n    if (!imageFile.type.startsWith(\"image/\")) {\r\n      return {\r\n        success: false,\r\n        error: \"Invalid file type. Please upload an image.\",\r\n      };\r\n    }\r\n\r\n    // Validate file size (max 15MB before compression)\r\n    if (imageFile.size > 15 * 1024 * 1024) {\r\n      return {\r\n        success: false,\r\n        error: \"File too large. Maximum size is 15MB.\",\r\n      };\r\n    }\r\n\r\n    const bucketName = \"business\";\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const imagePath = getCustomAdImagePath(user.id, timestamp);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Custom Ad Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    // Auto-save to database - update custom_ads field\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: true,\r\n          image_url: urlData.publicUrl,\r\n          link_url: \"\", // Will be updated separately\r\n          uploaded_at: new Date().toISOString(),\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\"Database update error:\", updateError);\r\n      // Image uploaded successfully but database update failed\r\n      // We could delete the image here, but let's keep it and return success\r\n      // The user can try again\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad upload error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred during upload.\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update custom ad link URL\r\n */\r\nexport async function updateCustomAdLink(linkUrl: string): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Validate URL if provided\r\n    if (linkUrl && linkUrl.trim()) {\r\n      try {\r\n        new URL(linkUrl);\r\n      } catch {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid URL format\",\r\n        };\r\n      }\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the link_url field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      link_url: linkUrl.trim(),\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to update ad link\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad link update error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Toggle custom ad enabled/disabled state\r\n */\r\nexport async function toggleCustomAd(enabled: boolean): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the enabled field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      enabled,\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to toggle ad state\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad toggle error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete custom ad image and reset data\r\n */\r\nexport async function deleteCustomAd(): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // First, get the current custom ad data to extract the image URL\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    const currentCustomAds = profile?.custom_ads;\r\n    const imageUrl = currentCustomAds?.image_url;\r\n\r\n    // Delete the image from storage if it exists\r\n    if (imageUrl) {\r\n      try {\r\n        // Extract the file path from the URL\r\n        // URL format: https://domain.supabase.co/storage/v1/object/public/business/users/xx/xx/userId/ads/custom_ad_timestamp.webp\r\n        const urlParts = imageUrl.split('/storage/v1/object/public/business/');\r\n        if (urlParts.length === 2) {\r\n          const filePath = urlParts[1];\r\n\r\n          // Use admin client to delete from storage\r\n          const adminSupabase = createAdminClient();\r\n          const { error: deleteError } = await adminSupabase.storage\r\n            .from(\"business\")\r\n            .remove([filePath]);\r\n\r\n          if (deleteError) {\r\n            console.error(\"Storage deletion error:\", deleteError);\r\n            // Continue with database update even if storage deletion fails\r\n          }\r\n        }\r\n      } catch (storageError) {\r\n        console.error(\"Error deleting custom ad from storage:\", storageError);\r\n        // Continue with database update even if storage deletion fails\r\n      }\r\n    }\r\n\r\n    // Reset custom_ads data in database\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: false,\r\n          image_url: \"\",\r\n          link_url: \"\",\r\n          uploaded_at: null,\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to delete custom ad\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad delete error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAuQsB,iBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/slug/slugUtils.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { checkBusinessSlugAvailability } from \"@/lib/utils/slugUtils\";\r\nimport { generateSlug } from \"../utils/slugGenerator\";\r\nimport { validateSlugFormat } from \"../validation/businessCardValidation\";\r\nimport { nanoid, MAX_SLUG_ATTEMPTS } from \"../utils/constants\";\r\n\r\n/**\r\n * Generates a unique slug for a business\r\n * @param businessName - The business name to generate slug from\r\n * @param currentSlug - Current slug if any\r\n * @param userId - User ID for availability checking\r\n * @returns Object with success status and final slug or error\r\n */\r\nexport async function generateUniqueSlug(\r\n  businessName: string,\r\n  currentSlug: string,\r\n  userId: string\r\n): Promise<{ success: boolean; slug?: string; error?: string }> {\r\n  const desiredSlug = currentSlug || generateSlug(businessName);\r\n\r\n  let isUnique = false;\r\n  let checkSlug = desiredSlug;\r\n  let attempts = 0;\r\n\r\n  while (!isUnique && attempts < MAX_SLUG_ATTEMPTS) {\r\n    // Use the shared slug availability check\r\n    const { available, error: slugCheckError } = await checkBusinessSlugAvailability(checkSlug, userId);\r\n\r\n    if (slugCheckError) {\r\n      console.error(\"Slug Check Error:\", slugCheckError);\r\n      return { success: false, error: \"Error checking slug availability.\" };\r\n    }\r\n\r\n    if (available) {\r\n      isUnique = true;\r\n      const finalSlug = checkSlug;\r\n      \r\n      // Validate the final slug format\r\n      const slugValidation = validateSlugFormat(finalSlug);\r\n      if (!slugValidation.success) {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid business slug format generated. Please set one manually.\",\r\n        };\r\n      }\r\n      \r\n      return { success: true, slug: finalSlug };\r\n    } else {\r\n      attempts++;\r\n      checkSlug = `${desiredSlug}-${nanoid()}`;\r\n      if (attempts === MAX_SLUG_ATTEMPTS) {\r\n        return {\r\n          success: false,\r\n          error: `Could not generate a unique slug for '${desiredSlug}'. Please try setting one manually.`,\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  return { success: false, error: \"Failed to generate unique slug.\" };\r\n}\r\n\r\n/**\r\n * Action to check slug availability (wrapper for shared utility)\r\n * @param slug - The slug to check\r\n * @returns Object with availability status\r\n */\r\nexport async function checkSlugAvailability(\r\n  slug: string\r\n): Promise<{ available: boolean; error?: string }> {\r\n  return checkBusinessSlugAvailability(slug);\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoEsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 439, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/CardEditorClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useTransition, useRef, useMemo, useCallback } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { toast } from \"sonner\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Link as LinkIcon } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { scrollToFirstError } from \"./utils/scrollToError\";\r\n\r\nimport {\r\n  BusinessCardData,\r\n  businessCardSchema,\r\n  defaultBusinessCardData,\r\n  requiredFieldsForOnline,\r\n  requiredFieldsForSaving,\r\n} from \"./schema\";\r\nimport { updateBusinessCard } from \"./actions\";\r\n\r\n// Import custom hooks\r\nimport { useLogoUpload } from \"./components/hooks/useLogoUpload\";\r\nimport { usePincodeDetails } from \"./components/hooks/usePincodeDetails\";\r\n\r\n// Import components\r\nimport CardEditFormContent from \"./components/CardEditForm/CardEditFormContent\";\r\nimport CardPreviewSection from \"./components/CardPreviewSection\";\r\nimport ImageCropDialog from \"./components/ImageCropDialog\";\r\nimport LogoDeleteDialog from \"./components/LogoDeleteDialog\";\r\nimport FormSubmitButton from \"./components/CardEditForm/FormSubmitButton\";\r\nimport UnsavedChangesReminder from \"./components/UnsavedChangesReminder\";\r\nimport { Form } from \"@/components/ui/form\";\r\n\r\nexport type UserPlanStatus =\r\n  | \"basic\"\r\n  | \"growth\"\r\n  | \"pro\"\r\n  | \"enterprise\"\r\n  | \"trial\"\r\n  | null;\r\n\r\ninterface CardEditorClientProps {\r\n  initialData: Partial<BusinessCardData>;\r\n  currentUserPlan: UserPlanStatus;\r\n  subscriptionStatus: string | null;\r\n}\r\n\r\nexport default function CardEditorClient({\r\n  initialData,\r\n  currentUserPlan,\r\n  subscriptionStatus,\r\n}: CardEditorClientProps) {\r\n  // Client-side check to prevent SSR issues\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Main state for the current card data (for preview)\r\n  const [currentCardData, setCurrentCardData] = useState<BusinessCardData>(\r\n    () => {\r\n      const mergedData = {\r\n        ...defaultBusinessCardData,\r\n        ...initialData,\r\n      };\r\n      return {\r\n        ...mergedData,\r\n        member_name: mergedData.member_name || \"\",\r\n        title: mergedData.title || \"\",\r\n        business_name: mergedData.business_name || \"\",\r\n        status: mergedData.status || \"offline\",\r\n      } as BusinessCardData;\r\n    }\r\n  );\r\n\r\n  // Store the original saved data for discard functionality\r\n  const [savedCardData, setSavedCardData] = useState<BusinessCardData>(\r\n    () => {\r\n      const mergedData = {\r\n        ...defaultBusinessCardData,\r\n        ...initialData,\r\n      };\r\n      return {\r\n        ...mergedData,\r\n        member_name: mergedData.member_name || \"\",\r\n        title: mergedData.title || \"\",\r\n        business_name: mergedData.business_name || \"\",\r\n        status: mergedData.status || \"offline\",\r\n      } as BusinessCardData;\r\n    }\r\n  );\r\n\r\n  // Form submission state\r\n  const [isPending, startTransition] = useTransition();\r\n  const [isResetting, setIsResetting] = useState(false);\r\n  const [isCheckingSlug, setIsCheckingSlug] = useState(false);\r\n\r\n  // Reference to the card preview element for QR code download\r\n  const cardPreviewRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  // Properly merge initial data with defaults to prevent dirty state on load\r\n  const formDefaultValues = useMemo(() => ({\r\n    ...defaultBusinessCardData,\r\n    ...initialData,\r\n    locality: initialData?.locality ?? \"\",\r\n    // Ensure custom_branding and custom_ads have proper structure\r\n    custom_branding: {\r\n      ...defaultBusinessCardData.custom_branding,\r\n      ...(initialData?.custom_branding || {}),\r\n    },\r\n    custom_ads: {\r\n      ...defaultBusinessCardData.custom_ads,\r\n      ...(initialData?.custom_ads || {}),\r\n    },\r\n  }), [initialData]);\r\n\r\n  // Initialize the form with zod resolver\r\n  const form = useForm<BusinessCardData>({\r\n    resolver: zodResolver(businessCardSchema),\r\n    defaultValues: formDefaultValues,\r\n    mode: \"onChange\",\r\n    // This ensures the form properly tracks changes\r\n    resetOptions: {\r\n      keepDirtyValues: false, // When form is reset, all fields are marked as pristine\r\n      keepErrors: false,      // Clear all errors when form is reset\r\n    }\r\n  });\r\n\r\n  // Get watched form values\r\n  const watchedFields = form.watch();\r\n\r\n  // Check if subscription is halted\r\n  const isSubscriptionHalted = subscriptionStatus === \"halted\";\r\n\r\n  // Determine if user can go online based on required fields and subscription status\r\n  // If subscription is halted, user cannot go online regardless of required fields\r\n  const canGoOnline = !isSubscriptionHalted && requiredFieldsForOnline.every(\r\n    (field) => watchedFields[field] && String(watchedFields[field]).trim() !== \"\"\r\n  );\r\n\r\n  // Use custom hooks\r\n  const { isPincodeLoading, availableLocalities, handlePincodeChange } =\r\n    usePincodeDetails({\r\n      form,\r\n      initialPincode: initialData?.pincode,\r\n      initialLocality: initialData?.locality,\r\n    });\r\n\r\n  const {\r\n    logoUploadStatus,\r\n    localPreviewUrl,\r\n    isLogoUploading,\r\n    imageToCrop,\r\n    onFileSelect,\r\n    handleCropComplete,\r\n    handleCropDialogClose,\r\n    handleLogoDelete,\r\n    logoErrorDisplay,\r\n    isDeleteDialogOpen,\r\n    isDeleting,\r\n    // openDeleteDialog is not used\r\n    closeDeleteDialog,\r\n    confirmLogoDelete,\r\n  } = useLogoUpload({\r\n    form,\r\n    initialLogoUrl: initialData?.logo_url || \"\",\r\n    onUpdateCardData: (data) =>\r\n      setCurrentCardData((prev) => ({ ...prev, ...data })),\r\n  });\r\n\r\n  // Effect to set client-side flag\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Effect to properly reset form after initial data is loaded to prevent dirty state\r\n  useEffect(() => {\r\n    if (!isClient) return;\r\n\r\n    // Reset the form with the properly merged data to ensure it's not marked as dirty\r\n    // Only run this once after component mounts\r\n    const timer = setTimeout(() => {\r\n      setIsResetting(true);\r\n      form.reset(formDefaultValues, {\r\n        keepDirtyValues: false,\r\n        keepErrors: false,\r\n        keepDirty: false,\r\n        keepIsSubmitted: false,\r\n      });\r\n      // Clear resetting flag after a short delay\r\n      setTimeout(() => setIsResetting(false), 100);\r\n    }, 100); // Small delay to ensure form is fully initialized\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [isClient, form, formDefaultValues]); // Include form and formDefaultValues dependencies\r\n\r\n  // Effect to watch form changes and update preview state\r\n  useEffect(() => {\r\n    const subscription = form.watch((value) => {\r\n      // Don't update preview during form resets to prevent loops\r\n      if (!isResetting) {\r\n        setCurrentCardData((prev) => ({\r\n          ...prev,\r\n          ...value,\r\n        }));\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      subscription.unsubscribe();\r\n      if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n    };\r\n  }, [form, localPreviewUrl, isResetting]);\r\n\r\n  // Helper function to get missing required fields\r\n  const getMissingFields = (forOnlineOnly: boolean = true): Array<keyof BusinessCardData> => {\r\n    const formValues = form.getValues();\r\n    const fieldsToCheck = forOnlineOnly ? requiredFieldsForOnline : requiredFieldsForSaving;\r\n    return fieldsToCheck.filter(\r\n      (field) => !formValues[field] || String(formValues[field]).trim() === \"\"\r\n    );\r\n  };\r\n\r\n  // Helper function to get human-readable field names\r\n  const getFieldLabel = (field: keyof BusinessCardData): string => {\r\n    const fieldLabels: Record<string, string> = {\r\n      member_name: \"Your Name\",\r\n      title: \"Your Title\",\r\n      business_name: \"Business Name\",\r\n      phone: \"Primary Phone\",\r\n      address_line: \"Address Line\",\r\n      pincode: \"Pincode\",\r\n      city: \"City\",\r\n      state: \"State\",\r\n      locality: \"Locality\",\r\n      contact_email: \"Contact Email\",\r\n      business_category: \"Business Category\",\r\n    };\r\n    return fieldLabels[field] || field;\r\n  };\r\n\r\n  // Handler to discard unsaved changes\r\n  const handleDiscardChanges = () => {\r\n    // Set resetting flag to prevent watch subscription from triggering\r\n    setIsResetting(true);\r\n\r\n    // Reset to the last saved state (savedCardData)\r\n    form.reset(savedCardData, {\r\n      keepDirtyValues: false,\r\n      keepErrors: false,\r\n      keepDirty: false,\r\n      keepIsSubmitted: false,\r\n    });\r\n\r\n    // Also update the current card data to match the saved state\r\n    setCurrentCardData(savedCardData);\r\n\r\n    // Clear resetting flag after a short delay\r\n    setTimeout(() => setIsResetting(false), 100);\r\n\r\n    toast.info(\"Changes discarded\");\r\n  };\r\n\r\n  // Single form submission handler (used by both floating save and form save button)\r\n  const onSubmit = (data: BusinessCardData) => {\r\n    // Check if there are any validation errors from zod\r\n    if (Object.keys(form.formState.errors).length > 0) {\r\n      // Scroll to the first error\r\n      scrollToFirstError('business-card-form');\r\n\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot save business card</p>\r\n          <p className=\"text-sm mb-1\">Please fix the validation errors</p>\r\n        </div>\r\n      );\r\n      return;\r\n    }\r\n\r\n    // First check if required fields for saving are missing\r\n    const missingSavingFields = getMissingFields(false);\r\n    if (missingSavingFields.length > 0) {\r\n      const missingFieldLabels = missingSavingFields.map(getFieldLabel);\r\n\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot save business card</p>\r\n          <p className=\"text-sm mb-1\">Please fill in the following required fields:</p>\r\n          <ul className=\"text-sm list-disc pl-4\">\r\n            {missingFieldLabels.map((field, index) => (\r\n              <li key={index}>{field}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      );\r\n\r\n      // Focus on the first missing field\r\n      const firstMissingField = missingSavingFields[0];\r\n      form.setFocus(firstMissingField);\r\n      return;\r\n    }\r\n\r\n    // Check if subscription is halted and trying to go online\r\n    if (data.status === \"online\" && isSubscriptionHalted) {\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot set card to online status</p>\r\n          <p className=\"text-sm mb-1\">Your subscription is currently paused. Please resume your subscription to set your card online.</p>\r\n        </div>\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Then check if trying to go online but missing required fields\r\n    if (data.status === \"online\" && !canGoOnline && !isSubscriptionHalted) {\r\n      const missingOnlineFields = getMissingFields(true);\r\n      const missingFieldLabels = missingOnlineFields.map(getFieldLabel);\r\n\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot set card to online status</p>\r\n          <p className=\"text-sm mb-1\">Please fill in the following required fields:</p>\r\n          <ul className=\"text-sm list-disc pl-4\">\r\n            {missingFieldLabels.map((field, index) => (\r\n              <li key={index}>{field}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      );\r\n\r\n      // Focus on the first missing field\r\n      const firstMissingField = missingOnlineFields[0];\r\n      form.setFocus(firstMissingField);\r\n      return;\r\n    }\r\n\r\n    startTransition(async () => {\r\n      const result = await updateBusinessCard(data);\r\n\r\n      if (result.success && result.data) {\r\n        toast.success(\"Business card updated successfully!\");\r\n\r\n        // Update both current and saved card data states\r\n        setCurrentCardData(result.data);\r\n        setSavedCardData(result.data);\r\n\r\n        // Set resetting flag to prevent watch subscription from triggering\r\n        setIsResetting(true);\r\n\r\n        // Reset the form with the updated data immediately\r\n        // Use the proper reset options to ensure form state is properly updated\r\n        form.reset(result.data, {\r\n          keepDirtyValues: false, // Mark all fields as pristine\r\n          keepErrors: false,      // Clear all errors\r\n          keepDirty: false,       // Reset dirty state\r\n          keepIsSubmitted: false, // Reset submitted state\r\n        });\r\n\r\n        // Clear resetting flag after a short delay\r\n        setTimeout(() => setIsResetting(false), 100);\r\n      } else {\r\n        toast.error(result.error || \"Failed to update business card.\");\r\n      }\r\n    });\r\n  };\r\n\r\n  // Callback for slug checking state changes\r\n  const handleSlugCheckingChange = useCallback((checking: boolean) => {\r\n    setIsCheckingSlug(checking);\r\n  }, []);\r\n\r\n  // Single save handler for both floating save and form save button\r\n  const handleSave = async () => {\r\n    // Prevent multiple simultaneous submissions or if async operations are in progress\r\n    if (isPending || isCheckingSlug || isPincodeLoading) {\r\n      return;\r\n    }\r\n\r\n    // Check if there are any form validation errors (including slug availability)\r\n    if (Object.keys(form.formState.errors).length > 0) {\r\n      toast.error(\"Please fix the form errors before saving\");\r\n      return;\r\n    }\r\n\r\n    // Get current form values\r\n    const formValues = form.getValues();\r\n\r\n    // Manually validate the form\r\n    const validation = businessCardSchema.safeParse(formValues);\r\n\r\n    if (!validation.success) {\r\n      console.error(\"Validation failed:\", validation.error);\r\n      toast.error(\"Please fix the form errors before saving\");\r\n      return;\r\n    }\r\n\r\n    // If validation passes, call onSubmit directly\r\n    onSubmit(validation.data);\r\n  };\r\n\r\n  // Show loading state until client is ready\r\n  if (!isClient) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-[400px]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-gold)] mx-auto mb-4\"></div>\r\n          <p className=\"text-sm text-muted-foreground\">Loading editor...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Floating Unsaved Changes Reminder */}\r\n      <UnsavedChangesReminder\r\n        form={form}\r\n        isPending={isPending}\r\n        isLogoUploading={isLogoUploading}\r\n        isCheckingSlug={isCheckingSlug}\r\n        isPincodeLoading={isPincodeLoading}\r\n        onSave={handleSave}\r\n        onDiscard={handleDiscardChanges}\r\n      />\r\n\r\n      {/* Single Form for both Mobile and Desktop */}\r\n      <Form {...form}>\r\n        <form onSubmit={(e) => e.preventDefault()} className=\"space-y-8\">\r\n          {/* Mobile/Tablet Layout - Card on top, form below (visible on screens below lg breakpoint) */}\r\n          <div className=\"flex flex-col gap-8 lg:hidden\">\r\n            {/* Preview Section */}\r\n            <CardPreviewSection\r\n              cardData={currentCardData}\r\n              logoUploadStatus={logoUploadStatus}\r\n              localPreviewUrl={localPreviewUrl}\r\n              userPlan={\r\n                currentUserPlan === \"trial\" ? \"basic\" : currentUserPlan ?? undefined\r\n              }\r\n              cardPreviewRef={cardPreviewRef}\r\n            />\r\n\r\n            {/* Edit Form Section */}\r\n            <motion.div initial=\"hidden\" animate=\"visible\" className=\"space-y-6\">\r\n              {/* Header Section */}\r\n              <div className=\"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6\">\r\n                <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n                  <LinkIcon className=\"w-6 h-6 text-foreground\" />\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <h1 className=\"text-2xl font-bold text-foreground\">\r\n                    Edit Business Card\r\n                  </h1>\r\n                  <p className=\"text-muted-foreground mt-1\">\r\n                    Customize your digital business card below. Changes reflect in real-time.\r\n                  </p>\r\n                </div>\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    const slug = form.getValues(\"business_slug\");\r\n                    if (slug) window.open(`/${slug}`, \"_blank\");\r\n                    else toast.error(\"Please set a business slug first.\");\r\n                  }}\r\n                  disabled={!form.getValues(\"business_slug\")}\r\n                  className=\"flex items-center gap-2\"\r\n                >\r\n                  <LinkIcon className=\"h-4 w-4\" />\r\n                  View Public Card\r\n                </Button>\r\n              </div>\r\n\r\n              {/* Form Content */}\r\n              <CardEditFormContent\r\n                form={form}\r\n                canGoOnline={canGoOnline}\r\n                currentUserPlan={currentUserPlan}\r\n                onFileSelect={onFileSelect}\r\n                isPincodeLoading={isPincodeLoading}\r\n                availableLocalities={availableLocalities}\r\n                onPincodeChange={handlePincodeChange}\r\n                isLogoUploading={isLogoUploading}\r\n                onLogoDelete={handleLogoDelete}\r\n                isSubscriptionHalted={isSubscriptionHalted}\r\n                onSlugCheckingChange={handleSlugCheckingChange}\r\n              />\r\n\r\n              <div className=\"flex flex-col gap-2 sm:gap-3 mt-6\">\r\n                {logoErrorDisplay && (\r\n                  <p className=\"text-xs text-red-500 dark:text-red-400 text-right\">\r\n                    {logoErrorDisplay}\r\n                  </p>\r\n                )}\r\n                <FormSubmitButton\r\n                  form={form}\r\n                  isPending={isPending}\r\n                  isLogoUploading={isLogoUploading}\r\n                  isCheckingSlug={isCheckingSlug}\r\n                  isPincodeLoading={isPincodeLoading}\r\n                  onSave={handleSave}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Desktop Layout - Only visible at lg breakpoint and above */}\r\n          <div className=\"hidden lg:flex flex-col lg:flex-row gap-8 sm:gap-12 pb-12 relative\">\r\n            {/* Preview Column */}\r\n            <div className=\"flex-[1] w-full lg:w-1/2 lg:sticky lg:top-24 self-start\">\r\n              <CardPreviewSection\r\n                cardData={currentCardData}\r\n                logoUploadStatus={logoUploadStatus}\r\n                localPreviewUrl={localPreviewUrl}\r\n                userPlan={\r\n                  currentUserPlan === \"trial\"\r\n                    ? \"basic\"\r\n                    : currentUserPlan ?? undefined\r\n                }\r\n                cardPreviewRef={cardPreviewRef}\r\n              />\r\n            </div>\r\n\r\n            {/* Edit Form Column */}\r\n            <motion.div\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              style={{flex: 2, width: '100%', position: 'sticky', top: '6rem', alignSelf: 'flex-start'}}\r\n            >\r\n              <div className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 sm:p-5 md:p-6 mb-4 transition-all duration-300 hover:shadow-lg\">\r\n                <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n                  <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n                    <LinkIcon className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n                  </div>\r\n                  <div className=\"flex-1\">\r\n                    <h2 className=\"text-lg sm:text-xl font-semibold bg-gradient-to-r from-[var(--brand-gold)] to-amber-500 bg-clip-text text-transparent\">\r\n                      Edit Details\r\n                    </h2>\r\n                    <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n                      Customize your digital business card below. Changes reflect in\r\n                      real-time.\r\n                    </p>\r\n                  </div>\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => {\r\n                      const slug = form.getValues(\"business_slug\");\r\n                      if (slug) window.open(`/${slug}`, \"_blank\");\r\n                      else toast.error(\"Please set a business slug first.\");\r\n                    }}\r\n                    disabled={\r\n                      !form.getValues(\"business_slug\") ||\r\n                      !!form.formState.errors.business_slug\r\n                    }\r\n                    className=\"text-xs px-2 py-0.5 sm:px-2.5 sm:py-1 border-[var(--brand-gold)]/50 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 rounded-full transition-all duration-200 shadow-sm hover:shadow-md\"\r\n                  >\r\n                    <LinkIcon className=\"mr-1 h-3 w-3 sm:h-4 sm:w-4\" /> View Public\r\n                    Card\r\n                  </Button>\r\n                </div>\r\n\r\n                {/* Form Content */}\r\n                <CardEditFormContent\r\n                  form={form}\r\n                  canGoOnline={canGoOnline}\r\n                  currentUserPlan={currentUserPlan}\r\n                  onFileSelect={onFileSelect}\r\n                  isPincodeLoading={isPincodeLoading}\r\n                  availableLocalities={availableLocalities}\r\n                  onPincodeChange={handlePincodeChange}\r\n                  isLogoUploading={isLogoUploading}\r\n                  onLogoDelete={handleLogoDelete}\r\n                  isSubscriptionHalted={isSubscriptionHalted}\r\n                  onSlugCheckingChange={handleSlugCheckingChange}\r\n                />\r\n\r\n                <div className=\"flex flex-col gap-2 sm:gap-3 mt-4 sm:mt-6\">\r\n                  {logoErrorDisplay && (\r\n                    <p className=\"text-xs text-red-500 dark:text-red-400 text-right\">\r\n                      {logoErrorDisplay}\r\n                    </p>\r\n                  )}\r\n                  <FormSubmitButton\r\n                    form={form}\r\n                    isPending={isPending}\r\n                    isLogoUploading={isLogoUploading}\r\n                    isCheckingSlug={isCheckingSlug}\r\n                    isPincodeLoading={isPincodeLoading}\r\n                    onSave={handleSave}\r\n                  />\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n        </form>\r\n      </Form>\r\n\r\n      {/* Image Crop Dialog */}\r\n      <ImageCropDialog\r\n        isOpen={!!imageToCrop}\r\n        imgSrc={imageToCrop}\r\n        onClose={handleCropDialogClose}\r\n        onCropComplete={handleCropComplete}\r\n      />\r\n\r\n      {/* Logo Delete Confirmation Dialog */}\r\n      <LogoDeleteDialog\r\n        isOpen={isDeleteDialogOpen}\r\n        isDeleting={isDeleting}\r\n        onClose={closeDeleteDialog}\r\n        onConfirm={confirmLogoDelete}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AAAA;AAEA,sBAAsB;AACtB;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA;;;;;;;;;;;;;;;;;;;;;AA+Ce,SAAS,iBAAiB,EACvC,WAAW,EACX,eAAe,EACf,kBAAkB,EACI;IACtB,0CAA0C;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qDAAqD;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnD;QACE,MAAM,aAAa;YACjB,GAAG,+JAAA,CAAA,0BAAuB;YAC1B,GAAG,WAAW;QAChB;QACA,OAAO;YACL,GAAG,UAAU;YACb,aAAa,WAAW,WAAW,IAAI;YACvC,OAAO,WAAW,KAAK,IAAI;YAC3B,eAAe,WAAW,aAAa,IAAI;YAC3C,QAAQ,WAAW,MAAM,IAAI;QAC/B;IACF;IAGF,0DAA0D;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C;QACE,MAAM,aAAa;YACjB,GAAG,+JAAA,CAAA,0BAAuB;YAC1B,GAAG,WAAW;QAChB;QACA,OAAO;YACL,GAAG,UAAU;YACb,aAAa,WAAW,WAAW,IAAI;YACvC,OAAO,WAAW,KAAK,IAAI;YAC3B,eAAe,WAAW,aAAa,IAAI;YAC3C,QAAQ,WAAW,MAAM,IAAI;QAC/B;IACF;IAGF,wBAAwB;IACxB,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,6DAA6D;IAC7D,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAErD,2EAA2E;IAC3E,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAC;YACvC,GAAG,+JAAA,CAAA,0BAAuB;YAC1B,GAAG,WAAW;YACd,UAAU,aAAa,YAAY;YACnC,8DAA8D;YAC9D,iBAAiB;gBACf,GAAG,+JAAA,CAAA,0BAAuB,CAAC,eAAe;gBAC1C,GAAI,aAAa,mBAAmB,CAAC,CAAC;YACxC;YACA,YAAY;gBACV,GAAG,+JAAA,CAAA,0BAAuB,CAAC,UAAU;gBACrC,GAAI,aAAa,cAAc,CAAC,CAAC;YACnC;QACF,CAAC,GAAG;QAAC;KAAY;IAEjB,wCAAwC;IACxC,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,+JAAA,CAAA,qBAAkB;QACxC,eAAe;QACf,MAAM;QACN,gDAAgD;QAChD,cAAc;YACZ,iBAAiB;YACjB,YAAY;QACd;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,KAAK,KAAK;IAEhC,kCAAkC;IAClC,MAAM,uBAAuB,uBAAuB;IAEpD,mFAAmF;IACnF,iFAAiF;IACjF,MAAM,cAAc,CAAC,wBAAwB,+JAAA,CAAA,0BAAuB,CAAC,KAAK,CACxE,CAAC,QAAU,aAAa,CAAC,MAAM,IAAI,OAAO,aAAa,CAAC,MAAM,EAAE,IAAI,OAAO;IAG7E,mBAAmB;IACnB,MAAM,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,GAClE,CAAA,GAAA,iMAAA,CAAA,oBAAiB,AAAD,EAAE;QAChB;QACA,gBAAgB,aAAa;QAC7B,iBAAiB,aAAa;IAChC;IAEF,MAAM,EACJ,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,UAAU,EACV,+BAA+B;IAC/B,iBAAiB,EACjB,iBAAiB,EAClB,GAAG,CAAA,GAAA,6LAAA,CAAA,gBAAa,AAAD,EAAE;QAChB;QACA,gBAAgB,aAAa,YAAY;QACzC,kBAAkB,CAAC,OACjB,mBAAmB,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,GAAG,IAAI;gBAAC,CAAC;IACtD;IAEA,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,oFAAoF;IACpF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,kFAAkF;QAClF,4CAA4C;QAC5C,MAAM,QAAQ,WAAW;YACvB,eAAe;YACf,KAAK,KAAK,CAAC,mBAAmB;gBAC5B,iBAAiB;gBACjB,YAAY;gBACZ,WAAW;gBACX,iBAAiB;YACnB;YACA,2CAA2C;YAC3C,WAAW,IAAM,eAAe,QAAQ;QAC1C,GAAG,MAAM,kDAAkD;QAE3D,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAU;QAAM;KAAkB,GAAG,kDAAkD;IAE3F,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC;YAC/B,2DAA2D;YAC3D,IAAI,CAAC,aAAa;gBAChB,mBAAmB,CAAC,OAAS,CAAC;wBAC5B,GAAG,IAAI;wBACP,GAAG,KAAK;oBACV,CAAC;YACH;QACF;QAEA,OAAO;YACL,aAAa,WAAW;YACxB,IAAI,iBAAiB,IAAI,eAAe,CAAC;QAC3C;IACF,GAAG;QAAC;QAAM;QAAiB;KAAY;IAEvC,iDAAiD;IACjD,MAAM,mBAAmB,CAAC,gBAAyB,IAAI;QACrD,MAAM,aAAa,KAAK,SAAS;QACjC,MAAM,gBAAgB,gBAAgB,+JAAA,CAAA,0BAAuB,GAAG,+JAAA,CAAA,0BAAuB;QACvF,OAAO,cAAc,MAAM,CACzB,CAAC,QAAU,CAAC,UAAU,CAAC,MAAM,IAAI,OAAO,UAAU,CAAC,MAAM,EAAE,IAAI,OAAO;IAE1E;IAEA,oDAAoD;IACpD,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAsC;YAC1C,aAAa;YACb,OAAO;YACP,eAAe;YACf,OAAO;YACP,cAAc;YACd,SAAS;YACT,MAAM;YACN,OAAO;YACP,UAAU;YACV,eAAe;YACf,mBAAmB;QACrB;QACA,OAAO,WAAW,CAAC,MAAM,IAAI;IAC/B;IAEA,qCAAqC;IACrC,MAAM,uBAAuB;QAC3B,mEAAmE;QACnE,eAAe;QAEf,gDAAgD;QAChD,KAAK,KAAK,CAAC,eAAe;YACxB,iBAAiB;YACjB,YAAY;YACZ,WAAW;YACX,iBAAiB;QACnB;QAEA,6DAA6D;QAC7D,mBAAmB;QAEnB,2CAA2C;QAC3C,WAAW,IAAM,eAAe,QAAQ;QAExC,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,mFAAmF;IACnF,MAAM,WAAW,CAAC;QAChB,oDAAoD;QACpD,IAAI,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG;YACjD,4BAA4B;YAC5B,CAAA,GAAA,+KAAA,CAAA,qBAAkB,AAAD,EAAE;YAEnB,wIAAA,CAAA,QAAK,CAAC,KAAK,eACT,8OAAC;;kCACC,8OAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,8OAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;YAGhC;QACF;QAEA,wDAAwD;QACxD,MAAM,sBAAsB,iBAAiB;QAC7C,IAAI,oBAAoB,MAAM,GAAG,GAAG;YAClC,MAAM,qBAAqB,oBAAoB,GAAG,CAAC;YAEnD,wIAAA,CAAA,QAAK,CAAC,KAAK,eACT,8OAAC;;kCACC,8OAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,8OAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,8OAAC;wBAAG,WAAU;kCACX,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,8OAAC;0CAAgB;+BAAR;;;;;;;;;;;;;;;;YAMjB,mCAAmC;YACnC,MAAM,oBAAoB,mBAAmB,CAAC,EAAE;YAChD,KAAK,QAAQ,CAAC;YACd;QACF;QAEA,0DAA0D;QAC1D,IAAI,KAAK,MAAM,KAAK,YAAY,sBAAsB;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,eACT,8OAAC;;kCACC,8OAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,8OAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;YAGhC;QACF;QAEA,gEAAgE;QAChE,IAAI,KAAK,MAAM,KAAK,YAAY,CAAC,eAAe,CAAC,sBAAsB;YACrE,MAAM,sBAAsB,iBAAiB;YAC7C,MAAM,qBAAqB,oBAAoB,GAAG,CAAC;YAEnD,wIAAA,CAAA,QAAK,CAAC,KAAK,eACT,8OAAC;;kCACC,8OAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,8OAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,8OAAC;wBAAG,WAAU;kCACX,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,8OAAC;0CAAgB;+BAAR;;;;;;;;;;;;;;;;YAMjB,mCAAmC;YACnC,MAAM,oBAAoB,mBAAmB,CAAC,EAAE;YAChD,KAAK,QAAQ,CAAC;YACd;QACF;QAEA,gBAAgB;YACd,MAAM,SAAS,MAAM,CAAA,GAAA,iNAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,iDAAiD;gBACjD,mBAAmB,OAAO,IAAI;gBAC9B,iBAAiB,OAAO,IAAI;gBAE5B,mEAAmE;gBACnE,eAAe;gBAEf,mDAAmD;gBACnD,wEAAwE;gBACxE,KAAK,KAAK,CAAC,OAAO,IAAI,EAAE;oBACtB,iBAAiB;oBACjB,YAAY;oBACZ,WAAW;oBACX,iBAAiB;gBACnB;gBAEA,2CAA2C;gBAC3C,WAAW,IAAM,eAAe,QAAQ;YAC1C,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF;IACF;IAEA,2CAA2C;IAC3C,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5C,kBAAkB;IACpB,GAAG,EAAE;IAEL,kEAAkE;IAClE,MAAM,aAAa;QACjB,mFAAmF;QACnF,IAAI,aAAa,kBAAkB,kBAAkB;YACnD;QACF;QAEA,8EAA8E;QAC9E,IAAI,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG;YACjD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,0BAA0B;QAC1B,MAAM,aAAa,KAAK,SAAS;QAEjC,6BAA6B;QAC7B,MAAM,aAAa,+JAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;QAEhD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,QAAQ,KAAK,CAAC,sBAAsB,WAAW,KAAK;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,+CAA+C;QAC/C,SAAS,WAAW,IAAI;IAC1B;IAEA,2CAA2C;IAC3C,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIrD;IAEA,qBACE;;0BAEE,8OAAC,8LAAA,CAAA,UAAsB;gBACrB,MAAM;gBACN,WAAW;gBACX,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,QAAQ;gBACR,WAAW;;;;;;0BAIb,8OAAC,yHAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBAAK,UAAU,CAAC,IAAM,EAAE,cAAc;oBAAI,WAAU;;sCAEnD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,mMAAA,CAAA,UAAkB;oCACjB,UAAU;oCACV,kBAAkB;oCAClB,iBAAiB;oCACjB,UACE,oBAAoB,UAAU,UAAU,mBAAmB;oCAE7D,gBAAgB;;;;;;8CAIlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,SAAQ;oCAAS,SAAQ;oCAAU,WAAU;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEAGnD,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAI5C,8OAAC,2HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,MAAM,OAAO,KAAK,SAAS,CAAC;wDAC5B,IAAI,MAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;6DAC7B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oDACnB;oDACA,UAAU,CAAC,KAAK,SAAS,CAAC;oDAC1B,WAAU;;sEAEV,8OAAC,kMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;sDAMpC,8OAAC,2MAAA,CAAA,UAAmB;4CAClB,MAAM;4CACN,aAAa;4CACb,iBAAiB;4CACjB,cAAc;4CACd,kBAAkB;4CAClB,qBAAqB;4CACrB,iBAAiB;4CACjB,iBAAiB;4CACjB,cAAc;4CACd,sBAAsB;4CACtB,sBAAsB;;;;;;sDAGxB,8OAAC;4CAAI,WAAU;;gDACZ,kCACC,8OAAC;oDAAE,WAAU;8DACV;;;;;;8DAGL,8OAAC,wMAAA,CAAA,UAAgB;oDACf,MAAM;oDACN,WAAW;oDACX,iBAAiB;oDACjB,gBAAgB;oDAChB,kBAAkB;oDAClB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mMAAA,CAAA,UAAkB;wCACjB,UAAU;wCACV,kBAAkB;wCAClB,iBAAiB;wCACjB,UACE,oBAAoB,UAChB,UACA,mBAAmB;wCAEzB,gBAAgB;;;;;;;;;;;8CAKpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAQ;oCACR,SAAQ;oCACR,OAAO;wCAAC,MAAM;wCAAG,OAAO;wCAAQ,UAAU;wCAAU,KAAK;wCAAQ,WAAW;oCAAY;8CAExF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAwH;;;;;;0EAGtI,8OAAC;gEAAE,WAAU;0EAAwD;;;;;;;;;;;;kEAKvE,8OAAC,2HAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,MAAM,OAAO,KAAK,SAAS,CAAC;4DAC5B,IAAI,MAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;iEAC7B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACnB;wDACA,UACE,CAAC,KAAK,SAAS,CAAC,oBAChB,CAAC,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,aAAa;wDAEvC,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAQ;gEAAC,WAAU;;;;;;4DAA+B;;;;;;;;;;;;;0DAMvD,8OAAC,2MAAA,CAAA,UAAmB;gDAClB,MAAM;gDACN,aAAa;gDACb,iBAAiB;gDACjB,cAAc;gDACd,kBAAkB;gDAClB,qBAAqB;gDACrB,iBAAiB;gDACjB,iBAAiB;gDACjB,cAAc;gDACd,sBAAsB;gDACtB,sBAAsB;;;;;;0DAGxB,8OAAC;gDAAI,WAAU;;oDACZ,kCACC,8OAAC;wDAAE,WAAU;kEACV;;;;;;kEAGL,8OAAC,wMAAA,CAAA,UAAgB;wDACf,MAAM;wDACN,WAAW;wDACX,iBAAiB;wDACjB,gBAAgB;wDAChB,kBAAkB;wDAClB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtB,8OAAC,uLAAA,CAAA,UAAe;gBACd,QAAQ,CAAC,CAAC;gBACV,QAAQ;gBACR,SAAS;gBACT,gBAAgB;;;;;;0BAIlB,8OAAC,wLAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,YAAY;gBACZ,SAAS;gBACT,WAAW;;;;;;;;AAInB", "debugId": null}}]}