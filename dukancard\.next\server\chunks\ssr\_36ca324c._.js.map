{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/activities.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n/**\r\n * Database Triggers Documentation\r\n *\r\n * The following triggers are set up in Supabase to automatically track activities:\r\n *\r\n * 1. add_like_activity() - Trigger function for likes\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'like',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is added\r\n * CREATE TRIGGER trigger_add_like_activity\r\n * AFTER INSERT ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_like_activity();\r\n * ```\r\n *\r\n * 1a. delete_like_activity() - Trigger function for removing like activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'like';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is deleted\r\n * CREATE TRIGGER trigger_delete_like_activity\r\n * AFTER DELETE ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_like_activity();\r\n * ```\r\n *\r\n * 2. add_subscription_activity() - Trigger function for subscriptions\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'subscribe',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is added\r\n * CREATE TRIGGER trigger_add_subscription_activity\r\n * AFTER INSERT ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_subscription_activity();\r\n * ```\r\n *\r\n * 2a. delete_subscription_activity() - Trigger function for removing subscription activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'subscribe';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is deleted\r\n * CREATE TRIGGER trigger_delete_subscription_activity\r\n * AFTER DELETE ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_subscription_activity();\r\n * ```\r\n *\r\n * 3. add_rating_activity() - Trigger function for ratings\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Check if this is an update or insert\r\n *   IF TG_OP = 'UPDATE' THEN\r\n *     -- For updates, only add activity if rating changed\r\n *     IF NEW.rating = OLD.rating THEN\r\n *       RETURN NEW;\r\n *     END IF;\r\n *   END IF;\r\n *\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     rating_value,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'rating',\r\n *     NEW.rating,\r\n *     NEW.updated_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is added or updated\r\n * CREATE TRIGGER trigger_add_rating_activity\r\n * AFTER INSERT OR UPDATE OF rating ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_rating_activity();\r\n * ```\r\n *\r\n * 3a. delete_rating_activity() - Trigger function for removing rating activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'rating';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is deleted\r\n * CREATE TRIGGER trigger_delete_rating_activity\r\n * AFTER DELETE ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_rating_activity();\r\n * ```\r\n */\r\n\r\n/**\r\n * Table Structure\r\n *\r\n * The business_activities table is structured as follows:\r\n * ```sql\r\n * CREATE TABLE business_activities (\r\n *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\r\n *   business_profile_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,\r\n *   user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\r\n *   activity_type TEXT NOT NULL CHECK (activity_type IN ('like', 'subscribe', 'rating')),\r\n *   rating_value INTEGER,\r\n *   created_at TIMESTAMPTZ NOT NULL DEFAULT now(),\r\n *   is_read BOOLEAN NOT NULL DEFAULT false,\r\n *\r\n *   -- Add constraint to ensure rating_value is only set for rating activities\r\n *   CONSTRAINT rating_value_only_for_ratings CHECK (\r\n *     (activity_type = 'rating' AND rating_value IS NOT NULL) OR\r\n *     (activity_type != 'rating' AND rating_value IS NULL)\r\n *   )\r\n * );\r\n *\r\n * -- Indexes for better performance\r\n * CREATE INDEX idx_business_activities_business_profile_id ON business_activities(business_profile_id);\r\n * CREATE INDEX idx_business_activities_user_id ON business_activities(user_id);\r\n * CREATE INDEX idx_business_activities_is_read ON business_activities(is_read);\r\n * CREATE INDEX idx_business_activities_activity_type ON business_activities(activity_type);\r\n * CREATE INDEX idx_business_activities_created_at ON business_activities(created_at);\r\n * ```\r\n */\r\n\r\n/**\r\n * Row Level Security (RLS) Policies\r\n *\r\n * The following RLS policies are set up in Supabase to secure the business_activities table:\r\n *\r\n * 1. Select Policy - Allows business owners to read their own activities\r\n * ```sql\r\n * CREATE POLICY business_activities_select_policy ON business_activities\r\n *   FOR SELECT\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n *\r\n * 2. Update Policy - Allows business owners to update their own activities (for marking as read)\r\n * ```sql\r\n * CREATE POLICY business_activities_update_policy ON business_activities\r\n *   FOR UPDATE\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n */\r\n\r\n// Define types for activities\r\nexport interface BusinessActivity {\r\n  id: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  activity_type: \"like\" | \"subscribe\" | \"rating\";\r\n  rating_value: number | null;\r\n  created_at: string;\r\n  is_read: boolean;\r\n  user_profile?: {\r\n    name?: string | null;\r\n    avatar_url?: string | null;\r\n    email?: string | null;\r\n    is_business?: boolean;\r\n    business_name?: string | null;\r\n    business_slug?: string | null;\r\n    logo_url?: string | null;\r\n  };\r\n}\r\n\r\nexport type ActivitySortBy = \"newest\" | \"oldest\" | \"unread_first\";\r\n\r\n/**\r\n * Fetches activities for a business with pagination and sorting\r\n * Optionally marks fetched activities as read automatically\r\n */\r\nexport async function getBusinessActivities({\r\n  businessProfileId,\r\n  page = 1,\r\n  pageSize = 15,\r\n  sortBy = \"newest\",\r\n  filterBy = \"all\",\r\n  autoMarkAsRead = true, // New parameter to control auto-marking as read\r\n}: {\r\n  businessProfileId: string;\r\n  page?: number;\r\n  pageSize?: number;\r\n  sortBy?: ActivitySortBy;\r\n  filterBy?: \"all\" | \"like\" | \"subscribe\" | \"rating\" | \"unread\";\r\n  autoMarkAsRead?: boolean;\r\n}) {\r\n  const supabase = await createClient();\r\n  const supabaseAdmin = createAdminClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { activities: [], count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { activities: [], count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // Calculate pagination\r\n    const from = (page - 1) * pageSize;\r\n    const to = from + pageSize - 1;\r\n\r\n    // Build the query\r\n    let query = supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(\"business_profile_id\", businessProfileId);\r\n\r\n    // Apply filter\r\n    if (filterBy === \"like\") {\r\n      query = query.eq(\"activity_type\", \"like\");\r\n    } else if (filterBy === \"subscribe\") {\r\n      query = query.eq(\"activity_type\", \"subscribe\");\r\n    } else if (filterBy === \"rating\") {\r\n      query = query.eq(\"activity_type\", \"rating\");\r\n    } else if (filterBy === \"unread\") {\r\n      query = query.eq(\"is_read\", false);\r\n    }\r\n\r\n    // Apply sorting\r\n    switch (sortBy) {\r\n      case \"oldest\":\r\n        query = query.order(\"created_at\", { ascending: true });\r\n        break;\r\n      case \"unread_first\":\r\n        query = query.order(\"is_read\", { ascending: true }).order(\"created_at\", { ascending: false });\r\n        break;\r\n      case \"newest\":\r\n      default:\r\n        query = query.order(\"created_at\", { ascending: false });\r\n        break;\r\n    }\r\n\r\n    // Apply pagination\r\n    query = query.range(from, to);\r\n\r\n    // Execute the query\r\n    const { data: activities, error, count } = await query;\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching business activities:\", error);\r\n      return { activities: [], count: 0, error: error.message };\r\n    }\r\n\r\n    // Get user profiles for the activities\r\n    const userIds = activities.map((activity) => activity.user_id);\r\n\r\n    // Fetch both customer and business profiles\r\n    const [customerProfiles, businessProfiles] = await Promise.all([\r\n      supabaseAdmin\r\n        .from(\"customer_profiles\")\r\n        .select(\"id, name, avatar_url, email\")\r\n        .in(\"id\", userIds),\r\n      supabaseAdmin\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_name, business_slug, logo_url\")\r\n        .in(\"id\", userIds),\r\n    ]);\r\n\r\n    // Combine the profiles\r\n    const userProfiles = new Map();\r\n\r\n    // Add customer profiles to the map\r\n    customerProfiles.data?.forEach((profile) => {\r\n      userProfiles.set(profile.id, {\r\n        name: profile.name,\r\n        avatar_url: profile.avatar_url,\r\n        email: profile.email,\r\n        is_business: false,\r\n      });\r\n    });\r\n\r\n    // Add business profiles to the map, overriding customer profiles if both exist\r\n    businessProfiles.data?.forEach((profile) => {\r\n      const existingProfile = userProfiles.get(profile.id) || {};\r\n      userProfiles.set(profile.id, {\r\n        ...existingProfile,\r\n        business_name: profile.business_name,\r\n        business_slug: profile.business_slug,\r\n        logo_url: profile.logo_url,\r\n        is_business: true,\r\n      });\r\n    });\r\n\r\n    // Attach user profiles to activities\r\n    const activitiesWithProfiles = activities.map((activity) => ({\r\n      ...activity,\r\n      user_profile: userProfiles.get(activity.user_id) || {},\r\n    }));\r\n\r\n    // Auto-mark fetched activities as read if enabled\r\n    if (autoMarkAsRead && activities.length > 0) {\r\n      // Get IDs of unread activities\r\n      const unreadActivityIds = activities\r\n        .filter(activity => !activity.is_read)\r\n        .map(activity => activity.id);\r\n\r\n      // Only proceed if there are unread activities\r\n      if (unreadActivityIds.length > 0) {\r\n        // Mark these activities as read\r\n        const { error: markError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", unreadActivityIds);\r\n\r\n        if (markError) {\r\n          console.error(\"Error auto-marking activities as read:\", markError);\r\n        } else {\r\n          // Update the activities in our result to reflect they're now read\r\n          activitiesWithProfiles.forEach(activity => {\r\n            if (unreadActivityIds.includes(activity.id)) {\r\n              activity.is_read = true;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      activities: activitiesWithProfiles,\r\n      count: count || 0,\r\n      error: null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching business activities:\", error);\r\n    return {\r\n      activities: [],\r\n      count: 0,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Marks activities as read\r\n * Handles pagination for large numbers of activities to work around Supabase's 1000 row limit\r\n */\r\nexport async function markActivitiesAsRead({\r\n  businessProfileId,\r\n  activityIds,\r\n}: {\r\n  businessProfileId: string;\r\n  activityIds: string[] | \"all\";\r\n}) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { success: false, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { success: false, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // If marking specific activities as read\r\n    if (activityIds !== \"all\") {\r\n      // Handle case where we have specific activity IDs\r\n      const { error } = await supabase\r\n        .from(\"business_activities\")\r\n        .update({ is_read: true })\r\n        .eq(\"business_profile_id\", businessProfileId)\r\n        .in(\"id\", activityIds);\r\n\r\n      if (error) {\r\n        console.error(\"Error marking specific activities as read:\", error);\r\n        return { success: false, error: error.message };\r\n      }\r\n    } else {\r\n      // Handle \"mark all as read\" with pagination to work around Supabase's 1000 row limit\r\n      const BATCH_SIZE = 1000; // Maximum number of rows to update at once\r\n      let hasMore = true;\r\n      let processedCount = 0;\r\n\r\n      while (hasMore) {\r\n        // Get a batch of unread activity IDs\r\n        const { data: unreadActivities, error: fetchError } = await supabase\r\n          .from(\"business_activities\")\r\n          .select(\"id\")\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .eq(\"is_read\", false)\r\n          .limit(BATCH_SIZE);\r\n\r\n        if (fetchError) {\r\n          console.error(\"Error fetching unread activities:\", fetchError);\r\n          return { success: false, error: fetchError.message };\r\n        }\r\n\r\n        // If no more unread activities, we're done\r\n        if (!unreadActivities || unreadActivities.length === 0) {\r\n          hasMore = false;\r\n          break;\r\n        }\r\n\r\n        // Extract IDs from the batch\r\n        const batchIds = unreadActivities.map(activity => activity.id);\r\n\r\n        // Mark this batch as read\r\n        const { error: updateError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", batchIds);\r\n\r\n        if (updateError) {\r\n          console.error(\"Error marking batch as read:\", updateError);\r\n          return { success: false, error: updateError.message };\r\n        }\r\n\r\n        // Update processed count and check if we need to continue\r\n        processedCount += batchIds.length;\r\n        hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more\r\n      }\r\n\r\n      console.log(`Marked ${processedCount} activities as read`);\r\n    }\r\n\r\n    // Revalidate the activities page\r\n    revalidatePath(\"/dashboard/business/activities\");\r\n\r\n    return { success: true, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error marking activities as read:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the count of unread activities\r\n */\r\nexport async function getUnreadActivitiesCount(businessProfileId: string) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    const { count, error } = await supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\", head: true })\r\n      .eq(\"business_profile_id\", businessProfileId)\r\n      .eq(\"is_read\", false);\r\n\r\n    if (error) {\r\n      console.error(\"Error getting unread activities count:\", error);\r\n      return { count: 0, error: error.message };\r\n    }\r\n\r\n    return { count: count || 0, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error getting unread activities count:\", error);\r\n    return { count: 0, error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;;;;;AAuPO,eAAe,sBAAsB,EAC1C,iBAAiB,EACjB,OAAO,CAAC,EACR,WAAW,EAAE,EACb,SAAS,QAAQ,EACjB,WAAW,KAAK,EAChB,iBAAiB,IAAI,EAQtB;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IAEtC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,YAAY,EAAE;YAAE,OAAO;YAAG,OAAO;QAAoB;IAChE;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,YAAY,EAAE;YAAE,OAAO;YAAG,OAAO;QAAe;IAC3D;IAEA,IAAI;QACF,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,WAAW;QAE7B,kBAAkB;QAClB,IAAI,QAAQ,SACT,IAAI,CAAC,uBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ,GAC7B,EAAE,CAAC,uBAAuB;QAE7B,eAAe;QACf,IAAI,aAAa,QAAQ;YACvB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,aAAa;YACnC,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,UAAU;YAChC,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,UAAU;YAChC,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,gBAAgB;QAChB,OAAQ;YACN,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBACpD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,WAAW;oBAAE,WAAW;gBAAK,GAAG,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAC3F;YACF,KAAK;YACL;gBACE,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBACrD;QACJ;QAEA,mBAAmB;QACnB,QAAQ,MAAM,KAAK,CAAC,MAAM;QAE1B,oBAAoB;QACpB,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAEjD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,YAAY,EAAE;gBAAE,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1D;QAEA,uCAAuC;QACvC,MAAM,UAAU,WAAW,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO;QAE7D,4CAA4C;QAC5C,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7D,cACG,IAAI,CAAC,qBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM;YACZ,cACG,IAAI,CAAC,qBACL,MAAM,CAAC,8CACP,EAAE,CAAC,MAAM;SACb;QAED,uBAAuB;QACvB,MAAM,eAAe,IAAI;QAEzB,mCAAmC;QACnC,iBAAiB,IAAI,EAAE,QAAQ,CAAC;YAC9B,aAAa,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC3B,MAAM,QAAQ,IAAI;gBAClB,YAAY,QAAQ,UAAU;gBAC9B,OAAO,QAAQ,KAAK;gBACpB,aAAa;YACf;QACF;QAEA,+EAA+E;QAC/E,iBAAiB,IAAI,EAAE,QAAQ,CAAC;YAC9B,MAAM,kBAAkB,aAAa,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;YACzD,aAAa,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC3B,GAAG,eAAe;gBAClB,eAAe,QAAQ,aAAa;gBACpC,eAAe,QAAQ,aAAa;gBACpC,UAAU,QAAQ,QAAQ;gBAC1B,aAAa;YACf;QACF;QAEA,qCAAqC;QACrC,MAAM,yBAAyB,WAAW,GAAG,CAAC,CAAC,WAAa,CAAC;gBAC3D,GAAG,QAAQ;gBACX,cAAc,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC;YACvD,CAAC;QAED,kDAAkD;QAClD,IAAI,kBAAkB,WAAW,MAAM,GAAG,GAAG;YAC3C,+BAA+B;YAC/B,MAAM,oBAAoB,WACvB,MAAM,CAAC,CAAA,WAAY,CAAC,SAAS,OAAO,EACpC,GAAG,CAAC,CAAA,WAAY,SAAS,EAAE;YAE9B,8CAA8C;YAC9C,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,gCAAgC;gBAChC,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,uBACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;gBAEZ,IAAI,WAAW;oBACb,QAAQ,KAAK,CAAC,0CAA0C;gBAC1D,OAAO;oBACL,kEAAkE;oBAClE,uBAAuB,OAAO,CAAC,CAAA;wBAC7B,IAAI,kBAAkB,QAAQ,CAAC,SAAS,EAAE,GAAG;4BAC3C,SAAS,OAAO,GAAG;wBACrB;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACL,YAAY;YACZ,OAAO,SAAS;YAChB,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;YACL,YAAY,EAAE;YACd,OAAO;YACP,OAAO;QACT;IACF;AACF;AAMO,eAAe,qBAAqB,EACzC,iBAAiB,EACjB,WAAW,EAIZ;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoB;IACtD;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;IAEA,IAAI;QACF,yCAAyC;QACzC,IAAI,gBAAgB,OAAO;YACzB,kDAAkD;YAClD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC;gBAAE,SAAS;YAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,8CAA8C;gBAC5D,OAAO;oBAAE,SAAS;oBAAO,OAAO,MAAM,OAAO;gBAAC;YAChD;QACF,OAAO;YACL,qFAAqF;YACrF,MAAM,aAAa,MAAM,2CAA2C;YACpE,IAAI,UAAU;YACd,IAAI,iBAAiB;YAErB,MAAO,QAAS;gBACd,qCAAqC;gBACrC,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,uBACL,MAAM,CAAC,MACP,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,WAAW,OACd,KAAK,CAAC;gBAET,IAAI,YAAY;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,OAAO;wBAAE,SAAS;wBAAO,OAAO,WAAW,OAAO;oBAAC;gBACrD;gBAEA,2CAA2C;gBAC3C,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;oBACtD,UAAU;oBACV;gBACF;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,iBAAiB,GAAG,CAAC,CAAA,WAAY,SAAS,EAAE;gBAE7D,0BAA0B;gBAC1B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,uBACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;gBAEZ,IAAI,aAAa;oBACf,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,OAAO;wBAAE,SAAS;wBAAO,OAAO,YAAY,OAAO;oBAAC;gBACtD;gBAEA,0DAA0D;gBAC1D,kBAAkB,SAAS,MAAM;gBACjC,UAAU,SAAS,MAAM,KAAK,YAAY,8CAA8C;YAC1F;YAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,eAAe,mBAAmB,CAAC;QAC3D;QAEA,iCAAiC;QACjC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAKO,eAAe,yBAAyB,iBAAyB;IACtE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;YAAG,OAAO;QAAoB;IAChD;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,OAAO;YAAG,OAAO;QAAe;IAC3C;IAEA,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAC5B,IAAI,CAAC,uBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,WAAW;QAEjB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBAAE,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1C;QAEA,OAAO;YAAE,OAAO,SAAS;YAAG,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YAAE,OAAO;YAAG,OAAO;QAA+B;IAC3D;AACF;;;IAlTsB;IA4KA;IAqGA;;AAjRA,+OAAA;AA4KA,+OAAA;AAqGA,+OAAA", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/business/overview/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00a78b43259bdfa35946a0918da66b9382dcd7b4dc'} from 'ACTIONS_MODULE0'\nexport {getUnreadActivitiesCount as '40f389eb27483c521497eadb1dbe197d2328544a4a'} from 'ACTIONS_MODULE1'\nexport {getBusinessActivities as '404306b831edd693397c4d200cb7cfaf72ef475b59'} from 'ACTIONS_MODULE1'\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/config/plans.ts"], "sourcesContent": ["/**\r\n * Central configuration for all plan-related constants\r\n * This file serves as the single source of truth for plan information\r\n */\r\n\r\n// Plan types\r\nexport type PlanType = \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\";\r\nexport type PlanCycle = \"monthly\" | \"yearly\";\r\nexport type PaymentGateway = \"razorpay\";\r\n\r\n// Plan features\r\nexport interface PlanFeature {\r\n  name: string;\r\n  included: boolean;\r\n  limit?: number | \"unlimited\";\r\n  description?: string;\r\n}\r\n\r\n// Define Razorpay plan IDs\r\n// Different plan IDs for production and development environments\r\nconst RAZORPAY_PLAN_IDS = {\r\n  free: {\r\n    monthly: \"free-plan-monthly\", // Free plan doesn't need a real Razorpay ID\r\n    yearly: \"free-plan-yearly\",   // Free plan doesn't need a real Razorpay ID\r\n  },\r\n  basic: {\r\n    monthly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QO9rDMTSLeT34b\" // Production Basic monthly\r\n      : \"plan_QRgoJF3OfM6mB0\", // Development Basic monthly\r\n    yearly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QO9sfgFBnEFATA\" // Production Basic yearly\r\n      : \"plan_QRgr1XzaksqvSZ\", // Development Basic yearly\r\n  },\r\n  growth: {\r\n    monthly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnOd77S3FVeUc\" // Production Growth monthly\r\n      : \"plan_QbnMGvYCN7BM0V\", // Development Growth monthly\r\n    yearly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnOuE7iogOGTq\" // Production Growth yearly\r\n      : \"plan_QbnMdSbSeFrykv\", // Development Growth yearly\r\n  },\r\n  pro: {\r\n    monthly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnP83wvmzUOqM\" // Production Pro monthly\r\n      : \"plan_QbnN4mwUu6H2Ho\", // Development Pro monthly\r\n    yearly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnPJinNt66Pik\" // Production Pro yearly\r\n      : \"plan_QbnNYfrCExI496\", // Development Pro yearly\r\n  },\r\n  enterprise: {\r\n    monthly: \"enterprise-plan-monthly-razorpay\", // Placeholder for future implementation\r\n    yearly: \"enterprise-plan-yearly-razorpay\", // Placeholder for future implementation\r\n  }\r\n};\r\n\r\n// Set payment gateway to Razorpay\r\nconst _paymentGateway: PaymentGateway = \"razorpay\";\r\n\r\n\r\n// Plan interface\r\nexport interface Plan {\r\n  id: PlanType;\r\n  name: string;\r\n  description: string;\r\n  features: PlanFeature[];\r\n  razorpayPlanIds: {\r\n    monthly: string;\r\n    yearly: string;\r\n  };\r\n  pricing: {\r\n    monthly: number;\r\n    yearly: number;\r\n  };\r\n  recommended?: boolean;\r\n}\r\n\r\n// Plan definitions\r\nexport const PLANS: Plan[] = [\r\n  {\r\n    id: \"free\",\r\n    name: \"Free\",\r\n    description: \"Basic features for individuals and startups\",\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.free,\r\n    pricing: {\r\n      monthly: 0,\r\n      yearly: 0,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Simple digital business card with contact information\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: 5,\r\n        description: \"Showcase your products or services (limited to 5)\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: false,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: false,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: false,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: false,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 1,\r\n        description: \"Upload and display 1 image in your gallery\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: true,\r\n        description: \"Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"basic\",\r\n    name: \"Basic\",\r\n    description: \"Essential features for small businesses\",\r\n    recommended: false,\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.basic,\r\n    pricing: {\r\n      monthly: 99,\r\n      yearly: 999,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Basic digital business card with contact information\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: 15,\r\n        description: \"Showcase your products or services (up to 15)\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: false,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: false,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: false,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 3,\r\n        description: \"Upload and display up to 3 images in your gallery\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: true,\r\n        description: \"Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"growth\",\r\n    name: \"Growth\",\r\n    description: \"Advanced features for growing businesses\",\r\n    recommended: true,\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.growth,\r\n    pricing: {\r\n      monthly: 499,\r\n      yearly: 4990,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Premium digital business card with enhanced features\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: 50,\r\n        description: \"Showcase your products or services (up to 50)\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: false,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: true,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: false,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 10,\r\n        description: \"Upload and display up to 10 images\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: true,\r\n        description: \"Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"pro\",\r\n    name: \"Pro\",\r\n    description: \"Premium features for established businesses\",\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.pro,\r\n    pricing: {\r\n      monthly: 1999,\r\n      yearly: 19990,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Elite digital business card with premium features\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: \"unlimited\",\r\n        description: \"Showcase unlimited products or services\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: true,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: true,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: true,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 50,\r\n        description: \"Upload and display up to 50 images\",\r\n      },\r\n      {\r\n        name: \"Priority Support\",\r\n        included: true,\r\n        description: \"Priority email and chat support\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: false,\r\n        description: \"No Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"enterprise\",\r\n    name: \"Enterprise\",\r\n    description: \"Custom solutions for large businesses\",\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.enterprise,\r\n    pricing: {\r\n      monthly: 0, // Will be handled as \"Contact Sales\"\r\n      yearly: 0, // Will be handled as \"Contact Sales\"\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description:\r\n          \"Enterprise-grade digital business card with all premium features\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: \"unlimited\",\r\n        description: \"Showcase unlimited products or services\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: true,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: true,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: true,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 100,\r\n        description: \"Upload and display up to 100 images\",\r\n      },\r\n      {\r\n        name: \"Dedicated Account Manager\",\r\n        included: true,\r\n        description: \"Get a dedicated account manager\",\r\n      },\r\n      {\r\n        name: \"Custom Analytics Dashboard\",\r\n        included: true,\r\n        description: \"Get a custom analytics dashboard\",\r\n      },\r\n      {\r\n        name: \"24/7 Priority Support\",\r\n        included: true,\r\n        description: \"24/7 priority support\",\r\n      },\r\n      {\r\n        name: \"White-Label Option\",\r\n        included: true,\r\n        description: \"Use your own branding instead of Dukancard\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: false,\r\n        description: \"No Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\n/**\r\n * Get a plan by its ID\r\n * @param planId The plan ID\r\n * @returns The plan object or undefined if not found\r\n */\r\nexport function getPlanById(planId: PlanType): Plan | undefined {\r\n  return PLANS.find((plan) => plan.id === planId);\r\n}\r\n\r\n/**\r\n * Get a plan by its Razorpay plan ID\r\n * @param planId The Razorpay plan ID\r\n * @returns The plan object or undefined if not found\r\n */\r\nexport function getPlanByRazorpayPlanId(\r\n  planId: string\r\n): Plan | undefined {\r\n  return PLANS.find(\r\n    (plan) =>\r\n      plan.razorpayPlanIds.monthly === planId ||\r\n      plan.razorpayPlanIds.yearly === planId\r\n  );\r\n}\r\n\r\n/**\r\n * Map a Razorpay plan ID to a Dukancard plan type\r\n * @param razorpayPlanId The Razorpay plan ID\r\n * @returns The corresponding Dukancard plan type or 'free' if not found\r\n */\r\nexport function mapRazorpayPlanToDukancardPlan(\r\n  razorpayPlanId: string\r\n): PlanType {\r\n  const plan = getPlanByRazorpayPlanId(razorpayPlanId);\r\n  return plan?.id || \"free\";\r\n}\r\n\r\n/**\r\n * Get the Razorpay plan ID for a given plan type and cycle\r\n * @param planType The plan type\r\n * @param planCycle The plan cycle\r\n * @returns The Razorpay plan ID or null if not found\r\n */\r\nexport function getRazorpayPlanId(\r\n  planType: PlanType,\r\n  planCycle: PlanCycle\r\n): string | null {\r\n  const plan = getPlanById(planType);\r\n  if (!plan) {\r\n    return null;\r\n  }\r\n\r\n  const planId = plan.razorpayPlanIds[planCycle];\r\n  return planId || null;\r\n}\r\n\r\n/**\r\n * Get the Razorpay plan ID for subscription actions\r\n * Centralized function for all subscription-related operations\r\n * @param planId The plan ID\r\n * @param planCycle The plan cycle\r\n * @returns The Razorpay plan ID or throws error if invalid\r\n */\r\nexport function getSubscriptionRazorpayPlanId(\r\n  planId: PlanType,\r\n  planCycle: PlanCycle\r\n): string {\r\n  if (planId === \"free\") {\r\n    return planCycle === \"monthly\" ? \"free-plan-monthly\" : \"free-plan-yearly\";\r\n  } else if (planId === \"basic\") {\r\n    return planCycle === \"monthly\"\r\n      ? (process.env.NODE_ENV === 'production' ? \"plan_QO9rDMTSLeT34b\" : \"plan_QRgoJF3OfM6mB0\")\r\n      : (process.env.NODE_ENV === 'production' ? \"plan_QO9sfgFBnEFATA\" : \"plan_QRgr1XzaksqvSZ\");\r\n  } else if (planId === \"growth\") {\r\n    return planCycle === \"monthly\"\r\n      ? (process.env.NODE_ENV === 'production' ? \"plan_QbnOd77S3FVeUc\" : \"plan_QbnMGvYCN7BM0V\")\r\n      : (process.env.NODE_ENV === 'production' ? \"plan_QbnOuE7iogOGTq\" : \"plan_QbnMdSbSeFrykv\");\r\n  } else if (planId === \"pro\") {\r\n    return planCycle === \"monthly\"\r\n      ? (process.env.NODE_ENV === 'production' ? \"plan_QbnP83wvmzUOqM\" : \"plan_QbnN4mwUu6H2Ho\")\r\n      : (process.env.NODE_ENV === 'production' ? \"plan_QbnPJinNt66Pik\" : \"plan_QbnNYfrCExI496\");\r\n  } else {\r\n    throw new Error(`Invalid plan selected: ${planId}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Get the plan ID for a given plan type and cycle\r\n * @param planType The plan type\r\n * @param planCycle The plan cycle\r\n * @returns The plan ID or undefined if not found\r\n */\r\nexport function getPlanId(\r\n  planType: PlanType,\r\n  planCycle: PlanCycle\r\n): string | undefined {\r\n  // Use Razorpay plan IDs since we've migrated to Razorpay\r\n  const planId = getRazorpayPlanId(planType, planCycle);\r\n  return planId === null ? undefined : planId;\r\n}\r\n\r\n/**\r\n * Get the product limit for a given plan type\r\n * @param planType The plan type\r\n * @returns The product limit (number or Infinity for unlimited)\r\n */\r\nexport function getProductLimit(planType: PlanType | null | undefined): number {\r\n  if (!planType) return 0;\r\n\r\n  const plan = getPlanById(planType);\r\n  if (!plan) return 0;\r\n\r\n  const productFeature = plan.features.find(\r\n    (feature) => feature.name === \"Product Listings\"\r\n  );\r\n  if (!productFeature || !productFeature.included) return 0;\r\n\r\n  return productFeature.limit === \"unlimited\"\r\n    ? Infinity\r\n    : productFeature.limit || 0;\r\n}\r\n\r\n/**\r\n * Check if a feature is included in a plan\r\n * @param planType The plan type\r\n * @param featureName The feature name\r\n * @returns True if the feature is included, false otherwise\r\n */\r\nexport function hasFeature(\r\n  planType: PlanType | null | undefined,\r\n  featureName: string\r\n): boolean {\r\n  if (!planType) return false;\r\n\r\n  const plan = getPlanById(planType);\r\n  if (!plan) return false;\r\n\r\n  const feature = plan.features.find((feature) => feature.name === featureName);\r\n  return feature?.included || false;\r\n}\r\n\r\n/**\r\n * Get all plans\r\n * @returns Array of plans\r\n */\r\nexport function pricingPlans(): Plan[] {\r\n  return PLANS;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,aAAa;;;;;;;;;;;;;AAab,2BAA2B;AAC3B,iEAAiE;AACjE,MAAM,oBAAoB;IACxB,MAAM;QACJ,SAAS;QACT,QAAQ;IACV;IACA,OAAO;QACL,SAAS,6EAEL;QACJ,QAAQ,6EAEJ;IACN;IACA,QAAQ;QACN,SAAS,6EAEL;QACJ,QAAQ,6EAEJ;IACN;IACA,KAAK;QACH,SAAS,6EAEL;QACJ,QAAQ,6EAEJ;IACN;IACA,YAAY;QACV,SAAS;QACT,QAAQ;IACV;AACF;AAEA,kCAAkC;AAClC,MAAM,kBAAkC;AAqBjC,MAAM,QAAgB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB,kBAAkB,IAAI;QACvC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,iBAAiB,kBAAkB,KAAK;QACxC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,iBAAiB,kBAAkB,MAAM;QACzC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB,kBAAkB,GAAG;QACtC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB,kBAAkB,UAAU;QAC7C,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aACE;YACJ;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;CACD;AAOM,SAAS,YAAY,MAAgB;IAC1C,OAAO,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;AAC1C;AAOO,SAAS,wBACd,MAAc;IAEd,OAAO,MAAM,IAAI,CACf,CAAC,OACC,KAAK,eAAe,CAAC,OAAO,KAAK,UACjC,KAAK,eAAe,CAAC,MAAM,KAAK;AAEtC;AAOO,SAAS,+BACd,cAAsB;IAEtB,MAAM,OAAO,wBAAwB;IACrC,OAAO,MAAM,MAAM;AACrB;AAQO,SAAS,kBACd,QAAkB,EAClB,SAAoB;IAEpB,MAAM,OAAO,YAAY;IACzB,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,SAAS,KAAK,eAAe,CAAC,UAAU;IAC9C,OAAO,UAAU;AACnB;AASO,SAAS,8BACd,MAAgB,EAChB,SAAoB;IAEpB,IAAI,WAAW,QAAQ;QACrB,OAAO,cAAc,YAAY,sBAAsB;IACzD,OAAO,IAAI,WAAW,SAAS;QAC7B,OAAO,cAAc,YAChB,6EAAgE,wBAChE,6EAAgE;IACvE,OAAO,IAAI,WAAW,UAAU;QAC9B,OAAO,cAAc,YAChB,6EAAgE,wBAChE,6EAAgE;IACvE,OAAO,IAAI,WAAW,OAAO;QAC3B,OAAO,cAAc,YAChB,6EAAgE,wBAChE,6EAAgE;IACvE,OAAO;QACL,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,QAAQ;IACpD;AACF;AAQO,SAAS,UACd,QAAkB,EAClB,SAAoB;IAEpB,yDAAyD;IACzD,MAAM,SAAS,kBAAkB,UAAU;IAC3C,OAAO,WAAW,OAAO,YAAY;AACvC;AAOO,SAAS,gBAAgB,QAAqC;IACnE,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,OAAO,YAAY;IACzB,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CACvC,CAAC,UAAY,QAAQ,IAAI,KAAK;IAEhC,IAAI,CAAC,kBAAkB,CAAC,eAAe,QAAQ,EAAE,OAAO;IAExD,OAAO,eAAe,KAAK,KAAK,cAC5B,WACA,eAAe,KAAK,IAAI;AAC9B;AAQO,SAAS,WACd,QAAqC,EACrC,WAAmB;IAEnB,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,OAAO,YAAY;IACzB,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,UAAU,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,IAAI,KAAK;IACjE,OAAO,SAAS,YAAY;AAC9B;AAMO,SAAS;IACd,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1043, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/PricingPlans.ts"], "sourcesContent": ["import {\r\n  Plan as ConfigPlan,\r\n  PlanType,\r\n  getProductLimit,\r\n  PLANS,\r\n} from \"@/lib/config/plans\";\r\n\r\n// Define the possible billing cycle types (for backward compatibility)\r\ntype BillingCycle = \"monthly\" | \"yearly\";\r\n\r\n// Legacy PricingPlan interface for backward compatibility\r\nexport interface PricingPlan {\r\n  id: string;\r\n  name: string;\r\n  price: string;\r\n  yearlyPrice?: string;\r\n  period: string;\r\n  description: string;\r\n  features: string[];\r\n  button: string;\r\n  available: boolean;\r\n  razorpayPlanIds?: {\r\n    monthly?: string | null;\r\n    yearly?: string | null;\r\n  } | null;\r\n  featured: boolean;\r\n  recommended?: boolean;\r\n  savings?: string;\r\n  mostPopular?: boolean;\r\n}\r\n\r\n// Convert a ConfigPlan to a PricingPlan for backward compatibility\r\nfunction convertToPricingPlan(\r\n  plan: ConfigPlan,\r\n  billingCycle: BillingCycle\r\n): PricingPlan {\r\n  // All plans are available now, Enterprise will show \"Contact Sales\"\r\n  const available = true;\r\n\r\n  // Handle Enterprise plan pricing differently\r\n  const isEnterprise = plan.id === \"enterprise\";\r\n\r\n  // Calculate savings for yearly plans (skip for Enterprise)\r\n  const monthlyCost = plan.pricing.monthly;\r\n  const yearlyCost = plan.pricing.yearly;\r\n  const yearlySavings = isEnterprise ? 0 : monthlyCost * 12 - yearlyCost;\r\n\r\n  return {\r\n    id: plan.id,\r\n    name: `${plan.name} Plan`,\r\n    razorpayPlanIds: {\r\n      monthly: plan.razorpayPlanIds.monthly || null,\r\n      yearly: plan.razorpayPlanIds.yearly || null,\r\n    },\r\n    price: isEnterprise\r\n      ? \"Contact Sales\"\r\n      : billingCycle === \"monthly\"\r\n        ? `₹${plan.pricing.monthly.toLocaleString(\"en-IN\")}`\r\n        : `₹${plan.pricing.yearly.toLocaleString(\"en-IN\")}`,\r\n    yearlyPrice: isEnterprise\r\n      ? \"Contact Sales\"\r\n      : `₹${plan.pricing.yearly.toLocaleString(\"en-IN\")}`,\r\n    period: isEnterprise ? \"\" : billingCycle === \"monthly\" ? \"/month\" : \"/year\",\r\n    savings: isEnterprise ? undefined : billingCycle === \"yearly\" ? `Save ₹${yearlySavings.toLocaleString(\"en-IN\")}` : undefined,\r\n    description: plan.description,\r\n    features: plan.features.map((f) => {\r\n      if (f.name === \"Product Listings\" && f.included) {\r\n        const limit = f.limit === \"unlimited\" ? \"Unlimited\" : f.limit;\r\n        return `Product/Service Listings (up to ${limit})`;\r\n      }\r\n      return f.included ? f.name : `❌ ${f.name}`;\r\n    }),\r\n    button: isEnterprise\r\n      ? \"Contact Sales\"\r\n      : \"Subscribe Now\",\r\n    available,\r\n    featured: plan.id === \"free\" || plan.id === \"basic\" || plan.id === \"growth\" || plan.id === \"pro\",\r\n    recommended: plan.recommended || false,\r\n    mostPopular: plan.recommended || false, // Use the recommended flag from the plan config\r\n  };\r\n}\r\n\r\n// pricingPlans function for backward compatibility\r\nexport const pricingPlans = (billingCycle: BillingCycle): PricingPlan[] => {\r\n  return PLANS.map((plan) => convertToPricingPlan(plan, billingCycle));\r\n};\r\n\r\n// Export a static list of plans (e.g., monthly) for onboarding or simpler displays\r\nexport const onboardingPlans: PricingPlan[] = pricingPlans(\"monthly\");\r\n\r\n// Note: All plan features are now defined in lib/config/plans.ts\r\n// This file only provides backward compatibility for existing code\r\n\r\n// Helper function to get product/service limits based on plan ID (for backward compatibility)\r\nexport const getPlanLimit = (planId: string | null | undefined): number => {\r\n  return getProductLimit(planId as PlanType);\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AA+BA,mEAAmE;AACnE,SAAS,qBACP,IAAgB,EAChB,YAA0B;IAE1B,oEAAoE;IACpE,MAAM,YAAY;IAElB,6CAA6C;IAC7C,MAAM,eAAe,KAAK,EAAE,KAAK;IAEjC,2DAA2D;IAC3D,MAAM,cAAc,KAAK,OAAO,CAAC,OAAO;IACxC,MAAM,aAAa,KAAK,OAAO,CAAC,MAAM;IACtC,MAAM,gBAAgB,eAAe,IAAI,cAAc,KAAK;IAE5D,OAAO;QACL,IAAI,KAAK,EAAE;QACX,MAAM,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC;QACzB,iBAAiB;YACf,SAAS,KAAK,eAAe,CAAC,OAAO,IAAI;YACzC,QAAQ,KAAK,eAAe,CAAC,MAAM,IAAI;QACzC;QACA,OAAO,eACH,kBACA,iBAAiB,YACf,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,UAAU,GAClD,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU;QACvD,aAAa,eACT,kBACA,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU;QACrD,QAAQ,eAAe,KAAK,iBAAiB,YAAY,WAAW;QACpE,SAAS,eAAe,YAAY,iBAAiB,WAAW,CAAC,MAAM,EAAE,cAAc,cAAc,CAAC,UAAU,GAAG;QACnH,aAAa,KAAK,WAAW;QAC7B,UAAU,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,EAAE,IAAI,KAAK,sBAAsB,EAAE,QAAQ,EAAE;gBAC/C,MAAM,QAAQ,EAAE,KAAK,KAAK,cAAc,cAAc,EAAE,KAAK;gBAC7D,OAAO,CAAC,gCAAgC,EAAE,MAAM,CAAC,CAAC;YACpD;YACA,OAAO,EAAE,QAAQ,GAAG,EAAE,IAAI,GAAG,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE;QAC5C;QACA,QAAQ,eACJ,kBACA;QACJ;QACA,UAAU,KAAK,EAAE,KAAK,UAAU,KAAK,EAAE,KAAK,WAAW,KAAK,EAAE,KAAK,YAAY,KAAK,EAAE,KAAK;QAC3F,aAAa,KAAK,WAAW,IAAI;QACjC,aAAa,KAAK,WAAW,IAAI;IACnC;AACF;AAGO,MAAM,eAAe,CAAC;IAC3B,OAAO,sHAAA,CAAA,QAAK,CAAC,GAAG,CAAC,CAAC,OAAS,qBAAqB,MAAM;AACxD;AAGO,MAAM,kBAAiC,aAAa;AAMpD,MAAM,eAAe,CAAC;IAC3B,OAAO,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;AACzB", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6U,GAC1W,2GACA", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyT,GACtV,uFACA", "debugId": null}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 1300, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1325, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/overview/page.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Metadata } from \"next\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\nimport { pricingPlans, PricingPlan } from \"@/lib/PricingPlans\";\r\nimport BusinessDashboardClient from \"../components/BusinessDashboardClient\";\r\nimport { Suspense } from \"react\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\n// Helper function to find plan details by ID and cycle\r\nconst getPlanDetails = (planId: string | null, planCycle: string | null): PricingPlan | undefined => {\r\n  if (!planId) return undefined;\r\n  // Use the provided plan cycle or default to monthly\r\n  const cycle = (planCycle === \"yearly\") ? \"yearly\" : \"monthly\";\r\n  const allPlans = pricingPlans(cycle);\r\n  return allPlans.find((plan) => plan.id === planId);\r\n};\r\n\r\n// Define types for the business profile and subscription data\r\ntype BusinessProfileInfo = {\r\n  business_name: string | null;\r\n  trial_end_date: string | null;\r\n};\r\n\r\ntype PaymentSubscriptionInfo = {\r\n  subscription_status: string | null;\r\n  plan_id: string | null;\r\n  plan_cycle: string | null;\r\n};\r\n\r\n// Helper function to check subscription status using centralized logic\r\nfunction checkSubscriptionStatus(\r\n  profile: BusinessProfileInfo | null,\r\n  subscription: PaymentSubscriptionInfo | null\r\n): \"active\" | \"trial\" | \"inactive\" {\r\n  if (!profile) return \"inactive\";\r\n\r\n  const now = new Date();\r\n  const trialEndDate = profile.trial_end_date\r\n    ? new Date(profile.trial_end_date)\r\n    : null;\r\n\r\n  // If we have a subscription status from the database, use centralized logic\r\n  if (subscription?.subscription_status) {\r\n    const status = subscription.subscription_status;\r\n    const planId = subscription.plan_id || 'free';\r\n\r\n    // Use centralized logic - only paid subscriptions count as active\r\n    // Trial and free plan users are NOT considered to have active subscription\r\n    if (status === 'active' || status === 'authenticated') {\r\n      // Only paid plans count as active subscription\r\n      if (planId !== 'free') {\r\n        return \"active\";\r\n      }\r\n    }\r\n\r\n    if (status === 'trial') {\r\n      return \"trial\";\r\n    }\r\n  }\r\n\r\n  // Fallback: Check if user is in trial period based on trial_end_date\r\n  if (trialEndDate && trialEndDate > now) {\r\n    return \"trial\";\r\n  }\r\n\r\n  return \"inactive\"; // Trial ended or never existed, and no active subscription\r\n}\r\n\r\n// Add metadata\r\nexport const metadata: Metadata = {\r\n  title: \"Business Dashboard Overview\",\r\n  robots: \"noindex, nofollow\",\r\n};\r\n\r\nexport default async function BusinessOverviewPage() {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return redirect(\"/login?message=Authentication required\");\r\n  }\r\n\r\n  // Fetch the business profile including interaction details and status\r\n  const { data: profileData, error: profileError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\r\n      \"business_name, business_slug, trial_end_date, total_likes, total_subscriptions, average_rating, logo_url, title, status\"\r\n    )\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  // Fetch subscription data from payment_subscriptions\r\n  const { data: subscription, error: subscriptionError } = await supabase\r\n    .from(\"payment_subscriptions\")\r\n    .select(\"subscription_status, plan_id, plan_cycle\")\r\n    .eq(\"business_profile_id\", user.id)\r\n    .order(\"created_at\", { ascending: false })\r\n    .limit(1)\r\n    .maybeSingle();\r\n\r\n  if (subscriptionError) {\r\n    console.error(\"Error fetching subscription data:\", subscriptionError);\r\n  }\r\n\r\n  if (profileError || !profileData) {\r\n    console.error(\r\n      \"Error fetching business profile or profile not found:\",\r\n      profileError?.message\r\n    );\r\n    return redirect(\"/login?message=Profile fetch error\");\r\n  }\r\n\r\n  // No need to generate visit data as we've removed the performance metrics component\r\n\r\n  // Get subscription status and plan details\r\n  const subscriptionStatus = checkSubscriptionStatus(profileData, subscription);\r\n\r\n  // Get the plan ID from the subscription data, even if the subscription is inactive or halted\r\n  // This ensures that users who have paused their subscription still see their actual plan\r\n  const planId = subscription?.plan_id || \"free\";\r\n  const planDetails = getPlanDetails(planId, subscription?.plan_cycle || null);\r\n\r\n  // Create the properly formatted initialProfile object\r\n  const initialProfile = {\r\n    business_name: profileData.business_name || \"\",\r\n    business_slug: profileData.business_slug || \"\",\r\n    plan_id: planId, // Use the planId we determined above\r\n    plan_cycle: subscription?.plan_cycle || null,\r\n    has_active_subscription:\r\n      // Use centralized logic: only paid subscriptions count as active\r\n      // Trial and free plan users have has_active_subscription = false\r\n      (subscription?.subscription_status === \"active\" || subscription?.subscription_status === \"authenticated\") &&\r\n      planId !== 'free',\r\n    trial_end_date: profileData.trial_end_date,\r\n    total_likes: profileData.total_likes || 0,\r\n    total_subscriptions: profileData.total_subscriptions || 0,\r\n    average_rating: profileData.average_rating || 0,\r\n    logo_url: profileData.logo_url,\r\n    title: profileData.title,\r\n    status: profileData.status || \"offline\" // Default to offline if status is not set\r\n  };\r\n\r\n  return (\r\n    <Suspense fallback={<Skeleton className=\"h-[600px] w-full\" />}>\r\n      <BusinessDashboardClient\r\n        initialProfile={initialProfile}\r\n        userId={user.id}\r\n        subscriptionStatus={subscriptionStatus}\r\n        planDetails={planDetails}\r\n        subscription={subscription}\r\n      />\r\n    </Suspense>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,uDAAuD;AACvD,MAAM,iBAAiB,CAAC,QAAuB;IAC7C,IAAI,CAAC,QAAQ,OAAO;IACpB,oDAAoD;IACpD,MAAM,QAAQ,AAAC,cAAc,WAAY,WAAW;IACpD,MAAM,WAAW,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE;IAC9B,OAAO,SAAS,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;AAC7C;AAcA,uEAAuE;AACvE,SAAS,wBACP,OAAmC,EACnC,YAA4C;IAE5C,IAAI,CAAC,SAAS,OAAO;IAErB,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,QAAQ,cAAc,GACvC,IAAI,KAAK,QAAQ,cAAc,IAC/B;IAEJ,4EAA4E;IAC5E,IAAI,cAAc,qBAAqB;QACrC,MAAM,SAAS,aAAa,mBAAmB;QAC/C,MAAM,SAAS,aAAa,OAAO,IAAI;QAEvC,kEAAkE;QAClE,2EAA2E;QAC3E,IAAI,WAAW,YAAY,WAAW,iBAAiB;YACrD,+CAA+C;YAC/C,IAAI,WAAW,QAAQ;gBACrB,OAAO;YACT;QACF;QAEA,IAAI,WAAW,SAAS;YACtB,OAAO;QACT;IACF;IAEA,qEAAqE;IACrE,IAAI,gBAAgB,eAAe,KAAK;QACtC,OAAO;IACT;IAEA,OAAO,YAAY,2DAA2D;AAChF;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,QAAQ;AACV;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAClB;IAEA,sEAAsE;IACtE,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,qBACL,MAAM,CACL,2HAED,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,qDAAqD;IACrD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,yBACL,MAAM,CAAC,4CACP,EAAE,CAAC,uBAAuB,KAAK,EAAE,EACjC,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC,GACN,WAAW;IAEd,IAAI,mBAAmB;QACrB,QAAQ,KAAK,CAAC,qCAAqC;IACrD;IAEA,IAAI,gBAAgB,CAAC,aAAa;QAChC,QAAQ,KAAK,CACX,yDACA,cAAc;QAEhB,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAClB;IAEA,oFAAoF;IAEpF,2CAA2C;IAC3C,MAAM,qBAAqB,wBAAwB,aAAa;IAEhE,6FAA6F;IAC7F,yFAAyF;IACzF,MAAM,SAAS,cAAc,WAAW;IACxC,MAAM,cAAc,eAAe,QAAQ,cAAc,cAAc;IAEvE,sDAAsD;IACtD,MAAM,iBAAiB;QACrB,eAAe,YAAY,aAAa,IAAI;QAC5C,eAAe,YAAY,aAAa,IAAI;QAC5C,SAAS;QACT,YAAY,cAAc,cAAc;QACxC,yBACE,iEAAiE;QACjE,iEAAiE;QACjE,CAAC,cAAc,wBAAwB,YAAY,cAAc,wBAAwB,eAAe,KACxG,WAAW;QACb,gBAAgB,YAAY,cAAc;QAC1C,aAAa,YAAY,WAAW,IAAI;QACxC,qBAAqB,YAAY,mBAAmB,IAAI;QACxD,gBAAgB,YAAY,cAAc,IAAI;QAC9C,UAAU,YAAY,QAAQ;QAC9B,OAAO,YAAY,KAAK;QACxB,QAAQ,YAAY,MAAM,IAAI,UAAU,0CAA0C;IACpF;IAEA,qBACE,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC,6HAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;kBACtC,cAAA,8OAAC,uLAAA,CAAA,UAAuB;YACtB,gBAAgB;YAChB,QAAQ,KAAK,EAAE;YACf,oBAAoB;YACpB,aAAa;YACb,cAAc;;;;;;;;;;;AAItB", "debugId": null}}]}