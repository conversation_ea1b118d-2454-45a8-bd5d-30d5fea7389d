{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from \"@supabase/ssr\";\r\nimport { cookies } from \"next/headers\";\r\n\r\nexport async function createClient() {\r\n  const cookieStore = await cookies();\r\n\r\n  return createServerClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\r\n    {\r\n      cookies: {\r\n        getAll() {\r\n          return cookieStore.getAll();\r\n        },\r\n        setAll(cookiesToSet) {\r\n          try {\r\n            cookiesToSet.forEach(({ name, value, options }) =>\r\n              cookieStore.set(name, value, options)\r\n            );\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/api/customer/reviews/route.ts"], "sourcesContent": ["import { createClient } from '@/utils/supabase/server';\r\nimport { createAdminClient } from '@/utils/supabase/admin';\r\nimport { NextRequest, NextResponse } from 'next/server';\r\n\r\n// Define interfaces for the expected data structure\r\ninterface BusinessProfileDataForReview {\r\n  id: string;\r\n  business_name: string | null;\r\n  business_slug: string | null;\r\n  logo_url: string | null;\r\n}\r\n\r\ninterface ReviewWithProfile {\r\n  id: string;\r\n  rating: number;\r\n  review_text: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  business_profiles: BusinessProfileDataForReview | null;\r\n}\r\n\r\n\r\n\r\n// Constants - optimized for 2-column grid (1x8, 2x4)\r\nconst REVIEWS_PER_PAGE = 8;\r\n\r\nexport async function GET(request: NextRequest) {\r\n  try {\r\n    const supabase = await createClient();\r\n\r\n    // Get user authentication\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return NextResponse.json(\r\n        { error: 'Authentication required' },\r\n        { status: 401 }\r\n      );\r\n    }\r\n\r\n    // Get query parameters\r\n    const searchParams = request.nextUrl.searchParams;\r\n    const page = parseInt(searchParams.get('page') || '1');\r\n    const sortBy = searchParams.get('sort') || 'newest';\r\n\r\n    // Calculate pagination\r\n    const from = (page - 1) * REVIEWS_PER_PAGE;\r\n    const to = from + REVIEWS_PER_PAGE - 1;\r\n\r\n    // Simple query without join for better performance\r\n    let baseQuery = supabase\r\n      .from('ratings_reviews')\r\n      .select(`\r\n        id,\r\n        rating,\r\n        review_text,\r\n        created_at,\r\n        updated_at,\r\n        business_profile_id,\r\n        user_id\r\n      `, { count: 'exact' })\r\n      .eq('user_id', user.id);\r\n\r\n    // Apply sorting to the base query\r\n    switch (sortBy) {\r\n      case \"oldest\":\r\n        baseQuery = baseQuery.order(\"created_at\", { ascending: true });\r\n        break;\r\n      case \"highest_rating\":\r\n        baseQuery = baseQuery.order(\"rating\", { ascending: false });\r\n        break;\r\n      case \"lowest_rating\":\r\n        baseQuery = baseQuery.order(\"rating\", { ascending: true });\r\n        break;\r\n      case \"newest\":\r\n      default:\r\n        baseQuery = baseQuery.order(\"created_at\", { ascending: false });\r\n        break;\r\n    }\r\n\r\n    // Get count first (without pagination)\r\n    const { count: totalCount, error: countError } = await baseQuery;\r\n\r\n    if (countError) {\r\n      console.error('Error counting customer reviews:', countError);\r\n      return NextResponse.json(\r\n        { error: 'Failed to count reviews' },\r\n        { status: 500 }\r\n      );\r\n    }\r\n\r\n    // Now get the actual data with pagination\r\n    const { data: reviews, error: reviewsError } = await baseQuery.range(from, to);\r\n\r\n    if (reviewsError) {\r\n      console.error('Error fetching customer reviews:', reviewsError);\r\n      return NextResponse.json(\r\n        { error: 'Failed to fetch reviews' },\r\n        { status: 500 }\r\n      );\r\n    }\r\n\r\n    // Process the reviews - fetch business profiles separately for better performance\r\n    let typedReviews: ReviewWithProfile[] = [];\r\n\r\n    if (reviews && reviews.length > 0) {\r\n      // Fetch business profiles separately for the paginated reviews only\r\n      const businessProfileIds = reviews.map(review => review.business_profile_id);\r\n      const supabaseAdmin = createAdminClient();\r\n      const { data: businessProfiles } = await supabaseAdmin\r\n        .from('business_profiles')\r\n        .select('id, business_name, business_slug, logo_url')\r\n        .in('id', businessProfileIds);\r\n\r\n      typedReviews = reviews.map(review => {\r\n        const businessProfile = businessProfiles?.find(profile => profile.id === review.business_profile_id) || null;\r\n        return {\r\n          ...review,\r\n          business_profiles: businessProfile\r\n        };\r\n      });\r\n    }\r\n\r\n    // Calculate total pages\r\n    const totalPages = Math.ceil((totalCount || 0) / REVIEWS_PER_PAGE);\r\n\r\n    return NextResponse.json({\r\n      reviews: typedReviews,\r\n      pagination: {\r\n        currentPage: page,\r\n        totalPages,\r\n        totalCount,\r\n        perPage: REVIEWS_PER_PAGE\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Error in reviews API:', error);\r\n    return NextResponse.json(\r\n      { error: 'Internal server error' },\r\n      { status: 500 }\r\n    );\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAuBA,qDAAqD;AACrD,MAAM,mBAAmB;AAElB,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;QAElC,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY;QACjD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,SAAS,aAAa,GAAG,CAAC,WAAW;QAE3C,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,mBAAmB;QAErC,mDAAmD;QACnD,IAAI,YAAY,SACb,IAAI,CAAC,mBACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EAAE;YAAE,OAAO;QAAQ,GACnB,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,kCAAkC;QAClC,OAAQ;YACN,KAAK;gBACH,YAAY,UAAU,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBAC5D;YACF,KAAK;gBACH,YAAY,UAAU,KAAK,CAAC,UAAU;oBAAE,WAAW;gBAAM;gBACzD;YACF,KAAK;gBACH,YAAY,UAAU,KAAK,CAAC,UAAU;oBAAE,WAAW;gBAAK;gBACxD;YACF,KAAK;YACL;gBACE,YAAY,UAAU,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAC7D;QACJ;QAEA,uCAAuC;QACvC,MAAM,EAAE,OAAO,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;QAEvD,IAAI,YAAY;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,0CAA0C;QAC1C,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,UAAU,KAAK,CAAC,MAAM;QAE3E,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,kFAAkF;QAClF,IAAI,eAAoC,EAAE;QAE1C,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;YACjC,oEAAoE;YACpE,MAAM,qBAAqB,QAAQ,GAAG,CAAC,CAAA,SAAU,OAAO,mBAAmB;YAC3E,MAAM,gBAAgB,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD;YACtC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,MAAM,cACtC,IAAI,CAAC,qBACL,MAAM,CAAC,8CACP,EAAE,CAAC,MAAM;YAEZ,eAAe,QAAQ,GAAG,CAAC,CAAA;gBACzB,MAAM,kBAAkB,kBAAkB,KAAK,CAAA,UAAW,QAAQ,EAAE,KAAK,OAAO,mBAAmB,KAAK;gBACxG,OAAO;oBACL,GAAG,MAAM;oBACT,mBAAmB;gBACrB;YACF;QACF;QAEA,wBAAwB;QACxB,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,YAAY;gBACV,aAAa;gBACb;gBACA;gBACA,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}