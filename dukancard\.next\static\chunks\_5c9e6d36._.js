(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AnimatedMetricCard)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
"use client";
;
;
function AnimatedMetricCard({ title, value, icon: Icon, description, color, isUpdated = false }) {
    // Simple, clean color variants without glow effects
    const iconColors = {
        rose: "text-rose-600 dark:text-rose-400",
        blue: "text-blue-600 dark:text-blue-400",
        amber: "text-amber-600 dark:text-amber-400",
        red: "text-red-600 dark:text-red-400",
        yellow: "text-yellow-600 dark:text-yellow-400"
    };
    const iconColor = iconColors[color];
    // Animation variants
    const containerVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                type: "spring",
                stiffness: 300,
                damping: 24
            }
        }
    };
    const counterVariants = {
        initial: {
            scale: 1
        },
        update: {
            scale: 1.05,
            transition: {
                duration: 0.3
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        className: "relative overflow-hidden rounded-xl p-6 bg-white dark:bg-black border border-border shadow-sm",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center text-center space-y-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-3 rounded-xl bg-muted",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Icon, {
                        className: `w-6 h-6 ${iconColor}`
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
                        lineNumber: 65,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
                    lineNumber: 64,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                            className: "text-2xl font-bold text-foreground",
                            variants: counterVariants,
                            initial: "initial",
                            animate: isUpdated ? "update" : "initial",
                            children: value
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
                            lineNumber: 70,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm font-medium text-muted-foreground",
                            children: title
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
                    lineNumber: 69,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "text-xs text-muted-foreground",
                    children: description
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
                    lineNumber: 84,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
            lineNumber: 62,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx",
        lineNumber: 57,
        columnNumber: 5
    }, this);
}
_c = AnimatedMetricCard;
var _c;
__turbopack_context__.k.register(_c, "AnimatedMetricCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/business/components/DashboardOverviewClient.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DashboardOverviewClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/heart.js [app-client] (ecmascript) <export default as Heart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$AnimatedMetricCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/AnimatedMetricCard.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
function DashboardOverviewClient({ initialProfile, userId }) {
    _s();
    // State for real-time data
    const [profile, setProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initialProfile);
    const [isLikesUpdated, setIsLikesUpdated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isSubscriptionsUpdated, setIsSubscriptionsUpdated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isRatingUpdated, setIsRatingUpdated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Animation variants
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };
    /**
   * Set up Supabase real-time subscriptions
   *
   * This effect sets up a real-time subscription to the business_profiles table
   * to listen for changes to the metrics (total_likes, total_subscriptions, average_rating).
   *
   * When changes are detected:
   * 1. The UI is updated with the new values
   * 2. Animations are triggered to highlight the changes
   *
   * Note: We only listen to the business_profiles table because:
   * - The metrics are pre-aggregated in this table
   * - Database triggers (update_total_likes, update_total_subscriptions, update_average_rating)
   *   automatically update these values when the underlying data changes
   * - This approach is more efficient than listening to individual tables or counting records
   */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DashboardOverviewClient.useEffect": ()=>{
            const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
            const dashboardChannel = supabase.channel("business-dashboard").on("postgres_changes", {
                event: "*",
                schema: "public",
                table: "business_profiles",
                filter: `id=eq.${userId}`
            }, {
                "DashboardOverviewClient.useEffect.dashboardChannel": (payload)=>{
                    const newData = payload.new;
                    if (newData) {
                        // Check which values have changed and trigger animations accordingly
                        if (newData.total_likes !== profile.total_likes) {
                            setIsLikesUpdated(true);
                            setTimeout({
                                "DashboardOverviewClient.useEffect.dashboardChannel": ()=>setIsLikesUpdated(false)
                            }["DashboardOverviewClient.useEffect.dashboardChannel"], 1000);
                        }
                        if (newData.total_subscriptions !== profile.total_subscriptions) {
                            setIsSubscriptionsUpdated(true);
                            setTimeout({
                                "DashboardOverviewClient.useEffect.dashboardChannel": ()=>setIsSubscriptionsUpdated(false)
                            }["DashboardOverviewClient.useEffect.dashboardChannel"], 1000);
                        }
                        if (newData.average_rating !== profile.average_rating) {
                            setIsRatingUpdated(true);
                            setTimeout({
                                "DashboardOverviewClient.useEffect.dashboardChannel": ()=>setIsRatingUpdated(false)
                            }["DashboardOverviewClient.useEffect.dashboardChannel"], 1000);
                        }
                        // Update all profile data
                        setProfile({
                            "DashboardOverviewClient.useEffect.dashboardChannel": (prev)=>({
                                    ...prev,
                                    total_likes: newData.total_likes,
                                    total_subscriptions: newData.total_subscriptions,
                                    average_rating: newData.average_rating
                                })
                        }["DashboardOverviewClient.useEffect.dashboardChannel"]);
                    }
                }
            }["DashboardOverviewClient.useEffect.dashboardChannel"]).subscribe();
            // Cleanup function
            return ({
                "DashboardOverviewClient.useEffect": ()=>{
                    supabase.removeChannel(dashboardChannel);
                }
            })["DashboardOverviewClient.useEffect"];
        }
    }["DashboardOverviewClient.useEffect"], [
        userId,
        profile
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        className: "space-y-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$AnimatedMetricCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    title: "Likes",
                    value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatIndianNumberShort"])(profile.total_likes),
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__["Heart"],
                    description: "People who liked your card",
                    color: "red",
                    isUpdated: isLikesUpdated
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/DashboardOverviewClient.tsx",
                    lineNumber: 143,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$AnimatedMetricCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    title: "Subscribers",
                    value: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatIndianNumberShort"])(profile.total_subscriptions),
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"],
                    description: "People subscribed to updates",
                    color: "blue",
                    isUpdated: isSubscriptionsUpdated
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/DashboardOverviewClient.tsx",
                    lineNumber: 153,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$AnimatedMetricCard$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    title: "Rating",
                    value: `${profile.average_rating?.toFixed(1) || "0.0"}/5.0`,
                    icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"],
                    description: "Average customer rating",
                    color: "yellow",
                    isUpdated: isRatingUpdated
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/DashboardOverviewClient.tsx",
                    lineNumber: 163,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/(dashboard)/dashboard/business/components/DashboardOverviewClient.tsx",
            lineNumber: 141,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/DashboardOverviewClient.tsx",
        lineNumber: 134,
        columnNumber: 5
    }, this);
}
_s(DashboardOverviewClient, "UDgy4A8U/bfhHb5SlVjPdy7Iyyc=");
_c = DashboardOverviewClient;
var _c;
__turbopack_context__.k.register(_c, "DashboardOverviewClient");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$intervalToDuration$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/intervalToDuration.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2d$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarClock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar-clock.js [app-client] (ecmascript) <export default as CalendarClock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/tooltip.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
const FlipTimer = ({ endDate, label = "Trial ends in:", tooltipText = "Your trial will expire soon. Upgrade to continue using all features.", className })=>{
    _s();
    const [now, setNow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Date());
    const [timeUnits, setTimeUnits] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const targetDate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FlipTimer.useMemo[targetDate]": ()=>{
            // Parse the date and ensure it's treated as IST (UTC+5:30)
            let date;
            if (typeof endDate === "string") {
                // For string dates, create a new Date object
                date = new Date(endDate);
            } else {
                // For Date objects, use as is
                date = endDate;
            }
            // Validate the date
            return !isNaN(date.getTime()) ? date : null;
        }
    }["FlipTimer.useMemo[targetDate]"], [
        endDate
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FlipTimer.useEffect": ()=>{
            if (!targetDate) return;
            const updateTime = {
                "FlipTimer.useEffect.updateTime": ()=>{
                    const currentTime = new Date();
                    setNow(currentTime);
                    if (targetDate && currentTime < targetDate) {
                        const duration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$intervalToDuration$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["intervalToDuration"])({
                            start: currentTime,
                            end: targetDate
                        });
                        const units = [];
                        // Always show a complete breakdown of time units
                        // Calculate years, months, days, hours, minutes, seconds
                        const diffMs = targetDate.getTime() - currentTime.getTime();
                        // Calculate years (approximate)
                        const millisecondsInYear = 1000 * 60 * 60 * 24 * 365.25; // Account for leap years
                        const years = Math.floor(diffMs / millisecondsInYear);
                        let remainder = diffMs % millisecondsInYear;
                        // Calculate months (approximate)
                        const millisecondsInMonth = 1000 * 60 * 60 * 24 * 30.44; // Average month length
                        const months = Math.floor(remainder / millisecondsInMonth);
                        remainder = remainder % millisecondsInMonth;
                        // Calculate days
                        const millisecondsInDay = 1000 * 60 * 60 * 24;
                        const days = Math.floor(remainder / millisecondsInDay);
                        remainder = remainder % millisecondsInDay;
                        // Hours, minutes, seconds from duration object
                        const hours = duration.hours || 0;
                        const minutes = duration.minutes || 0;
                        const seconds = duration.seconds || 0;
                        // Add time units to the display, but limit to 5 units maximum to avoid overcrowding
                        const allUnits = [];
                        if (years > 0) {
                            allUnits.push({
                                label: "years",
                                value: years
                            });
                        }
                        if (months > 0) {
                            allUnits.push({
                                label: "months",
                                value: months
                            });
                        }
                        if (days > 0) {
                            allUnits.push({
                                label: "days",
                                value: days
                            });
                        }
                        // Always include hours, minutes, and seconds
                        allUnits.push({
                            label: "hrs",
                            value: hours
                        }, {
                            label: "min",
                            value: minutes
                        }, {
                            label: "sec",
                            value: seconds
                        });
                        // Always include seconds and take up to 5 other units
                        // Extract seconds first
                        const secondsUnit = allUnits.pop(); // This is the seconds unit
                        // Take up to 5 other units
                        units.push(...allUnits.slice(0, 5));
                        // Always add seconds at the end
                        if (secondsUnit) {
                            units.push(secondsUnit);
                        }
                        setTimeUnits(units);
                    }
                }
            }["FlipTimer.useEffect.updateTime"];
            // Initial update
            updateTime();
            // Set up interval
            const intervalId = setInterval(updateTime, 1000);
            // Cleanup
            return ({
                "FlipTimer.useEffect": ()=>clearInterval(intervalId)
            })["FlipTimer.useEffect"];
        }
    }["FlipTimer.useEffect"], [
        targetDate
    ]);
    if (!targetDate || now >= targetDate) {
        return null; // Don't render if date is invalid or trial ended
    }
    // Determine if this is a short or long countdown
    const isLongCountdown = timeUnits.some((unit)=>unit.label === "months" || unit.label === "years");
    // Determine color theme based on time remaining
    const getColorTheme = ()=>{
        if (isLongCountdown) {
            return {
                gradient: "from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-900/20",
                border: "border-blue-200 dark:border-blue-800/50",
                text: "text-blue-600 dark:text-blue-400",
                shadow: "shadow-blue-200/20 dark:shadow-blue-900/20",
                glow: "after:bg-blue-500/10 dark:after:bg-blue-400/10"
            };
        }
        // Less than a day remaining
        if (!timeUnits.some((unit)=>unit.label === "days")) {
            return {
                gradient: "from-amber-100 to-amber-50 dark:from-amber-900/40 dark:to-amber-900/20",
                border: "border-amber-200 dark:border-amber-800/50",
                text: "text-amber-600 dark:text-amber-400",
                shadow: "shadow-amber-200/20 dark:shadow-amber-900/20",
                glow: "after:bg-amber-500/10 dark:after:bg-amber-400/10"
            };
        }
        // Default (days remaining)
        return {
            gradient: "from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/5 dark:from-[var(--brand-gold)]/30 dark:to-[var(--brand-gold)]/10",
            border: "border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/20",
            text: "text-[var(--brand-gold)] dark:text-[var(--brand-gold)]",
            shadow: "shadow-[var(--brand-gold)]/10 dark:shadow-[var(--brand-gold)]/10",
            glow: "after:bg-[var(--brand-gold)]/10 dark:after:bg-[var(--brand-gold)]/10"
        };
    };
    const colorTheme = getColorTheme();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex flex-col items-center w-full ${className || ""}`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipProvider"], {
                delayDuration: 100,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipTrigger"], {
                            asChild: true,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-1.5 mb-1.5",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "transition-transform hover:scale-110",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2d$clock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CalendarClock$3e$__["CalendarClock"], {
                                            className: `w-4 h-4 ${colorTheme.text}`
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                            lineNumber: 193,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                        lineNumber: 192,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-xs font-medium text-muted-foreground",
                                        children: label
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                        lineNumber: 195,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                lineNumber: 191,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                            lineNumber: 190,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$tooltip$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TooltipContent"], {
                            side: "bottom",
                            className: "bg-neutral-800/95 dark:bg-neutral-950/95 backdrop-blur-sm border border-neutral-700/50 dark:border-[var(--brand-gold)]/20 text-white text-xs p-3 rounded-lg shadow-lg",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                children: tooltipText
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                lineNumber: 204,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                            lineNumber: 200,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                    lineNumber: 189,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                lineNumber: 188,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex gap-1 xs:gap-1.5 sm:gap-2 flex-wrap justify-center",
                children: timeUnits.map((unit, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: `relative w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 md:w-11 md:h-11 overflow-hidden rounded-md ${colorTheme.shadow} shadow-md after:absolute after:inset-0 after:rounded-md after:opacity-30 after:blur-xl ${colorTheme.glow}`,
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute top-0 left-0 right-0 h-[1px] bg-white/40 dark:bg-white/10 z-10"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                        lineNumber: 216,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute bottom-0 left-0 right-0 h-[1px] bg-black/10 dark:bg-black/20 z-10"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                        lineNumber: 219,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute top-1/2 left-0 right-0 h-[1px] bg-black/10 dark:bg-white/10 z-10"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `absolute inset-0 flex items-center justify-center bg-gradient-to-b ${colorTheme.gradient} rounded-md border ${colorTheme.border}`,
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-xs xs:text-sm sm:text-base md:text-lg font-mono font-bold text-neutral-800 dark:text-neutral-200",
                                            children: unit.value.toString().padStart(2, "0")
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                            lineNumber: 228,
                                            columnNumber: 17
                                        }, this)
                                    }, `${unit.label}-${unit.value}`, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                        lineNumber: 224,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                lineNumber: 212,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-[7px] xs:text-[8px] sm:text-[9px] font-medium text-muted-foreground mt-0.5",
                                children: unit.label
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                                lineNumber: 233,
                                columnNumber: 13
                            }, this)
                        ]
                    }, index, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                        lineNumber: 211,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
                lineNumber: 209,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx",
        lineNumber: 187,
        columnNumber: 5
    }, this);
};
_s(FlipTimer, "Dr1r+35pn90bGmlkuhSsZFO4R/Y=");
_c = FlipTimer;
const __TURBOPACK__default__export__ = FlipTimer;
var _c;
__turbopack_context__.k.register(_c, "FlipTimer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Subscription status constants - IMMUTABLE SINGLE SOURCE OF TRUTH
__turbopack_context__.s({
    "PLAN_IDS": (()=>PLAN_IDS),
    "SUBSCRIPTION_STATUS": (()=>SUBSCRIPTION_STATUS)
});
const SUBSCRIPTION_STATUS = {
    ACTIVE: 'active',
    AUTHENTICATED: 'authenticated',
    TRIAL: 'trial',
    PENDING: 'pending',
    HALTED: 'halted',
    CANCELLED: 'cancelled',
    EXPIRED: 'expired',
    COMPLETED: 'completed',
    PAYMENT_FAILED: 'payment_failed',
    CANCELLATION_SCHEDULED: 'cancellation_scheduled'
};
const PLAN_IDS = {
    FREE: 'free',
    BASIC: 'basic',
    GROWTH: 'growth',
    PRO: 'pro',
    ENTERPRISE: 'enterprise'
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SubscriptionStateManager": (()=>SubscriptionStateManager),
    "isActivePaidSubscription": (()=>isActivePaidSubscription),
    "isFreeStatus": (()=>isFreeStatus),
    "isTerminalStatus": (()=>isTerminalStatus),
    "isTrialStatus": (()=>isTrialStatus),
    "shouldHaveActiveSubscription": (()=>shouldHaveActiveSubscription),
    "shouldHaveActiveSubscriptionByPlan": (()=>shouldHaveActiveSubscriptionByPlan)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-client] (ecmascript)");
;
class SubscriptionStateManager {
    /**
   * MASTER FUNCTION: Determines if a user should have active subscription access
   * This is the ONLY function that should be used for subscription access control
   *
   * @param status The subscription status
   * @param planId The plan ID (required for accurate determination)
   * @returns true if the user should have active subscription features
   */ static shouldHaveActiveSubscription(status, planId = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
        // Free plan users NEVER have "active subscription" - they're on free tier
        if (planId === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
            return false;
        }
        // Trial users NEVER have "active subscription" - they're testing, not paying
        if (status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL) {
            return false;
        }
        // Only ACTIVE status on PAID plans counts as "active subscription"
        // Authenticated users have selected a plan but haven't paid yet
        const activeStatuses = [
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].ACTIVE
        ];
        return activeStatuses.includes(status);
    }
    /**
   * Determines if a subscription status is considered terminal (final state)
   * Terminal states cannot transition to active states without creating new subscription
   */ static isTerminalStatus(status) {
        const terminalStatuses = [
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].CANCELLED,
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].EXPIRED,
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].COMPLETED
        ];
        return terminalStatuses.includes(status);
    }
    /**
   * Determines if a subscription status indicates trial period
   */ static isTrialStatus(status) {
        return status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL;
    }
    /**
   * Determines if a plan is a free plan
   */ static isFreeStatus(status, planId) {
        return planId === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE || status === 'free';
    }
    /**
   * Get user's access level based on subscription state
   */ static getAccessLevel(status, planId = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
        if (planId === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) return 'free';
        if (status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL) return 'trial';
        if (this.shouldHaveActiveSubscription(status, planId)) return 'paid';
        return 'free';
    }
    /**
   * Determines if a subscription status indicates an active paid subscription
   */ static isActivePaidSubscription(status, planId = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
        return this.shouldHaveActiveSubscription(status, planId);
    }
    /**
   * Validates if a status transition is allowed
   */ static isValidStatusTransition(fromStatus, toStatus) {
        // Terminal states cannot transition to non-terminal states
        if (this.isTerminalStatus(fromStatus) && !this.isTerminalStatus(toStatus)) {
            return false;
        }
        // All other transitions are allowed
        return true;
    }
}
function shouldHaveActiveSubscription(status) {
    console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');
    return SubscriptionStateManager.shouldHaveActiveSubscription(status, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE);
}
function isTerminalStatus(status) {
    return SubscriptionStateManager.isTerminalStatus(status);
}
function isActivePaidSubscription(status) {
    console.warn('[DEPRECATED] Use SubscriptionStateManager.isActivePaidSubscription(status, planId) instead');
    return SubscriptionStateManager.isActivePaidSubscription(status, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE);
}
function isTrialStatus(status) {
    return SubscriptionStateManager.isTrialStatus(status);
}
function isFreeStatus(status, planId) {
    return SubscriptionStateManager.isFreeStatus(status, planId);
}
function shouldHaveActiveSubscriptionByPlan(status, planId) {
    console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');
    return SubscriptionStateManager.shouldHaveActiveSubscription(status, planId);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/utils/supabase/admin.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdminClient": (()=>createAdminClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
;
function createAdminClient() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co"), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.SUPABASE_SERVICE_ROLE_KEY);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/subscription-state-validator.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "validateAndFixSubscriptionState": (()=>validateAndFixSubscriptionState),
    "validateSubscriptionState": (()=>validateSubscriptionState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-client] (ecmascript)");
;
;
;
function validateSubscriptionState(businessProfile, paymentSubscription) {
    const warnings = [];
    const errors = [];
    // Determine expected state based on payment subscription using centralized manager
    let expectedHasActiveSubscription = false;
    let accessLevel = 'free';
    if (paymentSubscription) {
        expectedHasActiveSubscription = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionStateManager"].shouldHaveActiveSubscription(paymentSubscription.subscription_status, paymentSubscription.plan_id);
        accessLevel = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionStateManager"].getAccessLevel(paymentSubscription.subscription_status, paymentSubscription.plan_id);
    }
    // Check for inconsistencies
    if (businessProfile.has_active_subscription !== expectedHasActiveSubscription) {
        errors.push(`has_active_subscription mismatch: business_profiles=${businessProfile.has_active_subscription}, expected=${expectedHasActiveSubscription}`);
    }
    // Check trial state consistency
    if (paymentSubscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL) {
        if (!businessProfile.trial_end_date) {
            warnings.push('Trial status but no trial_end_date set');
        } else {
            const trialEnd = new Date(businessProfile.trial_end_date);
            const now = new Date();
            if (trialEnd <= now) {
                warnings.push('Trial status but trial period has expired');
            }
        }
    }
    return {
        isValid: errors.length === 0,
        hasActiveSubscription: expectedHasActiveSubscription,
        accessLevel,
        warnings,
        errors
    };
}
async function validateAndFixSubscriptionState(businessProfileId) {
    try {
        const adminClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Get current state from both tables
        const [profileResult, subscriptionResult] = await Promise.all([
            adminClient.from('business_profiles').select('has_active_subscription, trial_end_date').eq('id', businessProfileId).maybeSingle(),
            adminClient.from('payment_subscriptions').select('subscription_status, plan_id').eq('business_profile_id', businessProfileId).order('created_at', {
                ascending: false
            }).limit(1).maybeSingle()
        ]);
        if (profileResult.error) {
            return {
                success: false,
                message: `Profile fetch error: ${profileResult.error.message}`,
                fixed: false
            };
        }
        if (!profileResult.data) {
            return {
                success: false,
                message: 'Business profile not found',
                fixed: false
            };
        }
        // Use centralized validation
        const validation = validateSubscriptionState(profileResult.data, subscriptionResult.data);
        if (validation.isValid) {
            return {
                success: true,
                message: 'Subscription state is consistent',
                fixed: false
            };
        }
        // Fix inconsistencies
        const expectedHasActiveSubscription = validation.hasActiveSubscription;
        if (profileResult.data.has_active_subscription !== expectedHasActiveSubscription) {
            const { error: fixError } = await adminClient.from('business_profiles').update({
                has_active_subscription: expectedHasActiveSubscription,
                updated_at: new Date().toISOString()
            }).eq('id', businessProfileId);
            if (fixError) {
                return {
                    success: false,
                    message: `Failed to fix inconsistency: ${fixError.message}`,
                    fixed: false
                };
            }
            console.log(`[STATE_VALIDATOR] Fixed has_active_subscription for ${businessProfileId}: ${profileResult.data.has_active_subscription} -> ${expectedHasActiveSubscription}`);
            return {
                success: true,
                message: `Fixed subscription state inconsistency`,
                fixed: true
            };
        }
        return {
            success: true,
            message: 'No fixes needed',
            fixed: false
        };
    } catch (error) {
        console.error(`[STATE_VALIDATOR] Exception:`, error);
        return {
            success: false,
            message: `Validation exception: ${error instanceof Error ? error.message : String(error)}`,
            fixed: false
        };
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/types.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Razorpay Subscription Webhook Event Types
 *
 * This file contains the types for Razorpay subscription webhook events.
 */ // Subscription webhook event types
__turbopack_context__.s({
    "RazorpayPaymentStatus": (()=>RazorpayPaymentStatus),
    "RazorpayRefundStatus": (()=>RazorpayRefundStatus),
    "RazorpaySubscriptionEventType": (()=>RazorpaySubscriptionEventType),
    "RazorpaySubscriptionStatus": (()=>RazorpaySubscriptionStatus),
    "SupabaseSubscriptionStatus": (()=>SupabaseSubscriptionStatus),
    "mapRazorpayStatusToSupabase": (()=>mapRazorpayStatusToSupabase)
});
var RazorpaySubscriptionEventType = /*#__PURE__*/ function(RazorpaySubscriptionEventType) {
    // Subscription events
    RazorpaySubscriptionEventType["_SUBSCRIPTION_AUTHENTICATED"] = "subscription.authenticated";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_ACTIVATED"] = "subscription.activated";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_CHARGED"] = "subscription.charged";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_PENDING"] = "subscription.pending";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_HALTED"] = "subscription.halted";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_CANCELLED"] = "subscription.cancelled";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_COMPLETED"] = "subscription.completed";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_EXPIRED"] = "subscription.expired";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_UPDATED"] = "subscription.updated";
    // Payment events
    RazorpaySubscriptionEventType["_PAYMENT_AUTHORIZED"] = "payment.authorized";
    RazorpaySubscriptionEventType["_PAYMENT_CAPTURED"] = "payment.captured";
    RazorpaySubscriptionEventType["_PAYMENT_FAILED"] = "payment.failed";
    // Invoice events
    RazorpaySubscriptionEventType["_INVOICE_PAID"] = "invoice.paid";
    // Refund events
    RazorpaySubscriptionEventType["_REFUND_CREATED"] = "refund.created";
    RazorpaySubscriptionEventType["_REFUND_PROCESSED"] = "refund.processed";
    RazorpaySubscriptionEventType["_REFUND_FAILED"] = "refund.failed";
    return RazorpaySubscriptionEventType;
}({});
var RazorpaySubscriptionStatus = /*#__PURE__*/ function(RazorpaySubscriptionStatus) {
    RazorpaySubscriptionStatus["_CREATED"] = "created";
    RazorpaySubscriptionStatus["_AUTHENTICATED"] = "authenticated";
    RazorpaySubscriptionStatus["_ACTIVE"] = "active";
    RazorpaySubscriptionStatus["_PENDING"] = "pending";
    RazorpaySubscriptionStatus["_HALTED"] = "halted";
    RazorpaySubscriptionStatus["_CANCELLED"] = "cancelled";
    RazorpaySubscriptionStatus["_COMPLETED"] = "completed";
    RazorpaySubscriptionStatus["_EXPIRED"] = "expired";
    return RazorpaySubscriptionStatus;
}({});
var RazorpayPaymentStatus = /*#__PURE__*/ function(RazorpayPaymentStatus) {
    RazorpayPaymentStatus["_CREATED"] = "created";
    RazorpayPaymentStatus["_AUTHORIZED"] = "authorized";
    RazorpayPaymentStatus["_CAPTURED"] = "captured";
    RazorpayPaymentStatus["_REFUNDED"] = "refunded";
    RazorpayPaymentStatus["_FAILED"] = "failed";
    return RazorpayPaymentStatus;
}({});
var RazorpayRefundStatus = /*#__PURE__*/ function(RazorpayRefundStatus) {
    RazorpayRefundStatus["_CREATED"] = "created";
    RazorpayRefundStatus["_PROCESSED"] = "processed";
    RazorpayRefundStatus["_FAILED"] = "failed";
    return RazorpayRefundStatus;
}({});
var SupabaseSubscriptionStatus = /*#__PURE__*/ function(SupabaseSubscriptionStatus) {
    SupabaseSubscriptionStatus["_ACTIVE"] = "active";
    SupabaseSubscriptionStatus["_PENDING"] = "pending";
    SupabaseSubscriptionStatus["_HALTED"] = "halted";
    SupabaseSubscriptionStatus["_CANCELLED"] = "cancelled";
    SupabaseSubscriptionStatus["_COMPLETED"] = "completed";
    SupabaseSubscriptionStatus["_EXPIRED"] = "expired";
    SupabaseSubscriptionStatus["_PAYMENT_FAILED"] = "payment_failed";
    SupabaseSubscriptionStatus["_AUTHENTICATED"] = "authenticated";
    return SupabaseSubscriptionStatus;
}({});
function mapRazorpayStatusToSupabase(razorpayStatus) {
    switch(razorpayStatus){
        case "active":
            return "active";
        case "pending":
            return "pending";
        case "halted":
            return "halted";
        case "cancelled":
            return "cancelled";
        case "completed":
            return "completed";
        case "authenticated":
            return "authenticated";
        case "expired":
            return "expired";
        default:
            // Default to pending for unknown statuses
            console.warn(`Unknown Razorpay status: ${razorpayStatus}, defaulting to pending`);
            return "pending";
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/subscription-db-updater.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "updateSubscription": (()=>updateSubscription),
    "updateSubscriptionWithBusinessProfile": (()=>updateSubscriptionWithBusinessProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/types.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-client] (ecmascript)");
;
;
;
async function updateSubscription(_supabase, subscriptionId, status, additionalData = {}) {
    try {
        // Get admin client to bypass RLS
        const adminClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Get subscription details from Razorpay to find the business_profile_id and other details
        const { getSubscription } = await __turbopack_context__.r("[project]/lib/razorpay/services/subscription/index.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
        const subscriptionDetails = await getSubscription(subscriptionId);
        if (!subscriptionDetails.success || !subscriptionDetails.data) {
            console.error(`[RAZORPAY_WEBHOOK] Failed to get subscription details from Razorpay for ${subscriptionId}`);
            // ENHANCED: Try to get business_profile_id from our database instead
            const { data: localSubscription, error: localError } = await adminClient.from('payment_subscriptions').select('business_profile_id, plan_id, plan_cycle').eq('razorpay_subscription_id', subscriptionId).maybeSingle();
            if (localError || !localSubscription) {
                console.error(`[RAZORPAY_WEBHOOK] Also failed to get subscription from local database for ${subscriptionId}:`, localError);
                return {
                    success: false,
                    message: `Failed to get subscription details from both Razorpay and local database`
                };
            }
            console.log(`[RAZORPAY_WEBHOOK] Using local subscription data for ${subscriptionId} since Razorpay API failed`);
            // Create a mock subscription details object using local data
            const mockSubscriptionDetails = {
                success: true,
                data: {
                    id: subscriptionId,
                    plan_id: `${localSubscription.plan_id}_${localSubscription.plan_cycle}`,
                    customer_id: null,
                    current_start: null,
                    current_end: null,
                    charge_at: null,
                    start_at: null,
                    notes: {
                        business_profile_id: localSubscription.business_profile_id,
                        plan_type: localSubscription.plan_id,
                        plan_cycle: localSubscription.plan_cycle
                    }
                }
            };
            // Use the mock data for the rest of the function
            const subscriptionDetailsToUse = mockSubscriptionDetails;
            return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetailsToUse.data);
        }
        // Continue with normal processing using Razorpay data
        return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetails.data);
    } catch (error) {
        console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);
        return {
            success: false,
            message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
/**
 * Process subscription update with given subscription data
 * This function contains the main logic extracted from updateSubscription
 */ async function processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionData) {
    try {
        // Extract business_profile_id from notes
        const businessProfileId = subscriptionData.notes?.business_profile_id || subscriptionData.notes?.user_id;
        if (!businessProfileId) {
            console.error(`[RAZORPAY_WEBHOOK] No business_profile_id found in subscription notes for ${subscriptionId}`);
            return {
                success: false,
                message: `No business_profile_id found in subscription notes`
            };
        }
        // Extract plan details from notes or plan_id
        let planType = subscriptionData.notes?.plan_type;
        let planCycle = subscriptionData.notes?.plan_cycle;
        // If plan type and cycle are not in notes, try to determine from plan_id
        if (!planType || !planCycle) {
            console.log(`[RAZORPAY_WEBHOOK] Plan type or cycle not found in notes, determining from plan_id: ${subscriptionData.plan_id}`);
            // Use centralized plan configuration to map Razorpay plan ID to plan details
            const { getPlanByRazorpayPlanId } = await __turbopack_context__.r("[project]/lib/config/plans.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
            const planDetails = getPlanByRazorpayPlanId(subscriptionData.plan_id);
            if (planDetails) {
                planType = planDetails.id;
                // Determine cycle by checking which Razorpay plan ID matches
                planCycle = planDetails.razorpayPlanIds.monthly === subscriptionData.plan_id ? "monthly" : "yearly";
                console.log(`[RAZORPAY_WEBHOOK] Determined plan type: ${planType}, cycle: ${planCycle} from plan_id using centralized config`);
            } else {
                // Default to basic monthly if we can't determine
                planType = "basic";
                planCycle = "monthly";
                console.log(`[RAZORPAY_WEBHOOK] Could not determine plan type and cycle from plan_id: ${subscriptionData.plan_id}, defaulting to basic monthly`);
            }
        }
        // CENTRALIZED LOGIC: Use SubscriptionStateManager to determine has_active_subscription
        const hasActiveSubscription = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SubscriptionStateManager"].shouldHaveActiveSubscription(status, planType || 'free');
        // Create a copy of additionalData to avoid modifying the original
        const additionalDataCopy = {
            ...additionalData
        };
        // Remove has_active_subscription from additionalData if it exists
        // This ensures we always set it based on the status, not what's passed in
        if ('has_active_subscription' in additionalDataCopy) {
            console.log(`[RAZORPAY_WEBHOOK] Removing has_active_subscription from additionalData for subscription with status ${status}`);
            delete additionalDataCopy.has_active_subscription;
        }
        // Extract subscription dates from Razorpay subscription details
        let subscriptionStartDate = subscriptionData.current_start ? new Date(subscriptionData.current_start * 1000).toISOString() : null;
        const subscriptionExpiryTime = subscriptionData.current_end ? new Date(subscriptionData.current_end * 1000).toISOString() : null;
        const subscriptionChargeTime = subscriptionData.charge_at ? new Date(subscriptionData.charge_at * 1000).toISOString() : null;
        // Check if the status is authenticated or active
        const isValidStatus = status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SupabaseSubscriptionStatus"]._AUTHENTICATED || status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SupabaseSubscriptionStatus"]._ACTIVE;
        if (!isValidStatus) {
            console.log(`[RAZORPAY_WEBHOOK] Skipping creation/update of subscription record for ${subscriptionId} with status ${status} - only handling authenticated or active statuses`);
            return {
                success: true,
                message: `Skipped creation/update of subscription record with status ${status}`
            };
        }
        // For authenticated subscriptions, ensure we're setting the correct dates
        // This is especially important for trial users where start_at is in the future
        if (status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SupabaseSubscriptionStatus"]._AUTHENTICATED) {
            console.log(`[RAZORPAY_WEBHOOK] Authenticated subscription detected, ensuring dates are set correctly`);
            // For authenticated subscriptions, we need to check if start_at is set
            // If it is, we should use that for subscription_start_date instead of current_start
            if (subscriptionData.start_at) {
                const startAt = new Date(subscriptionData.start_at * 1000).toISOString();
                console.log(`[RAZORPAY_WEBHOOK] Using start_at (${startAt}) for subscription_start_date`);
                // Override the subscription_start_date with start_at
                subscriptionStartDate = startAt;
            }
        }
        // Find existing subscription record
        const { data: subscription, error: findError } = await adminClient.from('payment_subscriptions').select('id, business_profile_id, razorpay_subscription_id').eq('razorpay_subscription_id', subscriptionId).maybeSingle();
        if (findError) {
            console.error(`[RAZORPAY_WEBHOOK] Error finding subscription ${subscriptionId}:`, findError);
            return {
                success: false,
                message: `Error finding subscription: ${findError.message}`
            };
        }
        // If no subscription record exists for this Razorpay subscription ID, check if there's an existing record for the business
        if (!subscription) {
            console.log(`[RAZORPAY_WEBHOOK] No subscription found with ID ${subscriptionId}, checking for existing subscription for business ${businessProfileId}`);
            // Check if there's an existing subscription for this business
            const { data: existingSubscriptions, error: existingError } = await adminClient.from('payment_subscriptions').select('id, razorpay_subscription_id, subscription_status').eq('business_profile_id', businessProfileId);
            if (existingError) {
                console.error(`[RAZORPAY_WEBHOOK] Error checking for existing subscriptions for business ${businessProfileId}:`, existingError);
                return {
                    success: false,
                    message: `Error checking for existing subscriptions: ${existingError.message}`
                };
            }
            // If there's an existing subscription for this business, update it instead of creating a new one
            if (existingSubscriptions && existingSubscriptions.length > 0) {
                console.log(`[RAZORPAY_WEBHOOK] Found existing subscription for business ${businessProfileId}, updating instead of creating new one`);
                const existingSubscription = existingSubscriptions[0];
                // Create the update data object
                const updateData = {
                    razorpay_subscription_id: subscriptionId,
                    razorpay_customer_id: subscriptionData.customer_id || null,
                    subscription_status: status,
                    plan_id: planType,
                    plan_cycle: planCycle,
                    subscription_start_date: subscriptionStartDate,
                    subscription_expiry_time: subscriptionExpiryTime,
                    subscription_charge_time: subscriptionChargeTime,
                    updated_at: new Date().toISOString(),
                    ...additionalDataCopy
                };
                // Update the existing subscription record
                const { error: updateError } = await adminClient.from('payment_subscriptions').update(updateData).eq('id', existingSubscription.id);
                if (updateError) {
                    console.error(`[RAZORPAY_WEBHOOK] Error updating existing subscription ${existingSubscription.id}:`, updateError);
                    return {
                        success: false,
                        message: `Error updating existing subscription: ${updateError.message}`
                    };
                }
                console.log(`[RAZORPAY_WEBHOOK] Updated existing subscription ${existingSubscription.id} with new Razorpay ID ${subscriptionId} and status ${status}`);
                // Use transaction utility to ensure consistency
                const transactionResult = await updateSubscriptionWithBusinessProfile({
                    subscription_id: subscriptionId,
                    business_profile_id: businessProfileId,
                    subscription_status: status,
                    has_active_subscription: hasActiveSubscription,
                    additional_data: updateData
                });
                if (!transactionResult.success) {
                    console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);
                    return {
                        success: false,
                        message: `Transaction failed: ${transactionResult.message}`
                    };
                }
                return {
                    success: true,
                    message: `Updated existing subscription with new Razorpay ID and status ${status}`
                };
            }
            // If no existing subscription for this business, create a new one
            console.log(`[RAZORPAY_WEBHOOK] No existing subscription found for business ${businessProfileId}, creating new one`);
            const insertData = {
                business_profile_id: businessProfileId,
                razorpay_subscription_id: subscriptionId,
                razorpay_customer_id: subscriptionData.customer_id || null,
                subscription_status: status,
                plan_id: planType,
                plan_cycle: planCycle,
                subscription_start_date: subscriptionStartDate,
                subscription_expiry_time: subscriptionExpiryTime,
                subscription_charge_time: subscriptionChargeTime,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                ...additionalDataCopy
            };
            const { data: _newSubscription, error: insertError } = await adminClient.from('payment_subscriptions').insert(insertData).select('id').single();
            if (insertError) {
                console.error(`[RAZORPAY_WEBHOOK] Error creating subscription record for ${subscriptionId}:`, insertError);
                return {
                    success: false,
                    message: `Error creating subscription record: ${insertError.message}`
                };
            }
            console.log(`[RAZORPAY_WEBHOOK] Created new subscription record for ${subscriptionId}`);
            // Use transaction utility to ensure consistency
            const transactionResult = await updateSubscriptionWithBusinessProfile({
                subscription_id: subscriptionId,
                business_profile_id: businessProfileId,
                subscription_status: status,
                has_active_subscription: hasActiveSubscription,
                additional_data: insertData
            });
            if (!transactionResult.success) {
                console.error(`[RAZORPAY_WEBHOOK] Transaction failed for new subscription ${subscriptionId}:`, transactionResult.message);
                return {
                    success: false,
                    message: `Transaction failed: ${transactionResult.message}`
                };
            }
            console.log(`[RAZORPAY_WEBHOOK] Created subscription for ${subscriptionId} with status ${status}`);
            return {
                success: true,
                message: `Created subscription with status ${status}`
            };
        }
        // If subscription exists, update it
        const updateData = {
            subscription_status: status,
            subscription_start_date: subscriptionStartDate,
            subscription_expiry_time: subscriptionExpiryTime,
            subscription_charge_time: subscriptionChargeTime,
            updated_at: new Date().toISOString(),
            ...additionalDataCopy
        };
        // ENHANCED: Use atomic RPC function for transaction safety
        const transactionResult = await updateSubscriptionWithBusinessProfile({
            subscription_id: subscriptionId,
            business_profile_id: businessProfileId,
            subscription_status: status,
            has_active_subscription: hasActiveSubscription,
            additional_data: updateData
        });
        if (!transactionResult.success) {
            console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);
            return {
                success: false,
                message: `Transaction failed: ${transactionResult.message}`
            };
        }
        console.log(`[RAZORPAY_WEBHOOK] Updated subscription ${subscription.id} with status ${status}`);
        return {
            success: true,
            message: `Updated subscription with status ${status}`
        };
    } catch (error) {
        console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);
        return {
            success: false,
            message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function updateSubscriptionWithBusinessProfile(params) {
    try {
        const adminClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createAdminClient"])();
        console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${params.subscription_id}`);
        // ENHANCED: Use atomic RPC function for true transaction safety
        const { data: result, error } = await adminClient.rpc('update_subscription_atomic', {
            p_subscription_id: params.subscription_id,
            p_new_status: params.subscription_status,
            p_business_profile_id: params.business_profile_id,
            p_has_active_subscription: params.has_active_subscription,
            p_additional_data: params.additional_data || {},
            p_webhook_timestamp: params.additional_data?.last_webhook_timestamp || null
        });
        if (error) {
            console.error(`[ATOMIC_TRANSACTION] RPC error for ${params.subscription_id}:`, error);
            return {
                success: false,
                message: `RPC error: ${error.message}`
            };
        }
        if (!result?.success) {
            console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${params.subscription_id}:`, result);
            return {
                success: false,
                message: result?.error || 'Unknown RPC error'
            };
        }
        console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${params.subscription_id} atomically`);
        return {
            success: true,
            message: 'Atomic transaction completed successfully via RPC'
        };
    } catch (error) {
        console.error(`[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:`, error);
        return {
            success: false,
            message: `Atomic transaction exception: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/webhook-utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractWebhookTimestamp": (()=>extractWebhookTimestamp),
    "getAppropriateSubscriptionStatus": (()=>getAppropriateSubscriptionStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-client] (ecmascript)");
;
function extractWebhookTimestamp(payload) {
    try {
        // Priority order for timestamp extraction (most reliable first)
        const timestampSources = [
            // 1. Main event timestamp (most reliable)
            ()=>payload.created_at,
            // 2. Subscription entity timestamps
            ()=>payload.payload?.subscription?.entity?.created_at,
            ()=>payload.payload?.subscription?.entity?.updated_at,
            // 3. Payment entity timestamps
            ()=>payload.payload?.payment?.entity?.created_at,
            ()=>payload.payload?.payment?.entity?.updated_at,
            // 4. Invoice entity timestamps
            ()=>payload.payload?.invoice?.entity?.created_at,
            ()=>payload.payload?.invoice?.entity?.updated_at,
            // 5. Generic entity timestamps
            ()=>payload.payload?.entity?.created_at,
            ()=>payload.payload?.entity?.updated_at,
            // 6. Event-specific timestamps
            ()=>payload.event_timestamp,
            ()=>payload.timestamp
        ];
        // Try each timestamp source in order
        for (const getTimestamp of timestampSources){
            try {
                const timestamp = getTimestamp();
                if (timestamp && typeof timestamp === 'number' && timestamp > 0) {
                    // Validate timestamp is reasonable (not too far in past/future)
                    const now = Math.floor(Date.now() / 1000);
                    const maxAge = 24 * 60 * 60; // 24 hours
                    const maxFuture = 5 * 60; // 5 minutes
                    if (timestamp >= now - maxAge && timestamp <= now + maxFuture) {
                        return timestamp;
                    } else {
                        console.warn(`[WEBHOOK_TIMESTAMP] Timestamp ${timestamp} outside reasonable range, trying next source`);
                    }
                }
            } catch (_sourceError) {
                continue;
            }
        }
        // Fallback to current time with warning
        console.warn('[WEBHOOK_TIMESTAMP] Could not extract valid timestamp from payload, using current time');
        console.warn('[WEBHOOK_TIMESTAMP] Payload structure:', JSON.stringify(payload, null, 2));
        return Math.floor(Date.now() / 1000);
    } catch (error) {
        console.error('[WEBHOOK_TIMESTAMP] Error extracting timestamp from payload:', error);
        return Math.floor(Date.now() / 1000);
    }
}
function getAppropriateSubscriptionStatus(hasActiveSubscription, trialEndDate, planId) {
    // If on free plan, always active status but has_active_subscription should be false
    if (planId === 'free') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].ACTIVE;
    }
    // Check if user is in trial period
    if (trialEndDate) {
        const trialEnd = new Date(trialEndDate);
        const now = new Date();
        if (trialEnd > now) {
            // User is in trial period - status is trial, has_active_subscription should be false
            return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL;
        }
    }
    // If has active subscription flag, it means they have a paid subscription
    if (hasActiveSubscription) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].ACTIVE;
    }
    // Default to pending if no active subscription and not in trial
    return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].PENDING;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/utils.ts [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$validator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-validator.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$db$2d$updater$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-db-updater.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$webhook$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/webhook-utils.ts [app-client] (ecmascript)");
;
;
;
;
;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/utils.ts [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$validator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-validator.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$db$2d$updater$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-db-updater.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$webhook$2d$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/webhook-utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/utils.ts [app-client] (ecmascript) <locals>");
}}),
"[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AnimatedSubscriptionStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/badge.tsx [app-client] (ecmascript)");
// Alert components not used in this file
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-client] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$crown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Crown$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/crown.js [app-client] (ecmascript) <export default as Crown>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/shield-check.js [app-client] (ecmascript) <export default as ShieldCheck>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$FlipTimer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/FlipTimer.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/utils.ts [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
function AnimatedSubscriptionStatus({ subscriptionStatus, planDetails, trialEndDate, planCycle, subscription }) {
    // Get plan icon based on plan ID - used in badge rendering
    const getPlanIcon = ()=>{
        if (!planDetails) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
            className: "w-3.5 h-3.5 mr-1"
        }, void 0, false, {
            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
            lineNumber: 35,
            columnNumber: 30
        }, this);
        switch(planDetails.id){
            case "premium":
            case "enterprise":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$crown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Crown$3e$__["Crown"], {
                    className: "w-3.5 h-3.5 mr-1"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                    lineNumber: 40,
                    columnNumber: 16
                }, this);
            case "pro":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
                    className: "w-3.5 h-3.5 mr-1"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                    lineNumber: 42,
                    columnNumber: 16
                }, this);
            case "growth":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
                    className: "w-3.5 h-3.5 mr-1"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                    lineNumber: 44,
                    columnNumber: 16
                }, this);
            case "basic":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
                    className: "w-3.5 h-3.5 mr-1"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                    lineNumber: 46,
                    columnNumber: 16
                }, this);
            case "free":
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
                    className: "w-3.5 h-3.5 mr-1"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                    lineNumber: 48,
                    columnNumber: 16
                }, this);
            default:
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
                    className: "w-3.5 h-3.5 mr-1"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                    lineNumber: 50,
                    columnNumber: 16
                }, this);
        }
    };
    // Get badge styles based on plan ID
    const getPlanBadgeStyles = ()=>{
        if (!planDetails) return "";
        switch(planDetails.id){
            case "premium":
            case "enterprise":
                return "border-[var(--brand-gold)]/50 text-[var(--brand-gold)]";
            case "pro":
                return "border-blue-500/50 text-blue-500 dark:border-blue-400/50 dark:text-blue-400";
            case "growth":
                return "border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400";
            case "basic":
                return "border-purple-500/50 text-purple-500 dark:border-purple-400/50 dark:text-purple-400";
            case "free":
                return "border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400";
            default:
                return "border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400";
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "h-full",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-2 xs:p-3 sm:p-4 md:p-5 transition-all duration-300 hover:shadow-lg overflow-hidden h-full flex flex-col relative",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative z-10 flex flex-col h-full",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-1.5 sm:p-2 rounded-lg bg-primary/10 text-primary",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
                                            className: "w-3.5 h-3.5 sm:w-4 sm:h-4"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 85,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 84,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate",
                                        children: "Subscription Status"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 87,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                lineNumber: 83,
                                columnNumber: 13
                            }, this),
                            subscriptionStatus === "active" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-3 h-3 rounded-full bg-green-500 animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                lineNumber: 92,
                                columnNumber: 15
                            }, this),
                            subscriptionStatus === "trial" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-3 h-3 rounded-full bg-amber-500 animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                lineNumber: 95,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                        lineNumber: 82,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 flex flex-col justify-center",
                        children: [
                            subscriptionStatus === "active" && planDetails && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col items-center justify-center text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "outline",
                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("border px-2 py-1 mb-2", getPlanBadgeStyles()),
                                            children: [
                                                getPlanIcon(),
                                                subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED ? "Paused" : "Active"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 103,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 102,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-base sm:text-lg font-medium mb-1",
                                        children: planDetails.name
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 112,
                                        columnNumber: 17
                                    }, this),
                                    planDetails.id !== "free" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs sm:text-sm font-medium text-primary mb-1",
                                        children: planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 117,
                                        columnNumber: 19
                                    }, this),
                                    subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3",
                                        children: "Your subscription is currently paused. Your business card is offline until you resume your subscription."
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 123,
                                        columnNumber: 19
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3",
                                        children: "Your subscription is active and all features are enabled"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 127,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-2 w-full px-2 mb-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            asChild: true,
                                            size: "sm",
                                            variant: "outline",
                                            className: "w-full sm:w-auto",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: "/dashboard/business/plan",
                                                className: "flex items-center justify-center gap-1",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "whitespace-nowrap",
                                                        children: "Manage Plan"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                        lineNumber: 135,
                                                        columnNumber: 23
                                                    }, this),
                                                    " ",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                        className: "w-3.5 h-3.5 flex-shrink-0"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                        lineNumber: 135,
                                                        columnNumber: 78
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                lineNumber: 134,
                                                columnNumber: 21
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 133,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 132,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                lineNumber: 101,
                                columnNumber: 15
                            }, this),
                            subscriptionStatus === "trial" && trialEndDate && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col items-center justify-center text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "outline",
                                            className: "border border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-1 mb-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$crown$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Crown$3e$__["Crown"], {
                                                    className: "w-3.5 h-3.5 mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 149,
                                                    columnNumber: 21
                                                }, this),
                                                "Trial"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 145,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 144,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-base sm:text-lg font-medium mb-1",
                                        children: [
                                            planDetails ? planDetails.name : "Premium",
                                            " Trial"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 154,
                                        columnNumber: 17
                                    }, this),
                                    planDetails && planDetails.id !== "free" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs sm:text-sm font-medium text-[var(--brand-gold)] mb-2",
                                        children: planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 159,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-full overflow-x-auto px-1",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$FlipTimer$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            endDate: trialEndDate,
                                            label: "",
                                            tooltipText: "Your trial will expire soon. Choose a plan to continue using all features."
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 165,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 164,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 w-full px-2 mb-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative group",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 171,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300",
                                                    style: {
                                                        boxShadow: `inset 0 0 20px rgba(194, 157, 91, 0.2), 0 0 20px rgba(194, 157, 91, 0.3)`
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 174,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute -top-1 -right-1 w-6 h-6 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 182,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute -bottom-1 -left-1 w-4 h-4 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 183,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                    asChild: true,
                                                    variant: "outline",
                                                    size: "sm",
                                                    className: "gap-1 w-full sm:w-auto border-amber-200/50 dark:border-amber-700/50 shadow-amber-500/40 shadow-lg hover:shadow-xl hover:shadow-amber-500/40 transition-all duration-300 text-amber-500 dark:text-amber-400 hover:bg-amber-500/5 dark:hover:bg-amber-500/10 rounded-xl",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/business/plan",
                                                        className: "flex items-center justify-center relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "whitespace-nowrap",
                                                                children: "Choose a Plan"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                                lineNumber: 192,
                                                                columnNumber: 25
                                                            }, this),
                                                            " ",
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                                className: "w-3.5 h-3.5 ml-1 flex-shrink-0"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                                lineNumber: 192,
                                                                columnNumber: 82
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                                lineNumber: 195,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                        lineNumber: 191,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 185,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 169,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 168,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                lineNumber: 143,
                                columnNumber: 15
                            }, this),
                            subscriptionStatus === "inactive" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col items-center justify-center text-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "outline",
                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("border px-2 py-1 mb-2", subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED && planDetails ? getPlanBadgeStyles() : "border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400"),
                                            children: [
                                                subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED && planDetails ? getPlanIcon() : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$shield$2d$check$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ShieldCheck$3e$__["ShieldCheck"], {
                                                    className: "w-3.5 h-3.5 mr-1"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 218,
                                                    columnNumber: 25
                                                }, this),
                                                subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED ? "Paused" : "Free Plan"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 208,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 207,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-base sm:text-lg font-medium mb-1",
                                        children: subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED && planDetails ? planDetails.name : "Free Plan"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 224,
                                        columnNumber: 17
                                    }, this),
                                    subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED && planDetails && planDetails.id !== "free" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs sm:text-sm font-medium text-primary mb-1",
                                        children: planCycle === "yearly" ? "Yearly Plan" : "Monthly Plan"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 233,
                                        columnNumber: 19
                                    }, this),
                                    subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3",
                                        children: "Your subscription is currently paused. Your business card is offline until you resume your subscription."
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 239,
                                        columnNumber: 19
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3",
                                        children: "You are on the free plan with limited features. Upgrade to access more features."
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 243,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-2 w-full px-2 mb-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative group",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 251,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300",
                                                    style: {
                                                        boxShadow: `inset 0 0 20px rgba(194, 157, 91, 0.2), 0 0 20px rgba(194, 157, 91, 0.3)`
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 254,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute -top-1 -right-1 w-6 h-6 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 262,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute -bottom-1 -left-1 w-4 h-4 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 263,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                    asChild: true,
                                                    variant: "outline",
                                                    size: "sm",
                                                    className: "w-full sm:w-auto border-amber-200/50 dark:border-amber-700/50 shadow-amber-500/40 shadow-lg hover:shadow-xl hover:shadow-amber-500/40 transition-all duration-300 text-amber-500 dark:text-amber-400 hover:bg-amber-500/5 dark:hover:bg-amber-500/10 rounded-xl",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: "/dashboard/business/plan",
                                                        className: "flex items-center justify-center gap-1 relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "whitespace-nowrap",
                                                                children: subscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].HALTED ? "Manage Plan" : "Upgrade Plan"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                                lineNumber: 272,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                                                className: "w-3.5 h-3.5 flex-shrink-0"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                                lineNumber: 275,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                                lineNumber: 278,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                        lineNumber: 271,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                                    lineNumber: 265,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                            lineNumber: 249,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                        lineNumber: 248,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                                lineNumber: 206,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                        lineNumber: 99,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
                lineNumber: 81,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
            lineNumber: 77,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
_c = AnimatedSubscriptionStatus;
var _c;
__turbopack_context__.k.register(_c, "AnimatedSubscriptionStatus");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>EnhancedQuickActions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chart-column.js [app-client] (ecmascript) <export default as BarChart3>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-client] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/package.js [app-client] (ecmascript) <export default as Package>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/heart.js [app-client] (ecmascript) <export default as Heart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-client] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
"use client";
;
;
;
;
function EnhancedQuickActions({ userPlan: _userPlan }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-5 transition-all duration-300 hover:shadow-lg h-full relative overflow-hidden",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative z-10",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "p-1.5 sm:p-2 rounded-lg bg-primary/10 text-primary",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                                        className: "w-3.5 h-3.5 sm:w-4 sm:h-4"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                        lineNumber: 21,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 20,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate",
                                    children: "Quick Actions"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 23,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                            lineNumber: 19,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-2 h-2 rounded-full bg-green-500 animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                            lineNumber: 27,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                    lineNumber: 18,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col gap-2 sm:gap-3 overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative group",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 33,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    asChild: true,
                                    variant: "outline",
                                    className: "w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/dashboard/business/card",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                                                className: "w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                                lineNumber: 41,
                                                columnNumber: 17
                                            }, this),
                                            "Edit Business Card"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                        lineNumber: 40,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 35,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                            lineNumber: 31,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative group",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 49,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    asChild: true,
                                    variant: "outline",
                                    className: "w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/dashboard/business/products",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$package$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Package$3e$__["Package"], {
                                                className: "w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                                lineNumber: 57,
                                                columnNumber: 17
                                            }, this),
                                            "Manage Products"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                        lineNumber: 56,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 51,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                            lineNumber: 47,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative group",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 67,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    asChild: true,
                                    variant: "outline",
                                    className: "w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/dashboard/business/analytics",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chart$2d$column$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__BarChart3$3e$__["BarChart3"], {
                                                className: "w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                                lineNumber: 75,
                                                columnNumber: 17
                                            }, this),
                                            "View Analytics"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                        lineNumber: 74,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 69,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                            lineNumber: 65,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative group",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    asChild: true,
                                    variant: "outline",
                                    className: "w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/dashboard/business/likes",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__["Heart"], {
                                                className: "w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                                lineNumber: 91,
                                                columnNumber: 17
                                            }, this),
                                            "My Likes"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                        lineNumber: 90,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 85,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                            lineNumber: 81,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative group",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 99,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                    asChild: true,
                                    variant: "outline",
                                    className: "w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/dashboard/business/subscriptions",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                                                className: "w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300"
                                            }, void 0, false, {
                                                fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                                lineNumber: 107,
                                                columnNumber: 17
                                            }, this),
                                            "My Subscriptions"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                        lineNumber: 106,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                                    lineNumber: 101,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                            lineNumber: 97,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
                    lineNumber: 30,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
            lineNumber: 17,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx",
        lineNumber: 13,
        columnNumber: 5
    }, this);
}
_c = EnhancedQuickActions;
var _c;
__turbopack_context__.k.register(_c, "EnhancedQuickActions");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/lib/actions/data:5488ce [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"404306b831edd693397c4d200cb7cfaf72ef475b59":"getBusinessActivities"},"lib/actions/activities.ts",""] */ __turbopack_context__.s({
    "getBusinessActivities": (()=>getBusinessActivities)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var getBusinessActivities = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("404306b831edd693397c4d200cb7cfaf72ef475b59", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getBusinessActivities"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RecentActivities)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
/**
 * Realtime Subscription Notes
 *
 * This component uses Supabase Realtime to listen for new activities.
 *
 * Important: You need to enable realtime for the business_activities table in Supabase:
 * 1. Go to Supabase Dashboard > Database > Replication
 * 2. Find the "business_activities" table in the list
 * 3. Enable realtime by toggling it on
 *
 * The component subscribes to INSERT events on the business_activities table
 * with a filter for the specific business_profile_id:
 *
 * ```javascript
 * supabase
 *   .channel('dashboard-activities-changes')
 *   .on(
 *     'postgres_changes',
 *     {
 *       event: 'INSERT',
 *       schema: 'public',
 *       table: 'business_activities',
 *       filter: `business_profile_id=eq.${businessProfileId}`,
 *     },
 *     async () => {
 *       // Handle new activity
 *     }
 *   )
 *   .subscribe();
 * ```
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bell.js [app-client] (ecmascript) <export default as Bell>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/heart.js [app-client] (ecmascript) <export default as Heart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-client] (ecmascript) <export default as Star>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-client] (ecmascript) <export default as Users>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-client] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/avatar.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/skeleton.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$formatDistanceToNow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/date-fns/formatDistanceToNow.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$5488ce__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/lib/actions/data:5488ce [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$services$2f$realtimeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/services/realtimeService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
function RecentActivities({ businessProfileId, unreadCount }) {
    _s();
    const [activities, setActivities] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    // Fetch recent activities
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RecentActivities.useEffect": ()=>{
            const fetchActivities = {
                "RecentActivities.useEffect.fetchActivities": async ()=>{
                    setLoading(true);
                    try {
                        const { activities } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$5488ce__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getBusinessActivities"])({
                            businessProfileId,
                            page: 1,
                            pageSize: 5,
                            sortBy: "newest",
                            filterBy: "all"
                        });
                        setActivities(activities || []);
                    } catch (error) {
                        console.error("Error fetching recent activities:", error);
                    } finally{
                        setLoading(false);
                    }
                }
            }["RecentActivities.useEffect.fetchActivities"];
            fetchActivities();
        }
    }["RecentActivities.useEffect"], [
        businessProfileId
    ]);
    // Set up real-time subscription for new activities
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "RecentActivities.useEffect": ()=>{
            if (!businessProfileId) return;
            const subscription = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$services$2f$realtimeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["realtimeService"].subscribeToBusinessActivities(businessProfileId, {
                "RecentActivities.useEffect.subscription": async ()=>{
                    // Fetch the updated activities
                    const { activities: newActivities } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$5488ce__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getBusinessActivities"])({
                        businessProfileId,
                        page: 1,
                        pageSize: 5,
                        sortBy: "newest",
                        filterBy: "all"
                    });
                    setActivities(newActivities || []);
                }
            }["RecentActivities.useEffect.subscription"], 'recent-activities');
            return ({
                "RecentActivities.useEffect": ()=>{
                    subscription.unsubscribe();
                }
            })["RecentActivities.useEffect"];
        }
    }["RecentActivities.useEffect"], [
        businessProfileId
    ]);
    // Container animation variants
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: containerVariants,
        initial: "hidden",
        animate: "visible",
        className: "rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 transition-all duration-300 h-full",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between mb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                                    className: "w-4 h-4 text-blue-600 dark:text-blue-300"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                    lineNumber: 131,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 130,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-semibold",
                                children: "Recent Activities"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 133,
                                columnNumber: 11
                            }, this),
                            unreadCount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: "secondary",
                                className: "ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                                children: [
                                    unreadCount > 99 ? "99+" : unreadCount,
                                    " new"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 135,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 129,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative group",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 bg-blue-500/5 dark:bg-blue-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 143,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300",
                                style: {
                                    boxShadow: `inset 0 0 20px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.3)`
                                }
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 146,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -top-1 -right-1 w-6 h-6 bg-blue-500/30 rounded-full shadow-blue-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -bottom-1 -left-1 w-4 h-4 bg-blue-500/30 rounded-full shadow-blue-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 155,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                asChild: true,
                                variant: "outline",
                                size: "sm",
                                className: "border-blue-200/50 dark:border-blue-700/50 shadow-blue-500/40 shadow-lg hover:shadow-xl hover:shadow-blue-500/40 transition-all duration-300 text-blue-500 dark:text-blue-400 hover:bg-blue-500/5 dark:hover:bg-blue-500/10 rounded-xl relative overflow-hidden",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    href: "/dashboard/business/activities",
                                    className: "flex items-center gap-1 relative",
                                    children: [
                                        "View All ",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                            className: "w-3 h-3 ml-1"
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                            lineNumber: 164,
                                            columnNumber: 24
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                    lineNumber: 163,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 157,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 169,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                lineNumber: 128,
                columnNumber: 7
            }, this),
            loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                        className: "h-16 w-full rounded-lg"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 175,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                        className: "h-16 w-full rounded-lg"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 176,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$skeleton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Skeleton"], {
                        className: "h-16 w-full rounded-lg"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 177,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                lineNumber: 174,
                columnNumber: 9
            }, this) : activities.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col items-center justify-center py-8 text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bell$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Bell$3e$__["Bell"], {
                        className: "w-10 h-10 text-neutral-300 dark:text-neutral-600 mb-3"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 181,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-base font-medium text-neutral-700 dark:text-neutral-300",
                        children: "No activities yet"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 182,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-neutral-500 dark:text-neutral-400 mt-1",
                        children: "When someone interacts with your business, it will appear here."
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 183,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                lineNumber: 180,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-3",
                children: activities.map((activity)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(RecentActivityItem, {
                        activity: activity
                    }, activity.id, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 190,
                        columnNumber: 13
                    }, this))
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                lineNumber: 188,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
        lineNumber: 122,
        columnNumber: 5
    }, this);
}
_s(RecentActivities, "jDgU0oks4QHbXVPyUorNxk8b4oo=");
_c = RecentActivities;
function RecentActivityItem({ activity }) {
    // Determine if the user is a business or customer
    const isBusiness = activity.user_profile?.is_business || false;
    // Get the appropriate name and avatar
    const displayName = isBusiness ? activity.user_profile?.business_name : activity.user_profile?.name;
    const avatarUrl = isBusiness ? activity.user_profile?.logo_url : activity.user_profile?.avatar_url;
    // Get initials for avatar fallback
    const getInitials = (name)=>{
        if (!name) return "?";
        return name.split(" ").map((n)=>n[0]).join("").toUpperCase().substring(0, 2);
    };
    const initials = getInitials(displayName);
    // Format the time
    const timeAgo = activity.created_at ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$formatDistanceToNow$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatDistanceToNow"])(new Date(activity.created_at), {
        addSuffix: true
    }) : "recently";
    // Get activity icon and text
    const getActivityDetails = ()=>{
        switch(activity.activity_type){
            case "like":
                return {
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__["Heart"], {
                        className: "w-4 h-4 text-rose-500"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 234,
                        columnNumber: 17
                    }, this),
                    text: "liked your business",
                    color: "bg-rose-50 text-rose-700 dark:bg-rose-900/20 dark:text-rose-300"
                };
            case "subscribe":
                return {
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Users$3e$__["Users"], {
                        className: "w-4 h-4 text-blue-500"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 240,
                        columnNumber: 17
                    }, this),
                    text: "subscribed to your business",
                    color: "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300"
                };
            case "rating":
                return {
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                        className: "w-4 h-4 text-amber-500"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 246,
                        columnNumber: 17
                    }, this),
                    text: `rated your business ${activity.rating_value}/5`,
                    color: "bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300"
                };
            default:
                return {
                    icon: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Star$3e$__["Star"], {
                        className: "w-4 h-4"
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 252,
                        columnNumber: 17
                    }, this),
                    text: "interacted with your business",
                    color: "bg-neutral-50 text-neutral-700 dark:bg-neutral-900/20 dark:text-neutral-300"
                };
        }
    };
    const { icon, text, color } = getActivityDetails();
    // Get profile link
    const profileLink = isBusiness && activity.user_profile?.business_slug ? `/${activity.user_profile.business_slug}` : "#";
    // Item animation variants
    const itemVariants = {
        hidden: {
            opacity: 0,
            y: 10
        },
        visible: {
            opacity: 1,
            y: 0
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        variants: itemVariants,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative rounded-lg border p-3 transition-all duration-300", activity.is_read ? "border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black" : "border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/10"),
        children: [
            !activity.is_read && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute top-3 right-3 w-2 h-2 rounded-full bg-blue-500"
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                lineNumber: 284,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center gap-3",
                children: [
                    isBusiness ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: profileLink,
                        className: "shrink-0",
                        target: "_blank",
                        rel: "noopener noreferrer",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                            className: "h-8 w-8 rounded-lg border border-amber-200 dark:border-amber-800",
                            children: [
                                avatarUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                    src: avatarUrl,
                                    alt: displayName || "User"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                    lineNumber: 293,
                                    columnNumber: 17
                                }, this) : null,
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                    className: "rounded-lg text-xs bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300",
                                    children: initials
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                    lineNumber: 295,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                            lineNumber: 291,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 290,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "shrink-0",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Avatar"], {
                            className: "h-8 w-8 rounded-lg border border-neutral-200 dark:border-neutral-700",
                            children: [
                                avatarUrl ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarImage"], {
                                    src: avatarUrl,
                                    alt: displayName || "User"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                    lineNumber: 304,
                                    columnNumber: 17
                                }, this) : null,
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$avatar$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AvatarFallback"], {
                                    className: "rounded-lg text-xs bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300",
                                    children: initials
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                    lineNumber: 306,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                            lineNumber: 302,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 301,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 min-w-0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap items-center gap-1",
                                children: [
                                    isBusiness ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: profileLink,
                                        className: "font-medium hover:underline truncate text-sm",
                                        target: "_blank",
                                        rel: "noopener noreferrer",
                                        children: displayName || "Anonymous User"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                        lineNumber: 318,
                                        columnNumber: 15
                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium truncate text-sm",
                                        children: displayName || "Anonymous User"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                        lineNumber: 327,
                                        columnNumber: 15
                                    }, this),
                                    isBusiness && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                        variant: "outline",
                                        className: "bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-800 flex items-center gap-1 py-0 h-4 text-[10px]",
                                        children: "Business"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                        lineNumber: 334,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-neutral-500 dark:text-neutral-400 text-xs",
                                        children: text
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                        lineNumber: 339,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 315,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-between mt-1",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                            variant: "secondary",
                                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center gap-1 h-5 text-xs", color),
                                            children: [
                                                icon,
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-[10px] capitalize",
                                                    children: activity.activity_type
                                                }, void 0, false, {
                                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                                    lineNumber: 348,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                            lineNumber: 346,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-[10px] text-neutral-500 dark:text-neutral-400",
                                            children: timeAgo
                                        }, void 0, false, {
                                            fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                            lineNumber: 351,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                    lineNumber: 345,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                                lineNumber: 344,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                        lineNumber: 314,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
                lineNumber: 287,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx",
        lineNumber: 273,
        columnNumber: 5
    }, this);
}
_c1 = RecentActivityItem;
var _c, _c1;
__turbopack_context__.k.register(_c, "RecentActivities");
__turbopack_context__.k.register(_c1, "RecentActivityItem");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/alert.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Alert": (()=>Alert),
    "AlertDescription": (()=>AlertDescription),
    "AlertTitle": (()=>AlertTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const alertVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current", {
    variants: {
        variant: {
            default: "bg-card text-card-foreground",
            destructive: "text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"
        }
    },
    defaultVariants: {
        variant: "default"
    }
});
function Alert({ className, variant, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert",
        role: "alert",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(alertVariants({
            variant
        }), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
}
_c = Alert;
function AlertTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
_c1 = AlertTitle;
function AlertDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "alert-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/components/ui/alert.tsx",
        lineNumber: 55,
        columnNumber: 5
    }, this);
}
_c2 = AlertDescription;
;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "Alert");
__turbopack_context__.k.register(_c1, "AlertTitle");
__turbopack_context__.k.register(_c2, "AlertDescription");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BusinessStatusAlert)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/alert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-client] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-right.js [app-client] (ecmascript) <export default as ArrowRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
"use client";
;
;
;
;
;
;
function BusinessStatusAlert() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: {
            opacity: 0,
            y: -10
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.3
        },
        className: "mb-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Alert"], {
            variant: "destructive",
            className: "bg-red-50 border-red-200 dark:bg-red-950/50 dark:border-red-800/50",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                    className: "h-5 w-5 text-red-600 dark:text-red-400"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                    lineNumber: 28,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertTitle"], {
                    className: "text-red-800 dark:text-red-300",
                    children: "Your Business Card is Offline"
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                    lineNumber: 29,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$alert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AlertDescription"], {
                    className: "text-red-700 dark:text-red-400",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mb-3",
                            children: "Your business card is currently set to offline status and won't appear in search results or discovery pages."
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                            lineNumber: 33,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            asChild: true,
                            size: "sm",
                            variant: "outline",
                            className: "border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/30",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/dashboard/business/card",
                                className: "flex items-center gap-1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Go Online Now"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                                        lineNumber: 43,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$right$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowRight$3e$__["ArrowRight"], {
                                        className: "h-3.5 w-3.5"
                                    }, void 0, false, {
                                        fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                                        lineNumber: 44,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                                lineNumber: 42,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                            lineNumber: 36,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
                    lineNumber: 32,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
            lineNumber: 24,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx",
        lineNumber: 18,
        columnNumber: 5
    }, this);
}
_c = BusinessStatusAlert;
var _c;
__turbopack_context__.k.register(_c, "BusinessStatusAlert");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BusinessDashboardClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$layout$2d$dashboard$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LayoutDashboard$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/layout-dashboard.js [app-client] (ecmascript) <export default as LayoutDashboard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/credit-card.js [app-client] (ecmascript) <export default as CreditCard>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$DashboardOverviewClient$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/DashboardOverviewClient.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$AnimatedSubscriptionStatus$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/AnimatedSubscriptionStatus.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$EnhancedQuickActions$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/EnhancedQuickActions.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$RecentActivities$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/RecentActivities.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$BusinessStatusAlert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/components/BusinessStatusAlert.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$d8853e__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/lib/actions/data:d8853e [app-client] (ecmascript) <text/javascript>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
function BusinessDashboardClient({ initialProfile, userId, subscriptionStatus, planDetails, subscription }) {
    _s();
    const [unreadActivitiesCount, setUnreadActivitiesCount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    /**
   * Fetch Unread Activities Count
   *
   * This effect fetches the initial unread activities count and sets up an interval
   * to refresh it every minute. This ensures the dashboard shows accurate counts
   * even if the realtime subscription misses any events.
   *
   * Note: For realtime updates of the activities count, you need to enable realtime
   * for the business_activities table in Supabase:
   * 1. Go to Supabase Dashboard > Database > Replication
   * 2. Find the "business_activities" table in the list
   * 3. Enable realtime by toggling it on
   */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BusinessDashboardClient.useEffect": ()=>{
            const fetchUnreadCount = {
                "BusinessDashboardClient.useEffect.fetchUnreadCount": async ()=>{
                    const { count } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$data$3a$d8853e__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["getUnreadActivitiesCount"])(userId);
                    setUnreadActivitiesCount(count);
                }
            }["BusinessDashboardClient.useEffect.fetchUnreadCount"];
            fetchUnreadCount();
            // Set up interval to refresh count every minute
            const intervalId = setInterval(fetchUnreadCount, 60000);
            return ({
                "BusinessDashboardClient.useEffect": ()=>clearInterval(intervalId)
            })["BusinessDashboardClient.useEffect"];
        }
    }["BusinessDashboardClient.useEffect"], [
        userId
    ]);
    // Animation variants for staggered animations
    const containerVariants = {
        hidden: {
            opacity: 0
        },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1
            }
        }
    };
    const itemVariants = {
        hidden: {
            opacity: 0,
            y: 20
        },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.4
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
        initial: "hidden",
        animate: "visible",
        variants: containerVariants,
        className: "space-y-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-3 rounded-xl bg-muted hidden sm:block",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$layout$2d$dashboard$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__LayoutDashboard$3e$__["LayoutDashboard"], {
                            className: "w-6 h-6 text-foreground"
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                            lineNumber: 106,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-foreground",
                                children: [
                                    "Welcome, ",
                                    initialProfile.business_name || 'Business Owner'
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                                lineNumber: 109,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground mt-1",
                                children: "Manage your business profile and track performance"
                            }, void 0, false, {
                                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                                lineNumber: 112,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                        lineNumber: 108,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        asChild: true,
                        variant: "outline",
                        size: "sm",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            href: "/dashboard/business/card",
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$credit$2d$card$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__CreditCard$3e$__["CreditCard"], {
                                    className: "mr-2 h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                                    lineNumber: 122,
                                    columnNumber: 13
                                }, this),
                                "Manage Card"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                            lineNumber: 121,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                        lineNumber: 116,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                lineNumber: 104,
                columnNumber: 7
            }, this),
            initialProfile.status === "offline" && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: itemVariants,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$BusinessStatusAlert$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                    lineNumber: 131,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                lineNumber: 130,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: itemVariants,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$DashboardOverviewClient$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    initialProfile: initialProfile,
                    userId: userId,
                    userPlan: initialProfile.plan_id
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                    lineNumber: 137,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                lineNumber: 136,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: itemVariants,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$AnimatedSubscriptionStatus$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            subscriptionStatus: subscriptionStatus,
                            planDetails: planDetails,
                            trialEndDate: initialProfile.trial_end_date,
                            planCycle: initialProfile.plan_cycle,
                            subscription: subscription
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                            lineNumber: 148,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$EnhancedQuickActions$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            userPlan: initialProfile.plan_id
                        }, void 0, false, {
                            fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                            lineNumber: 157,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                    lineNumber: 146,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                lineNumber: 145,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].div, {
                variants: itemVariants,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$components$2f$RecentActivities$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    businessProfileId: userId,
                    unreadCount: unreadActivitiesCount
                }, void 0, false, {
                    fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                    lineNumber: 163,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
                lineNumber: 162,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/(dashboard)/dashboard/business/components/BusinessDashboardClient.tsx",
        lineNumber: 97,
        columnNumber: 5
    }, this);
}
_s(BusinessDashboardClient, "IXPp2GTR62/7K82badqyJTeQCLE=");
_c = BusinessDashboardClient;
var _c;
__turbopack_context__.k.register(_c, "BusinessDashboardClient");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=_5c9e6d36._.js.map