{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\n// Reusable base schema for password complexity\r\nexport const PasswordComplexitySchema = z\r\n  .string()\r\n  .min(6, { message: \"Password must be at least 6 characters\" })\r\n  .regex(/[A-Z]/, { message: \"Password must contain at least one capital letter\" })\r\n  .regex(/[a-z]/, { message: \"Password must contain at least one lowercase letter.\" }) // Added lowercase check for consistency\r\n  .regex(/[0-9]/, { message: \"Password must contain at least one number\" })\r\n  .regex(/[^A-Za-z0-9]/, { message: \"Password must contain at least one symbol\" });\r\n\r\n// Reusable schema for mobile number validation\r\nexport const IndianMobileSchema = z\r\n  .string()\r\n  .min(10, { message: \"Mobile number must be at least 10 digits\" })\r\n  .max(10, { message: \"Mobile number must be exactly 10 digits\" })\r\n  .regex(/^\\d{10}$/, {\r\n    message: \"Please enter a valid 10-digit mobile number\"\r\n  });\r\n\r\n// Schema for forms requiring password confirmation (e.g., registration, change password)\r\nexport const PasswordConfirmationSchema = z\r\n  .object({\r\n    password: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Passwords don't match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for forms requiring new password confirmation (used in settings/reset)\r\n// Renaming fields for clarity in those contexts\r\nexport const NewPasswordConfirmationSchema = z\r\n  .object({\r\n    newPassword: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.newPassword === data.confirmPassword, {\r\n    message: \"Passwords do not match.\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for just the password field (e.g., for login or single field validation)\r\nexport const SinglePasswordSchema = z.object({\r\n    password: PasswordComplexitySchema,\r\n});\r\n\r\n// Schema for just the new password field (used in reset password action)\r\nexport const SingleNewPasswordSchema = z.object({\r\n    password: PasswordComplexitySchema, // Action receives it as 'password'\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG;IAAE,SAAS;AAAyC,GAC3D,KAAK,CAAC,SAAS;IAAE,SAAS;AAAoD,GAC9E,KAAK,CAAC,SAAS;IAAE,SAAS;AAAuD,GAAG,wCAAwC;CAC5H,KAAK,CAAC,SAAS;IAAE,SAAS;AAA4C,GACtE,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAGzE,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,GAAG,CAAC,IAAI;IAAE,SAAS;AAA2C,GAC9D,GAAG,CAAC,IAAI;IAAE,SAAS;AAA0C,GAC7D,KAAK,CAAC,YAAY;IACjB,SAAS;AACX;AAGK,MAAM,6BAA6B,oIAAA,CAAA,IAAC,CACxC,MAAM,CAAC;IACN,UAAU;IACV,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIK,MAAM,gCAAgC,oIAAA,CAAA,IAAC,CAC3C,MAAM,CAAC;IACN,aAAa;IACb,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC3D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGK,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU;AACd;AAGO,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU;AACd", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/client-image-compression.ts"], "sourcesContent": ["/**\r\n * Client-side image compression using Canvas API\r\n * This avoids memory issues in serverless environments like Google Cloud Run\r\n */\r\n\r\nexport interface CompressionOptions {\r\n  targetSizeKB?: number;\r\n  maxDimension?: number;\r\n  quality?: number;\r\n  format?: \"webp\" | \"jpeg\" | \"png\";\r\n}\r\n\r\nexport interface CompressionResult {\r\n  blob: Blob;\r\n  finalSizeKB: number;\r\n  compressionRatio: number;\r\n  dimensions: { width: number; height: number };\r\n}\r\n\r\n/**\r\n * Compress image on client-side using Canvas API\r\n */\r\nexport async function compressImageClientSide(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const {\r\n    format = \"webp\",\r\n    targetSizeKB = 100,\r\n    maxDimension = 800,\r\n    quality: initialQuality = 0.8\r\n  } = options;\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      try {\r\n        const canvas = document.createElement('canvas');\r\n        const ctx = canvas.getContext('2d');\r\n        \r\n        if (!ctx) {\r\n          reject(new Error('Could not get canvas context'));\r\n          return;\r\n        }\r\n\r\n        // Calculate new dimensions\r\n        let { width, height } = img;\r\n        \r\n        if (width > maxDimension || height > maxDimension) {\r\n          if (width > height) {\r\n            height = (height * maxDimension) / width;\r\n            width = maxDimension;\r\n          } else {\r\n            width = (width * maxDimension) / height;\r\n            height = maxDimension;\r\n          }\r\n        }\r\n\r\n        canvas.width = width;\r\n        canvas.height = height;\r\n\r\n        // Draw and compress\r\n        ctx.drawImage(img, 0, 0, width, height);\r\n\r\n        // Try different quality levels until we hit target size\r\n        let quality = initialQuality;\r\n        let attempts = 0;\r\n        const maxAttempts = 5;\r\n\r\n        const tryCompress = () => {\r\n          canvas.toBlob((blob) => {\r\n            if (!blob) {\r\n              reject(new Error('Failed to create blob'));\r\n              return;\r\n            }\r\n\r\n            const sizeKB = blob.size / 1024;\r\n            \r\n            if (sizeKB <= targetSizeKB || attempts >= maxAttempts || quality <= 0.1) {\r\n              // Success or max attempts reached\r\n              const compressionRatio = file.size / blob.size;\r\n              resolve({\r\n                blob,\r\n                finalSizeKB: Math.round(sizeKB * 100) / 100,\r\n                compressionRatio: Math.round(compressionRatio * 100) / 100,\r\n                dimensions: { width, height }\r\n              });\r\n            } else {\r\n              // Try again with lower quality\r\n              attempts++;\r\n              quality = Math.max(0.1, quality - 0.15);\r\n              tryCompress();\r\n            }\r\n          }, `image/${format}`, quality);\r\n        };\r\n\r\n        tryCompress();\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    };\r\n\r\n    img.onerror = () => reject(new Error('Failed to load image'));\r\n    img.src = URL.createObjectURL(file);\r\n  });\r\n}\r\n\r\n/**\r\n * Ultra-aggressive client-side compression\r\n */\r\nexport async function compressImageUltraAggressiveClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  const originalSizeMB = file.size / (1024 * 1024);\r\n  \r\n  // Auto-determine settings based on file size\r\n  let targetSizeKB = 100;\r\n  let maxDimension = 800;\r\n  let quality = 0.7;\r\n\r\n  if (originalSizeMB <= 2) {\r\n    quality = 0.7;\r\n    maxDimension = 800;\r\n    targetSizeKB = 90;\r\n  } else if (originalSizeMB <= 5) {\r\n    quality = 0.55;\r\n    maxDimension = 700;\r\n    targetSizeKB = 80;\r\n  } else if (originalSizeMB <= 10) {\r\n    quality = 0.45;\r\n    maxDimension = 600;\r\n    targetSizeKB = 70;\r\n  } else {\r\n    quality = 0.35;\r\n    maxDimension = 550;\r\n    targetSizeKB = 60;\r\n  }\r\n\r\n  return compressImageClientSide(file, {\r\n    ...options,\r\n    targetSizeKB: options.targetSizeKB || targetSizeKB,\r\n    maxDimension: options.maxDimension || maxDimension,\r\n    quality: options.quality || quality\r\n  });\r\n}\r\n\r\n/**\r\n * Moderate client-side compression\r\n */\r\nexport async function compressImageModerateClient(\r\n  file: File,\r\n  options: CompressionOptions = {}\r\n): Promise<CompressionResult> {\r\n  return compressImageClientSide(file, {\r\n    targetSizeKB: 200,\r\n    maxDimension: 800,\r\n    quality: 0.75,\r\n    ...options\r\n  });\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAmBM,eAAe,wBACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,EACJ,SAAS,MAAM,EACf,eAAe,GAAG,EAClB,eAAe,GAAG,EAClB,SAAS,iBAAiB,GAAG,EAC9B,GAAG;IAEJ,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,IAAI;gBACF,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,MAAM,MAAM,OAAO,UAAU,CAAC;gBAE9B,IAAI,CAAC,KAAK;oBACR,OAAO,IAAI,MAAM;oBACjB;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBAExB,IAAI,QAAQ,gBAAgB,SAAS,cAAc;oBACjD,IAAI,QAAQ,QAAQ;wBAClB,SAAS,AAAC,SAAS,eAAgB;wBACnC,QAAQ;oBACV,OAAO;wBACL,QAAQ,AAAC,QAAQ,eAAgB;wBACjC,SAAS;oBACX;gBACF;gBAEA,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,oBAAoB;gBACpB,IAAI,SAAS,CAAC,KAAK,GAAG,GAAG,OAAO;gBAEhC,wDAAwD;gBACxD,IAAI,UAAU;gBACd,IAAI,WAAW;gBACf,MAAM,cAAc;gBAEpB,MAAM,cAAc;oBAClB,OAAO,MAAM,CAAC,CAAC;wBACb,IAAI,CAAC,MAAM;4BACT,OAAO,IAAI,MAAM;4BACjB;wBACF;wBAEA,MAAM,SAAS,KAAK,IAAI,GAAG;wBAE3B,IAAI,UAAU,gBAAgB,YAAY,eAAe,WAAW,KAAK;4BACvE,kCAAkC;4BAClC,MAAM,mBAAmB,KAAK,IAAI,GAAG,KAAK,IAAI;4BAC9C,QAAQ;gCACN;gCACA,aAAa,KAAK,KAAK,CAAC,SAAS,OAAO;gCACxC,kBAAkB,KAAK,KAAK,CAAC,mBAAmB,OAAO;gCACvD,YAAY;oCAAE;oCAAO;gCAAO;4BAC9B;wBACF,OAAO;4BACL,+BAA+B;4BAC/B;4BACA,UAAU,KAAK,GAAG,CAAC,KAAK,UAAU;4BAClC;wBACF;oBACF,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE;gBACxB;gBAEA;YACF,EAAE,OAAO,OAAO;gBACd,OAAO;YACT;QACF;QAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;QACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAChC;AACF;AAKO,eAAe,mCACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,MAAM,iBAAiB,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI;IAE/C,6CAA6C;IAC7C,IAAI,eAAe;IACnB,IAAI,eAAe;IACnB,IAAI,UAAU;IAEd,IAAI,kBAAkB,GAAG;QACvB,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,GAAG;QAC9B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO,IAAI,kBAAkB,IAAI;QAC/B,UAAU;QACV,eAAe;QACf,eAAe;IACjB,OAAO;QACL,UAAU;QACV,eAAe;QACf,eAAe;IACjB;IAEA,OAAO,wBAAwB,MAAM;QACnC,GAAG,OAAO;QACV,cAAc,QAAQ,YAAY,IAAI;QACtC,cAAc,QAAQ,YAAY,IAAI;QACtC,SAAS,QAAQ,OAAO,IAAI;IAC9B;AACF;AAKO,eAAe,4BACpB,IAAU,EACV,UAA8B,CAAC,CAAC;IAEhC,OAAO,wBAAwB,MAAM;QACnC,cAAc;QACd,cAAc;QACd,SAAS;QACT,GAAG,OAAO;IACZ;AACF", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/location.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\n\r\n// --- Pincode Lookup Action ---\r\nexport async function getPincodeDetails(pincode: string): Promise<{\r\n  data?: {\r\n    city: string;\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  city?: string;\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!pincode || !/^\\d{6}$/.test(pincode)) {\r\n    return { error: \"Invalid Pincode format.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // First get city and state from pincodes table\r\n    const { data: pincodeData, error: pincodeError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"OfficeName, DivisionName, StateName\")\r\n      .eq(\"Pincode\", pincode) // Updated column name to match database\r\n      .order(\"OfficeName\");\r\n\r\n    if (pincodeError) {\r\n      console.error(\"Pincode Fetch Error:\", pincodeError);\r\n      return { error: \"Database error fetching pincode details.\" };\r\n    }\r\n\r\n    if (!pincodeData || pincodeData.length === 0) {\r\n      return { error: \"Pincode not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = pincodeData[0].StateName;\r\n\r\n    // Use DivisionName as the city (already cleaned)\r\n    const city = pincodeData[0].DivisionName;\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(pincodeData.map((item) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { city, state, localities },\r\n      city,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Pincode Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during pincode lookup.\" };\r\n  }\r\n}\r\n// --- End Pincode Lookup ---\r\n\r\n// --- City Lookup Action ---\r\nexport async function getCityDetails(city: string): Promise<{\r\n  data?: {\r\n    pincodes: string[];\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  pincodes?: string[];\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!city || city.length < 2) {\r\n    return { error: \"City name must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // Get pincodes and state for the city - DivisionName is the city column\r\n    const { data: cityData, error: cityError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"Pincode, OfficeName, StateName, DivisionName\")\r\n      .ilike(\"DivisionName\", `%${city}%`)\r\n      .order(\"Pincode\");\r\n\r\n    if (cityError) {\r\n      console.error(\"City Fetch Error:\", cityError);\r\n      return { error: \"Database error fetching city details.\" };\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { error: \"City not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = cityData[0].StateName;\r\n\r\n    // Get unique pincodes\r\n    const pincodes = [...new Set(cityData.map((item) => item.Pincode))] as string[];\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(cityData.map((item) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { pincodes, state, localities },\r\n      pincodes,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during city lookup.\" };\r\n  }\r\n}\r\n// --- End City Lookup ---\r\n\r\n// --- City Autocomplete Action ---\r\n/**\r\n * Get city suggestions based on a search query\r\n *\r\n * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.\r\n * The PostgreSQL function is defined as:\r\n *\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)\r\n * RETURNS TABLE(city TEXT) AS $$\r\n * BEGIN\r\n *   RETURN QUERY\r\n *   SELECT DISTINCT \"DivisionName\" as city\r\n *   FROM pincodes\r\n *   WHERE \"DivisionName\" ILIKE search_query\r\n *   ORDER BY \"DivisionName\"\r\n *   LIMIT result_limit;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n * ```\r\n *\r\n * @param query The search query (minimum 2 characters)\r\n * @returns Array of up to 5 unique city suggestions\r\n */\r\nexport async function getCitySuggestions(query: string): Promise<{\r\n  data?: {\r\n    cities: string[];\r\n  };\r\n  cities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!query || query.length < 2) {\r\n    return { error: \"Query must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabaseAdmin = createAdminClient();\r\n  try {\r\n    // Use the PostgreSQL function to get distinct cities (up to 5)\r\n    const { data: cityData, error: cityError } = await supabaseAdmin\r\n      .rpc('get_distinct_cities', {\r\n        search_query: `%${query}%`,\r\n        result_limit: 5\r\n      });\r\n\r\n    if (cityError) {\r\n      console.error(\"City Suggestions Error:\", cityError);\r\n\r\n      // Fallback to regular query if RPC fails\r\n      try {\r\n        // Use a regular query as fallback\r\n        const { data: fallbackData, error: fallbackError } = await supabaseAdmin\r\n          .from(\"pincodes\")\r\n          .select(\"DivisionName\")\r\n          .ilike(\"DivisionName\", `%${query}%`)\r\n          .order(\"DivisionName\")\r\n          .limit(100);\r\n\r\n        if (fallbackError) {\r\n          throw fallbackError;\r\n        }\r\n\r\n        if (!fallbackData || fallbackData.length === 0) {\r\n          return { data: { cities: [] }, cities: [] };\r\n        }\r\n\r\n        // Get unique cities and format them\r\n        const cities = [...new Set(fallbackData.map((item) =>\r\n          item.DivisionName.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n        ))] as string[];\r\n\r\n        const topCities = cities.slice(0, 5);\r\n\r\n        return {\r\n          data: { cities: topCities },\r\n          cities: topCities\r\n        };\r\n      } catch (fallbackErr) {\r\n        console.error(\"Fallback City Query Error:\", fallbackErr);\r\n        return { error: \"Database error fetching city suggestions.\" };\r\n      }\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { data: { cities: [] }, cities: [] };\r\n    }\r\n\r\n    // Format the city names to Title Case\r\n    const cities = cityData.map((item: { city: string }) =>\r\n      item.city.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n    );\r\n\r\n    return {\r\n      data: { cities },\r\n      cities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Suggestions Exception:\", e);\r\n    return { error: \"An unexpected error occurred while fetching city suggestions.\" };\r\n  }\r\n}\r\n// --- End City Autocomplete ---\r\n"], "names": [], "mappings": ";;;;;;IAMsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/config/categories.ts"], "sourcesContent": ["import { LucideIcon } from \"lucide-react\";\r\nimport {\r\n  Utensils,\r\n  Briefcase,\r\n  GraduationCap,\r\n  Car,\r\n  Store,\r\n  Stethoscope,\r\n  Scissors,\r\n  Wrench,\r\n  ShoppingBag,\r\n  Laptop,\r\n  Home,\r\n  Dumbbell,\r\n  Plane,\r\n  Coffee,\r\n  Music,\r\n  Camera,\r\n  Pen,\r\n  PenTool,\r\n  Code,\r\n  Shirt,\r\n  Truck,\r\n  Building,\r\n  Landmark,\r\n  Hammer,\r\n  Leaf,\r\n  Palette,\r\n  BookOpen,\r\n  Users,\r\n  HeartPulse,\r\n  Sparkles,\r\n  Gem,\r\n  Smartphone,\r\n  Megaphone,\r\n  Banknote,\r\n  Gavel,\r\n  Scroll,\r\n  Handshake,\r\n  Warehouse,\r\n  Factory,\r\n  Tractor,\r\n  Cake,\r\n  Bed,\r\n  Luggage,\r\n  Bus,\r\n  Flower,\r\n  ShoppingCart,\r\n  Brush,\r\n  Mic,\r\n  Film,\r\n  Gamepad2,\r\n  Printer,\r\n  Cog,\r\n  Boxes,\r\n  Wallet,\r\n  BadgeIndianRupee,\r\n  Building2,\r\n  Presentation,\r\n  Zap,\r\n  Droplets,\r\n  Recycle,\r\n  Microscope,\r\n  Pill,\r\n  Baby,\r\n  Glasses,\r\n  Footprints,\r\n  Bike,\r\n  Sofa,\r\n  Stamp,\r\n  ShieldCheck,\r\n  Bug,\r\n} from \"lucide-react\";\r\n\r\nexport interface BusinessCategory {\r\n  name: string;\r\n  icon: LucideIcon;\r\n  slug?: string;\r\n  description?: string;\r\n  isPopular?: boolean;\r\n}\r\n\r\n/**\r\n * Shared business categories used across the application\r\n * This centralized list ensures consistency across different components\r\n * Some categories are marked as popular for display on homepage and discovery page\r\n */\r\nexport const BUSINESS_CATEGORIES: BusinessCategory[] = [\r\n  // Food & Dining\r\n  { name: \"Restaurants\", icon: Utensils, slug: \"restaurants\", description: \"Restaurants and dining establishments\", isPopular: true },\r\n  { name: \"Cafes & Bakeries\", icon: Coffee, slug: \"cafes-bakeries\", description: \"Coffee shops, bakeries, and dessert places\", isPopular: true },\r\n  { name: \"Food Delivery\", icon: Truck, slug: \"food-delivery\", description: \"Food delivery services\" },\r\n  { name: \"Catering\", icon: Utensils, slug: \"catering\", description: \"Catering services for events\" },\r\n  { name: \"Sweet Shops\", icon: Cake, slug: \"sweet-shops\", description: \"Traditional sweet shops and confectioneries\" },\r\n  { name: \"Street Food\", icon: Utensils, slug: \"street-food\", description: \"Street food vendors and stalls\" },\r\n  { name: \"Cloud Kitchen\", icon: Utensils, slug: \"cloud-kitchen\", description: \"Delivery-only food businesses\" },\r\n  { name: \"Tiffin Services\", icon: Utensils, slug: \"tiffin-services\", description: \"Home-cooked meal delivery services\" },\r\n\r\n  // Retail & Shopping\r\n  { name: \"Retail Stores\", icon: Store, slug: \"retail\", description: \"General retail and shops\", isPopular: true },\r\n  { name: \"Grocery & Supermarkets\", icon: ShoppingCart, slug: \"grocery\", description: \"Grocery stores and supermarkets\", isPopular: true },\r\n  { name: \"Fashion & Clothing\", icon: Shirt, slug: \"fashion\", description: \"Clothing and fashion retailers\", isPopular: true },\r\n  { name: \"Electronics\", icon: Smartphone, slug: \"electronics\", description: \"Electronics and gadget stores\" },\r\n  { name: \"Home Decor\", icon: Sofa, slug: \"home-decor\", description: \"Home decor and furnishing stores\" },\r\n  { name: \"Jewelry\", icon: Gem, slug: \"jewelry\", description: \"Jewelry and accessory stores\" },\r\n  { name: \"Bookstores\", icon: BookOpen, slug: \"bookstores\", description: \"Book shops and stationers\" },\r\n  { name: \"Footwear\", icon: Footprints, slug: \"footwear\", description: \"Shoe and footwear stores\" },\r\n  { name: \"Gift Shops\", icon: ShoppingBag, slug: \"gift-shops\", description: \"Gift and souvenir shops\" },\r\n  { name: \"Eyewear\", icon: Glasses, slug: \"eyewear\", description: \"Optical shops and eyewear retailers\" },\r\n  { name: \"Mobile Shops\", icon: Smartphone, slug: \"mobile-shops\", description: \"Mobile phone retailers and repair shops\" },\r\n\r\n  // Professional Services\r\n  { name: \"Legal Services\", icon: Gavel, slug: \"legal\", description: \"Lawyers and legal consultants\", isPopular: true },\r\n  { name: \"Financial Services\", icon: Banknote, slug: \"financial\", description: \"Financial advisors and services\", isPopular: true },\r\n  { name: \"Accounting\", icon: BadgeIndianRupee, slug: \"accounting\", description: \"Accounting and tax services\" },\r\n  { name: \"Consulting\", icon: Briefcase, slug: \"consulting\", description: \"Business and management consulting\" },\r\n  { name: \"Insurance\", icon: Wallet, slug: \"insurance\", description: \"Insurance services and agents\" },\r\n  { name: \"HR Services\", icon: Users, slug: \"hr-services\", description: \"Human resources and recruitment services\" },\r\n  { name: \"Tax Consultants\", icon: BadgeIndianRupee, slug: \"tax-consultants\", description: \"Tax filing and consultation services\" },\r\n  { name: \"Notary Services\", icon: Stamp, slug: \"notary\", description: \"Notary and document verification services\" },\r\n  { name: \"Translation Services\", icon: Scroll, slug: \"translation\", description: \"Language translation and interpretation services\" },\r\n\r\n  // Healthcare\r\n  { name: \"Medical Clinics\", icon: Stethoscope, slug: \"medical\", description: \"Medical clinics and doctors\", isPopular: true },\r\n  { name: \"Dental Care\", icon: Sparkles, slug: \"dental\", description: \"Dental clinics and services\" },\r\n  { name: \"Pharmacy\", icon: Pill, slug: \"pharmacy\", description: \"Pharmacies and medical supplies\" },\r\n  { name: \"Mental Health\", icon: HeartPulse, slug: \"mental-health\", description: \"Mental health services and counseling\" },\r\n  { name: \"Alternative Medicine\", icon: Leaf, slug: \"alternative-medicine\", description: \"Ayurveda, homeopathy, and alternative treatments\" },\r\n  { name: \"Diagnostic Centers\", icon: Microscope, slug: \"diagnostic\", description: \"Medical testing and diagnostic centers\" },\r\n  { name: \"Physiotherapy\", icon: HeartPulse, slug: \"physiotherapy\", description: \"Physiotherapy and rehabilitation services\" },\r\n  { name: \"Veterinary\", icon: HeartPulse, slug: \"veterinary\", description: \"Veterinary clinics and pet healthcare\" },\r\n  { name: \"Elder Care\", icon: HeartPulse, slug: \"elder-care\", description: \"Elder care and assisted living services\" },\r\n  { name: \"Maternity Care\", icon: Baby, slug: \"maternity\", description: \"Maternity and childcare services\" },\r\n\r\n  // Beauty & Wellness\r\n  { name: \"Salon & Spa\", icon: Scissors, slug: \"salon-spa\", description: \"Beauty salons and spa services\", isPopular: true },\r\n  { name: \"Fitness\", icon: Dumbbell, slug: \"fitness\", description: \"Gyms and fitness centers\", isPopular: true },\r\n  { name: \"Yoga & Meditation\", icon: Users, slug: \"yoga\", description: \"Yoga studios and meditation centers\" },\r\n  { name: \"Cosmetics\", icon: Sparkles, slug: \"cosmetics\", description: \"Cosmetics and beauty products\" },\r\n  { name: \"Barber Shops\", icon: Scissors, slug: \"barber\", description: \"Men's grooming and barber shops\" },\r\n  { name: \"Wellness Centers\", icon: Sparkles, slug: \"wellness\", description: \"Wellness and holistic health centers\" },\r\n  { name: \"Massage Therapy\", icon: Sparkles, slug: \"massage\", description: \"Massage and bodywork services\" },\r\n  { name: \"Skin Care\", icon: Sparkles, slug: \"skin-care\", description: \"Skin care clinics and dermatology\" },\r\n\r\n  // Education & Training\r\n  { name: \"Schools\", icon: GraduationCap, slug: \"schools\", description: \"Schools and educational institutions\", isPopular: true },\r\n  { name: \"Coaching Centers\", icon: BookOpen, slug: \"coaching\", description: \"Coaching and tutoring centers\" },\r\n  { name: \"Vocational Training\", icon: Presentation, slug: \"vocational\", description: \"Vocational and skill training\" },\r\n  { name: \"Online Education\", icon: Laptop, slug: \"online-education\", description: \"Online courses and e-learning\" },\r\n  { name: \"Language Schools\", icon: BookOpen, slug: \"language\", description: \"Language learning and training centers\" },\r\n  { name: \"Music Classes\", icon: Music, slug: \"music-classes\", description: \"Music schools and training\" },\r\n  { name: \"Dance Classes\", icon: Music, slug: \"dance-classes\", description: \"Dance schools and training\" },\r\n  { name: \"Art Schools\", icon: Palette, slug: \"art-schools\", description: \"Art and craft education\" },\r\n  { name: \"Driving Schools\", icon: Car, slug: \"driving-education\", description: \"Driving training and education\" },\r\n  { name: \"Playschools\", icon: Baby, slug: \"playschools\", description: \"Preschools and early education\" },\r\n  { name: \"Tuition Centers\", icon: BookOpen, slug: \"tuition\", description: \"Private tutoring and academic support\" },\r\n\r\n  // Technology\r\n  { name: \"IT Services\", icon: Code, slug: \"it-services\", description: \"IT services and support\", isPopular: true },\r\n  { name: \"Software Development\", icon: Laptop, slug: \"software\", description: \"Software development companies\" },\r\n  { name: \"Web Development\", icon: Code, slug: \"web-development\", description: \"Web design and development services\" },\r\n  { name: \"Digital Marketing\", icon: Megaphone, slug: \"digital-marketing\", description: \"Digital marketing agencies and services\" },\r\n  { name: \"App Development\", icon: Smartphone, slug: \"app-development\", description: \"Mobile app development services\" },\r\n  { name: \"IT Hardware\", icon: Laptop, slug: \"it-hardware\", description: \"Computer hardware sales and services\" },\r\n  { name: \"Cyber Security\", icon: ShieldCheck, slug: \"cyber-security\", description: \"Cybersecurity services and solutions\" },\r\n  { name: \"Cloud Services\", icon: Code, slug: \"cloud-services\", description: \"Cloud computing and hosting services\" },\r\n  { name: \"Data Analytics\", icon: Code, slug: \"data-analytics\", description: \"Data analysis and business intelligence\" },\r\n\r\n  // Automotive\r\n  { name: \"Auto Repair\", icon: Wrench, slug: \"auto-repair\", description: \"Car repair and service centers\", isPopular: true },\r\n  { name: \"Car Dealerships\", icon: Car, slug: \"car-dealerships\", description: \"New and used car dealerships\" },\r\n  { name: \"Auto Parts\", icon: Cog, slug: \"auto-parts\", description: \"Automotive parts and accessories\" },\r\n  { name: \"Two-Wheeler Services\", icon: Bike, slug: \"two-wheeler\", description: \"Motorcycle and scooter services\" },\r\n  { name: \"Car Wash\", icon: Car, slug: \"car-wash\", description: \"Car washing and detailing services\" },\r\n  { name: \"Tyre Shops\", icon: Car, slug: \"tyre-shops\", description: \"Tyre sales and services\" },\r\n  { name: \"Auto Electricians\", icon: Zap, slug: \"auto-electricians\", description: \"Automotive electrical repair services\" },\r\n  { name: \"Vehicle Rental\", icon: Car, slug: \"vehicle-rental\", description: \"Car and bike rental services\" },\r\n\r\n  // Real Estate & Construction\r\n  { name: \"Real Estate\", icon: Home, slug: \"real-estate\", description: \"Property and real estate services\", isPopular: true },\r\n  { name: \"Construction\", icon: Hammer, slug: \"construction\", description: \"Construction services and contractors\" },\r\n  { name: \"Interior Design\", icon: Palette, slug: \"interior-design\", description: \"Interior design and decoration services\" },\r\n  { name: \"Architecture\", icon: Building, slug: \"architecture\", description: \"Architectural services and firms\" },\r\n  { name: \"Property Management\", icon: Building, slug: \"property-management\", description: \"Property management services\" },\r\n  { name: \"Building Materials\", icon: Boxes, slug: \"building-materials\", description: \"Construction materials suppliers\" },\r\n  { name: \"Plumbing Services\", icon: Wrench, slug: \"plumbing\", description: \"Plumbing installation and repair\" },\r\n  { name: \"Electrical Services\", icon: Zap, slug: \"electrical\", description: \"Electrical installation and repair\" },\r\n  { name: \"Painting Services\", icon: Brush, slug: \"painting\", description: \"House painting and finishing services\" },\r\n  { name: \"Carpentry\", icon: Hammer, slug: \"carpentry\", description: \"Carpentry and woodworking services\" },\r\n  { name: \"Landscaping\", icon: Flower, slug: \"landscaping\", description: \"Garden and landscape design services\" },\r\n\r\n  // Travel & Hospitality\r\n  { name: \"Hotels\", icon: Bed, slug: \"hotels\", description: \"Hotels and accommodations\", isPopular: true },\r\n  { name: \"Travel Agencies\", icon: Plane, slug: \"travel-agencies\", description: \"Travel agencies and tour operators\" },\r\n  { name: \"Transportation\", icon: Bus, slug: \"transportation\", description: \"Transportation services\" },\r\n  { name: \"Tourism\", icon: Luggage, slug: \"tourism\", description: \"Tourism services and attractions\" },\r\n  { name: \"Homestays\", icon: Home, slug: \"homestays\", description: \"Homestays and guest houses\" },\r\n  { name: \"Tour Guides\", icon: Plane, slug: \"tour-guides\", description: \"Local tour guides and services\" },\r\n  { name: \"Adventure Tourism\", icon: Plane, slug: \"adventure-tourism\", description: \"Adventure sports and tourism\" },\r\n  { name: \"Resorts\", icon: Bed, slug: \"resorts\", description: \"Resorts and vacation properties\" },\r\n  { name: \"Visa Services\", icon: Stamp, slug: \"visa-services\", description: \"Visa application and processing services\" },\r\n\r\n  // Entertainment & Events\r\n  { name: \"Entertainment\", icon: Music, slug: \"entertainment\", description: \"Entertainment venues and services\", isPopular: true },\r\n  { name: \"Event Management\", icon: Sparkles, slug: \"event-management\", description: \"Event planning and management services\" },\r\n  { name: \"Wedding Services\", icon: Handshake, slug: \"wedding\", description: \"Wedding planning and related services\" },\r\n  { name: \"Photography\", icon: Camera, slug: \"photography\", description: \"Photography and videography services\" },\r\n  { name: \"Cinema Halls\", icon: Film, slug: \"cinema\", description: \"Movie theaters and cinemas\" },\r\n  { name: \"Gaming Zones\", icon: Gamepad2, slug: \"gaming\", description: \"Gaming arcades and entertainment centers\" },\r\n  { name: \"Party Venues\", icon: Music, slug: \"party-venues\", description: \"Party and event venues\" },\r\n  { name: \"DJs & Musicians\", icon: Music, slug: \"djs-musicians\", description: \"DJs and live music performers\" },\r\n  { name: \"Amusement Parks\", icon: Sparkles, slug: \"amusement-parks\", description: \"Amusement and theme parks\" },\r\n\r\n  // Freelancers & Creative Professionals\r\n  { name: \"Freelance Services\", icon: Briefcase, slug: \"freelance\", description: \"Independent professionals and freelancers\", isPopular: true },\r\n  { name: \"Graphic Design\", icon: PenTool, slug: \"graphic-design\", description: \"Graphic design services\" },\r\n  { name: \"Content Creation\", icon: Pen, slug: \"content-creation\", description: \"Content writing and creation services\" },\r\n  { name: \"Art & Crafts\", icon: Brush, slug: \"art-crafts\", description: \"Artists and craftspeople\" },\r\n  { name: \"Music & Performance\", icon: Mic, slug: \"music-performance\", description: \"Musicians and performers\" },\r\n  { name: \"Videography\", icon: Film, slug: \"videography\", description: \"Video production and editing services\" },\r\n  { name: \"Voice Over Artists\", icon: Mic, slug: \"voice-over\", description: \"Voice over and narration services\" },\r\n  { name: \"Translators\", icon: Scroll, slug: \"translators\", description: \"Language translation services\" },\r\n  { name: \"Tutors\", icon: BookOpen, slug: \"tutors\", description: \"Private tutors and educators\" },\r\n  { name: \"Consultants\", icon: Briefcase, slug: \"consultants\", description: \"Independent consultants and advisors\" },\r\n  { name: \"Astrologers\", icon: Sparkles, slug: \"astrologers\", description: \"Astrology and horoscope services\" },\r\n\r\n  // Manufacturing & Industry\r\n  { name: \"Manufacturing\", icon: Factory, slug: \"manufacturing\", description: \"Manufacturing businesses\" },\r\n  { name: \"Wholesale\", icon: Warehouse, slug: \"wholesale\", description: \"Wholesale suppliers and distributors\" },\r\n  { name: \"Textiles\", icon: Shirt, slug: \"textiles\", description: \"Textile manufacturing and supplies\" },\r\n  { name: \"Printing\", icon: Printer, slug: \"printing\", description: \"Printing services and press\" },\r\n  { name: \"Packaging\", icon: Boxes, slug: \"packaging\", description: \"Packaging materials and services\" },\r\n  { name: \"Metal Works\", icon: Hammer, slug: \"metal-works\", description: \"Metal fabrication and works\" },\r\n  { name: \"Plastic Products\", icon: Factory, slug: \"plastic-products\", description: \"Plastic manufacturing and products\" },\r\n  { name: \"Handicrafts\", icon: Brush, slug: \"handicrafts\", description: \"Handmade crafts and products\" },\r\n  { name: \"Furniture Making\", icon: Sofa, slug: \"furniture\", description: \"Furniture manufacturing and carpentry\" },\r\n\r\n  // Agriculture\r\n  { name: \"Agriculture\", icon: Tractor, slug: \"agriculture\", description: \"Farming and agricultural services\" },\r\n  { name: \"Dairy\", icon: Droplets, slug: \"dairy\", description: \"Dairy farms and products\" },\r\n  { name: \"Organic Products\", icon: Leaf, slug: \"organic\", description: \"Organic farming and products\" },\r\n  { name: \"Poultry\", icon: Leaf, slug: \"poultry\", description: \"Poultry farming and products\" },\r\n  { name: \"Fisheries\", icon: Droplets, slug: \"fisheries\", description: \"Fish farming and aquaculture\" },\r\n  { name: \"Nurseries\", icon: Leaf, slug: \"nurseries\", description: \"Plant nurseries and gardening supplies\" },\r\n  { name: \"Farm Equipment\", icon: Tractor, slug: \"farm-equipment\", description: \"Agricultural equipment and supplies\" },\r\n  { name: \"Seed Suppliers\", icon: Leaf, slug: \"seed-suppliers\", description: \"Seeds and agricultural inputs\" },\r\n  { name: \"Floriculture\", icon: Flower, slug: \"floriculture\", description: \"Flower growing and selling\" },\r\n\r\n  // Utilities & Services\r\n  { name: \"Utilities\", icon: Zap, slug: \"utilities\", description: \"Utility services\" },\r\n  { name: \"Cleaning Services\", icon: Sparkles, slug: \"cleaning\", description: \"Cleaning and maintenance services\" },\r\n  { name: \"Waste Management\", icon: Recycle, slug: \"waste-management\", description: \"Waste collection and recycling services\" },\r\n  { name: \"Courier & Logistics\", icon: Truck, slug: \"logistics\", description: \"Courier, delivery, and logistics services\" },\r\n  { name: \"Home Services\", icon: Home, slug: \"home-services\", description: \"Home repair and maintenance services\" },\r\n  { name: \"Pest Control\", icon: Bug, slug: \"pest-control\", description: \"Pest control and extermination services\" },\r\n  { name: \"Security Services\", icon: ShieldCheck, slug: \"security\", description: \"Security guards and services\" },\r\n  { name: \"Laundry Services\", icon: Sparkles, slug: \"laundry\", description: \"Laundry and dry cleaning services\" },\r\n  { name: \"Water Supply\", icon: Droplets, slug: \"water-supply\", description: \"Water delivery and supply services\" },\r\n  { name: \"Rental Services\", icon: Boxes, slug: \"rental\", description: \"Equipment and item rental services\" },\r\n\r\n  // Other Categories\r\n  { name: \"Religious Services\", icon: Landmark, slug: \"religious\", description: \"Religious institutions and services\" },\r\n  { name: \"NGOs & Charities\", icon: Handshake, slug: \"ngo\", description: \"Non-profit organizations and charities\" },\r\n  { name: \"Government Services\", icon: Building2, slug: \"government\", description: \"Government offices and services\" },\r\n  { name: \"Repair Services\", icon: Wrench, slug: \"repair\", description: \"General repair and maintenance services\" },\r\n  { name: \"Tailoring\", icon: Scissors, slug: \"tailoring\", description: \"Tailoring and alteration services\" },\r\n  { name: \"Printing & Copying\", icon: Printer, slug: \"printing-copying\", description: \"Printing, copying, and document services\" },\r\n  { name: \"Astrology\", icon: Sparkles, slug: \"astrology\", description: \"Astrology and spiritual services\" },\r\n  { name: \"Funeral Services\", icon: Landmark, slug: \"funeral\", description: \"Funeral homes and memorial services\" },\r\n  { name: \"Daycare\", icon: Baby, slug: \"daycare\", description: \"Childcare and daycare services\" },\r\n  { name: \"Pet Services\", icon: HeartPulse, slug: \"pet-services\", description: \"Pet grooming, boarding, and care\" },\r\n  { name: \"Other Services\", icon: Briefcase, slug: \"other\", description: \"Other business services not listed elsewhere\" },\r\n];\r\n\r\n/**\r\n * Get a subset of categories\r\n * @param count Number of categories to return\r\n * @returns Array of categories limited to the specified count\r\n */\r\nexport function getCategories(count?: number): BusinessCategory[] {\r\n  if (count && count > 0 && count < BUSINESS_CATEGORIES.length) {\r\n    return BUSINESS_CATEGORIES.slice(0, count);\r\n  }\r\n  return BUSINESS_CATEGORIES;\r\n}\r\n\r\n/**\r\n * Get popular categories\r\n * @param count Maximum number of popular categories to return\r\n * @returns Array of popular categories\r\n */\r\nexport function getPopularCategories(count?: number): BusinessCategory[] {\r\n  const popularCategories = BUSINESS_CATEGORIES.filter(category => category.isPopular);\r\n  if (count && count > 0 && count < popularCategories.length) {\r\n    return popularCategories.slice(0, count);\r\n  }\r\n  return popularCategories;\r\n}\r\n\r\n/**\r\n * Get a category by slug\r\n * @param slug The category slug to find\r\n * @returns The category object or undefined if not found\r\n */\r\nexport function getCategoryBySlug(slug: string): BusinessCategory | undefined {\r\n  return BUSINESS_CATEGORIES.find(\r\n    (category) => category.slug === slug\r\n  );\r\n}\r\n\r\n/**\r\n * Get a category by name\r\n * @param name The category name to find\r\n * @returns The category object or undefined if not found\r\n */\r\nexport function getCategoryByName(name: string): BusinessCategory | undefined {\r\n  return BUSINESS_CATEGORIES.find(\r\n    (category) => category.name.toLowerCase() === name.toLowerCase()\r\n  );\r\n}\r\n\r\n/**\r\n * Category groups with their respective categories\r\n * This is manually defined to match the comments in the BUSINESS_CATEGORIES array\r\n */\r\nexport const CATEGORY_GROUPS = [\r\n  {\r\n    name: \"Food & Dining\",\r\n    categories: BUSINESS_CATEGORIES.slice(0, 8)\r\n  },\r\n  {\r\n    name: \"Retail & Shopping\",\r\n    categories: BUSINESS_CATEGORIES.slice(8, 19)\r\n  },\r\n  {\r\n    name: \"Professional Services\",\r\n    categories: BUSINESS_CATEGORIES.slice(19, 28)\r\n  },\r\n  {\r\n    name: \"Healthcare\",\r\n    categories: BUSINESS_CATEGORIES.slice(28, 38)\r\n  },\r\n  {\r\n    name: \"Beauty & Wellness\",\r\n    categories: BUSINESS_CATEGORIES.slice(38, 46)\r\n  },\r\n  {\r\n    name: \"Education & Training\",\r\n    categories: BUSINESS_CATEGORIES.slice(46, 57)\r\n  },\r\n  {\r\n    name: \"Technology\",\r\n    categories: BUSINESS_CATEGORIES.slice(57, 66)\r\n  },\r\n  {\r\n    name: \"Automotive\",\r\n    categories: BUSINESS_CATEGORIES.slice(66, 75)\r\n  },\r\n  {\r\n    name: \"Real Estate & Construction\",\r\n    categories: BUSINESS_CATEGORIES.slice(75, 86)\r\n  },\r\n  {\r\n    name: \"Travel & Hospitality\",\r\n    categories: BUSINESS_CATEGORIES.slice(86, 95)\r\n  },\r\n  {\r\n    name: \"Entertainment & Events\",\r\n    categories: BUSINESS_CATEGORIES.slice(95, 104)\r\n  },\r\n  {\r\n    name: \"Freelancers & Creative Professionals\",\r\n    categories: BUSINESS_CATEGORIES.slice(104, 115)\r\n  },\r\n  {\r\n    name: \"Manufacturing & Industry\",\r\n    categories: BUSINESS_CATEGORIES.slice(115, 124)\r\n  },\r\n  {\r\n    name: \"Agriculture\",\r\n    categories: BUSINESS_CATEGORIES.slice(124, 133)\r\n  },\r\n  {\r\n    name: \"Utilities & Services\",\r\n    categories: BUSINESS_CATEGORIES.slice(133, 143)\r\n  },\r\n  {\r\n    name: \"Other Categories\",\r\n    categories: BUSINESS_CATEGORIES.slice(143)\r\n  }\r\n];\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAsFO,MAAM,sBAA0C;IACrD,gBAAgB;IAChB;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAe,aAAa;QAAyC,WAAW;IAAK;IAClI;QAAE,MAAM;QAAoB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAkB,aAAa;QAA8C,WAAW;IAAK;IAC7I;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAAyB;IACnG;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAA+B;IAClG;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAA8C;IACnH;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAe,aAAa;IAAiC;IAC1G;QAAE,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAiB,aAAa;IAAgC;IAC7G;QAAE,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAmB,aAAa;IAAqC;IAEtH,oBAAoB;IACpB;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAU,aAAa;QAA4B,WAAW;IAAK;IAC/G;QAAE,MAAM;QAA0B,MAAM,sNAAA,CAAA,eAAY;QAAE,MAAM;QAAW,aAAa;QAAmC,WAAW;IAAK;IACvI;QAAE,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAW,aAAa;QAAkC,WAAW;IAAK;IAC3H;QAAE,MAAM;QAAe,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAe,aAAa;IAAgC;IAC3G;QAAE,MAAM;QAAc,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAc,aAAa;IAAmC;IACtG;QAAE,MAAM;QAAW,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAW,aAAa;IAA+B;IAC3F;QAAE,MAAM;QAAc,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAc,aAAa;IAA4B;IACnG;QAAE,MAAM;QAAY,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAY,aAAa;IAA2B;IAChG;QAAE,MAAM;QAAc,MAAM,oNAAA,CAAA,cAAW;QAAE,MAAM;QAAc,aAAa;IAA0B;IACpG;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAW,aAAa;IAAsC;IACtG;QAAE,MAAM;QAAgB,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAgB,aAAa;IAA0C;IAEvH,wBAAwB;IACxB;QAAE,MAAM;QAAkB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAS,aAAa;QAAiC,WAAW;IAAK;IACpH;QAAE,MAAM;QAAsB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;QAAmC,WAAW;IAAK;IACjI;QAAE,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;QAAE,MAAM;QAAc,aAAa;IAA8B;IAC7G;QAAE,MAAM;QAAc,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAc,aAAa;IAAqC;IAC7G;QAAE,MAAM;QAAa,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAa,aAAa;IAAgC;IACnG;QAAE,MAAM;QAAe,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAe,aAAa;IAA2C;IACjH;QAAE,MAAM;QAAmB,MAAM,kOAAA,CAAA,mBAAgB;QAAE,MAAM;QAAmB,aAAa;IAAuC;IAChI;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAU,aAAa;IAA4C;IACjH;QAAE,MAAM;QAAwB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAmD;IAEnI,aAAa;IACb;QAAE,MAAM;QAAmB,MAAM,gNAAA,CAAA,cAAW;QAAE,MAAM;QAAW,aAAa;QAA+B,WAAW;IAAK;IAC3H;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAA8B;IAClG;QAAE,MAAM;QAAY,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAY,aAAa;IAAkC;IACjG;QAAE,MAAM;QAAiB,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAiB,aAAa;IAAwC;IACvH;QAAE,MAAM;QAAwB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAwB,aAAa;IAAmD;IAC1I;QAAE,MAAM;QAAsB,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAc,aAAa;IAAyC;IAC1H;QAAE,MAAM;QAAiB,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAiB,aAAa;IAA4C;IAC3H;QAAE,MAAM;QAAc,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAc,aAAa;IAAwC;IACjH;QAAE,MAAM;QAAc,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAc,aAAa;IAA0C;IACnH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAAmC;IAEzG,oBAAoB;IACpB;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;QAAkC,WAAW;IAAK;IACzH;QAAE,MAAM;QAAW,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;QAA4B,WAAW;IAAK;IAC7G;QAAE,MAAM;QAAqB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAQ,aAAa;IAAsC;IAC3G;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAgC;IACrG;QAAE,MAAM;QAAgB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAAkC;IACvG;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAuC;IAClH;QAAE,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAgC;IACzG;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAoC;IAEzG,uBAAuB;IACvB;QAAE,MAAM;QAAW,MAAM,wNAAA,CAAA,gBAAa;QAAE,MAAM;QAAW,aAAa;QAAwC,WAAW;IAAK;IAC9H;QAAE,MAAM;QAAoB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAgC;IAC3G;QAAE,MAAM;QAAuB,MAAM,kNAAA,CAAA,eAAY;QAAE,MAAM;QAAc,aAAa;IAAgC;IACpH;QAAE,MAAM;QAAoB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAoB,aAAa;IAAgC;IACjH;QAAE,MAAM;QAAoB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAyC;IACpH;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAA6B;IACvG;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAA6B;IACvG;QAAE,MAAM;QAAe,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAe,aAAa;IAA0B;IAClG;QAAE,MAAM;QAAmB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAqB,aAAa;IAAiC;IAC/G;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAAiC;IACtG;QAAE,MAAM;QAAmB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAwC;IAEjH,aAAa;IACb;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;QAA2B,WAAW;IAAK;IAChH;QAAE,MAAM;QAAwB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAY,aAAa;IAAiC;IAC9G;QAAE,MAAM;QAAmB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAmB,aAAa;IAAsC;IACnH;QAAE,MAAM;QAAqB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAqB,aAAa;IAA0C;IAChI;QAAE,MAAM;QAAmB,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAmB,aAAa;IAAkC;IACrH;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAuC;IAC9G;QAAE,MAAM;QAAkB,MAAM,oNAAA,CAAA,cAAW;QAAE,MAAM;QAAkB,aAAa;IAAuC;IACzH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAkB,aAAa;IAAuC;IAClH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAkB,aAAa;IAA0C;IAErH,aAAa;IACb;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;QAAkC,WAAW;IAAK;IACzH;QAAE,MAAM;QAAmB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAmB,aAAa;IAA+B;IAC3G;QAAE,MAAM;QAAc,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAAmC;IACrG;QAAE,MAAM;QAAwB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAAkC;IAChH;QAAE,MAAM;QAAY,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAY,aAAa;IAAqC;IACnG;QAAE,MAAM;QAAc,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAA0B;IAC5F;QAAE,MAAM;QAAqB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAqB,aAAa;IAAwC;IACxH;QAAE,MAAM;QAAkB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAkB,aAAa;IAA+B;IAEzG,6BAA6B;IAC7B;QAAE,MAAM;QAAe,MAAM,mMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;QAAqC,WAAW;IAAK;IAC1H;QAAE,MAAM;QAAgB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAgB,aAAa;IAAwC;IACjH;QAAE,MAAM;QAAmB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAmB,aAAa;IAA0C;IAC1H;QAAE,MAAM;QAAgB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAgB,aAAa;IAAmC;IAC9G;QAAE,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAuB,aAAa;IAA+B;IACxH;QAAE,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAsB,aAAa;IAAmC;IACvH;QAAE,MAAM;QAAqB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAY,aAAa;IAAmC;IAC7G;QAAE,MAAM;QAAuB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAAqC;IAChH;QAAE,MAAM;QAAqB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAY,aAAa;IAAwC;IACjH;QAAE,MAAM;QAAa,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAa,aAAa;IAAqC;IACxG;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAuC;IAE9G,uBAAuB;IACvB;QAAE,MAAM;QAAU,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAU,aAAa;QAA6B,WAAW;IAAK;IACvG;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAmB,aAAa;IAAqC;IACnH;QAAE,MAAM;QAAkB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAkB,aAAa;IAA0B;IACpG;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAW,aAAa;IAAmC;IACnG;QAAE,MAAM;QAAa,MAAM,mMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAA6B;IAC9F;QAAE,MAAM;QAAe,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAe,aAAa;IAAiC;IACvG;QAAE,MAAM;QAAqB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAqB,aAAa;IAA+B;IACjH;QAAE,MAAM;QAAW,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAW,aAAa;IAAkC;IAC9F;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAA2C;IAErH,yBAAyB;IACzB;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;QAAqC,WAAW;IAAK;IAC/H;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAoB,aAAa;IAAyC;IAC5H;QAAE,MAAM;QAAoB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAW,aAAa;IAAwC;IACnH;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAuC;IAC9G;QAAE,MAAM;QAAgB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAU,aAAa;IAA6B;IAC9F;QAAE,MAAM;QAAgB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAA2C;IAChH;QAAE,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAgB,aAAa;IAAyB;IACjG;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAAgC;IAC5G;QAAE,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAmB,aAAa;IAA4B;IAE7G,uCAAuC;IACvC;QAAE,MAAM;QAAsB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAa,aAAa;QAA6C,WAAW;IAAK;IAC5I;QAAE,MAAM;QAAkB,MAAM,4MAAA,CAAA,UAAO;QAAE,MAAM;QAAkB,aAAa;IAA0B;IACxG;QAAE,MAAM;QAAoB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAoB,aAAa;IAAwC;IACtH;QAAE,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAc,aAAa;IAA2B;IACjG;QAAE,MAAM;QAAuB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAqB,aAAa;IAA2B;IAC7G;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAAwC;IAC7G;QAAE,MAAM;QAAsB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAAoC;IAC9G;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAgC;IACvG;QAAE,MAAM;QAAU,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAA+B;IAC9F;QAAE,MAAM;QAAe,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAe,aAAa;IAAuC;IACjH;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAe,aAAa;IAAmC;IAE5G,2BAA2B;IAC3B;QAAE,MAAM;QAAiB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAiB,aAAa;IAA2B;IACvG;QAAE,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAa,aAAa;IAAuC;IAC7G;QAAE,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAY,aAAa;IAAqC;IACrG;QAAE,MAAM;QAAY,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAY,aAAa;IAA8B;IAChG;QAAE,MAAM;QAAa,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAa,aAAa;IAAmC;IACrG;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAA8B;IACrG;QAAE,MAAM;QAAoB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAoB,aAAa;IAAqC;IACvH;QAAE,MAAM;QAAe,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAe,aAAa;IAA+B;IACrG;QAAE,MAAM;QAAoB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAAwC;IAEhH,cAAc;IACd;QAAE,MAAM;QAAe,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAe,aAAa;IAAoC;IAC5G;QAAE,MAAM;QAAS,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAS,aAAa;IAA2B;IACxF;QAAE,MAAM;QAAoB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAW,aAAa;IAA+B;IACrG;QAAE,MAAM;QAAW,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAW,aAAa;IAA+B;IAC5F;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAA+B;IACpG;QAAE,MAAM;QAAa,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAAyC;IAC1G;QAAE,MAAM;QAAkB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAkB,aAAa;IAAsC;IACpH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAkB,aAAa;IAAgC;IAC3G;QAAE,MAAM;QAAgB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAgB,aAAa;IAA6B;IAEtG,uBAAuB;IACvB;QAAE,MAAM;QAAa,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAa,aAAa;IAAmB;IACnF;QAAE,MAAM;QAAqB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAoC;IAChH;QAAE,MAAM;QAAoB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAoB,aAAa;IAA0C;IAC5H;QAAE,MAAM;QAAuB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAa,aAAa;IAA4C;IACxH;QAAE,MAAM;QAAiB,MAAM,mMAAA,CAAA,OAAI;QAAE,MAAM;QAAiB,aAAa;IAAuC;IAChH;QAAE,MAAM;QAAgB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAgB,aAAa;IAA0C;IAChH;QAAE,MAAM;QAAqB,MAAM,oNAAA,CAAA,cAAW;QAAE,MAAM;QAAY,aAAa;IAA+B;IAC9G;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAoC;IAC9G;QAAE,MAAM;QAAgB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAgB,aAAa;IAAqC;IAChH;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAU,aAAa;IAAqC;IAE1G,mBAAmB;IACnB;QAAE,MAAM;QAAsB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAsC;IACpH;QAAE,MAAM;QAAoB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAO,aAAa;IAAyC;IAChH;QAAE,MAAM;QAAuB,MAAM,gNAAA,CAAA,YAAS;QAAE,MAAM;QAAc,aAAa;IAAkC;IACnH;QAAE,MAAM;QAAmB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAU,aAAa;IAA0C;IAChH;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAoC;IACzG;QAAE,MAAM;QAAsB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAoB,aAAa;IAA2C;IAC/H;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAmC;IACxG;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAsC;IAChH;QAAE,MAAM;QAAW,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAW,aAAa;IAAiC;IAC9F;QAAE,MAAM;QAAgB,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAgB,aAAa;IAAmC;IAChH;QAAE,MAAM;QAAkB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAS,aAAa;IAA+C;CACvH;AAOM,SAAS,cAAc,KAAc;IAC1C,IAAI,SAAS,QAAQ,KAAK,QAAQ,oBAAoB,MAAM,EAAE;QAC5D,OAAO,oBAAoB,KAAK,CAAC,GAAG;IACtC;IACA,OAAO;AACT;AAOO,SAAS,qBAAqB,KAAc;IACjD,MAAM,oBAAoB,oBAAoB,MAAM,CAAC,CAAA,WAAY,SAAS,SAAS;IACnF,IAAI,SAAS,QAAQ,KAAK,QAAQ,kBAAkB,MAAM,EAAE;QAC1D,OAAO,kBAAkB,KAAK,CAAC,GAAG;IACpC;IACA,OAAO;AACT;AAOO,SAAS,kBAAkB,IAAY;IAC5C,OAAO,oBAAoB,IAAI,CAC7B,CAAC,WAAa,SAAS,IAAI,KAAK;AAEpC;AAOO,SAAS,kBAAkB,IAAY;IAC5C,OAAO,oBAAoB,IAAI,CAC7B,CAAC,WAAa,SAAS,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW;AAElE;AAMO,MAAM,kBAAkB;IAC7B;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,GAAG;IAC3C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,GAAG;IAC3C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC;IACxC;CACD", "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/customBranding.ts"], "sourcesContent": ["export type UserPlan = \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n\r\nexport interface CustomBrandingSettings {\r\n  custom_header_text?: string;\r\n  custom_header_image_url?: string; // Legacy field for backward compatibility\r\n  custom_header_image_light_url?: string; // Light theme specific image\r\n  custom_header_image_dark_url?: string; // Dark theme specific image\r\n  hide_dukancard_branding?: boolean;\r\n}\r\n\r\n/**\r\n * Check if a user has access to custom branding features\r\n */\r\nexport function hasCustomBrandingAccess(userPlan?: UserPlan | null): boolean {\r\n  return userPlan === \"pro\" || userPlan === \"enterprise\";\r\n}\r\n\r\n/**\r\n * Check if Dukancard branding should be shown\r\n */\r\nexport function shouldShowDukancardBranding(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null\r\n): boolean {\r\n  // For users NOT on Pro/Enterprise plans, ALWAYS show DukanCard branding\r\n  // regardless of any custom branding settings\r\n  if (!hasCustomBrandingAccess(userPlan)) {\r\n    return true;\r\n  }\r\n\r\n  // For Pro/Enterprise users, only hide branding if they explicitly set it to hidden\r\n  return !customBranding?.hide_dukancard_branding;\r\n}\r\n\r\n/**\r\n * Get the theme-specific header image URL with fallback logic\r\n */\r\nexport function getThemeSpecificHeaderImage(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null,\r\n  currentTheme?: string | null\r\n): string | null {\r\n  // Only return custom images for Pro/Enterprise users\r\n  if (!hasCustomBrandingAccess(userPlan) || !customBranding) {\r\n    return null;\r\n  }\r\n\r\n  const isDark = currentTheme === \"dark\";\r\n\r\n  // Priority 1: Theme-specific images\r\n  if (isDark && customBranding.custom_header_image_dark_url?.trim()) {\r\n    return customBranding.custom_header_image_dark_url;\r\n  }\r\n\r\n  if (!isDark && customBranding.custom_header_image_light_url?.trim()) {\r\n    return customBranding.custom_header_image_light_url;\r\n  }\r\n\r\n  // Priority 2: Fallback to opposite theme if current theme image is missing\r\n  if (isDark && customBranding.custom_header_image_light_url?.trim()) {\r\n    return customBranding.custom_header_image_light_url;\r\n  }\r\n\r\n  if (!isDark && customBranding.custom_header_image_dark_url?.trim()) {\r\n    return customBranding.custom_header_image_dark_url;\r\n  }\r\n\r\n  // Priority 3: Legacy single image field for backward compatibility\r\n  if (customBranding.custom_header_image_url?.trim()) {\r\n    return customBranding.custom_header_image_url;\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Get the branding text to display (custom or default)\r\n */\r\nexport function getBrandingText(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null\r\n): string | null {\r\n  // Only return custom text for Pro/Enterprise users\r\n  if (hasCustomBrandingAccess(userPlan) && customBranding?.custom_header_text) {\r\n    return customBranding.custom_header_text;\r\n  }\r\n  return null; // Will show default Dukancard branding\r\n}\r\n\r\n/**\r\n * Get the primary theme color (custom or default)\r\n */\r\nexport function getPrimaryThemeColor(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null,\r\n  fallbackThemeColor?: string\r\n): string {\r\n  const defaultColor = \"var(--brand-gold)\";\r\n\r\n  // Use theme_color if available and user is Pro/Enterprise\r\n  if (hasCustomBrandingAccess(userPlan) && fallbackThemeColor) {\r\n    return fallbackThemeColor;\r\n  }\r\n\r\n  return defaultColor;\r\n}\r\n\r\n\r\n\r\n/**\r\n * Generate custom CSS variables for the card based on custom branding\r\n */\r\nexport function generateCustomBrandingStyles(\r\n  userPlan?: UserPlan | null,\r\n  customBranding?: CustomBrandingSettings | null,\r\n  fallbackThemeColor?: string\r\n): React.CSSProperties {\r\n  const primaryColor = getPrimaryThemeColor(userPlan, customBranding, fallbackThemeColor);\r\n\r\n  const styles = {\r\n    \"--theme-color\": primaryColor,\r\n    \"--theme-color-80\": `${primaryColor}CC`,\r\n    \"--theme-color-50\": `${primaryColor}80`,\r\n    \"--theme-color-30\": `${primaryColor}4D`,\r\n    \"--theme-color-20\": `${primaryColor}33`,\r\n    \"--theme-color-10\": `${primaryColor}1A`,\r\n    \"--theme-color-5\": `${primaryColor}0D`,\r\n    \"--theme-accent-end\": \"#E5C76E\", // Default accent\r\n  } as React.CSSProperties;\r\n\r\n  return styles;\r\n}\r\n\r\n/**\r\n * Validate custom branding settings\r\n */\r\nexport function validateCustomBrandingSettings(\r\n  settings: Partial<CustomBrandingSettings>\r\n): { valid: boolean; errors: string[] } {\r\n  const errors: string[] = [];\r\n\r\n  // Validate header text length\r\n  if (settings.custom_header_text && settings.custom_header_text.length > 50) {\r\n    errors.push(\"Custom header text must be 50 characters or less\");\r\n  }\r\n\r\n  return {\r\n    valid: errors.length === 0,\r\n    errors\r\n  };\r\n}\r\n\r\n/**\r\n * Get default custom branding settings\r\n */\r\nexport function getDefaultCustomBrandingSettings(): CustomBrandingSettings {\r\n  return {\r\n    custom_header_text: \"\",\r\n    hide_dukancard_branding: false,\r\n  };\r\n}\r\n\r\n/**\r\n * Check if custom branding has any active settings\r\n */\r\nexport function hasActiveCustomBranding(\r\n  customBranding?: CustomBrandingSettings | null\r\n): boolean {\r\n  if (!customBranding) return false;\r\n\r\n  return !!(\r\n    customBranding.custom_header_text ||\r\n    customBranding.custom_header_image_url ||\r\n    customBranding.custom_header_image_light_url ||\r\n    customBranding.custom_header_image_dark_url ||\r\n    customBranding.hide_dukancard_branding\r\n  );\r\n}\r\n\r\n/**\r\n * Check if any header image exists (for any theme)\r\n */\r\nexport function hasAnyHeaderImage(\r\n  customBranding?: CustomBrandingSettings | null\r\n): boolean {\r\n  if (!customBranding) return false;\r\n\r\n  return !!(\r\n    customBranding.custom_header_image_url?.trim() ||\r\n    customBranding.custom_header_image_light_url?.trim() ||\r\n    customBranding.custom_header_image_dark_url?.trim()\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaO,SAAS,wBAAwB,QAA0B;IAChE,OAAO,aAAa,SAAS,aAAa;AAC5C;AAKO,SAAS,4BACd,QAA0B,EAC1B,cAA8C;IAE9C,wEAAwE;IACxE,6CAA6C;IAC7C,IAAI,CAAC,wBAAwB,WAAW;QACtC,OAAO;IACT;IAEA,mFAAmF;IACnF,OAAO,CAAC,gBAAgB;AAC1B;AAKO,SAAS,4BACd,QAA0B,EAC1B,cAA8C,EAC9C,YAA4B;IAE5B,qDAAqD;IACrD,IAAI,CAAC,wBAAwB,aAAa,CAAC,gBAAgB;QACzD,OAAO;IACT;IAEA,MAAM,SAAS,iBAAiB;IAEhC,oCAAoC;IACpC,IAAI,UAAU,eAAe,4BAA4B,EAAE,QAAQ;QACjE,OAAO,eAAe,4BAA4B;IACpD;IAEA,IAAI,CAAC,UAAU,eAAe,6BAA6B,EAAE,QAAQ;QACnE,OAAO,eAAe,6BAA6B;IACrD;IAEA,2EAA2E;IAC3E,IAAI,UAAU,eAAe,6BAA6B,EAAE,QAAQ;QAClE,OAAO,eAAe,6BAA6B;IACrD;IAEA,IAAI,CAAC,UAAU,eAAe,4BAA4B,EAAE,QAAQ;QAClE,OAAO,eAAe,4BAA4B;IACpD;IAEA,mEAAmE;IACnE,IAAI,eAAe,uBAAuB,EAAE,QAAQ;QAClD,OAAO,eAAe,uBAAuB;IAC/C;IAEA,OAAO;AACT;AAKO,SAAS,gBACd,QAA0B,EAC1B,cAA8C;IAE9C,mDAAmD;IACnD,IAAI,wBAAwB,aAAa,gBAAgB,oBAAoB;QAC3E,OAAO,eAAe,kBAAkB;IAC1C;IACA,OAAO,MAAM,uCAAuC;AACtD;AAKO,SAAS,qBACd,QAA0B,EAC1B,cAA8C,EAC9C,kBAA2B;IAE3B,MAAM,eAAe;IAErB,0DAA0D;IAC1D,IAAI,wBAAwB,aAAa,oBAAoB;QAC3D,OAAO;IACT;IAEA,OAAO;AACT;AAOO,SAAS,6BACd,QAA0B,EAC1B,cAA8C,EAC9C,kBAA2B;IAE3B,MAAM,eAAe,qBAAqB,UAAU,gBAAgB;IAEpE,MAAM,SAAS;QACb,iBAAiB;QACjB,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,oBAAoB,GAAG,aAAa,EAAE,CAAC;QACvC,mBAAmB,GAAG,aAAa,EAAE,CAAC;QACtC,sBAAsB;IACxB;IAEA,OAAO;AACT;AAKO,SAAS,+BACd,QAAyC;IAEzC,MAAM,SAAmB,EAAE;IAE3B,8BAA8B;IAC9B,IAAI,SAAS,kBAAkB,IAAI,SAAS,kBAAkB,CAAC,MAAM,GAAG,IAAI;QAC1E,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAKO,SAAS;IACd,OAAO;QACL,oBAAoB;QACpB,yBAAyB;IAC3B;AACF;AAKO,SAAS,wBACd,cAA8C;IAE9C,IAAI,CAAC,gBAAgB,OAAO;IAE5B,OAAO,CAAC,CAAC,CACP,eAAe,kBAAkB,IACjC,eAAe,uBAAuB,IACtC,eAAe,6BAA6B,IAC5C,eAAe,4BAA4B,IAC3C,eAAe,uBAAuB,AACxC;AACF;AAKO,SAAS,kBACd,cAA8C;IAE9C,IAAI,CAAC,gBAAgB,OAAO;IAE5B,OAAO,CAAC,CAAC,CACP,eAAe,uBAAuB,EAAE,UACxC,eAAe,6BAA6B,EAAE,UAC9C,eAAe,4BAA4B,EAAE,MAC/C;AACF", "debugId": null}}, {"offset": {"line": 1431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/qrCodeGenerator.ts"], "sourcesContent": ["/**\r\n * Utility functions to generate and download QR codes in different formats:\r\n * 1. Enhanced QR code with business information in A4 format for better printing options\r\n * 2. Raw QR image for digital use\r\n */\r\n\r\ninterface BusinessQRInfo {\r\n  businessName: string;\r\n  ownerName: string;\r\n  address: string;\r\n  slug: string;\r\n  qrValue: string;\r\n  themeColor?: string;\r\n}\r\n\r\n/**\r\n * Downloads just the raw QR code image without any additional formatting\r\n * Creates a high-quality, large PNG image suitable for various uses\r\n */\r\nexport async function downloadRawQRImage(\r\n  svgElement: SVGSVGElement,\r\n  slug: string\r\n): Promise<void> {\r\n  const svgData = new XMLSerializer().serializeToString(svgElement);\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      // Create a canvas with padding around the QR code\r\n      // Increase size for higher quality output\r\n      const padding = 50; // Increased padding for better appearance\r\n      const canvas = document.createElement(\"canvas\");\r\n\r\n      // Set a larger fixed size for better quality (1000x1000 pixels)\r\n      const canvasSize = 1000;\r\n      canvas.width = canvasSize;\r\n      canvas.height = canvasSize;\r\n\r\n      const ctx = canvas.getContext(\"2d\");\r\n      if (!ctx) {\r\n        reject(new Error(\"Could not get canvas context\"));\r\n        return;\r\n      }\r\n\r\n      // Fill with white background\r\n      ctx.fillStyle = \"#FFFFFF\";\r\n      ctx.fillRect(0, 0, canvasSize, canvasSize);\r\n\r\n      // Enable high-quality image rendering\r\n      ctx.imageSmoothingEnabled = true;\r\n      ctx.imageSmoothingQuality = \"high\";\r\n\r\n      // Calculate QR code size (canvas size minus padding on all sides)\r\n      const qrSize = canvasSize - (padding * 2);\r\n\r\n      // Draw QR code centered with higher quality\r\n      ctx.drawImage(\r\n        img,\r\n        padding,\r\n        padding,\r\n        qrSize,\r\n        qrSize\r\n      );\r\n\r\n      // Add a subtle border around the QR code for better definition\r\n      ctx.strokeStyle = \"#EEEEEE\";\r\n      ctx.lineWidth = 2;\r\n      ctx.strokeRect(padding - 2, padding - 2, qrSize + 4, qrSize + 4);\r\n\r\n      // Convert to PNG with maximum quality and trigger download\r\n      const pngFile = canvas.toDataURL(\"image/png\", 1.0);\r\n      const link = document.createElement(\"a\");\r\n      link.download = `${slug}-qr-code.png`;\r\n      link.href = pngFile;\r\n      link.click();\r\n\r\n      resolve();\r\n    };\r\n\r\n    img.onerror = () => {\r\n      reject(new Error(\"Could not load QR code SVG\"));\r\n    };\r\n\r\n    img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;\r\n  });\r\n}\r\n\r\nexport async function generateAndDownloadQRCode(\r\n  svgElement: SVGSVGElement,\r\n  businessInfo: BusinessQRInfo\r\n): Promise<void> {\r\n  const {\r\n    businessName,\r\n    ownerName,\r\n    address,\r\n    slug,\r\n    themeColor = \"#F59E0B\",\r\n  } = businessInfo;\r\n\r\n  // A4 dimensions in pixels at 300 DPI\r\n  // A4 is 210mm × 297mm, which is approximately 2480 × 3508 pixels at 300 DPI\r\n  const width = 2480;\r\n  const height = 3508;\r\n\r\n  // Create canvas\r\n  const canvas = document.createElement(\"canvas\");\r\n  canvas.width = width;\r\n  canvas.height = height;\r\n\r\n  const ctx = canvas.getContext(\"2d\");\r\n  if (!ctx) {\r\n    throw new Error(\"Could not get canvas context\");\r\n  }\r\n\r\n  // Fill background with white\r\n  ctx.fillStyle = \"#FFFFFF\";\r\n  ctx.fillRect(0, 0, width, height);\r\n\r\n  // Create a subtle gradient background\r\n  const gradient = ctx.createLinearGradient(0, 0, 0, height);\r\n  gradient.addColorStop(0, \"#FFFFFF\");\r\n  gradient.addColorStop(1, \"#F8F8F8\");\r\n  ctx.fillStyle = gradient;\r\n  ctx.fillRect(0, 0, width, height);\r\n\r\n  // Add a subtle pattern overlay for texture\r\n  createSubtlePattern(ctx, width, height);\r\n\r\n  // Draw a more sophisticated frame with enhanced visual design\r\n  drawSophisticatedFrame(ctx, width, height, themeColor);\r\n\r\n  // Add decorative elements\r\n  drawDecorativeElements(ctx, width, height, themeColor);\r\n\r\n  // Add header with business name - handle long business names\r\n  ctx.fillStyle = \"#333333\";\r\n  ctx.textAlign = \"center\";\r\n\r\n  // Start with large font size and reduce if needed\r\n  let businessNameFontSize = 160;\r\n  ctx.font = `bold ${businessNameFontSize}px 'Arial'`;\r\n\r\n  // Check if business name is too long and reduce font size if needed\r\n  let businessNameWidth = ctx.measureText(businessName).width;\r\n  while (businessNameWidth > width * 0.75 && businessNameFontSize > 80) {\r\n    businessNameFontSize -= 10;\r\n    ctx.font = `bold ${businessNameFontSize}px 'Arial'`;\r\n    businessNameWidth = ctx.measureText(businessName).width;\r\n  }\r\n\r\n  // If still too long, split into multiple lines\r\n  if (businessNameWidth > width * 0.75) {\r\n    const businessNameLines = splitTextIntoLines(businessName, 30);\r\n    let yPos = height * 0.12;\r\n    businessNameLines.forEach((line) => {\r\n      ctx.fillText(line, width / 2, yPos, width * 0.8);\r\n      yPos += businessNameFontSize * 0.8; // Add spacing between lines\r\n    });\r\n  } else {\r\n    ctx.fillText(businessName, width / 2, height * 0.15, width * 0.8);\r\n  }\r\n\r\n  // Add a subtle underline below the business name\r\n  const textWidth = ctx.measureText(businessName).width;\r\n  const underlineWidth = Math.min(textWidth, width * 0.6);\r\n  ctx.beginPath();\r\n  ctx.moveTo(width / 2 - underlineWidth / 2, height * 0.17);\r\n  ctx.lineTo(width / 2 + underlineWidth / 2, height * 0.17);\r\n  ctx.strokeStyle = themeColor;\r\n  ctx.lineWidth = 6;\r\n  ctx.stroke();\r\n\r\n  // Add \"Scan to view our digital card\" text with better styling\r\n  ctx.font = \"80px 'Arial'\";\r\n  ctx.fillStyle = \"#555555\";\r\n  ctx.fillText(\r\n    \"Scan to view our digital card\",\r\n    width / 2,\r\n    height * 0.22,\r\n    width * 0.8\r\n  );\r\n\r\n  // QR code size and positioning - adjust based on business name length\r\n  // Use smaller QR code if business name is very long (multiple lines)\r\n  const businessNameLines = splitTextIntoLines(businessName, 30);\r\n  const qrSizeMultiplier = businessNameLines.length > 1 ? 0.3 : 0.35;\r\n  const qrSize = Math.min(width, height) * qrSizeMultiplier;\r\n  const qrX = (width - qrSize) / 2;\r\n  const qrY = businessNameLines.length > 1 ? height * 0.35 : height * 0.3;\r\n\r\n  // Draw an elegant container for the QR code\r\n  drawQRCodeContainer(ctx, qrX, qrY, qrSize, themeColor);\r\n\r\n  // Draw QR code\r\n  const svgData = new XMLSerializer().serializeToString(svgElement);\r\n\r\n  return new Promise((resolve, reject) => {\r\n    const img = new Image();\r\n    img.onload = () => {\r\n      // Draw QR code centered\r\n      ctx.drawImage(img, qrX, qrY, qrSize, qrSize);\r\n\r\n      // Add URL text below QR code with better styling\r\n      // Handle potentially long slugs by reducing font size if needed\r\n      const urlText = `dukancard.in/${slug}`;\r\n      let urlFontSize = 70;\r\n      ctx.font = `bold ${urlFontSize}px 'Arial'`;\r\n\r\n      // Check if URL is too long and reduce font size if needed\r\n      let urlWidth = ctx.measureText(urlText).width;\r\n      while (urlWidth > width * 0.7 && urlFontSize > 40) {\r\n        urlFontSize -= 5;\r\n        ctx.font = `bold ${urlFontSize}px 'Arial'`;\r\n        urlWidth = ctx.measureText(urlText).width;\r\n      }\r\n\r\n      // Position URL with sufficient distance from QR code\r\n      ctx.fillStyle = \"#333333\";\r\n      ctx.fillText(\r\n        urlText,\r\n        width / 2,\r\n        qrY + qrSize + 180, // Increased distance from QR code\r\n        width * 0.8\r\n      );\r\n\r\n      // Add a divider line - position based on URL position\r\n      const dividerY = qrY + qrSize + 240; // Position after URL\r\n      drawDivider(ctx, width, dividerY, width * 0.7, themeColor);\r\n\r\n      // Add owner name with better styling - handle long names\r\n      let ownerNameFontSize = 100;\r\n      ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;\r\n      ctx.fillStyle = \"#333333\";\r\n\r\n      // Check if owner name is too long and reduce font size if needed\r\n      let ownerNameWidth = ctx.measureText(ownerName).width;\r\n      while (ownerNameWidth > width * 0.75 && ownerNameFontSize > 60) {\r\n        ownerNameFontSize -= 5;\r\n        ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;\r\n        ownerNameWidth = ctx.measureText(ownerName).width;\r\n      }\r\n\r\n      // If still too long, split into multiple lines\r\n      if (ownerNameWidth > width * 0.75) {\r\n        const ownerNameLines = splitTextIntoLines(ownerName, 25);\r\n        let yPos = height * 0.75;\r\n        ownerNameLines.forEach((line) => {\r\n          ctx.fillText(line, width / 2, yPos, width * 0.8);\r\n          yPos += ownerNameFontSize * 0.7; // Add spacing between lines\r\n        });\r\n      } else {\r\n        ctx.fillText(ownerName, width / 2, height * 0.75, width * 0.8);\r\n      }\r\n\r\n      // We're removing the location icon as it's causing visual issues\r\n      // No need to draw the location icon anymore\r\n\r\n      // Add address with better styling\r\n      let addressFontSize = 70;\r\n      ctx.font = `${addressFontSize}px 'Arial'`;\r\n      ctx.fillStyle = \"#555555\";\r\n\r\n      // Calculate starting position based on owner name position and length\r\n      let yPosition: number;\r\n      if (ownerNameWidth > width * 0.75) {\r\n        // If owner name was split into multiple lines, position address accordingly\r\n        const ownerNameLines = splitTextIntoLines(ownerName, 25);\r\n        yPosition =\r\n          height * 0.75 + ownerNameLines.length * ownerNameFontSize * 0.7 + 50;\r\n      } else {\r\n        yPosition = height * 0.8;\r\n      }\r\n\r\n      // Split address into multiple lines\r\n      const addressLines = splitTextIntoLines(address, 50);\r\n\r\n      // If address is very long, reduce font size\r\n      if (addressLines.length > 3) {\r\n        addressFontSize = 60;\r\n        ctx.font = `${addressFontSize}px 'Arial'`;\r\n      }\r\n\r\n      // Draw each line of the address\r\n      addressLines.forEach((line) => {\r\n        ctx.fillText(line, width / 2, yPosition, width * 0.8);\r\n        yPosition += addressFontSize + 20; // Line height with spacing\r\n      });\r\n\r\n      // Add a footer with powered by text - position dynamically based on content\r\n      ctx.font = \"50px 'Arial'\";\r\n      ctx.fillStyle = \"#888888\";\r\n\r\n      // Calculate footer position based on address length\r\n      const footerY = Math.min(height - 100, yPosition + 150);\r\n\r\n      ctx.fillText(\"Powered by Dukancard\", width / 2, footerY, width * 0.8);\r\n\r\n      // Convert to JPG and trigger download\r\n      const jpgFile = canvas.toDataURL(\"image/jpeg\", 0.95);\r\n      const link = document.createElement(\"a\");\r\n      link.download = `${slug}-qrcode.jpg`;\r\n      link.href = jpgFile;\r\n      link.click();\r\n\r\n      resolve();\r\n    };\r\n\r\n    img.onerror = () => {\r\n      reject(new Error(\"Could not load QR code SVG\"));\r\n    };\r\n\r\n    img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;\r\n  });\r\n}\r\n\r\n/**\r\n * Creates a subtle pattern overlay for texture\r\n */\r\nfunction createSubtlePattern(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number\r\n) {\r\n  ctx.save();\r\n  ctx.globalAlpha = 0.03;\r\n\r\n  // Create a pattern of small dots\r\n  const patternSize = 20;\r\n  for (let x = 0; x < width; x += patternSize) {\r\n    for (let y = 0; y < height; y += patternSize) {\r\n      ctx.beginPath();\r\n      ctx.arc(x, y, 1, 0, Math.PI * 2);\r\n      ctx.fillStyle = \"#000000\";\r\n      ctx.fill();\r\n    }\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Draws a sophisticated frame with enhanced visual design\r\n */\r\nfunction drawSophisticatedFrame(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number,\r\n  themeColor: string\r\n) {\r\n  // Create a more elegant border with gradient\r\n  const borderGradient = ctx.createLinearGradient(0, 0, width, height);\r\n  borderGradient.addColorStop(0, themeColor);\r\n  borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));\r\n  borderGradient.addColorStop(1, themeColor);\r\n\r\n  // Draw outer border with gradient\r\n  ctx.strokeStyle = borderGradient;\r\n  ctx.lineWidth = 3;\r\n  ctx.strokeRect(40, 40, width - 80, height - 80);\r\n\r\n  // Draw inner border with gradient and shadow\r\n  ctx.shadowColor = \"rgba(0, 0, 0, 0.1)\";\r\n  ctx.shadowBlur = 15;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 0;\r\n  ctx.strokeStyle = borderGradient;\r\n  ctx.lineWidth = 8;\r\n  ctx.strokeRect(80, 80, width - 160, height - 160);\r\n\r\n  // Reset shadow\r\n  ctx.shadowColor = \"transparent\";\r\n  ctx.shadowBlur = 0;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 0;\r\n\r\n  // Add corner decorations with enhanced design\r\n  const cornerSize = 120;\r\n\r\n  // Top-left corner\r\n  drawCornerDecoration(ctx, 80, 80, cornerSize, themeColor, \"top-left\");\r\n  // Top-right corner\r\n  drawCornerDecoration(\r\n    ctx,\r\n    width - 80,\r\n    80,\r\n    cornerSize,\r\n    themeColor,\r\n    \"top-right\"\r\n  );\r\n  // Bottom-left corner\r\n  drawCornerDecoration(\r\n    ctx,\r\n    80,\r\n    height - 80,\r\n    cornerSize,\r\n    themeColor,\r\n    \"bottom-left\"\r\n  );\r\n  // Bottom-right corner\r\n  drawCornerDecoration(\r\n    ctx,\r\n    width - 80,\r\n    height - 80,\r\n    cornerSize,\r\n    themeColor,\r\n    \"bottom-right\"\r\n  );\r\n\r\n  // Add subtle decorative patterns along the borders\r\n  drawBorderPatterns(ctx, width, height, themeColor);\r\n}\r\n\r\n/**\r\n * Draws enhanced corner decorations\r\n */\r\nfunction drawCornerDecoration(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  size: number,\r\n  color: string,\r\n  position: \"top-left\" | \"top-right\" | \"bottom-left\" | \"bottom-right\"\r\n) {\r\n  ctx.save();\r\n\r\n  // Create gradient for more elegant corners\r\n  const cornerGradient = ctx.createLinearGradient(\r\n    position.includes(\"left\") ? x : x - size,\r\n    position.includes(\"top\") ? y : y - size,\r\n    position.includes(\"left\") ? x + size : x,\r\n    position.includes(\"top\") ? y + size : y\r\n  );\r\n\r\n  cornerGradient.addColorStop(0, color);\r\n  cornerGradient.addColorStop(1, adjustColor(color, 20));\r\n\r\n  ctx.strokeStyle = cornerGradient;\r\n  ctx.lineWidth = 8;\r\n  ctx.lineCap = \"round\";\r\n  ctx.beginPath();\r\n\r\n  if (position === \"top-left\") {\r\n    ctx.moveTo(x, y + size);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x + size, y);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x + 30, y + 30);\r\n    ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);\r\n  } else if (position === \"top-right\") {\r\n    ctx.moveTo(x - size, y);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x, y + size);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x - 30, y + 30);\r\n    ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);\r\n  } else if (position === \"bottom-left\") {\r\n    ctx.moveTo(x, y - size);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x + size, y);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x + 30, y - 30);\r\n    ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);\r\n  } else if (position === \"bottom-right\") {\r\n    ctx.moveTo(x - size, y);\r\n    ctx.lineTo(x, y);\r\n    ctx.lineTo(x, y - size);\r\n\r\n    // Add decorative dot\r\n    ctx.moveTo(x - 30, y - 30);\r\n    ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);\r\n  }\r\n\r\n  ctx.stroke();\r\n\r\n  // Fill the decorative dots\r\n  ctx.fillStyle = color;\r\n  if (position === \"top-left\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  } else if (position === \"top-right\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  } else if (position === \"bottom-left\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  } else if (position === \"bottom-right\") {\r\n    ctx.beginPath();\r\n    ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);\r\n    ctx.fill();\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Draws enhanced decorative elements\r\n */\r\nfunction drawDecorativeElements(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number,\r\n  themeColor: string\r\n) {\r\n  // Add subtle decorative elements with enhanced design\r\n  ctx.save();\r\n\r\n  // Create gradient for decorative elements\r\n  const gradientTopLeft = ctx.createRadialGradient(0, 0, 0, 0, 0, 400);\r\n  gradientTopLeft.addColorStop(0, themeColor);\r\n  gradientTopLeft.addColorStop(1, \"rgba(255, 255, 255, 0)\");\r\n\r\n  const gradientBottomRight = ctx.createRadialGradient(\r\n    width,\r\n    height,\r\n    0,\r\n    width,\r\n    height,\r\n    400\r\n  );\r\n  gradientBottomRight.addColorStop(0, themeColor);\r\n  gradientBottomRight.addColorStop(1, \"rgba(255, 255, 255, 0)\");\r\n\r\n  // Adjust opacity for subtlety\r\n  ctx.globalAlpha = 0.08;\r\n\r\n  // Draw decorative gradient in top-left\r\n  ctx.beginPath();\r\n  ctx.arc(0, 0, 400, 0, Math.PI * 2);\r\n  ctx.fillStyle = gradientTopLeft;\r\n  ctx.fill();\r\n\r\n  // Draw decorative gradient in bottom-right\r\n  ctx.beginPath();\r\n  ctx.arc(width, height, 400, 0, Math.PI * 2);\r\n  ctx.fillStyle = gradientBottomRight;\r\n  ctx.fill();\r\n\r\n  // Add decorative patterns\r\n  ctx.globalAlpha = 0.05;\r\n\r\n  // Draw decorative pattern in center\r\n  const patternSize = 60;\r\n  const patternRows = Math.ceil(height / patternSize);\r\n  const patternCols = Math.ceil(width / patternSize);\r\n\r\n  for (let row = 0; row < patternRows; row++) {\r\n    for (let col = 0; col < patternCols; col++) {\r\n      // Only draw pattern in a diamond shape in the center\r\n      const distanceFromCenter =\r\n        Math.abs(row - patternRows / 2) + Math.abs(col - patternCols / 2);\r\n      if (distanceFromCenter < patternRows / 3) {\r\n        const x = col * patternSize;\r\n        const y = row * patternSize;\r\n\r\n        // Draw subtle pattern element\r\n        if ((row + col) % 2 === 0) {\r\n          ctx.beginPath();\r\n          ctx.arc(x + patternSize / 2, y + patternSize / 2, 2, 0, Math.PI * 2);\r\n          ctx.fillStyle = themeColor;\r\n          ctx.fill();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Draws an elegant container for the QR code with enhanced visual design\r\n */\r\nfunction drawQRCodeContainer(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  size: number,\r\n  themeColor: string\r\n) {\r\n  const padding = 100;\r\n  const containerWidth = size + padding * 2;\r\n  const containerHeight = size + padding * 2;\r\n  const containerX = x - padding;\r\n  const containerY = y - padding;\r\n\r\n  // Draw white background with rounded corners\r\n  ctx.fillStyle = \"#FFFFFF\";\r\n  roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);\r\n  ctx.fill();\r\n\r\n  // Draw subtle shadow\r\n  ctx.shadowColor = \"rgba(0, 0, 0, 0.15)\";\r\n  ctx.shadowBlur = 40;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 15;\r\n  ctx.fillStyle = \"#FFFFFF\";\r\n  roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);\r\n  ctx.fill();\r\n\r\n  // Reset shadow\r\n  ctx.shadowColor = \"transparent\";\r\n  ctx.shadowBlur = 0;\r\n  ctx.shadowOffsetX = 0;\r\n  ctx.shadowOffsetY = 0;\r\n\r\n  // Draw elegant border with gradient\r\n  const borderGradient = ctx.createLinearGradient(\r\n    containerX,\r\n    containerY,\r\n    containerX + containerWidth,\r\n    containerY + containerHeight\r\n  );\r\n  borderGradient.addColorStop(0, themeColor);\r\n  borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));\r\n  borderGradient.addColorStop(1, themeColor);\r\n\r\n  ctx.strokeStyle = borderGradient;\r\n  ctx.lineWidth = 3;\r\n  roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);\r\n  ctx.stroke();\r\n\r\n  // Add decorative corner elements to the QR container\r\n  const cornerSize = 30;\r\n\r\n  // Top-left corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(containerX, containerY + cornerSize);\r\n  ctx.lineTo(containerX, containerY);\r\n  ctx.lineTo(containerX + cornerSize, containerY);\r\n  ctx.strokeStyle = themeColor;\r\n  ctx.lineWidth = 5;\r\n  ctx.stroke();\r\n\r\n  // Top-right corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(containerX + containerWidth - cornerSize, containerY);\r\n  ctx.lineTo(containerX + containerWidth, containerY);\r\n  ctx.lineTo(containerX + containerWidth, containerY + cornerSize);\r\n  ctx.stroke();\r\n\r\n  // Bottom-left corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(containerX, containerY + containerHeight - cornerSize);\r\n  ctx.lineTo(containerX, containerY + containerHeight);\r\n  ctx.lineTo(containerX + cornerSize, containerY + containerHeight);\r\n  ctx.stroke();\r\n\r\n  // Bottom-right corner decoration\r\n  ctx.beginPath();\r\n  ctx.moveTo(\r\n    containerX + containerWidth - cornerSize,\r\n    containerY + containerHeight\r\n  );\r\n  ctx.lineTo(containerX + containerWidth, containerY + containerHeight);\r\n  ctx.lineTo(\r\n    containerX + containerWidth,\r\n    containerY + containerHeight - cornerSize\r\n  );\r\n  ctx.stroke();\r\n}\r\n\r\n/**\r\n * Draws an enhanced divider line with decorative elements\r\n */\r\nfunction drawDivider(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  width: number,\r\n  color: string\r\n) {\r\n  ctx.save();\r\n\r\n  // Create gradient for divider\r\n  const dividerGradient = ctx.createLinearGradient(\r\n    x / 2 - width / 2,\r\n    y,\r\n    x / 2 + width / 2,\r\n    y\r\n  );\r\n  dividerGradient.addColorStop(0, \"rgba(255, 255, 255, 0)\");\r\n  dividerGradient.addColorStop(0.1, color);\r\n  dividerGradient.addColorStop(0.5, adjustColor(color, 20));\r\n  dividerGradient.addColorStop(0.9, color);\r\n  dividerGradient.addColorStop(1, \"rgba(255, 255, 255, 0)\");\r\n\r\n  // Draw main line with gradient\r\n  ctx.beginPath();\r\n  ctx.moveTo(x / 2 - width / 2, y);\r\n  ctx.lineTo(x / 2 + width / 2, y);\r\n  ctx.strokeStyle = dividerGradient;\r\n  ctx.lineWidth = 3;\r\n  ctx.stroke();\r\n\r\n  // Draw decorative element in the middle\r\n  ctx.beginPath();\r\n  ctx.arc(x / 2, y, 15, 0, Math.PI * 2);\r\n  ctx.fillStyle = color;\r\n  ctx.fill();\r\n\r\n  // Add outer ring to the decorative element\r\n  ctx.beginPath();\r\n  ctx.arc(x / 2, y, 20, 0, Math.PI * 2);\r\n  ctx.strokeStyle = color;\r\n  ctx.lineWidth = 2;\r\n  ctx.stroke();\r\n\r\n  // Add small decorative elements along the line\r\n  const numDots = 6;\r\n  const dotSpacing = width / 4 / numDots;\r\n\r\n  // Left side dots\r\n  for (let i = 1; i <= numDots; i++) {\r\n    const dotX = x / 2 - width / 8 - i * dotSpacing;\r\n    ctx.beginPath();\r\n    ctx.arc(dotX, y, 3, 0, Math.PI * 2);\r\n    ctx.fillStyle = color;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Right side dots\r\n  for (let i = 1; i <= numDots; i++) {\r\n    const dotX = x / 2 + width / 8 + i * dotSpacing;\r\n    ctx.beginPath();\r\n    ctx.arc(dotX, y, 3, 0, Math.PI * 2);\r\n    ctx.fillStyle = color;\r\n    ctx.fill();\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Helper function to draw a rectangle with rounded corners\r\n */\r\nfunction roundRect(\r\n  ctx: CanvasRenderingContext2D,\r\n  x: number,\r\n  y: number,\r\n  width: number,\r\n  height: number,\r\n  radius: number\r\n) {\r\n  ctx.beginPath();\r\n  ctx.moveTo(x + radius, y);\r\n  ctx.lineTo(x + width - radius, y);\r\n  ctx.quadraticCurveTo(x + width, y, x + width, y + radius);\r\n  ctx.lineTo(x + width, y + height - radius);\r\n  ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);\r\n  ctx.lineTo(x + radius, y + height);\r\n  ctx.quadraticCurveTo(x, y + height, x, y + height - radius);\r\n  ctx.lineTo(x, y + radius);\r\n  ctx.quadraticCurveTo(x, y, x + radius, y);\r\n  ctx.closePath();\r\n}\r\n\r\n/**\r\n * Helper function to adjust color brightness\r\n */\r\nfunction adjustColor(color: string, amount: number): string {\r\n  // Convert hex to RGB\r\n  let r, g, b;\r\n  if (color.startsWith(\"#\")) {\r\n    r = parseInt(color.slice(1, 3), 16);\r\n    g = parseInt(color.slice(3, 5), 16);\r\n    b = parseInt(color.slice(5, 7), 16);\r\n  } else {\r\n    // Default fallback color\r\n    r = 245;\r\n    g = 158;\r\n    b = 11; // Default to brand gold\r\n  }\r\n\r\n  // Adjust brightness\r\n  r = Math.max(0, Math.min(255, r + amount));\r\n  g = Math.max(0, Math.min(255, g + amount));\r\n  b = Math.max(0, Math.min(255, b + amount));\r\n\r\n  // Convert back to hex\r\n  return `#${r.toString(16).padStart(2, \"0\")}${g\r\n    .toString(16)\r\n    .padStart(2, \"0\")}${b.toString(16).padStart(2, \"0\")}`;\r\n}\r\n\r\n/**\r\n * Draws decorative patterns along the borders\r\n */\r\nfunction drawBorderPatterns(\r\n  ctx: CanvasRenderingContext2D,\r\n  width: number,\r\n  height: number,\r\n  themeColor: string\r\n) {\r\n  ctx.save();\r\n  ctx.globalAlpha = 0.2;\r\n\r\n  // Top border pattern\r\n  for (let x = 120; x < width - 120; x += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(x, 80, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Bottom border pattern\r\n  for (let x = 120; x < width - 120; x += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(x, height - 80, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Left border pattern\r\n  for (let y = 120; y < height - 120; y += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(80, y, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  // Right border pattern\r\n  for (let y = 120; y < height - 120; y += 40) {\r\n    ctx.beginPath();\r\n    ctx.arc(width - 80, y, 2, 0, Math.PI * 2);\r\n    ctx.fillStyle = themeColor;\r\n    ctx.fill();\r\n  }\r\n\r\n  ctx.restore();\r\n}\r\n\r\n/**\r\n * Helper function to split text into multiple lines\r\n */\r\nfunction splitTextIntoLines(text: string, maxCharsPerLine: number): string[] {\r\n  const words = text.split(\" \");\r\n  const lines: string[] = [];\r\n  let currentLine = \"\";\r\n\r\n  words.forEach((word) => {\r\n    if ((currentLine + word).length <= maxCharsPerLine) {\r\n      currentLine += (currentLine ? \" \" : \"\") + word;\r\n    } else {\r\n      lines.push(currentLine);\r\n      currentLine = word;\r\n    }\r\n  });\r\n\r\n  if (currentLine) {\r\n    lines.push(currentLine);\r\n  }\r\n\r\n  return lines;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAeM,eAAe,mBACpB,UAAyB,EACzB,IAAY;IAEZ,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;IAEtD,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,kDAAkD;YAClD,0CAA0C;YAC1C,MAAM,UAAU,IAAI,0CAA0C;YAC9D,MAAM,SAAS,SAAS,aAAa,CAAC;YAEtC,gEAAgE;YAChE,MAAM,aAAa;YACnB,OAAO,KAAK,GAAG;YACf,OAAO,MAAM,GAAG;YAEhB,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;gBACR,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,6BAA6B;YAC7B,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,YAAY;YAE/B,sCAAsC;YACtC,IAAI,qBAAqB,GAAG;YAC5B,IAAI,qBAAqB,GAAG;YAE5B,kEAAkE;YAClE,MAAM,SAAS,aAAc,UAAU;YAEvC,4CAA4C;YAC5C,IAAI,SAAS,CACX,KACA,SACA,SACA,QACA;YAGF,+DAA+D;YAC/D,IAAI,WAAW,GAAG;YAClB,IAAI,SAAS,GAAG;YAChB,IAAI,UAAU,CAAC,UAAU,GAAG,UAAU,GAAG,SAAS,GAAG,SAAS;YAE9D,2DAA2D;YAC3D,MAAM,UAAU,OAAO,SAAS,CAAC,aAAa;YAC9C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG,GAAG,KAAK,YAAY,CAAC;YACrC,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK;YAEV;QACF;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,GAAG,GAAG,CAAC,0BAA0B,EAAE,KAAK,UAAU;IACxD;AACF;AAEO,eAAe,0BACpB,UAAyB,EACzB,YAA4B;IAE5B,MAAM,EACJ,YAAY,EACZ,SAAS,EACT,OAAO,EACP,IAAI,EACJ,aAAa,SAAS,EACvB,GAAG;IAEJ,qCAAqC;IACrC,4EAA4E;IAC5E,MAAM,QAAQ;IACd,MAAM,SAAS;IAEf,gBAAgB;IAChB,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,KAAK,GAAG;IACf,OAAO,MAAM,GAAG;IAEhB,MAAM,MAAM,OAAO,UAAU,CAAC;IAC9B,IAAI,CAAC,KAAK;QACR,MAAM,IAAI,MAAM;IAClB;IAEA,6BAA6B;IAC7B,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO;IAE1B,sCAAsC;IACtC,MAAM,WAAW,IAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG;IACnD,SAAS,YAAY,CAAC,GAAG;IACzB,SAAS,YAAY,CAAC,GAAG;IACzB,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,CAAC,GAAG,GAAG,OAAO;IAE1B,2CAA2C;IAC3C,oBAAoB,KAAK,OAAO;IAEhC,8DAA8D;IAC9D,uBAAuB,KAAK,OAAO,QAAQ;IAE3C,0BAA0B;IAC1B,uBAAuB,KAAK,OAAO,QAAQ;IAE3C,6DAA6D;IAC7D,IAAI,SAAS,GAAG;IAChB,IAAI,SAAS,GAAG;IAEhB,kDAAkD;IAClD,IAAI,uBAAuB;IAC3B,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,qBAAqB,UAAU,CAAC;IAEnD,oEAAoE;IACpE,IAAI,oBAAoB,IAAI,WAAW,CAAC,cAAc,KAAK;IAC3D,MAAO,oBAAoB,QAAQ,QAAQ,uBAAuB,GAAI;QACpE,wBAAwB;QACxB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,qBAAqB,UAAU,CAAC;QACnD,oBAAoB,IAAI,WAAW,CAAC,cAAc,KAAK;IACzD;IAEA,+CAA+C;IAC/C,IAAI,oBAAoB,QAAQ,MAAM;QACpC,MAAM,oBAAoB,mBAAmB,cAAc;QAC3D,IAAI,OAAO,SAAS;QACpB,kBAAkB,OAAO,CAAC,CAAC;YACzB,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ;YAC5C,QAAQ,uBAAuB,KAAK,4BAA4B;QAClE;IACF,OAAO;QACL,IAAI,QAAQ,CAAC,cAAc,QAAQ,GAAG,SAAS,MAAM,QAAQ;IAC/D;IAEA,iDAAiD;IACjD,MAAM,YAAY,IAAI,WAAW,CAAC,cAAc,KAAK;IACrD,MAAM,iBAAiB,KAAK,GAAG,CAAC,WAAW,QAAQ;IACnD,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,QAAQ,IAAI,iBAAiB,GAAG,SAAS;IACpD,IAAI,MAAM,CAAC,QAAQ,IAAI,iBAAiB,GAAG,SAAS;IACpD,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,+DAA+D;IAC/D,IAAI,IAAI,GAAG;IACX,IAAI,SAAS,GAAG;IAChB,IAAI,QAAQ,CACV,iCACA,QAAQ,GACR,SAAS,MACT,QAAQ;IAGV,sEAAsE;IACtE,qEAAqE;IACrE,MAAM,oBAAoB,mBAAmB,cAAc;IAC3D,MAAM,mBAAmB,kBAAkB,MAAM,GAAG,IAAI,MAAM;IAC9D,MAAM,SAAS,KAAK,GAAG,CAAC,OAAO,UAAU;IACzC,MAAM,MAAM,CAAC,QAAQ,MAAM,IAAI;IAC/B,MAAM,MAAM,kBAAkB,MAAM,GAAG,IAAI,SAAS,OAAO,SAAS;IAEpE,4CAA4C;IAC5C,oBAAoB,KAAK,KAAK,KAAK,QAAQ;IAE3C,eAAe;IACf,MAAM,UAAU,IAAI,gBAAgB,iBAAiB,CAAC;IAEtD,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,GAAG;YACX,wBAAwB;YACxB,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,QAAQ;YAErC,iDAAiD;YACjD,gEAAgE;YAChE,MAAM,UAAU,CAAC,aAAa,EAAE,MAAM;YACtC,IAAI,cAAc;YAClB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,YAAY,UAAU,CAAC;YAE1C,0DAA0D;YAC1D,IAAI,WAAW,IAAI,WAAW,CAAC,SAAS,KAAK;YAC7C,MAAO,WAAW,QAAQ,OAAO,cAAc,GAAI;gBACjD,eAAe;gBACf,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,YAAY,UAAU,CAAC;gBAC1C,WAAW,IAAI,WAAW,CAAC,SAAS,KAAK;YAC3C;YAEA,qDAAqD;YACrD,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CACV,SACA,QAAQ,GACR,MAAM,SAAS,KACf,QAAQ;YAGV,sDAAsD;YACtD,MAAM,WAAW,MAAM,SAAS,KAAK,qBAAqB;YAC1D,YAAY,KAAK,OAAO,UAAU,QAAQ,KAAK;YAE/C,yDAAyD;YACzD,IAAI,oBAAoB;YACxB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,kBAAkB,UAAU,CAAC;YAChD,IAAI,SAAS,GAAG;YAEhB,iEAAiE;YACjE,IAAI,iBAAiB,IAAI,WAAW,CAAC,WAAW,KAAK;YACrD,MAAO,iBAAiB,QAAQ,QAAQ,oBAAoB,GAAI;gBAC9D,qBAAqB;gBACrB,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,kBAAkB,UAAU,CAAC;gBAChD,iBAAiB,IAAI,WAAW,CAAC,WAAW,KAAK;YACnD;YAEA,+CAA+C;YAC/C,IAAI,iBAAiB,QAAQ,MAAM;gBACjC,MAAM,iBAAiB,mBAAmB,WAAW;gBACrD,IAAI,OAAO,SAAS;gBACpB,eAAe,OAAO,CAAC,CAAC;oBACtB,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ;oBAC5C,QAAQ,oBAAoB,KAAK,4BAA4B;gBAC/D;YACF,OAAO;gBACL,IAAI,QAAQ,CAAC,WAAW,QAAQ,GAAG,SAAS,MAAM,QAAQ;YAC5D;YAEA,iEAAiE;YACjE,4CAA4C;YAE5C,kCAAkC;YAClC,IAAI,kBAAkB;YACtB,IAAI,IAAI,GAAG,GAAG,gBAAgB,UAAU,CAAC;YACzC,IAAI,SAAS,GAAG;YAEhB,sEAAsE;YACtE,IAAI;YACJ,IAAI,iBAAiB,QAAQ,MAAM;gBACjC,4EAA4E;gBAC5E,MAAM,iBAAiB,mBAAmB,WAAW;gBACrD,YACE,SAAS,OAAO,eAAe,MAAM,GAAG,oBAAoB,MAAM;YACtE,OAAO;gBACL,YAAY,SAAS;YACvB;YAEA,oCAAoC;YACpC,MAAM,eAAe,mBAAmB,SAAS;YAEjD,4CAA4C;YAC5C,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,kBAAkB;gBAClB,IAAI,IAAI,GAAG,GAAG,gBAAgB,UAAU,CAAC;YAC3C;YAEA,gCAAgC;YAChC,aAAa,OAAO,CAAC,CAAC;gBACpB,IAAI,QAAQ,CAAC,MAAM,QAAQ,GAAG,WAAW,QAAQ;gBACjD,aAAa,kBAAkB,IAAI,2BAA2B;YAChE;YAEA,4EAA4E;YAC5E,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAEhB,oDAAoD;YACpD,MAAM,UAAU,KAAK,GAAG,CAAC,SAAS,KAAK,YAAY;YAEnD,IAAI,QAAQ,CAAC,wBAAwB,QAAQ,GAAG,SAAS,QAAQ;YAEjE,sCAAsC;YACtC,MAAM,UAAU,OAAO,SAAS,CAAC,cAAc;YAC/C,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,QAAQ,GAAG,GAAG,KAAK,WAAW,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,KAAK;YAEV;QACF;QAEA,IAAI,OAAO,GAAG;YACZ,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,GAAG,GAAG,CAAC,0BAA0B,EAAE,KAAK,UAAU;IACxD;AACF;AAEA;;CAEC,GACD,SAAS,oBACP,GAA6B,EAC7B,KAAa,EACb,MAAc;IAEd,IAAI,IAAI;IACR,IAAI,WAAW,GAAG;IAElB,iCAAiC;IACjC,MAAM,cAAc;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,KAAK,YAAa;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,KAAK,YAAa;YAC5C,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;YAC9B,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI;QACV;IACF;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,uBACP,GAA6B,EAC7B,KAAa,EACb,MAAc,EACd,UAAkB;IAElB,6CAA6C;IAC7C,MAAM,iBAAiB,IAAI,oBAAoB,CAAC,GAAG,GAAG,OAAO;IAC7D,eAAe,YAAY,CAAC,GAAG;IAC/B,eAAe,YAAY,CAAC,KAAK,YAAY,YAAY;IACzD,eAAe,YAAY,CAAC,GAAG;IAE/B,kCAAkC;IAClC,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,UAAU,CAAC,IAAI,IAAI,QAAQ,IAAI,SAAS;IAE5C,6CAA6C;IAC7C,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IACpB,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,UAAU,CAAC,IAAI,IAAI,QAAQ,KAAK,SAAS;IAE7C,eAAe;IACf,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IAEpB,8CAA8C;IAC9C,MAAM,aAAa;IAEnB,kBAAkB;IAClB,qBAAqB,KAAK,IAAI,IAAI,YAAY,YAAY;IAC1D,mBAAmB;IACnB,qBACE,KACA,QAAQ,IACR,IACA,YACA,YACA;IAEF,qBAAqB;IACrB,qBACE,KACA,IACA,SAAS,IACT,YACA,YACA;IAEF,sBAAsB;IACtB,qBACE,KACA,QAAQ,IACR,SAAS,IACT,YACA,YACA;IAGF,mDAAmD;IACnD,mBAAmB,KAAK,OAAO,QAAQ;AACzC;AAEA;;CAEC,GACD,SAAS,qBACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,IAAY,EACZ,KAAa,EACb,QAAmE;IAEnE,IAAI,IAAI;IAER,2CAA2C;IAC3C,MAAM,iBAAiB,IAAI,oBAAoB,CAC7C,SAAS,QAAQ,CAAC,UAAU,IAAI,IAAI,MACpC,SAAS,QAAQ,CAAC,SAAS,IAAI,IAAI,MACnC,SAAS,QAAQ,CAAC,UAAU,IAAI,OAAO,GACvC,SAAS,QAAQ,CAAC,SAAS,IAAI,OAAO;IAGxC,eAAe,YAAY,CAAC,GAAG;IAC/B,eAAe,YAAY,CAAC,GAAG,YAAY,OAAO;IAElD,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,OAAO,GAAG;IACd,IAAI,SAAS;IAEb,IAAI,aAAa,YAAY;QAC3B,IAAI,MAAM,CAAC,GAAG,IAAI;QAClB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,IAAI,MAAM;QAErB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C,OAAO,IAAI,aAAa,aAAa;QACnC,IAAI,MAAM,CAAC,IAAI,MAAM;QACrB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,GAAG,IAAI;QAElB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C,OAAO,IAAI,aAAa,eAAe;QACrC,IAAI,MAAM,CAAC,GAAG,IAAI;QAClB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,IAAI,MAAM;QAErB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C,OAAO,IAAI,aAAa,gBAAgB;QACtC,IAAI,MAAM,CAAC,IAAI,MAAM;QACrB,IAAI,MAAM,CAAC,GAAG;QACd,IAAI,MAAM,CAAC,GAAG,IAAI;QAElB,qBAAqB;QACrB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;IAC1C;IAEA,IAAI,MAAM;IAEV,2BAA2B;IAC3B,IAAI,SAAS,GAAG;IAChB,IAAI,aAAa,YAAY;QAC3B,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV,OAAO,IAAI,aAAa,aAAa;QACnC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV,OAAO,IAAI,aAAa,eAAe;QACrC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV,OAAO,IAAI,aAAa,gBAAgB;QACtC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,IAAI;IACV;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,uBACP,GAA6B,EAC7B,KAAa,EACb,MAAc,EACd,UAAkB;IAElB,sDAAsD;IACtD,IAAI,IAAI;IAER,0CAA0C;IAC1C,MAAM,kBAAkB,IAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG;IAChE,gBAAgB,YAAY,CAAC,GAAG;IAChC,gBAAgB,YAAY,CAAC,GAAG;IAEhC,MAAM,sBAAsB,IAAI,oBAAoB,CAClD,OACA,QACA,GACA,OACA,QACA;IAEF,oBAAoB,YAAY,CAAC,GAAG;IACpC,oBAAoB,YAAY,CAAC,GAAG;IAEpC,8BAA8B;IAC9B,IAAI,WAAW,GAAG;IAElB,uCAAuC;IACvC,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,KAAK,EAAE,GAAG;IAChC,IAAI,SAAS,GAAG;IAChB,IAAI,IAAI;IAER,2CAA2C;IAC3C,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,OAAO,QAAQ,KAAK,GAAG,KAAK,EAAE,GAAG;IACzC,IAAI,SAAS,GAAG;IAChB,IAAI,IAAI;IAER,0BAA0B;IAC1B,IAAI,WAAW,GAAG;IAElB,oCAAoC;IACpC,MAAM,cAAc;IACpB,MAAM,cAAc,KAAK,IAAI,CAAC,SAAS;IACvC,MAAM,cAAc,KAAK,IAAI,CAAC,QAAQ;IAEtC,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAO;QAC1C,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAO;YAC1C,qDAAqD;YACrD,MAAM,qBACJ,KAAK,GAAG,CAAC,MAAM,cAAc,KAAK,KAAK,GAAG,CAAC,MAAM,cAAc;YACjE,IAAI,qBAAqB,cAAc,GAAG;gBACxC,MAAM,IAAI,MAAM;gBAChB,MAAM,IAAI,MAAM;gBAEhB,8BAA8B;gBAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,GAAG;oBACzB,IAAI,SAAS;oBACb,IAAI,GAAG,CAAC,IAAI,cAAc,GAAG,IAAI,cAAc,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;oBAClE,IAAI,SAAS,GAAG;oBAChB,IAAI,IAAI;gBACV;YACF;QACF;IACF;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,oBACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,IAAY,EACZ,UAAkB;IAElB,MAAM,UAAU;IAChB,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,kBAAkB,OAAO,UAAU;IACzC,MAAM,aAAa,IAAI;IACvB,MAAM,aAAa,IAAI;IAEvB,6CAA6C;IAC7C,IAAI,SAAS,GAAG;IAChB,UAAU,KAAK,YAAY,YAAY,gBAAgB,iBAAiB;IACxE,IAAI,IAAI;IAER,qBAAqB;IACrB,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IACpB,IAAI,SAAS,GAAG;IAChB,UAAU,KAAK,YAAY,YAAY,gBAAgB,iBAAiB;IACxE,IAAI,IAAI;IAER,eAAe;IACf,IAAI,WAAW,GAAG;IAClB,IAAI,UAAU,GAAG;IACjB,IAAI,aAAa,GAAG;IACpB,IAAI,aAAa,GAAG;IAEpB,oCAAoC;IACpC,MAAM,iBAAiB,IAAI,oBAAoB,CAC7C,YACA,YACA,aAAa,gBACb,aAAa;IAEf,eAAe,YAAY,CAAC,GAAG;IAC/B,eAAe,YAAY,CAAC,KAAK,YAAY,YAAY;IACzD,eAAe,YAAY,CAAC,GAAG;IAE/B,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,UAAU,KAAK,YAAY,YAAY,gBAAgB,iBAAiB;IACxE,IAAI,MAAM;IAEV,qDAAqD;IACrD,MAAM,aAAa;IAEnB,6BAA6B;IAC7B,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,YAAY,aAAa;IACpC,IAAI,MAAM,CAAC,YAAY;IACvB,IAAI,MAAM,CAAC,aAAa,YAAY;IACpC,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,8BAA8B;IAC9B,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,aAAa,iBAAiB,YAAY;IACrD,IAAI,MAAM,CAAC,aAAa,gBAAgB;IACxC,IAAI,MAAM,CAAC,aAAa,gBAAgB,aAAa;IACrD,IAAI,MAAM;IAEV,gCAAgC;IAChC,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,YAAY,aAAa,kBAAkB;IACtD,IAAI,MAAM,CAAC,YAAY,aAAa;IACpC,IAAI,MAAM,CAAC,aAAa,YAAY,aAAa;IACjD,IAAI,MAAM;IAEV,iCAAiC;IACjC,IAAI,SAAS;IACb,IAAI,MAAM,CACR,aAAa,iBAAiB,YAC9B,aAAa;IAEf,IAAI,MAAM,CAAC,aAAa,gBAAgB,aAAa;IACrD,IAAI,MAAM,CACR,aAAa,gBACb,aAAa,kBAAkB;IAEjC,IAAI,MAAM;AACZ;AAEA;;CAEC,GACD,SAAS,YACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,KAAa,EACb,KAAa;IAEb,IAAI,IAAI;IAER,8BAA8B;IAC9B,MAAM,kBAAkB,IAAI,oBAAoB,CAC9C,IAAI,IAAI,QAAQ,GAChB,GACA,IAAI,IAAI,QAAQ,GAChB;IAEF,gBAAgB,YAAY,CAAC,GAAG;IAChC,gBAAgB,YAAY,CAAC,KAAK;IAClC,gBAAgB,YAAY,CAAC,KAAK,YAAY,OAAO;IACrD,gBAAgB,YAAY,CAAC,KAAK;IAClC,gBAAgB,YAAY,CAAC,GAAG;IAEhC,+BAA+B;IAC/B,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,GAAG;IAC9B,IAAI,MAAM,CAAC,IAAI,IAAI,QAAQ,GAAG;IAC9B,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,wCAAwC;IACxC,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,EAAE,GAAG;IACnC,IAAI,SAAS,GAAG;IAChB,IAAI,IAAI;IAER,2CAA2C;IAC3C,IAAI,SAAS;IACb,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,GAAG,KAAK,EAAE,GAAG;IACnC,IAAI,WAAW,GAAG;IAClB,IAAI,SAAS,GAAG;IAChB,IAAI,MAAM;IAEV,+CAA+C;IAC/C,MAAM,UAAU;IAChB,MAAM,aAAa,QAAQ,IAAI;IAE/B,iBAAiB;IACjB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAK;QACjC,MAAM,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;QACrC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QACjC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,kBAAkB;IAClB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,IAAK;QACjC,MAAM,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI;QACrC,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QACjC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,UACP,GAA6B,EAC7B,CAAS,EACT,CAAS,EACT,KAAa,EACb,MAAc,EACd,MAAc;IAEd,IAAI,SAAS;IACb,IAAI,MAAM,CAAC,IAAI,QAAQ;IACvB,IAAI,MAAM,CAAC,IAAI,QAAQ,QAAQ;IAC/B,IAAI,gBAAgB,CAAC,IAAI,OAAO,GAAG,IAAI,OAAO,IAAI;IAClD,IAAI,MAAM,CAAC,IAAI,OAAO,IAAI,SAAS;IACnC,IAAI,gBAAgB,CAAC,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,QAAQ,IAAI;IACpE,IAAI,MAAM,CAAC,IAAI,QAAQ,IAAI;IAC3B,IAAI,gBAAgB,CAAC,GAAG,IAAI,QAAQ,GAAG,IAAI,SAAS;IACpD,IAAI,MAAM,CAAC,GAAG,IAAI;IAClB,IAAI,gBAAgB,CAAC,GAAG,GAAG,IAAI,QAAQ;IACvC,IAAI,SAAS;AACf;AAEA;;CAEC,GACD,SAAS,YAAY,KAAa,EAAE,MAAc;IAChD,qBAAqB;IACrB,IAAI,GAAG,GAAG;IACV,IAAI,MAAM,UAAU,CAAC,MAAM;QACzB,IAAI,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI;QAChC,IAAI,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI;QAChC,IAAI,SAAS,MAAM,KAAK,CAAC,GAAG,IAAI;IAClC,OAAO;QACL,yBAAyB;QACzB,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,wBAAwB;IAClC;IAEA,oBAAoB;IACpB,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI;IAClC,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI;IAClC,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI;IAElC,sBAAsB;IACtB,OAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,OAAO,EAC1C,QAAQ,CAAC,IACT,QAAQ,CAAC,GAAG,OAAO,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM;AACzD;AAEA;;CAEC,GACD,SAAS,mBACP,GAA6B,EAC7B,KAAa,EACb,MAAc,EACd,UAAkB;IAElB,IAAI,IAAI;IACR,IAAI,WAAW,GAAG;IAElB,qBAAqB;IACrB,IAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,GAAI;QAC1C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QAC/B,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,wBAAwB;IACxB,IAAK,IAAI,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,GAAI;QAC1C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,GAAG,SAAS,IAAI,GAAG,GAAG,KAAK,EAAE,GAAG;QACxC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,sBAAsB;IACtB,IAAK,IAAI,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAI;QAC3C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QAC/B,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,uBAAuB;IACvB,IAAK,IAAI,IAAI,KAAK,IAAI,SAAS,KAAK,KAAK,GAAI;QAC3C,IAAI,SAAS;QACb,IAAI,GAAG,CAAC,QAAQ,IAAI,GAAG,GAAG,GAAG,KAAK,EAAE,GAAG;QACvC,IAAI,SAAS,GAAG;QAChB,IAAI,IAAI;IACV;IAEA,IAAI,OAAO;AACb;AAEA;;CAEC,GACD,SAAS,mBAAmB,IAAY,EAAE,eAAuB;IAC/D,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,QAAkB,EAAE;IAC1B,IAAI,cAAc;IAElB,MAAM,OAAO,CAAC,CAAC;QACb,IAAI,CAAC,cAAc,IAAI,EAAE,MAAM,IAAI,iBAAiB;YAClD,eAAe,CAAC,cAAc,MAAM,EAAE,IAAI;QAC5C,OAAO;YACL,MAAM,IAAI,CAAC;YACX,cAAc;QAChB;IACF;IAEA,IAAI,aAAa;QACf,MAAM,IAAI,CAAC;IACb;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2031, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/cardDownloader.ts"], "sourcesContent": ["/**\r\n * Utility functions to download business cards as PNG images\r\n * Uses modern-screenshot for HTML to image conversion with proper rounded corners\r\n */\r\n\r\nimport { domToPng } from 'modern-screenshot';\r\n\r\ninterface BusinessCardDownloadOptions {\r\n  businessName: string;\r\n  businessSlug: string;\r\n  quality?: number;\r\n  scale?: number;\r\n  preserveRoundedCorners?: boolean;\r\n}\r\n\r\n/**\r\n * Downloads the business card as a PNG image with proper rounded corners\r\n */\r\nexport async function downloadBusinessCardAsPNG(\r\n  cardElement: HTMLElement,\r\n  options: BusinessCardDownloadOptions\r\n): Promise<void> {\r\n  const { businessSlug, quality = 1, scale = 3, preserveRoundedCorners = true } = options;\r\n\r\n  try {\r\n    // Get the actual dimensions of the card element\r\n    const rect = cardElement.getBoundingClientRect();\r\n    console.log('Card element dimensions:', {\r\n      width: rect.width,\r\n      height: rect.height,\r\n      offsetWidth: cardElement.offsetWidth,\r\n      offsetHeight: cardElement.offsetHeight,\r\n      className: cardElement.className,\r\n      tagName: cardElement.tagName\r\n    });\r\n\r\n    // Ensure we have valid dimensions\r\n    if (rect.width === 0 || rect.height === 0) {\r\n      throw new Error('Card element has invalid dimensions');\r\n    }\r\n\r\n    // Generate high-quality PNG using modern-screenshot with proper settings for rounded corners\r\n    const dataUrl = await domToPng(cardElement, {\r\n      quality,\r\n      scale,\r\n      backgroundColor: 'transparent', // Use transparent background to preserve rounded corners\r\n      style: {\r\n        // Ensure consistent rendering and preserve rounded corners\r\n        transform: 'scale(1)',\r\n        transformOrigin: 'top left',\r\n        borderRadius: preserveRoundedCorners ? 'inherit' : '0',\r\n        // Ensure the element is properly sized\r\n        width: `${rect.width}px`,\r\n        height: `${rect.height}px`,\r\n        maxWidth: `${rect.width}px`,\r\n        maxHeight: `${rect.height}px`,\r\n        overflow: 'hidden',\r\n      },\r\n      // Use the actual rendered dimensions\r\n      width: rect.width,\r\n      height: rect.height,\r\n    });\r\n\r\n    // Create download link\r\n    const link = document.createElement('a');\r\n    link.download = `${businessSlug}-digital-card.png`;\r\n    link.href = dataUrl;\r\n    link.click();\r\n  } catch (error) {\r\n    console.error('Error downloading business card as PNG:', error);\r\n    throw new Error('Failed to download business card as PNG');\r\n  }\r\n}\r\n\r\n/**\r\n * Main function to download business card as PNG\r\n */\r\nexport async function downloadBusinessCard(\r\n  cardElement: HTMLElement,\r\n  options: BusinessCardDownloadOptions\r\n): Promise<void> {\r\n  return downloadBusinessCardAsPNG(cardElement, options);\r\n}\r\n\r\n/**\r\n * Helper function to find the business card element in the DOM\r\n * Prioritizes cards that are likely to have correct authentication context\r\n */\r\nexport function findBusinessCardElement(containerRef?: React.RefObject<HTMLElement>): HTMLElement | null {\r\n  // Try to find the card element using various selectors, prioritizing the most specific\r\n  const selectors = [\r\n    '[data-card-element]', // The actual business card component\r\n    '.business-card-preview',\r\n    '.business-card',\r\n    '#business-card',\r\n    '.card-preview',\r\n  ];\r\n\r\n  const container = containerRef?.current || document;\r\n  const candidates: HTMLElement[] = [];\r\n\r\n  // Collect all potential card elements\r\n  for (const selector of selectors) {\r\n    const elements = container.querySelectorAll(selector) as NodeListOf<HTMLElement>;\r\n    elements.forEach(element => {\r\n      const rect = element.getBoundingClientRect();\r\n      // Business card should have reasonable dimensions (not too large)\r\n      // Max width should be around 384px (max-w-sm) and height should be proportional\r\n      if (rect.width > 0 && rect.height > 0 && rect.width <= 500) {\r\n        candidates.push(element);\r\n      }\r\n    });\r\n  }\r\n\r\n  if (candidates.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  if (candidates.length === 1) {\r\n    return candidates[0];\r\n  }\r\n\r\n  // If multiple candidates, prioritize based on context\r\n  // 1. Prefer cards that are not in demo mode (check for unmasked data)\r\n  // 2. Prefer cards that are visible and not hidden\r\n  // 3. Prefer cards that are in the main content area (not in headers/footers)\r\n\r\n  const scoredCandidates = candidates.map(element => {\r\n    let score = 0;\r\n\r\n    // Check if the card has unmasked phone/email (indicates authenticated context)\r\n    const phoneElement = element.querySelector('a[href^=\"tel:\"]');\r\n    const emailElement = element.querySelector('a[href^=\"mailto:\"]');\r\n    const phoneText = phoneElement?.textContent || '';\r\n    const emailText = emailElement?.textContent || '';\r\n\r\n    // If phone/email don't contain asterisks, it's likely unmasked (authenticated)\r\n    if (phoneText && !phoneText.includes('*')) score += 10;\r\n    if (emailText && !emailText.includes('*')) score += 10;\r\n\r\n    // Prefer visible elements\r\n    const rect = element.getBoundingClientRect();\r\n    if (rect.top >= 0 && rect.left >= 0) score += 5;\r\n\r\n    // Prefer elements in main content areas (not in navigation or footer)\r\n    const isInNav = element.closest('nav, header, footer');\r\n    if (!isInNav) score += 5;\r\n\r\n    // Prefer larger cards (main content vs thumbnails)\r\n    if (rect.width > 300) score += 3;\r\n\r\n    return { element, score };\r\n  });\r\n\r\n  // Sort by score (highest first) and return the best candidate\r\n  scoredCandidates.sort((a, b) => b.score - a.score);\r\n  return scoredCandidates[0].element;\r\n}\r\n\r\n/**\r\n * Prepare card element for screenshot by temporarily adjusting styles\r\n */\r\nexport function prepareCardForScreenshot(cardElement: HTMLElement): () => void {\r\n  const originalStyles = {\r\n    transform: cardElement.style.transform,\r\n    transformOrigin: cardElement.style.transformOrigin,\r\n    position: cardElement.style.position,\r\n    zIndex: cardElement.style.zIndex,\r\n  };\r\n\r\n  // Apply screenshot-friendly styles\r\n  cardElement.style.transform = 'scale(1)';\r\n  cardElement.style.transformOrigin = 'top left';\r\n  cardElement.style.position = 'relative';\r\n  cardElement.style.zIndex = '1';\r\n\r\n  // Return cleanup function\r\n  return () => {\r\n    cardElement.style.transform = originalStyles.transform;\r\n    cardElement.style.transformOrigin = originalStyles.transformOrigin;\r\n    cardElement.style.position = originalStyles.position;\r\n    cardElement.style.zIndex = originalStyles.zIndex;\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;;AAaO,eAAe,0BACpB,WAAwB,EACxB,OAAoC;IAEpC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE,QAAQ,CAAC,EAAE,yBAAyB,IAAI,EAAE,GAAG;IAEhF,IAAI;QACF,gDAAgD;QAChD,MAAM,OAAO,YAAY,qBAAqB;QAC9C,QAAQ,GAAG,CAAC,4BAA4B;YACtC,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;YACnB,aAAa,YAAY,WAAW;YACpC,cAAc,YAAY,YAAY;YACtC,WAAW,YAAY,SAAS;YAChC,SAAS,YAAY,OAAO;QAC9B;QAEA,kCAAkC;QAClC,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG;YACzC,MAAM,IAAI,MAAM;QAClB;QAEA,6FAA6F;QAC7F,MAAM,UAAU,MAAM,CAAA,GAAA,sJAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YAC1C;YACA;YACA,iBAAiB;YACjB,OAAO;gBACL,2DAA2D;gBAC3D,WAAW;gBACX,iBAAiB;gBACjB,cAAc,yBAAyB,YAAY;gBACnD,uCAAuC;gBACvC,OAAO,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;gBACxB,QAAQ,GAAG,KAAK,MAAM,CAAC,EAAE,CAAC;gBAC1B,UAAU,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;gBAC3B,WAAW,GAAG,KAAK,MAAM,CAAC,EAAE,CAAC;gBAC7B,UAAU;YACZ;YACA,qCAAqC;YACrC,OAAO,KAAK,KAAK;YACjB,QAAQ,KAAK,MAAM;QACrB;QAEA,uBAAuB;QACvB,MAAM,OAAO,SAAS,aAAa,CAAC;QACpC,KAAK,QAAQ,GAAG,GAAG,aAAa,iBAAiB,CAAC;QAClD,KAAK,IAAI,GAAG;QACZ,KAAK,KAAK;IACZ,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,MAAM,IAAI,MAAM;IAClB;AACF;AAKO,eAAe,qBACpB,WAAwB,EACxB,OAAoC;IAEpC,OAAO,0BAA0B,aAAa;AAChD;AAMO,SAAS,wBAAwB,YAA2C;IACjF,uFAAuF;IACvF,MAAM,YAAY;QAChB;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY,cAAc,WAAW;IAC3C,MAAM,aAA4B,EAAE;IAEpC,sCAAsC;IACtC,KAAK,MAAM,YAAY,UAAW;QAChC,MAAM,WAAW,UAAU,gBAAgB,CAAC;QAC5C,SAAS,OAAO,CAAC,CAAA;YACf,MAAM,OAAO,QAAQ,qBAAqB;YAC1C,kEAAkE;YAClE,gFAAgF;YAChF,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,KAAK,IAAI,KAAK;gBAC1D,WAAW,IAAI,CAAC;YAClB;QACF;IACF;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO,UAAU,CAAC,EAAE;IACtB;IAEA,sDAAsD;IACtD,sEAAsE;IACtE,kDAAkD;IAClD,6EAA6E;IAE7E,MAAM,mBAAmB,WAAW,GAAG,CAAC,CAAA;QACtC,IAAI,QAAQ;QAEZ,+EAA+E;QAC/E,MAAM,eAAe,QAAQ,aAAa,CAAC;QAC3C,MAAM,eAAe,QAAQ,aAAa,CAAC;QAC3C,MAAM,YAAY,cAAc,eAAe;QAC/C,MAAM,YAAY,cAAc,eAAe;QAE/C,+EAA+E;QAC/E,IAAI,aAAa,CAAC,UAAU,QAAQ,CAAC,MAAM,SAAS;QACpD,IAAI,aAAa,CAAC,UAAU,QAAQ,CAAC,MAAM,SAAS;QAEpD,0BAA0B;QAC1B,MAAM,OAAO,QAAQ,qBAAqB;QAC1C,IAAI,KAAK,GAAG,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,SAAS;QAE9C,sEAAsE;QACtE,MAAM,UAAU,QAAQ,OAAO,CAAC;QAChC,IAAI,CAAC,SAAS,SAAS;QAEvB,mDAAmD;QACnD,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS;QAE/B,OAAO;YAAE;YAAS;QAAM;IAC1B;IAEA,8DAA8D;IAC9D,iBAAiB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;IACjD,OAAO,gBAAgB,CAAC,EAAE,CAAC,OAAO;AACpC;AAKO,SAAS,yBAAyB,WAAwB;IAC/D,MAAM,iBAAiB;QACrB,WAAW,YAAY,KAAK,CAAC,SAAS;QACtC,iBAAiB,YAAY,KAAK,CAAC,eAAe;QAClD,UAAU,YAAY,KAAK,CAAC,QAAQ;QACpC,QAAQ,YAAY,KAAK,CAAC,MAAM;IAClC;IAEA,mCAAmC;IACnC,YAAY,KAAK,CAAC,SAAS,GAAG;IAC9B,YAAY,KAAK,CAAC,eAAe,GAAG;IACpC,YAAY,KAAK,CAAC,QAAQ,GAAG;IAC7B,YAAY,KAAK,CAAC,MAAM,GAAG;IAE3B,0BAA0B;IAC1B,OAAO;QACL,YAAY,KAAK,CAAC,SAAS,GAAG,eAAe,SAAS;QACtD,YAAY,KAAK,CAAC,eAAe,GAAG,eAAe,eAAe;QAClE,YAAY,KAAK,CAAC,QAAQ,GAAG,eAAe,QAAQ;QACpD,YAAY,KAAK,CAAC,MAAM,GAAG,eAAe,MAAM;IAClD;AACF", "debugId": null}}]}