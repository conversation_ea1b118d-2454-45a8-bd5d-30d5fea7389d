{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_9dd07395._.js", "server/edge/chunks/node_modules_@supabase_auth-js_dist_module_17bbb6b5._.js", "server/edge/chunks/node_modules_@upstash_redis_b3b75fae._.js", "server/edge/chunks/node_modules_a5b8fa46._.js", "server/edge/chunks/[root-of-the-server]__c2258e89._.js", "server/edge/chunks/edge-wrapper_3918d6b0.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "PmcKF30SqCPkNYQmqp/KEb/JOOZXtLlhvlBDAdzzX24=", "__NEXT_PREVIEW_MODE_ID": "d483d17c1ec16a8f1eca7e6de532f843", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bb575ea06d29498badfd373a9f56a2ee25fcf018674ed2584603547d38d6a5af", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d1dd7f631931807a7b2710d7b2fc889d90de8cf85eacdb7548248e97728435ee"}}}, "instrumentation": null, "functions": {}}