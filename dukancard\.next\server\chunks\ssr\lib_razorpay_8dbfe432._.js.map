{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/services/subscription/types.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Types\r\n *\r\n * This file contains types for Razorpay subscription API requests and responses.\r\n */\r\n\r\nimport { RazorpaySubscription } from \"../../types/api\";\r\n\r\n// Razorpay API response type\r\nexport interface RazorpayApiResponse<T> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: unknown;\r\n}\r\n\r\n// Addon item interface\r\nexport interface AddonItem {\r\n  name: string;\r\n  amount: number;\r\n  currency: string;\r\n}\r\n\r\n// Addon interface\r\nexport interface Addon {\r\n  item: AddonItem;\r\n}\r\n\r\n// Create subscription parameters\r\nexport interface CreateSubscriptionParams {\r\n  plan_id: string;\r\n  total_count: number;\r\n  quantity?: number;\r\n  start_at?: number;\r\n  expire_by?: number;\r\n  customer_notify?: boolean;\r\n  addons?: Addon[];\r\n  offer_id?: string;\r\n  notes?: Record<string, string>;\r\n}\r\n\r\n// Subscription response type (re-exported for convenience)\r\nexport type { RazorpaySubscription };\r\n\r\n// Cancel subscription parameters\r\nexport interface CancelSubscriptionParams {\r\n  cancel_at_cycle_end?: 0 | 1;\r\n}\r\n\r\n// Pause subscription parameters\r\nexport interface PauseSubscriptionParams {\r\n  pause_at?: string; // \"now\" or \"cycle_end\" - not required for authenticated subscriptions\r\n}\r\n\r\n// Resume subscription parameters\r\nexport interface ResumeSubscriptionParams {\r\n  resume_at: string; // \"now\"\r\n}\r\n\r\n// Update subscription parameters\r\nexport interface UpdateSubscriptionParams {\r\n  plan_id?: string;\r\n  quantity?: number;\r\n  remaining_count?: number;\r\n  schedule_change_at?: string; // \"now\" or \"cycle_end\"\r\n  customer_notify?: boolean;\r\n  addons?: Addon[];\r\n  notes?: Record<string, string>;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/utils/auth.ts"], "sourcesContent": ["import crypto from 'crypto';\r\n\r\n// Razorpay API URLs\r\nexport const RAZORPAY_API_URL = process.env.NODE_ENV === 'production'\r\n  ? 'https://api.razorpay.com/v2'\r\n  : 'https://api.razorpay.com/v2';\r\n\r\n/**\r\n * Get Razorpay authentication credentials\r\n * @returns The Razorpay key ID and secret\r\n */\r\nexport const getRazorpayAuth = () => {\r\n  // Use different keys for production and development environments\r\n  let keyId: string | undefined;\r\n  let keySecret: string | undefined;\r\n\r\n  if (process.env.NODE_ENV === 'production') {\r\n    // Use production keys\r\n    keyId = process.env.RAZORPAY_LIVE_KEY_ID || '***********************';\r\n    keySecret = process.env.RAZORPAY_LIVE_SECRET_KEY || 'ZE2AurACFXhx0b1sQaLG4YfQ';\r\n  } else {\r\n    // Use test keys\r\n    keyId = process.env.RAZORPAY_KEY_ID || 'rzp_test_ksxy6FklIhV1xC';\r\n    // Check for both possible environment variable names for the secret\r\n    keySecret = process.env.RAZORPAY_KEY_SECRET || process.env.RAZORPAY_SECRET_KEY || 'ZE2AurACFXhx0b1sQaLG4YfQ';\r\n  }\r\n\r\n  if (!keyId || !keySecret) {\r\n    console.error('[RAZORPAY] Missing credentials:', {\r\n      keyId: !!keyId,\r\n      keySecret: !!keySecret,\r\n      env: process.env.NODE_ENV\r\n    });\r\n    throw new Error('Razorpay credentials not configured');\r\n  }\r\n\r\n  return { keyId, keySecret };\r\n};\r\n\r\n/**\r\n * Get Razorpay API headers with authentication\r\n * @returns The headers for Razorpay API requests\r\n */\r\nexport const getRazorpayApiHeaders = () => {\r\n  const { keyId, keySecret } = getRazorpayAuth();\r\n\r\n  // Create Basic Auth header\r\n  const authString = Buffer.from(`${keyId}:${keySecret}`).toString('base64');\r\n\r\n  return {\r\n    'Authorization': `Basic ${authString}`,\r\n    'Content-Type': 'application/json'\r\n  };\r\n};\r\n\r\n/**\r\n * Verify Razorpay webhook signature\r\n *\r\n * Razorpay uses HMAC SHA256 for webhook signatures\r\n *\r\n * @param payload The webhook payload as a string\r\n * @param signature The webhook signature from the headers\r\n * @param secret The webhook secret from environment variables\r\n * @returns Whether the signature is valid\r\n */\r\nexport const verifyWebhookSignature = (\r\n  payload: string,\r\n  signature: string,\r\n  secret: string\r\n): boolean => {\r\n  try {\r\n    // Razorpay uses HMAC SHA256 for webhook signatures\r\n    const expectedSignature = crypto\r\n      .createHmac('sha256', secret)\r\n      .update(payload)\r\n      .digest('hex');\r\n\r\n    // Use timing-safe comparison to prevent timing attacks\r\n    return crypto.timingSafeEqual(\r\n      Buffer.from(signature),\r\n      Buffer.from(expectedSignature)\r\n    );\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_WEBHOOK] Error verifying webhook signature:', error);\r\n    return false;\r\n  }\r\n};\r\n\r\n/**\r\n * Verify Razorpay credentials by making a test API call\r\n * @returns Whether the credentials are valid\r\n */\r\nexport const verifyRazorpayCredentials = async (): Promise<boolean> => {\r\n  try {\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Make a simple API call to verify credentials\r\n    const response = await fetch(`${RAZORPAY_API_URL}/customers`, {\r\n      method: 'GET',\r\n      headers\r\n    });\r\n\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error('[RAZORPAY] Error verifying credentials:', error);\r\n    return false;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGO,MAAM,mBAAmB,6EAE5B;AAMG,MAAM,kBAAkB;IAC7B,iEAAiE;IACjE,IAAI;IACJ,IAAI;IAEJ,uCAA2C;;IAI3C,OAAO;QACL,gBAAgB;QAChB,QAAQ,QAAQ,GAAG,CAAC,eAAe,IAAI;QACvC,oEAAoE;QACpE,YAAY,QAAQ,GAAG,CAAC,mBAAmB,IAAI,QAAQ,GAAG,CAAC,mBAAmB,IAAI;IACpF;IAEA,IAAI,CAAC,SAAS,CAAC,WAAW;QACxB,QAAQ,KAAK,CAAC,mCAAmC;YAC/C,OAAO,CAAC,CAAC;YACT,WAAW,CAAC,CAAC;YACb,GAAG;QACL;QACA,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAO;IAAU;AAC5B;AAMO,MAAM,wBAAwB;IACnC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG;IAE7B,2BAA2B;IAC3B,MAAM,aAAa,OAAO,IAAI,CAAC,GAAG,MAAM,CAAC,EAAE,WAAW,EAAE,QAAQ,CAAC;IAEjE,OAAO;QACL,iBAAiB,CAAC,MAAM,EAAE,YAAY;QACtC,gBAAgB;IAClB;AACF;AAYO,MAAM,yBAAyB,CACpC,SACA,WACA;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,oBAAoB,qGAAA,CAAA,UAAM,CAC7B,UAAU,CAAC,UAAU,QACrB,MAAM,CAAC,SACP,MAAM,CAAC;QAEV,uDAAuD;QACvD,OAAO,qGAAA,CAAA,UAAM,CAAC,eAAe,CAC3B,OAAO,IAAI,CAAC,YACZ,OAAO,IAAI,CAAC;IAEhB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yDAAyD;QACvE,OAAO;IACT;AACF;AAMO,MAAM,4BAA4B;IACvC,IAAI;QACF,MAAM,UAAU;QAEhB,+CAA+C;QAC/C,MAAM,WAAW,MAAM,MAAM,GAAG,iBAAiB,UAAU,CAAC,EAAE;YAC5D,QAAQ;YACR;QACF;QAEA,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/services/subscription/create.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Creation\r\n *\r\n * This file contains functions for creating Razorpay subscriptions.\r\n */\r\n\r\nimport { RAZORPAY_API_URL, getRazorpayApiHeaders } from \"../../utils/auth\";\r\nimport { CreateSubscriptionParams, RazorpayApiResponse, RazorpaySubscription } from \"./types\";\r\n\r\n/**\r\n * Create a subscription in Razorpay\r\n *\r\n * API Endpoint: POST https://api.razorpay.com/v1/subscriptions\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param params The subscription parameters\r\n * @returns The created subscription or error\r\n *\r\n * Example usage:\r\n * ```\r\n * const result = await createSubscription({\r\n *   plan_id: 'plan_00000000000001',\r\n *   total_count: 12,\r\n *   quantity: 1,\r\n *   customer_notify: true,\r\n *   notes: {\r\n *     note_key_1: 'Note value 1',\r\n *     note_key_2: 'Note value 2'\r\n *   }\r\n * });\r\n * ```\r\n */\r\nexport async function createSubscription(\r\n  params: CreateSubscriptionParams\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  try {\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Make API request\r\n    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions`, {\r\n      method: 'POST',\r\n      headers: {\r\n        ...headers,\r\n        'Content-Type': 'application/json'\r\n      },\r\n      body: JSON.stringify(params)\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error creating subscription:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception creating subscription:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AA4BO,eAAe,mBACpB,MAAgC;IAEhC,IAAI;QAEF,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,cAAc,CAAC,EAAE;YACtF,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAGA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/services/subscription/get.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Retrieval\r\n *\r\n * This file contains functions for fetching Razorpay subscription details.\r\n */\r\n\r\nimport { RAZORPAY_API_URL, getRazorpayApiHeaders } from \"../../utils/auth\";\r\nimport { RazorpayApiResponse, RazorpaySubscription } from \"./types\";\r\n\r\n/**\r\n * Get a subscription from Razorpay\r\n *\r\n * API Endpoint: GET https://api.razorpay.com/v1/subscriptions/:id\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param subscriptionId The subscription ID\r\n * @returns The subscription details or error\r\n */\r\nexport async function getSubscription(\r\n  subscriptionId: string\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  try {\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Make API request\r\n    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}`, {\r\n      method: 'GET',\r\n      headers\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error fetching subscription:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception fetching subscription:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * List all subscriptions from Razorpay\r\n *\r\n * API Endpoint: GET https://api.razorpay.com/v1/subscriptions\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param params Optional query parameters (count, skip, etc.)\r\n * @returns List of subscriptions or error\r\n */\r\nexport async function listSubscriptions(\r\n  params: Record<string, string | number> = {}\r\n): Promise<RazorpayApiResponse<{ entity: string; count: number; items: RazorpaySubscription[] }>> {\r\n  try {\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Build query string\r\n    const queryString = Object.entries(params)\r\n      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)\r\n      .join('&');\r\n\r\n    // Make API request\r\n    const url = `${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions${queryString ? `?${queryString}` : ''}`;\r\n    const response = await fetch(url, {\r\n      method: 'GET',\r\n      headers\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error fetching subscriptions:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception fetching subscriptions:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;;AAcO,eAAe,gBACpB,cAAsB;IAEtB,IAAI;QAEF,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,eAAe,EAAE,gBAAgB,EAAE;YACxG,QAAQ;YACR;QACF;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAGA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF;AAaO,eAAe,kBACpB,SAA0C,CAAC,CAAC;IAE5C,IAAI;QAEF,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,qBAAqB;QACrB,MAAM,cAAc,OAAO,OAAO,CAAC,QAChC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,mBAAmB,QAAQ,EAC3D,IAAI,CAAC;QAER,mBAAmB;QACnB,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,cAAc,EAAE,cAAc,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;QAC5G,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR;QACF;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,kDAAkD;YAChE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAGA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;QACpE,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/services/subscription/cancel.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Cancellation\r\n *\r\n * This file contains functions for cancelling Razorpay subscriptions.\r\n */\r\n\r\nimport { RAZORPAY_API_URL, getRazorpayApiHeaders } from \"../../utils/auth\";\r\nimport { RazorpayApiResponse, RazorpaySubscription, CancelSubscriptionParams } from \"./types\";\r\n\r\n/**\r\n * Cancel a subscription in Razorpay\r\n *\r\n * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/cancel\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param subscriptionId The subscription ID\r\n * @param cancelAtCycleEnd Whether to cancel at the end of the current billing cycle\r\n * @returns The cancelled subscription or error\r\n */\r\nexport async function cancelSubscription(\r\n  subscriptionId: string,\r\n  cancelAtCycleEnd: boolean = false\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  try {\r\n    console.log(`[RAZORPAY_DEBUG] Cancelling subscription with ID: ${subscriptionId}`);\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Prepare request body\r\n    const body: CancelSubscriptionParams = cancelAtCycleEnd ? { cancel_at_cycle_end: 1 } : {};\r\n\r\n    // Make API request\r\n    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/cancel`, {\r\n      method: 'POST',\r\n      headers: {\r\n        ...headers,\r\n        'Content-Type': 'application/json'\r\n      },\r\n      body: JSON.stringify(body)\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error cancelling subscription:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n    console.log(`[RAZORPAY_DEBUG] Successfully cancelled subscription: ${data.id}`);\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception cancelling subscription:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AAED;;AAeO,eAAe,mBACpB,cAAsB,EACtB,mBAA4B,KAAK;IAEjC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,gBAAgB;QAEjF,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,uBAAuB;QACvB,MAAM,OAAiC,mBAAmB;YAAE,qBAAqB;QAAE,IAAI,CAAC;QAExF,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,eAAe,EAAE,eAAe,OAAO,CAAC,EAAE;YAC/G,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,mDAAmD;YACjE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAEA,QAAQ,GAAG,CAAC,CAAC,sDAAsD,EAAE,KAAK,EAAE,EAAE;QAC9E,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uDAAuD;QACrE,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/services/subscription/update.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Update\r\n *\r\n * This file contains functions for updating Razorpay subscriptions.\r\n */\r\n\r\nimport { RAZORPAY_API_URL, getRazorpayApiHeaders } from \"../../utils/auth\";\r\nimport { RazorpayApiResponse, RazorpaySubscription, UpdateSubscriptionParams, PauseSubscriptionParams, ResumeSubscriptionParams } from \"./types\";\r\n\r\n/**\r\n * Update a subscription in Razorpay\r\n *\r\n * API Endpoint: PATCH https://api.razorpay.com/v1/subscriptions/:id\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param subscriptionId The subscription ID\r\n * @param params The update parameters\r\n * @returns The updated subscription or error\r\n */\r\nexport async function updateSubscription(\r\n  subscriptionId: string,\r\n  params: UpdateSubscriptionParams\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  try {\r\n    console.log(`[RAZORPAY_DEBUG] Updating subscription with ID: ${subscriptionId}`);\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Make API request\r\n    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}`, {\r\n      method: 'PATCH',\r\n      headers: {\r\n        ...headers,\r\n        'Content-Type': 'application/json'\r\n      },\r\n      body: JSON.stringify(params)\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error updating subscription:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n    console.log(`[RAZORPAY_DEBUG] Successfully updated subscription: ${data.id}`);\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception updating subscription:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Pause a subscription in Razorpay\r\n *\r\n * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/pause\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param subscriptionId The subscription ID\r\n * @param pauseAt When to pause the subscription (\"now\" or \"cycle_end\")\r\n * @param isAuthenticated Whether the subscription is in authenticated state\r\n * @returns The paused subscription or error\r\n */\r\nexport async function pauseSubscription(\r\n  subscriptionId: string,\r\n  pauseAt: \"now\" | \"cycle_end\" = \"now\",\r\n  isAuthenticated: boolean = false\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  const maxRetries = 3;\r\n  let lastError: unknown;\r\n\r\n  for (let attempt = 1; attempt <= maxRetries; attempt++) {\r\n    try {\r\n      console.log(`[RAZORPAY_DEBUG] Pausing subscription with ID: ${subscriptionId} (attempt ${attempt}/${maxRetries})`);\r\n\r\n      // Get API headers\r\n      const headers = getRazorpayApiHeaders();\r\n\r\n      // Prepare request body - for authenticated subscriptions, don't send pause_at\r\n      const body: PauseSubscriptionParams = isAuthenticated ? {} : { pause_at: pauseAt };\r\n\r\n      console.log(`[RAZORPAY_DEBUG] Request body for pause:`, body);\r\n\r\n      // Make API request with timeout\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout\r\n\r\n      const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/pause`, {\r\n        method: 'POST',\r\n        headers: {\r\n          ...headers,\r\n          'Content-Type': 'application/json'\r\n        },\r\n        body: JSON.stringify(body),\r\n        signal: controller.signal\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      // Parse response\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        console.error(`[RAZORPAY_ERROR] Error pausing subscription (attempt ${attempt}):`, data);\r\n\r\n        // If it's a client error (4xx), don't retry\r\n        if (response.status >= 400 && response.status < 500) {\r\n          return { success: false, error: data };\r\n        }\r\n\r\n        // For server errors (5xx), retry\r\n        lastError = data;\r\n        if (attempt === maxRetries) {\r\n          return { success: false, error: data };\r\n        }\r\n\r\n        // Wait before retrying (exponential backoff)\r\n        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s\r\n        console.log(`[RAZORPAY_DEBUG] Retrying in ${delay}ms...`);\r\n        await new Promise(resolve => setTimeout(resolve, delay));\r\n        continue;\r\n      }\r\n\r\n      console.log(`[RAZORPAY_DEBUG] Successfully paused subscription: ${data.id}`);\r\n      return { success: true, data };\r\n    } catch (error) {\r\n      console.error(`[RAZORPAY_ERROR] Exception pausing subscription (attempt ${attempt}):`, error);\r\n      lastError = error;\r\n\r\n      // If it's an abort error, don't retry\r\n      if (error instanceof Error && error.name === 'AbortError') {\r\n        return {\r\n          success: false,\r\n          error: {\r\n            message: 'Request timeout - please try again',\r\n            code: 'TIMEOUT_ERROR',\r\n            type: 'EXCEPTION'\r\n          }\r\n        };\r\n      }\r\n\r\n      // For network errors, retry\r\n      if (attempt < maxRetries) {\r\n        const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s\r\n        console.log(`[RAZORPAY_DEBUG] Network error, retrying in ${delay}ms...`);\r\n        await new Promise(resolve => setTimeout(resolve, delay));\r\n        continue;\r\n      }\r\n    }\r\n  }\r\n\r\n  // If all retries failed\r\n  return {\r\n    success: false,\r\n    error: {\r\n      message: lastError instanceof Error ? lastError.message : 'Network error after multiple retries',\r\n      code: 'NETWORK_ERROR',\r\n      type: 'EXCEPTION'\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Resume a subscription in Razorpay\r\n *\r\n * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/resume\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * Request Parameters:\r\n * - resume_at: \"now\" (The value should be now to resume a Subscription immediately)\r\n *\r\n * @param subscriptionId The subscription ID\r\n * @returns The resumed subscription or error\r\n *\r\n * Example response:\r\n * ```json\r\n * {\r\n *   \"id\": \"sub_00000000000001\",\r\n *   \"entity\": \"subscription\",\r\n *   \"plan_id\": \"plan_00000000000001\",\r\n *   \"status\": \"active\",\r\n *   \"current_start\": null,\r\n *   \"current_end\": null,\r\n *   \"ended_at\": null,\r\n *   \"quantity\": 1,\r\n *   \"notes\": {\r\n *     \"notes_key_1\": \"Tea, Earl Grey, Hot\",\r\n *     \"notes_key_2\": \"Tea, Earl Grey… decaf.\"\r\n *   },\r\n *   \"charge_at\": 1580453311,\r\n *   \"start_at\": 1580626111,\r\n *   \"end_at\": 1583433000,\r\n *   \"auth_attempts\": 0,\r\n *   \"total_count\": 6,\r\n *   \"paid_count\": 0,\r\n *   \"customer_notify\": true,\r\n *   \"created_at\": 1580280581,\r\n *   \"paused_at\": 1590280581,\r\n *   \"expire_by\": 1580626111,\r\n *   \"pause_initiated_by\": null,\r\n *   \"short_url\": \"https://rzp.io/i/z3b1R61A9\",\r\n *   \"has_scheduled_changes\": false,\r\n *   \"change_scheduled_at\": null,\r\n *   \"source\": \"api\",\r\n *   \"offer_id\": \"offer_JHD834hjbxzhd38d\",\r\n *   \"remaining_count\": 6\r\n * }\r\n * ```\r\n */\r\nexport async function resumeSubscription(\r\n  subscriptionId: string\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  try {\r\n    console.log(`[RAZORPAY_DEBUG] Resuming subscription with ID: ${subscriptionId}`);\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Prepare request body\r\n    const body: ResumeSubscriptionParams = { resume_at: \"now\" };\r\n\r\n    // Make API request\r\n    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/resume`, {\r\n      method: 'POST',\r\n      headers: {\r\n        ...headers,\r\n        'Content-Type': 'application/json'\r\n      },\r\n      body: JSON.stringify(body)\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error resuming subscription:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n    console.log(`[RAZORPAY_DEBUG] Successfully resumed subscription: ${data.id}`);\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception resuming subscription:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;AAED;;AAeO,eAAe,mBACpB,cAAsB,EACtB,MAAgC;IAEhC,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,gBAAgB;QAE/E,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,eAAe,EAAE,gBAAgB,EAAE;YACxG,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAEA,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,KAAK,EAAE,EAAE;QAC5E,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF;AAeO,eAAe,kBACpB,cAAsB,EACtB,UAA+B,KAAK,EACpC,kBAA2B,KAAK;IAEhC,MAAM,aAAa;IACnB,IAAI;IAEJ,IAAK,IAAI,UAAU,GAAG,WAAW,YAAY,UAAW;QACtD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,eAAe,UAAU,EAAE,QAAQ,CAAC,EAAE,WAAW,CAAC,CAAC;YAEjH,kBAAkB;YAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;YAEpC,8EAA8E;YAC9E,MAAM,OAAgC,kBAAkB,CAAC,IAAI;gBAAE,UAAU;YAAQ;YAEjF,QAAQ,GAAG,CAAC,CAAC,wCAAwC,CAAC,EAAE;YAExD,gCAAgC;YAChC,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,QAAQ,oBAAoB;YAEnF,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,eAAe,EAAE,eAAe,MAAM,CAAC,EAAE;gBAC9G,QAAQ;gBACR,SAAS;oBACP,GAAG,OAAO;oBACV,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;gBACrB,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YAEb,iBAAiB;YACjB,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,QAAQ,EAAE,CAAC,EAAE;gBAEnF,4CAA4C;gBAC5C,IAAI,SAAS,MAAM,IAAI,OAAO,SAAS,MAAM,GAAG,KAAK;oBACnD,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAK;gBACvC;gBAEA,iCAAiC;gBACjC,YAAY;gBACZ,IAAI,YAAY,YAAY;oBAC1B,OAAO;wBAAE,SAAS;wBAAO,OAAO;oBAAK;gBACvC;gBAEA,6CAA6C;gBAC7C,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,UAAU,KAAK,MAAM,aAAa;gBAC5D,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,MAAM,KAAK,CAAC;gBACxD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,mDAAmD,EAAE,KAAK,EAAE,EAAE;YAC3E,OAAO;gBAAE,SAAS;gBAAM;YAAK;QAC/B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yDAAyD,EAAE,QAAQ,EAAE,CAAC,EAAE;YACvF,YAAY;YAEZ,sCAAsC;YACtC,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,OAAO;oBACL,SAAS;oBACT,OAAO;wBACL,SAAS;wBACT,MAAM;wBACN,MAAM;oBACR;gBACF;YACF;YAEA,4BAA4B;YAC5B,IAAI,UAAU,YAAY;gBACxB,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,UAAU,KAAK,MAAM,aAAa;gBAC5D,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,MAAM,KAAK,CAAC;gBACvE,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD;YACF;QACF;IACF;IAEA,wBAAwB;IACxB,OAAO;QACL,SAAS;QACT,OAAO;YACL,SAAS,qBAAqB,QAAQ,UAAU,OAAO,GAAG;YAC1D,MAAM;YACN,MAAM;QACR;IACF;AACF;AAmDO,eAAe,mBACpB,cAAsB;IAEtB,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,gBAAgB;QAE/E,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,uBAAuB;QACvB,MAAM,OAAiC;YAAE,WAAW;QAAM;QAE1D,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,eAAe,EAAE,eAAe,OAAO,CAAC,EAAE;YAC/G,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,iDAAiD;YAC/D,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAEA,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,KAAK,EAAE,EAAE;QAC5E,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 483, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/services/subscription/scheduled.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Scheduled Changes\r\n *\r\n * This file contains functions for managing scheduled changes to Razorpay subscriptions.\r\n */\r\n\r\nimport { RAZORPAY_API_URL, getRazorpayApiHeaders } from \"../../utils/auth\";\r\nimport { RazorpayApiResponse, RazorpaySubscription } from \"./types\";\r\n\r\n/**\r\n * Retrieve scheduled changes for a subscription in Razorpay\r\n *\r\n * API Endpoint: GET https://api.razorpay.com/v1/subscriptions/:id/retrieve_scheduled_changes\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param subscriptionId The subscription ID\r\n * @returns The subscription with scheduled changes or error\r\n *\r\n * Example response:\r\n * ```json\r\n * {\r\n *   \"id\":\"sub_00000000000001\",\r\n *   \"entity\":\"subscription\",\r\n *   \"plan_id\":\"plan_00000000000003\",\r\n *   \"customer_id\":\"cust_00000000000001\",\r\n *   \"status\":\"active\",\r\n *   \"current_start\":1580284732,\r\n *   \"current_end\":1580841000,\r\n *   \"ended_at\":null,\r\n *   \"quantity\":25,\r\n *   \"notes\":{\r\n *     \"notes_key_1\":\"<PERSON>, <PERSON>, <PERSON>\",\r\n *     \"notes_key_2\":\"<PERSON>, <PERSON>… decaf.\"\r\n *   },\r\n *   \"charge_at\":1580841000,\r\n *   \"start_at\":1580284732,\r\n *   \"end_at\":1611081000,\r\n *   \"auth_attempts\":0,\r\n *   \"total_count\":6,\r\n *   \"paid_count\":1,\r\n *   \"customer_notify\":true,\r\n *   \"created_at\":1580284702,\r\n *   \"expire_by\":1580626111,\r\n *   \"short_url\":\"https://rzp.io/i/fFWTkbf\",\r\n *   \"has_scheduled_changes\":true,\r\n *   \"change_scheduled_at\":1557253800,\r\n *   \"source\": \"api\",\r\n *   \"offer_id\":\"offer_JHD834hjbxzhd38d\",\r\n *   \"remaining_count\":5\r\n * }\r\n * ```\r\n */\r\nexport async function getScheduledChanges(\r\n  subscriptionId: string\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  try {\r\n    console.log(`[RAZORPAY_DEBUG] Fetching scheduled changes for subscription with ID: ${subscriptionId}`);\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Make API request\r\n    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/retrieve_scheduled_changes`, {\r\n      method: 'GET',\r\n      headers\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error fetching scheduled changes:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n    console.log(`[RAZORPAY_DEBUG] Successfully fetched scheduled changes for subscription: ${data.id}`);\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception fetching scheduled changes:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Cancel scheduled changes for a subscription in Razorpay\r\n *\r\n * API Endpoint: POST https://api.razorpay.com/v1/subscriptions/:id/cancel_scheduled_changes\r\n *\r\n * Required Headers:\r\n * - Authorization: Basic base64(key_id:key_secret)\r\n *\r\n * @param subscriptionId The subscription ID\r\n * @returns The subscription with cancelled scheduled changes or error\r\n *\r\n * Example response:\r\n * ```json\r\n * {\r\n *   \"id\": \"sub_00000000000001\",\r\n *   \"entity\": \"subscription\",\r\n *   \"plan_id\": \"plan_00000000000001\",\r\n *   \"customer_id\": \"cust_00000000000001\",\r\n *   \"status\": \"active\",\r\n *   \"current_start\": 1580284732,\r\n *   \"current_end\": 1580841000,\r\n *   \"ended_at\": null,\r\n *   \"quantity\": 1,\r\n *   \"notes\": {\r\n *     \"notes_key_1\": \"Tea, Earl Grey, Hot\",\r\n *     \"notes_key_2\": \"Tea, Earl Grey… decaf.\"\r\n *   },\r\n *   \"charge_at\": 1580841000,\r\n *   \"start_at\": 1580284732,\r\n *   \"end_at\": 1611081000,\r\n *   \"auth_attempts\": 0,\r\n *   \"total_count\": 6,\r\n *   \"paid_count\": 1,\r\n *   \"customer_notify\": true,\r\n *   \"created_at\": 1580284702,\r\n *   \"expire_by\": 1580626111,\r\n *   \"short_url\": \"https://rzp.io/i/fFWTkbf\",\r\n *   \"has_scheduled_changes\": false,\r\n *   \"change_scheduled_at\": null,\r\n *   \"source\": \"api\",\r\n *   \"offer_id\": \"offer_JHD834hjbxzhd38d\",\r\n *   \"remaining_count\": 5\r\n * }\r\n * ```\r\n */\r\nexport async function cancelScheduledChanges(\r\n  subscriptionId: string\r\n): Promise<RazorpayApiResponse<RazorpaySubscription>> {\r\n  try {\r\n    console.log(`[RAZORPAY_DEBUG] Cancelling scheduled changes for subscription with ID: ${subscriptionId}`);\r\n\r\n    // Get API headers\r\n    const headers = getRazorpayApiHeaders();\r\n\r\n    // Make API request\r\n    const response = await fetch(`${RAZORPAY_API_URL.replace('/v2', '/v1')}/subscriptions/${subscriptionId}/cancel_scheduled_changes`, {\r\n      method: 'POST',\r\n      headers: {\r\n        ...headers,\r\n        'Content-Type': 'application/json'\r\n      },\r\n      body: JSON.stringify({})\r\n    });\r\n\r\n    // Parse response\r\n    const data = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error('[RAZORPAY_ERROR] Error cancelling scheduled changes:', data);\r\n      return { success: false, error: data };\r\n    }\r\n\r\n    console.log(`[RAZORPAY_DEBUG] Successfully cancelled scheduled changes for subscription: ${data.id}`);\r\n    return { success: true, data };\r\n  } catch (error) {\r\n    console.error('[RAZORPAY_ERROR] Exception cancelling scheduled changes:', error);\r\n    return {\r\n      success: false,\r\n      error: {\r\n        message: error instanceof Error ? error.message : 'Unknown error occurred',\r\n        code: 'UNKNOWN_ERROR',\r\n        type: 'EXCEPTION'\r\n      }\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;;AAgDO,eAAe,oBACpB,cAAsB;IAEtB,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,sEAAsE,EAAE,gBAAgB;QAErG,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,eAAe,EAAE,eAAe,2BAA2B,CAAC,EAAE;YACnI,QAAQ;YACR;QACF;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,sDAAsD;YACpE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAEA,QAAQ,GAAG,CAAC,CAAC,0EAA0E,EAAE,KAAK,EAAE,EAAE;QAClG,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0DAA0D;QACxE,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF;AA+CO,eAAe,uBACpB,cAAsB;IAEtB,IAAI;QACF,QAAQ,GAAG,CAAC,CAAC,wEAAwE,EAAE,gBAAgB;QAEvG,kBAAkB;QAClB,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD;QAEpC,mBAAmB;QACnB,MAAM,WAAW,MAAM,MAAM,GAAG,gIAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC,OAAO,OAAO,eAAe,EAAE,eAAe,yBAAyB,CAAC,EAAE;YACjI,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;QAEA,iBAAiB;QACjB,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,wDAAwD;YACtE,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAK;QACvC;QAEA,QAAQ,GAAG,CAAC,CAAC,4EAA4E,EAAE,KAAK,EAAE,EAAE;QACpG,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4DAA4D;QAC1E,OAAO;YACL,SAAS;YACT,OAAO;gBACL,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,MAAM;gBACN,MAAM;YACR;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/services/subscription/index.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Service\r\n *\r\n * This file re-exports all subscription-related functions and types.\r\n */\r\n\r\n// Re-export types\r\nexport * from \"./types\";\r\n\r\n// Re-export create functions\r\nexport { createSubscription } from \"./create\";\r\n\r\n// Re-export get functions\r\nexport { getSubscription, listSubscriptions } from \"./get\";\r\n\r\n// Re-export cancel functions\r\nexport { cancelSubscription } from \"./cancel\";\r\n\r\n// Re-export update functions\r\nexport { updateSubscription, pauseSubscription, resumeSubscription } from \"./update\";\r\n\r\n// Re-export scheduled changes functions\r\nexport { getScheduledChanges, cancelScheduledChanges } from \"./scheduled\";\r\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,kBAAkB;;AAClB;AAEA,6BAA6B;AAC7B;AAEA,0BAA0B;AAC1B;AAEA,6BAA6B;AAC7B;AAEA,6BAA6B;AAC7B;AAEA,wCAAwC;AACxC", "debugId": null}}]}