module.exports = {

"[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/next/headers.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/lib_fd498647._.js",
  "server/chunks/ssr/lib_razorpay_webhooks_eec17b82._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript)");
    });
});
}}),

};