(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/lib/razorpay/services/subscription/index.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_next_dist_compiled_76d6f497._.js",
  "static/chunks/lib_razorpay_e7f53f3d._.js",
  "static/chunks/lib_razorpay_services_subscription_index_ts_28d33b9c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/razorpay/services/subscription/index.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/lib/config/plans.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/lib_config_plans_ts_5503ede3._.js",
  "static/chunks/lib_config_plans_ts_a3d06f7a._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/config/plans.ts [app-client] (ecmascript)");
    });
});
}}),
}]);