{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 246, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-pointer items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-pointer items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 508, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 565, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAMsB,cAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/MinimalHeader.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\n// Removed unused imports: useRout<PERSON>, createClient\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { LogOut } from \"lucide-react\";\r\n// Removed unused ThemeToggle import\r\nimport { signOutUser } from \"@/app/auth/actions\"; // Import the server action\r\n// Removed unused Sheet import\r\n\r\ninterface MinimalHeaderProps {\r\n  children?: React.ReactNode;\r\n  // Updated props\r\n  businessName?: string | null; // For business context or fallback\r\n  logoUrl?: string | null;\r\n  userName?: string | null; // Added for user's actual name\r\n}\r\n\r\n// Helper to get initials - prioritize userName if available, else businessName\r\nconst getInitials = (userName?: string | null, businessName?: string | null): string => {\r\n  const nameToUse = userName || businessName; // Use user name first for avatar\r\n  if (!nameToUse) return \"?\";\r\n  const names = nameToUse.trim().split(/\\s+/);\r\n  if (names.length === 1) return names[0].charAt(0).toUpperCase();\r\n  return (\r\n    names[0].charAt(0).toUpperCase() +\r\n    names[names.length - 1].charAt(0).toUpperCase()\r\n  );\r\n};\r\n\r\n// Helper to get current page name from pathname\r\nconst getPageName = (pathname: string): string => {\r\n  const pathSegments = pathname.split('/').filter(Boolean);\r\n\r\n  // Handle customer dashboard routes\r\n  if (pathSegments.includes('customer')) {\r\n    const lastSegment = pathSegments[pathSegments.length - 1];\r\n\r\n    switch (lastSegment) {\r\n      case 'customer':\r\n        return 'Feed';\r\n      case 'overview':\r\n        return 'Overview';\r\n      case 'likes':\r\n        return 'My Likes';\r\n      case 'subscriptions':\r\n        return 'Subscriptions';\r\n      case 'reviews':\r\n        return 'My Reviews';\r\n      case 'profile':\r\n        return 'Profile';\r\n      case 'settings':\r\n        return 'Settings';\r\n      default:\r\n        return 'Dashboard';\r\n    }\r\n  }\r\n\r\n  // Handle business dashboard routes\r\n  if (pathSegments.includes('business')) {\r\n    const lastSegment = pathSegments[pathSegments.length - 1];\r\n\r\n    switch (lastSegment) {\r\n      case 'business':\r\n        return 'Feed';\r\n      case 'overview':\r\n        return 'Overview';\r\n      case 'analytics':\r\n        return 'Analytics';\r\n      case 'card':\r\n        return 'Manage Card';\r\n      case 'products':\r\n        return 'Products & Services';\r\n      case 'gallery':\r\n        return 'Gallery';\r\n      case 'subscriptions':\r\n        return 'Subscriptions';\r\n      case 'likes':\r\n        return 'Likes';\r\n      case 'reviews':\r\n        return 'Reviews';\r\n      case 'activities':\r\n        return 'Activities';\r\n      case 'settings':\r\n        return 'Settings';\r\n      case 'plan':\r\n        return 'Plan Management';\r\n      default:\r\n        return 'Business Dashboard';\r\n    }\r\n  }\r\n\r\n  // Default fallback\r\n  return 'Dashboard';\r\n};\r\n\r\nconst MinimalHeader: React.FC<MinimalHeaderProps> = ({\r\n  children,\r\n  businessName: propBusinessName,\r\n  logoUrl: propLogoUrl,\r\n  userName: propUserName, // Added prop\r\n}) => {\r\n  // Use props directly for now - context can be added back later if needed\r\n  const businessName = propBusinessName;\r\n  const logoUrl = propLogoUrl;\r\n  const userName = propUserName;\r\n\r\n  // Get current pathname and page name\r\n  const pathname = usePathname();\r\n  const currentPageName = getPageName(pathname);\r\n\r\n  // Initials logic updated to prioritize userName\r\n  const initials = getInitials(userName, businessName);\r\n\r\n  // Determine display name for the dropdown\r\n  let displayName = \"User\"; // Default fallback\r\n  if (userName && businessName) {\r\n    displayName = `${businessName} (${userName})`; // Business context\r\n  } else if (userName) {\r\n    displayName = userName; // Customer context\r\n  } else if (businessName) {\r\n    displayName = businessName; // Fallback if only business name exists\r\n  }\r\n\r\n\r\n  // Access children assuming specific order from layout: [Sheet, Button, ThemeToggle]\r\n  const childArray = React.Children.toArray(children);\r\n  const mobileSheetTrigger = childArray[0]; // Assumes Sheet is the first child\r\n  const desktopToggleButton = childArray[1]; // Assumes Button is the second child\r\n  const themeToggleElement = childArray[2]; // Assumes ThemeToggle is the third child\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-40 w-full border-b border-border/50 bg-background/80 backdrop-blur-xl supports-[backdrop-filter]:bg-background/80\">\r\n      {/* Enhanced container with better spacing */}\r\n      <div className=\"container flex h-16 max-w-screen-2xl items-center justify-between px-4 md:px-6 lg:px-8\">\r\n        {/* Left Section: Mobile Toggle -> Logo -> Desktop Toggle */}\r\n        <div className=\"flex items-center space-x-2 md:space-x-4\">\r\n          {/* Render Mobile Sheet Trigger First */}\r\n          {mobileSheetTrigger}\r\n\r\n          {/* Current page name instead of logo */}\r\n          <div className=\"flex items-center\">\r\n            <h1 className=\"text-xl font-semibold text-foreground\">\r\n              {currentPageName}\r\n            </h1>\r\n          </div>\r\n\r\n          {/* Render Desktop Toggle Button after logo */}\r\n          {desktopToggleButton}\r\n        </div>\r\n\r\n        {/* Right Section: User Menu + Theme Toggle */}\r\n        <div className=\"flex items-center space-x-2 md:space-x-4\">\r\n          {/* Enhanced User Profile with Name Display */}\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"cursor-pointer flex items-center gap-3 h-10 px-3 rounded-lg hover:bg-accent/50 transition-all duration-200 focus-visible:ring-0 focus-visible:ring-offset-0\"\r\n              >\r\n                <Avatar className=\"h-8 w-8 border-2 border-border hover:border-primary/50 transition-all duration-200\">\r\n                  {/* Use logoUrl for avatar image only if it exists */}\r\n                  {logoUrl ? (\r\n                    <AvatarImage\r\n                      src={logoUrl}\r\n                      alt={userName || businessName || \"User\"}\r\n                    />\r\n                  ) : null}\r\n                  <AvatarFallback className=\"bg-gradient-to-br from-primary/10 to-primary/5 text-primary font-semibold text-sm\">\r\n                    {initials}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                {/* Display user name on larger screens */}\r\n                <span className=\"hidden sm:block text-sm font-medium text-foreground max-w-32 truncate\">\r\n                  {userName || businessName || \"User\"}\r\n                </span>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent className=\"w-64\" align=\"end\" forceMount> {/* Increased width slightly */}\r\n              {/* Updated Dropdown Label */}\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <p className=\"text-sm font-medium leading-none truncate py-2\">\r\n                  {displayName} {/* Use the combined/correct display name */}\r\n                </p>\r\n                {/* Optionally add email back if needed, maybe only for customers? */}\r\n                {/* {userName && !businessName && userEmail && (\r\n                  <p className=\"text-xs leading-none text-muted-foreground truncate pt-1\">\r\n                    {userEmail}\r\n                  </p>\r\n                )} */}\r\n              </DropdownMenuLabel>\r\n              {/* Removed email display and extra separator */}\r\n              {/* <DropdownMenuSeparator /> */}\r\n              {/* Add links to profile/settings if needed */}\r\n              {/* <DropdownMenuItem asChild>\r\n                <Link href=\"/dashboard/profile\">\r\n                  <UserIcon className=\"mr-2 h-4 w-4\" />\r\n                  <span>Profile</span>\r\n                </Link>\r\n              </DropdownMenuItem> */}\r\n              <DropdownMenuSeparator />\r\n              {/* Logout Button using Server Action */}\r\n              <form action={signOutUser} className=\"w-full px-2 py-1.5\">\r\n                <Button\r\n                  type=\"submit\"\r\n                  variant=\"ghost\"\r\n                  className=\"w-full justify-start font-normal text-sm h-auto py-1 cursor-pointer\"\r\n                >\r\n                  <LogOut className=\"mr-2 h-4 w-4\" />\r\n                  <span>Log out</span>\r\n                </Button>\r\n              </form>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n          {themeToggleElement} {/* Render theme toggle last */}\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\nexport default MinimalHeader;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,kDAAkD;AAClD;AACA;AAOA;AACA;AACA,oCAAoC;AACpC,8PAAkD,2BAA2B;AAhB7E;;;;;;;;;AA2BA,+EAA+E;AAC/E,MAAM,cAAc,CAAC,UAA0B;IAC7C,MAAM,YAAY,YAAY,cAAc,iCAAiC;IAC7E,IAAI,CAAC,WAAW,OAAO;IACvB,MAAM,QAAQ,UAAU,IAAI,GAAG,KAAK,CAAC;IACrC,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IAC7D,OACE,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAC9B,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;AAEjD;AAEA,gDAAgD;AAChD,MAAM,cAAc,CAAC;IACnB,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;IAEhD,mCAAmC;IACnC,IAAI,aAAa,QAAQ,CAAC,aAAa;QACrC,MAAM,cAAc,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAEzD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mCAAmC;IACnC,IAAI,aAAa,QAAQ,CAAC,aAAa;QACrC,MAAM,cAAc,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;QAEzD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,mBAAmB;IACnB,OAAO;AACT;AAEA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,EACR,cAAc,gBAAgB,EAC9B,SAAS,WAAW,EACpB,UAAU,YAAY,EACvB;IACC,yEAAyE;IACzE,MAAM,eAAe;IACrB,MAAM,UAAU;IAChB,MAAM,WAAW;IAEjB,qCAAqC;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,kBAAkB,YAAY;IAEpC,gDAAgD;IAChD,MAAM,WAAW,YAAY,UAAU;IAEvC,0CAA0C;IAC1C,IAAI,cAAc,QAAQ,mBAAmB;IAC7C,IAAI,YAAY,cAAc;QAC5B,cAAc,GAAG,aAAa,EAAE,EAAE,SAAS,CAAC,CAAC,EAAE,mBAAmB;IACpE,OAAO,IAAI,UAAU;QACnB,cAAc,UAAU,mBAAmB;IAC7C,OAAO,IAAI,cAAc;QACvB,cAAc,cAAc,wCAAwC;IACtE;IAGA,oFAAoF;IACpF,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,OAAO,CAAC;IAC1C,MAAM,qBAAqB,UAAU,CAAC,EAAE,EAAE,mCAAmC;IAC7E,MAAM,sBAAsB,UAAU,CAAC,EAAE,EAAE,qCAAqC;IAChF,MAAM,qBAAqB,UAAU,CAAC,EAAE,EAAE,yCAAyC;IAEnF,qBACE,8OAAC;QAAO,WAAU;kBAEhB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;wBAEZ;sCAGD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;wBAKJ;;;;;;;8BAIH,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,qIAAA,CAAA,eAAY;;8CACX,8OAAC,qIAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;;0DAEV,8OAAC,2HAAA,CAAA,SAAM;gDAAC,WAAU;;oDAEf,wBACC,8OAAC,2HAAA,CAAA,cAAW;wDACV,KAAK;wDACL,KAAK,YAAY,gBAAgB;;;;;+DAEjC;kEACJ,8OAAC,2HAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB;;;;;;;;;;;;0DAIL,8OAAC;gDAAK,WAAU;0DACb,YAAY,gBAAgB;;;;;;;;;;;;;;;;;8CAInC,8OAAC,qIAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;wCAAC;sDAE3D,8OAAC,qIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAE,WAAU;;oDACV;oDAAY;;;;;;;;;;;;sDAkBjB,8OAAC,qIAAA,CAAA,wBAAqB;;;;;sDAEtB,8OAAC;4CAAK,QAAQ,mJAAA,CAAA,cAAW;4CAAE,WAAU;sDACnC,cAAA,8OAAC,2HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAKb;wBAAmB;;;;;;;;;;;;;;;;;;AAK9B;uCAEe", "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/hooks/use-mobile.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\nconst MOBILE_BREAKPOINT = 768;\r\n\r\nexport function useIsMobile() {\r\n  // Initialize with undefined to avoid hydration mismatch\r\n  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined);\r\n\r\n  useEffect(() => {\r\n    // Check if window is available (client-side)\r\n    if (typeof window !== \"undefined\") {\r\n      const checkMobile = () => {\r\n        setIsMobile(window.innerWidth < MOBILE_BREAKPOINT);\r\n      };\r\n\r\n      // Initial check\r\n      checkMobile();\r\n\r\n      // Set up media query listener\r\n      const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`);\r\n      const onChange = () => {\r\n        checkMobile();\r\n      };\r\n\r\n      mql.addEventListener(\"change\", onChange);\r\n      return () => mql.removeEventListener(\"change\", onChange);\r\n    }\r\n  }, []);\r\n\r\n  // Return false during SSR to avoid hydration issues\r\n  return isMobile ?? false;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,MAAM,oBAAoB;AAEnB,SAAS;IACd,wDAAwD;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6CAA6C;QAC7C,uCAAmC;;QAgBnC;IACF,GAAG,EAAE;IAEL,oDAAoD;IACpD,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 904, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ThemeToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Moon, Sun, Monitor } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface ThemeToggleProps {\r\n  variant?: \"default\" | \"dashboard\";\r\n}\r\n\r\nexport function ThemeToggle({ variant = \"default\" }: ThemeToggleProps = {}) {\r\n  const { setTheme, theme } = useTheme();\r\n  const isMobile = useIsMobile();\r\n\r\n  // Mobile version with modern card design (only for default variant, not dashboard)\r\n  if (isMobile && variant === \"default\") {\r\n    return (\r\n      <div className=\"w-full\">\r\n        <div className=\"flex items-center justify-between p-4 rounded-xl bg-white/50 dark:bg-neutral-800/50 border border-neutral-200/50 dark:border-neutral-700/50 backdrop-blur-sm\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <div className=\"flex items-center justify-center w-10 h-10 rounded-lg bg-muted text-foreground\">\r\n              {theme === \"light\" ? (\r\n                <Sun className=\"h-5 w-5\" />\r\n              ) : theme === \"dark\" ? (\r\n                <Moon className=\"h-5 w-5\" />\r\n              ) : (\r\n                <Monitor className=\"h-5 w-5\" />\r\n              )}\r\n            </div>\r\n            <div className=\"flex flex-col\">\r\n              <span className=\"font-medium text-foreground\">Theme</span>\r\n              <span className=\"text-xs text-muted-foreground capitalize\">\r\n                {theme || \"system\"}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 px-3\">\r\n                Change\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-40\">\r\n              <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n                <Sun className=\"mr-2 h-4 w-4\" />\r\n                <span>Light</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n                <Moon className=\"mr-2 h-4 w-4\" />\r\n                <span>Dark</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n                <Monitor className=\"mr-2 h-4 w-4\" />\r\n                <span>System</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Dashboard variant - simplified icon-only button matching avatar size\r\n  if (variant === \"dashboard\") {\r\n    return (\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button variant=\"ghost\" className=\"h-9 w-9 rounded-full focus-visible:ring-0 focus-visible:ring-offset-0\">\r\n            <Sun className=\"h-[1.1rem] w-[1.1rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n            <Moon className=\"absolute h-[1.1rem] w-[1.1rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n            <span className=\"sr-only\">Toggle theme</span>\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"end\">\r\n          <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n            <Sun className=\"mr-2 h-4 w-4\" />\r\n            <span>Light</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n            <Moon className=\"mr-2 h-4 w-4\" />\r\n            <span>Dark</span>\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n            <Monitor className=\"mr-2 h-4 w-4\" />\r\n            <span>System</span>\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    );\r\n  }\r\n\r\n  // Desktop version (original)\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button variant=\"outline\" size=\"icon\">\r\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\r\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\r\n          <span className=\"sr-only\">Toggle theme</span>\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\">\r\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\r\n          <Sun className=\"mr-2 h-4 w-4\" />\r\n          <span>Light</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\r\n          <Moon className=\"mr-2 h-4 w-4\" />\r\n          <span>Dark</span>\r\n        </DropdownMenuItem>\r\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\r\n          <Monitor className=\"mr-2 h-4 w-4\" />\r\n          <span>System</span>\r\n        </DropdownMenuItem>\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAmBO,SAAS,YAAY,EAAE,UAAU,SAAS,EAAoB,GAAG,CAAC,CAAC;IACxE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAE3B,mFAAmF;IACnF,IAAI,YAAY,YAAY,WAAW;QACrC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,UAAU,wBACT,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;2CACb,UAAU,uBACZ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAGvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;kDAC9C,8OAAC;wCAAK,WAAU;kDACb,SAAS;;;;;;;;;;;;;;;;;;kCAIhB,8OAAC,qIAAA,CAAA,eAAY;;0CACX,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAIzD,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS,IAAM,SAAS;;0DACxC,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOpB;IAEA,uEAAuE;IACvE,IAAI,YAAY,aAAa;QAC3B,qBACE,8OAAC,qIAAA,CAAA,eAAY;;8BACX,8OAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAO;8BAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,WAAU;;0CAChC,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;8BAG9B,8OAAC,qIAAA,CAAA,sBAAmB;oBAAC,OAAM;;sCACzB,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,SAAS,IAAM,SAAS;;8CACxC,8OAAC,wMAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,6BAA6B;IAC7B,qBACE,8OAAC,qIAAA,CAAA,eAAY;;0BACX,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,qIAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC,qIAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;;0CACxC,8OAAC,wMAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 1369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1415, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/BottomNav.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { motion } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { Home, Search, User, Store, Users } from \"lucide-react\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\ninterface BottomNavItemProps {\r\n  href: string;\r\n  icon: React.ReactNode;\r\n  label: string;\r\n  isActive: boolean;\r\n  isTablet?: boolean;\r\n  badge?: string;\r\n  disabled?: boolean;\r\n}\r\n\r\nconst BottomNavItem = ({\r\n  href,\r\n  icon,\r\n  label,\r\n  isActive,\r\n  isTablet = false,\r\n  badge,\r\n  disabled = false\r\n}: BottomNavItemProps) => {\r\n  const content = (\r\n    <>\r\n      <div className=\"relative mb-1\">\r\n        {icon}\r\n        {badge && (\r\n          <Badge\r\n            variant=\"outline\"\r\n            className=\"absolute -top-2 -right-3 px-1 py-0 text-[7px] bg-[var(--brand-gold)] text-[var(--brand-gold-foreground)] border-[var(--brand-gold)]\"\r\n          >\r\n            {badge}\r\n          </Badge>\r\n        )}\r\n      </div>\r\n      <span className={cn(\r\n        \"transition-all\",\r\n        isTablet ? \"text-[9px]\" : \"text-[10px]\"\r\n      )}>{label}</span>\r\n    </>\r\n  );\r\n\r\n  const itemClassName = cn(\r\n    \"flex flex-col items-center justify-center flex-1 py-2 text-xs transition-colors\",\r\n    isActive\r\n      ? \"text-[var(--brand-gold)]\"\r\n      : \"text-muted-foreground hover:text-[var(--brand-gold)]\",\r\n    disabled && \"opacity-70 pointer-events-none\"\r\n  );\r\n\r\n  if (disabled) {\r\n    return (\r\n      <div className={itemClassName}>\r\n        {content}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Link href={href} className={itemClassName}>\r\n      {content}\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default function BottomNav() {\r\n  const pathname = usePathname();\r\n  const isMobile = useIsMobile();\r\n  const [isTablet, setIsTablet] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (typeof window === \"undefined\") return;\r\n\r\n    // Check if device is a tablet (between 768px and 1024px)\r\n    const checkTablet = () => {\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    // Initial check\r\n    checkTablet();\r\n\r\n    // Add event listener for resize\r\n    window.addEventListener('resize', checkTablet);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkTablet);\r\n  }, []);\r\n\r\n  // Don't render on desktop\r\n  if (!isMobile && !isTablet) {\r\n    return null;\r\n  }\r\n\r\n  // Unified navigation items for all layouts\r\n  // Determine the appropriate account link based on the current path\r\n  let accountLink = \"/login\";\r\n  let accountIsActive = false;\r\n\r\n  // If user is in business dashboard\r\n  if (pathname.startsWith(\"/dashboard/business\")) {\r\n    accountLink = \"/dashboard/business/settings\";\r\n    accountIsActive = pathname.includes(\"/dashboard/business/settings\");\r\n  }\r\n  // If user is in customer dashboard\r\n  else if (pathname.startsWith(\"/dashboard/customer\")) {\r\n    accountLink = \"/dashboard/customer/settings\";\r\n    accountIsActive = pathname.includes(\"/dashboard/customer/settings\");\r\n  }\r\n  // If user is in auth or onboarding flow\r\n  else if (pathname.startsWith(\"/login\") ||\r\n           pathname.startsWith(\"/choose-role\") || pathname.startsWith(\"/onboarding\")) {\r\n    accountLink = pathname; // Keep current page\r\n    accountIsActive = true;\r\n  }\r\n  // For public pages\r\n  else {\r\n    accountLink = \"/login\";\r\n    accountIsActive = pathname === \"/login\";\r\n  }\r\n\r\n  // Determine feed link based on current location and authentication status\r\n  let feedLink = \"/login\"; // Default to login for non-authenticated users\r\n  let feedIsActive = false;\r\n\r\n  // If user is in business dashboard, redirect to business dashboard (main feed page)\r\n  if (pathname.startsWith(\"/dashboard/business\")) {\r\n    feedLink = \"/dashboard/business\";\r\n    feedIsActive = pathname === \"/dashboard/business\";\r\n  }\r\n  // If user is in customer dashboard, redirect to customer dashboard (main feed page)\r\n  else if (pathname.startsWith(\"/dashboard/customer\")) {\r\n    feedLink = \"/dashboard/customer\";\r\n    feedIsActive = pathname === \"/dashboard/customer\";\r\n  }\r\n  // For all other pages (public pages), redirect to login\r\n  else {\r\n    feedLink = \"/login\";\r\n    feedIsActive = false;\r\n  }\r\n\r\n  // Unified navigation items\r\n  const navItems = [\r\n    {\r\n      key: \"home\",\r\n      href: pathname.startsWith(\"/dashboard/business\") ? \"/?view=home\" :\r\n            pathname.startsWith(\"/dashboard/customer\") ? \"/?view=home\" : \"/\",\r\n      icon: <Home size={20} />,\r\n      label: \"Home\",\r\n      isActive: pathname === \"/\" ||\r\n                pathname === \"/dashboard/business/overview\" ||\r\n                pathname === \"/dashboard/customer/overview\"\r\n    },\r\n    {\r\n      key: \"discover\",\r\n      href: \"/discover\",\r\n      icon: <Search size={20} />,\r\n      label: \"Discover\",\r\n      isActive: pathname === \"/discover\"\r\n    },\r\n    {\r\n      key: \"feed\",\r\n      href: feedLink,\r\n      icon: <Users size={20} />,\r\n      label: \"Feed\", // Always show \"Feed\" for consistency\r\n      isActive: feedIsActive\r\n    },\r\n    {\r\n      key: \"dukan-ai\",\r\n      href: \"#\",\r\n      icon: <Store size={20} />,\r\n      label: \"Dukan AI\",\r\n      isActive: false,\r\n      badge: \"Soon\",\r\n      disabled: true\r\n    },\r\n    {\r\n      key: \"account\",\r\n      href: accountLink,\r\n      icon: <User size={20} />,\r\n      label: \"Account\",\r\n      isActive: accountIsActive\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ y: 100 }}\r\n      animate={{ y: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n      className={cn(\r\n        \"fixed bottom-0 left-0 right-0 z-50 flex items-center justify-around bg-background/95 backdrop-blur-lg border-t border-border/80 px-2\",\r\n        isTablet ? \"h-14\" : \"h-16\"\r\n      )}\r\n    >\r\n      {navItems.map((item) => (\r\n        <BottomNavItem\r\n          key={item.key}\r\n          href={item.href}\r\n          icon={item.icon}\r\n          label={item.label}\r\n          isActive={item.isActive}\r\n          isTablet={isTablet}\r\n          badge={item.badge}\r\n          disabled={item.disabled}\r\n        />\r\n      ))}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;AAsBA,MAAM,gBAAgB,CAAC,EACrB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,KAAK,EACL,WAAW,KAAK,EACG;IACnB,MAAM,wBACJ;;0BACE,8OAAC;gBAAI,WAAU;;oBACZ;oBACA,uBACC,8OAAC,0HAAA,CAAA,QAAK;wBACJ,SAAQ;wBACR,WAAU;kCAET;;;;;;;;;;;;0BAIP,8OAAC;gBAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAChB,kBACA,WAAW,eAAe;0BACxB;;;;;;;;IAIR,MAAM,gBAAgB,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACrB,mFACA,WACI,6BACA,wDACJ,YAAY;IAGd,IAAI,UAAU;QACZ,qBACE,8OAAC;YAAI,WAAW;sBACb;;;;;;IAGP;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW;kBAC1B;;;;;;AAGP;AAEe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,yDAAyD;QACzD,MAAM;IAYR,GAAG,EAAE;IAEL,0BAA0B;IAC1B,IAAI,CAAC,YAAY,CAAC,UAAU;QAC1B,OAAO;IACT;IAEA,2CAA2C;IAC3C,mEAAmE;IACnE,IAAI,cAAc;IAClB,IAAI,kBAAkB;IAEtB,mCAAmC;IACnC,IAAI,SAAS,UAAU,CAAC,wBAAwB;QAC9C,cAAc;QACd,kBAAkB,SAAS,QAAQ,CAAC;IACtC,OAEK,IAAI,SAAS,UAAU,CAAC,wBAAwB;QACnD,cAAc;QACd,kBAAkB,SAAS,QAAQ,CAAC;IACtC,OAEK,IAAI,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,mBAAmB,SAAS,UAAU,CAAC,gBAAgB;QAClF,cAAc,UAAU,oBAAoB;QAC5C,kBAAkB;IACpB,OAEK;QACH,cAAc;QACd,kBAAkB,aAAa;IACjC;IAEA,0EAA0E;IAC1E,IAAI,WAAW,UAAU,+CAA+C;IACxE,IAAI,eAAe;IAEnB,oFAAoF;IACpF,IAAI,SAAS,UAAU,CAAC,wBAAwB;QAC9C,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK,IAAI,SAAS,UAAU,CAAC,wBAAwB;QACnD,WAAW;QACX,eAAe,aAAa;IAC9B,OAEK;QACH,WAAW;QACX,eAAe;IACjB;IAEA,2BAA2B;IAC3B,MAAM,WAAW;QACf;YACE,KAAK;YACL,MAAM,SAAS,UAAU,CAAC,yBAAyB,gBAC7C,SAAS,UAAU,CAAC,yBAAyB,gBAAgB;YACnE,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU,aAAa,OACb,aAAa,kCACb,aAAa;QACzB;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,MAAM;;;;;;YACpB,OAAO;YACP,UAAU,aAAa;QACzB;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,MAAM;;;;;;YACnB,OAAO;YACP,UAAU;YACV,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,MAAM;YACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,MAAM;;;;;;YAClB,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,GAAG;QAAI;QAClB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wIACA,WAAW,SAAS;kBAGrB,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;gBAEC,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;gBACV,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;eAPlB,KAAK,GAAG;;;;;;;;;;AAYvB", "debugId": null}}, {"offset": {"line": 1646, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1672, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1702, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none cursor-pointer\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1984, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { VariantProps, cva } from \"class-variance-authority\"\r\nimport { PanelLeftIcon } from \"lucide-react\"\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Input } from \"@/components/ui/input\"\r\nimport { Separator } from \"@/components/ui/separator\"\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\"\r\nimport { Skeleton } from \"@/components/ui/skeleton\"\r\nimport {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\"\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\r\nconst SIDEBAR_WIDTH = \"16rem\"\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\"\r\n  open: boolean\r\n  setOpen: (_open: boolean) => void // Keep prefix\r\n  openMobile: boolean\r\n  setOpenMobile: (_open: boolean) => void // Disabled warning for unused type param\r\n  isMobile: boolean\r\n  toggleSidebar: () => void\r\n}\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext)\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean\r\n  open?: boolean\r\n  onOpenChange?: (_open: boolean) => void // Disabled warning for unused type param\r\n}) {\r\n  const isMobile = useIsMobile()\r\n  const [openMobile, setOpenMobile] = React.useState(false)\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen)\r\n  const open = openProp ?? _open\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((_value: boolean) => boolean)) => { // Prefixed inner 'value' parameter\r\n      const openState = typeof value === \"function\" ? value(open) : value\r\n      if (setOpenProp) {\r\n        setOpenProp(openState)\r\n      } else {\r\n        _setOpen(openState)\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      if (typeof document !== \"undefined\") {\r\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\r\n      }\r\n    },\r\n    [setOpenProp, open]\r\n  )\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((_open) => !_open) : setOpen((_open) => !_open) // Disabled warning for unused inner params\r\n  }, [isMobile, setOpen, setOpenMobile])\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    if (typeof window === \"undefined\") return;\r\n\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault()\r\n        toggleSidebar()\r\n      }\r\n    }\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown)\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\r\n  }, [toggleSidebar])\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\"\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  )\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  )\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\"\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-white dark:bg-black text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-white dark:bg-black group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event)\r\n        toggleSidebar()\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar()\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex cursor-pointer\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  isActive?: boolean\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n  const { isMobile, state } = useSidebar()\r\n\r\n  const button = (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n\r\n  if (!tooltip) {\r\n    return button\r\n  }\r\n\r\n  if (typeof tooltip === \"string\") {\r\n    tooltip = {\r\n      children: tooltip,\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={state !== \"collapsed\" || isMobile}\r\n        {...tooltip}\r\n      />\r\n    </Tooltip>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean\r\n  showOnHover?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`\r\n  }, [])\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n  size?: \"sm\" | \"md\"\r\n  isActive?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 cursor-pointer\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,IAAI,OAAO,aAAa,aAAa;YACnC,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;IACF,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,QAAU,CAAC,SAAS,QAAQ,CAAC,QAAU,CAAC,OAAO,2CAA2C;;IAC7H,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,wCAAmC;;QAEnC,MAAM;IAYR,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,4HAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,0HAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,0HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,0HAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,0HAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kQACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,0HAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,8HAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,o0BACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,MAAM,uBACJ,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;YAAE;YAAS;QAAK,IAAI;QAC3D,GAAG,KAAK;;;;;;IAIb,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YACR,UAAU;QACZ;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAO;;0BACN,8OAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,4HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ,UAAU,eAAe;gBAChC,GAAG,OAAO;;;;;;;;;;;;AAInB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mWACA,kDAAkD;QAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,6HAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,aAAW;QACX,eAAa;QACb,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ggBACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2631, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/SidebarLink.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\n\r\ninterface SidebarLinkProps {\r\n  href: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}\r\n\r\nexport function SidebarLink({ href, children, className }: SidebarLinkProps) {\r\n  const { isMobile, setOpenMobile } = useSidebar();\r\n\r\n  const handleClick = () => {\r\n    // Only close the sidebar on mobile devices\r\n    if (isMobile) {\r\n      setOpenMobile(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link href={href} className={className} onClick={handleClick}>\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAYO,SAAS,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAoB;IACzE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAE7C,MAAM,cAAc;QAClB,2CAA2C;QAC3C,IAAI,UAAU;YACZ,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW;QAAW,SAAS;kBAC9C;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2713, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/NavBusinessUser.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  ChevronsUpDown,\r\n  LogOut,\r\n  // Import other icons if needed for dropdown items\r\n} from \"lucide-react\";\r\nimport { signOutUser } from \"@/app/auth/actions\"; // Assuming this action is correct\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\nimport { Button } from \"@/components/ui/button\"; // Import Button for the form\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Helper to get initials (can be moved to utils if used elsewhere)\r\nconst getInitials = (name: string | null | undefined): string => {\r\n  if (!name) return \"?\";\r\n  const names = name.trim().split(/\\s+/);\r\n  if (names.length === 1 && names[0]) return names[0].charAt(0).toUpperCase();\r\n  if (names.length > 1 && names[0] && names[names.length - 1]) {\r\n    return (\r\n      names[0].charAt(0).toUpperCase() +\r\n      names[names.length - 1].charAt(0).toUpperCase()\r\n    );\r\n  }\r\n  return \"?\";\r\n};\r\n\r\n\r\ninterface NavBusinessUserProps {\r\n  user: {\r\n    name: string | null; // Member name\r\n    avatar: string | null; // Business logo URL\r\n  };\r\n  businessName: string | null;\r\n}\r\n\r\nexport function NavBusinessUser({ user, businessName }: NavBusinessUserProps) {\r\n  const { isMobile } = useSidebar();\r\n  const businessInitials = getInitials(businessName);\r\n  const [isTablet, setIsTablet] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Check if device is a tablet (between 768px and 1024px)\r\n    const checkTablet = () => {\r\n      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);\r\n    };\r\n\r\n    // Initial check\r\n    checkTablet();\r\n\r\n    // Add event listener for resize\r\n    window.addEventListener('resize', checkTablet);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener('resize', checkTablet);\r\n  }, []);\r\n\r\n  // Use business logo/initials for the main trigger avatar\r\n  const displayAvatar = user.avatar;\r\n  const displayFallback = businessInitials;\r\n  const displayName = businessName || \"Business\";\r\n  const displaySubText = user.name || \"Member\";\r\n\r\n  // Calculate bottom padding based on device type\r\n  const bottomPadding = isMobile ? \"pb-16\" : isTablet ? \"pb-14\" : \"\";\r\n\r\n  return (\r\n    <SidebarMenu className={cn(bottomPadding)}>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n            >\r\n              <Avatar className=\"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30\">\r\n                {displayAvatar ? (\r\n                  <AvatarImage src={displayAvatar} alt={displayName} />\r\n                ) : null}\r\n                <AvatarFallback className=\"rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs\">\r\n                  {displayFallback}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                <span className=\"truncate font-semibold\">{displayName}</span>\r\n                <span className=\"truncate text-xs text-muted-foreground\">{displaySubText}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto size-4\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                 <Avatar className=\"h-8 w-8 rounded-lg border border-[var(--brand-gold)]/30\">\r\n                    {displayAvatar ? (\r\n                      <AvatarImage src={displayAvatar} alt={displayName} />\r\n                    ) : null}\r\n                    <AvatarFallback className=\"rounded-lg bg-muted border border-[var(--brand-gold)]/30 text-xs\">\r\n                      {displayFallback}\r\n                    </AvatarFallback>\r\n                 </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-semibold\">{displayName}</span>\r\n                   <span className=\"truncate text-xs text-muted-foreground\">{displaySubText}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            {/* Add any relevant dropdown items here if needed */}\r\n            {/* <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <Settings /> Settings // Example\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator /> */}\r\n            <form action={signOutUser} className=\"w-full px-2 py-1.5\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                type=\"submit\"\r\n                className=\"w-full justify-start p-0 h-auto font-normal cursor-pointer\"\r\n              >\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                Log out\r\n              </Button>\r\n            </form>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAKA,8PAAkD,kCAAkC;AACpF;AACA;AAOA;AAMA,wNAAiD,6BAA6B;AAC9E;AAxBA;;;;;;;;;;AA0BA,mEAAmE;AACnE,MAAM,cAAc,CAAC;IACnB,IAAI,CAAC,MAAM,OAAO;IAClB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IACzE,IAAI,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAE;QAC3D,OACE,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW,KAC9B,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,GAAG,WAAW;IAEjD;IACA,OAAO;AACT;AAWO,SAAS,gBAAgB,EAAE,IAAI,EAAE,YAAY,EAAwB;IAC1E,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,mBAAmB,YAAY;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;QAC9D;QAEA,gBAAgB;QAChB;QAEA,gCAAgC;QAChC,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,gBAAgB,KAAK,MAAM;IACjC,MAAM,kBAAkB;IACxB,MAAM,cAAc,gBAAgB;IACpC,MAAM,iBAAiB,KAAK,IAAI,IAAI;IAEpC,gDAAgD;IAChD,MAAM,gBAAgB,WAAW,UAAU,WAAW,UAAU;IAEhE,qBACE,8OAAC,4HAAA,CAAA,cAAW;QAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;kBACzB,cAAA,8OAAC,4HAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,qIAAA,CAAA,eAAY;;kCACX,8OAAC,qIAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;wCACf,8BACC,8OAAC,2HAAA,CAAA,cAAW;4CAAC,KAAK;4CAAe,KAAK;;;;;mDACpC;sDACJ,8OAAC,2HAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB;;;;;;;;;;;;8CAGL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAA0B;;;;;;sDAC1C,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;;;;;;;8CAE5D,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,qIAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,qIAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACZ,8OAAC,2HAAA,CAAA,SAAM;4CAAC,WAAU;;gDACd,8BACC,8OAAC,2HAAA,CAAA,cAAW;oDAAC,KAAK;oDAAe,KAAK;;;;;2DACpC;8DACJ,8OAAC,2HAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB;;;;;;;;;;;;sDAGP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAA0B;;;;;;8DACzC,8OAAC;oDAAK,WAAU;8DAA0C;;;;;;;;;;;;;;;;;;;;;;;0CAIjE,8OAAC,qIAAA,CAAA,wBAAqB;;;;;0CAQtB,8OAAC;gCAAK,QAAQ,mJAAA,CAAA,cAAW;gCAAE,WAAU;0CACnC,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnD", "debugId": null}}, {"offset": {"line": 2981, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/NavBusinessMain.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { usePathname } from 'next/navigation';\r\nimport { SidebarLink } from \"./SidebarLink\";\r\nimport {\r\n  ChevronRight,\r\n  LayoutDashboard,\r\n  CreditCard,\r\n  Package,\r\n  BarChart3,\r\n  Settings,\r\n  WalletCards,\r\n  Tag,\r\n  Heart,\r\n  Bell,\r\n  Store,\r\n  Users,\r\n  User,\r\n  Activity,\r\n  Star,\r\n  Image,\r\n  LayoutList,\r\n  FileEdit,\r\n  type LucideIcon\r\n} from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\n// Map string icon names to actual Lucide components\r\nexport const iconMap: { [key: string]: LucideIcon } = {\r\n  LayoutDashboard,\r\n  CreditCard,\r\n  Package,\r\n  BarChart3,\r\n  Settings,\r\n  WalletCards,\r\n  Tag,\r\n  Heart,\r\n  Bell,\r\n  Store,\r\n  Users,\r\n  User,\r\n  Activity,\r\n  Star,\r\n  Image,\r\n  LayoutList,\r\n  FileEdit,\r\n};\r\n\r\ninterface NavItemData {\r\n  href: string;\r\n  icon: string;\r\n  label: string;\r\n  isActive?: boolean;\r\n  badge?: string;\r\n  badgeVariant?: \"default\" | \"secondary\" | \"destructive\" | \"outline\" | \"upgrade\";\r\n  items?: {\r\n    href: string;\r\n    label: string;\r\n  }[];\r\n}\r\n\r\ninterface NavBusinessMainProps {\r\n  items: NavItemData[];\r\n}\r\n\r\nexport function NavBusinessMain({ items }: NavBusinessMainProps) {\r\n  const pathname = usePathname();\r\n\r\n  return (\r\n    <SidebarGroup>\r\n      {/* <SidebarGroupLabel>Platform</SidebarGroupLabel> // Optional label */}\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          const IconComponent = iconMap[item.icon];\r\n          // Determine active state based on current path\r\n          const isActive = pathname === item.href || (item.href !== \"/dashboard/business\" && pathname.startsWith(item.href));\r\n\r\n          // If item has sub-items, render as collapsible\r\n          if (item.items && item.items.length > 0) {\r\n            return (\r\n              <Collapsible\r\n                key={item.label}\r\n                asChild\r\n                defaultOpen={isActive}\r\n                className=\"group/collapsible\"\r\n              >\r\n                <SidebarMenuItem>\r\n                  <CollapsibleTrigger asChild>\r\n                    {/* Apply brand gold styling more directly to active state */}\r\n                    <SidebarMenuButton tooltip={item.label} isActive={isActive} className={cn(isActive && \"bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]\")}>\r\n                      {IconComponent && <IconComponent className={cn(isActive ? \"text-[var(--brand-gold)]\" : \"\")}/>}\r\n                      <span>{item.label}</span>\r\n                      <ChevronRight className=\"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\" />\r\n                    </SidebarMenuButton>\r\n                  </CollapsibleTrigger>\r\n                  <CollapsibleContent>\r\n                    <SidebarMenuSub>\r\n                      {item.items.map((subItem) => {\r\n                         const isSubActive = pathname === subItem.href;\r\n                         return (\r\n                            <SidebarMenuSubItem key={subItem.label}>\r\n                              <SidebarMenuSubButton asChild isActive={isSubActive}>\r\n                                <SidebarLink href={subItem.href}>\r\n                                  <span>{subItem.label}</span>\r\n                                </SidebarLink>\r\n                              </SidebarMenuSubButton>\r\n                            </SidebarMenuSubItem>\r\n                         );\r\n                      })}\r\n                    </SidebarMenuSub>\r\n                  </CollapsibleContent>\r\n                </SidebarMenuItem>\r\n              </Collapsible>\r\n            );\r\n          }\r\n\r\n          // Otherwise, render as a direct link\r\n          return (\r\n            <SidebarMenuItem key={item.label}>\r\n               {/* Apply brand gold styling more directly to active state */}\r\n              <SidebarMenuButton asChild tooltip={item.label} isActive={isActive} className={cn(isActive && \"bg-accent text-[var(--brand-gold)] dark:bg-[var(--brand-gold)]/10 dark:text-[var(--brand-gold)]\")}>\r\n                <SidebarLink href={item.href}>\r\n                  {IconComponent && <IconComponent className={cn(isActive ? \"text-[var(--brand-gold)]\" : \"\")}/>}\r\n                  <span>{item.label}</span>\r\n                  {item.badge && (\r\n                    <Badge\r\n                      variant={item.badgeVariant === \"upgrade\" ? \"default\" : item.badgeVariant || \"default\"}\r\n                      className={cn(\r\n                        \"ml-2 text-xs\",\r\n                        item.badgeVariant === \"upgrade\" && \"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300\"\r\n                      )}\r\n                    >\r\n                      {item.badge}\r\n                    </Badge>\r\n                  )}\r\n                </SidebarLink>\r\n              </SidebarMenuButton>\r\n            </SidebarMenuItem>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBA;AACA;AAKA;AASA;AAzCA;;;;;;;;;AA4CO,MAAM,UAAyC;IACpD,iBAAA,4NAAA,CAAA,kBAAe;IACf,YAAA,kNAAA,CAAA,aAAU;IACV,SAAA,wMAAA,CAAA,UAAO;IACP,WAAA,kNAAA,CAAA,YAAS;IACT,UAAA,0MAAA,CAAA,WAAQ;IACR,aAAA,oNAAA,CAAA,cAAW;IACX,KAAA,gMAAA,CAAA,MAAG;IACH,OAAA,oMAAA,CAAA,QAAK;IACL,MAAA,kMAAA,CAAA,OAAI;IACJ,OAAA,oMAAA,CAAA,QAAK;IACL,OAAA,oMAAA,CAAA,QAAK;IACL,MAAA,kMAAA,CAAA,OAAI;IACJ,UAAA,0MAAA,CAAA,WAAQ;IACR,MAAA,kMAAA,CAAA,OAAI;IACJ,OAAA,oMAAA,CAAA,QAAK;IACL,YAAA,kNAAA,CAAA,aAAU;IACV,UAAA,6MAAA,CAAA,WAAQ;AACV;AAmBO,SAAS,gBAAgB,EAAE,KAAK,EAAwB;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,qBACE,8OAAC,4HAAA,CAAA,eAAY;kBAEX,cAAA,8OAAC,4HAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,MAAM,gBAAgB,OAAO,CAAC,KAAK,IAAI,CAAC;gBACxC,+CAA+C;gBAC/C,MAAM,WAAW,aAAa,KAAK,IAAI,IAAK,KAAK,IAAI,KAAK,yBAAyB,SAAS,UAAU,CAAC,KAAK,IAAI;gBAEhH,+CAA+C;gBAC/C,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,qBACE,8OAAC,gIAAA,CAAA,cAAW;wBAEV,OAAO;wBACP,aAAa;wBACb,WAAU;kCAEV,cAAA,8OAAC,4HAAA,CAAA,kBAAe;;8CACd,8OAAC,gIAAA,CAAA,qBAAkB;oCAAC,OAAO;8CAEzB,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;wCAAC,SAAS,KAAK,KAAK;wCAAE,UAAU;wCAAU,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;4CACnF,+BAAiB,8OAAC;gDAAc,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,6BAA6B;;;;;;0DACvF,8OAAC;0DAAM,KAAK,KAAK;;;;;;0DACjB,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG5B,8OAAC,gIAAA,CAAA,qBAAkB;8CACjB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;kDACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC;4CACd,MAAM,cAAc,aAAa,QAAQ,IAAI;4CAC7C,qBACG,8OAAC,4HAAA,CAAA,qBAAkB;0DACjB,cAAA,8OAAC,4HAAA,CAAA,uBAAoB;oDAAC,OAAO;oDAAC,UAAU;8DACtC,cAAA,8OAAC,qIAAA,CAAA,cAAW;wDAAC,MAAM,QAAQ,IAAI;kEAC7B,cAAA,8OAAC;sEAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;+CAHD,QAAQ,KAAK;;;;;wCAQ5C;;;;;;;;;;;;;;;;;uBA3BD,KAAK,KAAK;;;;;gBAiCrB;gBAEA,qCAAqC;gBACrC,qBACE,8OAAC,4HAAA,CAAA,kBAAe;8BAEd,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;wBAAC,OAAO;wBAAC,SAAS,KAAK,KAAK;wBAAE,UAAU;wBAAU,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;kCAC5F,cAAA,8OAAC,qIAAA,CAAA,cAAW;4BAAC,MAAM,KAAK,IAAI;;gCACzB,+BAAiB,8OAAC;oCAAc,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,6BAA6B;;;;;;8CACvF,8OAAC;8CAAM,KAAK,KAAK;;;;;;gCAChB,KAAK,KAAK,kBACT,8OAAC,0HAAA,CAAA,QAAK;oCACJ,SAAS,KAAK,YAAY,KAAK,YAAY,YAAY,KAAK,YAAY,IAAI;oCAC5E,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gBACA,KAAK,YAAY,KAAK,aAAa;8CAGpC,KAAK,KAAK;;;;;;;;;;;;;;;;;mBAdC,KAAK,KAAK;;;;;YAqBpC;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 3215, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/activities.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n/**\r\n * Database Triggers Documentation\r\n *\r\n * The following triggers are set up in Supabase to automatically track activities:\r\n *\r\n * 1. add_like_activity() - Trigger function for likes\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'like',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is added\r\n * CREATE TRIGGER trigger_add_like_activity\r\n * AFTER INSERT ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_like_activity();\r\n * ```\r\n *\r\n * 1a. delete_like_activity() - Trigger function for removing like activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'like';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is deleted\r\n * CREATE TRIGGER trigger_delete_like_activity\r\n * AFTER DELETE ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_like_activity();\r\n * ```\r\n *\r\n * 2. add_subscription_activity() - Trigger function for subscriptions\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'subscribe',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is added\r\n * CREATE TRIGGER trigger_add_subscription_activity\r\n * AFTER INSERT ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_subscription_activity();\r\n * ```\r\n *\r\n * 2a. delete_subscription_activity() - Trigger function for removing subscription activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'subscribe';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is deleted\r\n * CREATE TRIGGER trigger_delete_subscription_activity\r\n * AFTER DELETE ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_subscription_activity();\r\n * ```\r\n *\r\n * 3. add_rating_activity() - Trigger function for ratings\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Check if this is an update or insert\r\n *   IF TG_OP = 'UPDATE' THEN\r\n *     -- For updates, only add activity if rating changed\r\n *     IF NEW.rating = OLD.rating THEN\r\n *       RETURN NEW;\r\n *     END IF;\r\n *   END IF;\r\n *\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     rating_value,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'rating',\r\n *     NEW.rating,\r\n *     NEW.updated_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is added or updated\r\n * CREATE TRIGGER trigger_add_rating_activity\r\n * AFTER INSERT OR UPDATE OF rating ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_rating_activity();\r\n * ```\r\n *\r\n * 3a. delete_rating_activity() - Trigger function for removing rating activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'rating';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is deleted\r\n * CREATE TRIGGER trigger_delete_rating_activity\r\n * AFTER DELETE ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_rating_activity();\r\n * ```\r\n */\r\n\r\n/**\r\n * Table Structure\r\n *\r\n * The business_activities table is structured as follows:\r\n * ```sql\r\n * CREATE TABLE business_activities (\r\n *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\r\n *   business_profile_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,\r\n *   user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\r\n *   activity_type TEXT NOT NULL CHECK (activity_type IN ('like', 'subscribe', 'rating')),\r\n *   rating_value INTEGER,\r\n *   created_at TIMESTAMPTZ NOT NULL DEFAULT now(),\r\n *   is_read BOOLEAN NOT NULL DEFAULT false,\r\n *\r\n *   -- Add constraint to ensure rating_value is only set for rating activities\r\n *   CONSTRAINT rating_value_only_for_ratings CHECK (\r\n *     (activity_type = 'rating' AND rating_value IS NOT NULL) OR\r\n *     (activity_type != 'rating' AND rating_value IS NULL)\r\n *   )\r\n * );\r\n *\r\n * -- Indexes for better performance\r\n * CREATE INDEX idx_business_activities_business_profile_id ON business_activities(business_profile_id);\r\n * CREATE INDEX idx_business_activities_user_id ON business_activities(user_id);\r\n * CREATE INDEX idx_business_activities_is_read ON business_activities(is_read);\r\n * CREATE INDEX idx_business_activities_activity_type ON business_activities(activity_type);\r\n * CREATE INDEX idx_business_activities_created_at ON business_activities(created_at);\r\n * ```\r\n */\r\n\r\n/**\r\n * Row Level Security (RLS) Policies\r\n *\r\n * The following RLS policies are set up in Supabase to secure the business_activities table:\r\n *\r\n * 1. Select Policy - Allows business owners to read their own activities\r\n * ```sql\r\n * CREATE POLICY business_activities_select_policy ON business_activities\r\n *   FOR SELECT\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n *\r\n * 2. Update Policy - Allows business owners to update their own activities (for marking as read)\r\n * ```sql\r\n * CREATE POLICY business_activities_update_policy ON business_activities\r\n *   FOR UPDATE\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n */\r\n\r\n// Define types for activities\r\nexport interface BusinessActivity {\r\n  id: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  activity_type: \"like\" | \"subscribe\" | \"rating\";\r\n  rating_value: number | null;\r\n  created_at: string;\r\n  is_read: boolean;\r\n  user_profile?: {\r\n    name?: string | null;\r\n    avatar_url?: string | null;\r\n    email?: string | null;\r\n    is_business?: boolean;\r\n    business_name?: string | null;\r\n    business_slug?: string | null;\r\n    logo_url?: string | null;\r\n  };\r\n}\r\n\r\nexport type ActivitySortBy = \"newest\" | \"oldest\" | \"unread_first\";\r\n\r\n/**\r\n * Fetches activities for a business with pagination and sorting\r\n * Optionally marks fetched activities as read automatically\r\n */\r\nexport async function getBusinessActivities({\r\n  businessProfileId,\r\n  page = 1,\r\n  pageSize = 15,\r\n  sortBy = \"newest\",\r\n  filterBy = \"all\",\r\n  autoMarkAsRead = true, // New parameter to control auto-marking as read\r\n}: {\r\n  businessProfileId: string;\r\n  page?: number;\r\n  pageSize?: number;\r\n  sortBy?: ActivitySortBy;\r\n  filterBy?: \"all\" | \"like\" | \"subscribe\" | \"rating\" | \"unread\";\r\n  autoMarkAsRead?: boolean;\r\n}) {\r\n  const supabase = await createClient();\r\n  const supabaseAdmin = createAdminClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { activities: [], count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { activities: [], count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // Calculate pagination\r\n    const from = (page - 1) * pageSize;\r\n    const to = from + pageSize - 1;\r\n\r\n    // Build the query\r\n    let query = supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(\"business_profile_id\", businessProfileId);\r\n\r\n    // Apply filter\r\n    if (filterBy === \"like\") {\r\n      query = query.eq(\"activity_type\", \"like\");\r\n    } else if (filterBy === \"subscribe\") {\r\n      query = query.eq(\"activity_type\", \"subscribe\");\r\n    } else if (filterBy === \"rating\") {\r\n      query = query.eq(\"activity_type\", \"rating\");\r\n    } else if (filterBy === \"unread\") {\r\n      query = query.eq(\"is_read\", false);\r\n    }\r\n\r\n    // Apply sorting\r\n    switch (sortBy) {\r\n      case \"oldest\":\r\n        query = query.order(\"created_at\", { ascending: true });\r\n        break;\r\n      case \"unread_first\":\r\n        query = query.order(\"is_read\", { ascending: true }).order(\"created_at\", { ascending: false });\r\n        break;\r\n      case \"newest\":\r\n      default:\r\n        query = query.order(\"created_at\", { ascending: false });\r\n        break;\r\n    }\r\n\r\n    // Apply pagination\r\n    query = query.range(from, to);\r\n\r\n    // Execute the query\r\n    const { data: activities, error, count } = await query;\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching business activities:\", error);\r\n      return { activities: [], count: 0, error: error.message };\r\n    }\r\n\r\n    // Get user profiles for the activities\r\n    const userIds = activities.map((activity) => activity.user_id);\r\n\r\n    // Fetch both customer and business profiles\r\n    const [customerProfiles, businessProfiles] = await Promise.all([\r\n      supabaseAdmin\r\n        .from(\"customer_profiles\")\r\n        .select(\"id, name, avatar_url, email\")\r\n        .in(\"id\", userIds),\r\n      supabaseAdmin\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_name, business_slug, logo_url\")\r\n        .in(\"id\", userIds),\r\n    ]);\r\n\r\n    // Combine the profiles\r\n    const userProfiles = new Map();\r\n\r\n    // Add customer profiles to the map\r\n    customerProfiles.data?.forEach((profile) => {\r\n      userProfiles.set(profile.id, {\r\n        name: profile.name,\r\n        avatar_url: profile.avatar_url,\r\n        email: profile.email,\r\n        is_business: false,\r\n      });\r\n    });\r\n\r\n    // Add business profiles to the map, overriding customer profiles if both exist\r\n    businessProfiles.data?.forEach((profile) => {\r\n      const existingProfile = userProfiles.get(profile.id) || {};\r\n      userProfiles.set(profile.id, {\r\n        ...existingProfile,\r\n        business_name: profile.business_name,\r\n        business_slug: profile.business_slug,\r\n        logo_url: profile.logo_url,\r\n        is_business: true,\r\n      });\r\n    });\r\n\r\n    // Attach user profiles to activities\r\n    const activitiesWithProfiles = activities.map((activity) => ({\r\n      ...activity,\r\n      user_profile: userProfiles.get(activity.user_id) || {},\r\n    }));\r\n\r\n    // Auto-mark fetched activities as read if enabled\r\n    if (autoMarkAsRead && activities.length > 0) {\r\n      // Get IDs of unread activities\r\n      const unreadActivityIds = activities\r\n        .filter(activity => !activity.is_read)\r\n        .map(activity => activity.id);\r\n\r\n      // Only proceed if there are unread activities\r\n      if (unreadActivityIds.length > 0) {\r\n        // Mark these activities as read\r\n        const { error: markError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", unreadActivityIds);\r\n\r\n        if (markError) {\r\n          console.error(\"Error auto-marking activities as read:\", markError);\r\n        } else {\r\n          // Update the activities in our result to reflect they're now read\r\n          activitiesWithProfiles.forEach(activity => {\r\n            if (unreadActivityIds.includes(activity.id)) {\r\n              activity.is_read = true;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      activities: activitiesWithProfiles,\r\n      count: count || 0,\r\n      error: null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching business activities:\", error);\r\n    return {\r\n      activities: [],\r\n      count: 0,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Marks activities as read\r\n * Handles pagination for large numbers of activities to work around Supabase's 1000 row limit\r\n */\r\nexport async function markActivitiesAsRead({\r\n  businessProfileId,\r\n  activityIds,\r\n}: {\r\n  businessProfileId: string;\r\n  activityIds: string[] | \"all\";\r\n}) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { success: false, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { success: false, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // If marking specific activities as read\r\n    if (activityIds !== \"all\") {\r\n      // Handle case where we have specific activity IDs\r\n      const { error } = await supabase\r\n        .from(\"business_activities\")\r\n        .update({ is_read: true })\r\n        .eq(\"business_profile_id\", businessProfileId)\r\n        .in(\"id\", activityIds);\r\n\r\n      if (error) {\r\n        console.error(\"Error marking specific activities as read:\", error);\r\n        return { success: false, error: error.message };\r\n      }\r\n    } else {\r\n      // Handle \"mark all as read\" with pagination to work around Supabase's 1000 row limit\r\n      const BATCH_SIZE = 1000; // Maximum number of rows to update at once\r\n      let hasMore = true;\r\n      let processedCount = 0;\r\n\r\n      while (hasMore) {\r\n        // Get a batch of unread activity IDs\r\n        const { data: unreadActivities, error: fetchError } = await supabase\r\n          .from(\"business_activities\")\r\n          .select(\"id\")\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .eq(\"is_read\", false)\r\n          .limit(BATCH_SIZE);\r\n\r\n        if (fetchError) {\r\n          console.error(\"Error fetching unread activities:\", fetchError);\r\n          return { success: false, error: fetchError.message };\r\n        }\r\n\r\n        // If no more unread activities, we're done\r\n        if (!unreadActivities || unreadActivities.length === 0) {\r\n          hasMore = false;\r\n          break;\r\n        }\r\n\r\n        // Extract IDs from the batch\r\n        const batchIds = unreadActivities.map(activity => activity.id);\r\n\r\n        // Mark this batch as read\r\n        const { error: updateError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", batchIds);\r\n\r\n        if (updateError) {\r\n          console.error(\"Error marking batch as read:\", updateError);\r\n          return { success: false, error: updateError.message };\r\n        }\r\n\r\n        // Update processed count and check if we need to continue\r\n        processedCount += batchIds.length;\r\n        hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more\r\n      }\r\n\r\n      console.log(`Marked ${processedCount} activities as read`);\r\n    }\r\n\r\n    // Revalidate the activities page\r\n    revalidatePath(\"/dashboard/business/activities\");\r\n\r\n    return { success: true, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error marking activities as read:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the count of unread activities\r\n */\r\nexport async function getUnreadActivitiesCount(businessProfileId: string) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    const { count, error } = await supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\", head: true })\r\n      .eq(\"business_profile_id\", businessProfileId)\r\n      .eq(\"is_read\", false);\r\n\r\n    if (error) {\r\n      console.error(\"Error getting unread activities count:\", error);\r\n      return { count: 0, error: error.message };\r\n    }\r\n\r\n    return { count: count || 0, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error getting unread activities count:\", error);\r\n    return { count: 0, error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA4gBsB,2BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3228, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/services/realtimeService.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createClient } from '@/utils/supabase/client';\r\nimport { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';\r\n\r\nexport interface RealtimeSubscription {\r\n  unsubscribe: () => void; \r\n  channel: RealtimeChannel;\r\n}\r\n\r\nexport type RealtimeCallback<T extends { [key: string]: any } = any> = (_payload: RealtimePostgresChangesPayload<T>) => void;\r\n\r\nexport interface SubscriptionOptions {\r\n  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';\r\n  filter?: string;\r\n  schema?: string;\r\n}\r\n\r\nclass RealtimeService {\r\n  private subscriptions = new Map<string, RealtimeChannel>();\r\n  private supabase = createClient();\r\n\r\n  /**\r\n   * Subscribe to table changes with automatic cleanup and unique channel names\r\n   */\r\n  subscribeToTable<T extends { [key: string]: any } = any>(\r\n    tableName: string,\r\n    callback: RealtimeCallback<T>,\r\n    options: SubscriptionOptions = {},\r\n    customChannelId?: string\r\n  ): RealtimeSubscription {\r\n    const {\r\n      event = '*',\r\n      filter,\r\n      schema = 'public'\r\n    } = options;\r\n\r\n    // Create unique channel name\r\n    const channelId = customChannelId || `${tableName}-${event}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\r\n    \r\n    // Clean up existing subscription with same ID if exists\r\n    if (this.subscriptions.has(channelId)) {\r\n      this.unsubscribe(channelId);\r\n    }\r\n\r\n    const channel = this.supabase\r\n      .channel(channelId)\r\n      .on(\r\n        'postgres_changes' as any,\r\n        {\r\n          event,\r\n          schema,\r\n          table: tableName,\r\n          ...(filter && { filter })\r\n        },\r\n        callback\r\n      )\r\n      .subscribe();\r\n\r\n    this.subscriptions.set(channelId, channel);\r\n\r\n    return {\r\n      unsubscribe: () => this.unsubscribe(channelId),\r\n      channel\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Subscribe to business activities for a specific business\r\n   */\r\n  subscribeToBusinessActivities(\r\n    businessProfileId: string,\r\n    callback: RealtimeCallback,\r\n    channelSuffix?: string\r\n  ): RealtimeSubscription {\r\n    const channelId = `business-activities-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;\r\n    \r\n    return this.subscribeToTable(\r\n      'business_activities',\r\n      callback,\r\n      {\r\n        event: 'INSERT',\r\n        filter: `business_profile_id=eq.${businessProfileId}`\r\n      },\r\n      channelId\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Subscribe to business profile changes\r\n   */\r\n  subscribeToBusinessProfile(\r\n    businessProfileId: string,\r\n    callback: RealtimeCallback,\r\n    channelSuffix?: string\r\n  ): RealtimeSubscription {\r\n    const channelId = `business-profile-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;\r\n    \r\n    return this.subscribeToTable(\r\n      'business_profiles',\r\n      callback,\r\n      {\r\n        event: '*',\r\n        filter: `id=eq.${businessProfileId}`\r\n      },\r\n      channelId\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Subscribe to monthly visit metrics\r\n   */\r\n  subscribeToMonthlyMetrics(\r\n    businessProfileId: string,\r\n    callback: RealtimeCallback,\r\n    channelSuffix?: string\r\n  ): RealtimeSubscription {\r\n    const channelId = `monthly-metrics-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;\r\n    \r\n    return this.subscribeToTable(\r\n      'monthly_visit_metrics',\r\n      callback,\r\n      {\r\n        event: '*',\r\n        filter: `business_profile_id=eq.${businessProfileId}`\r\n      },\r\n      channelId\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Subscribe to payment subscriptions\r\n   */\r\n  subscribeToPaymentSubscriptions(\r\n    businessProfileId: string,\r\n    callback: RealtimeCallback,\r\n    channelSuffix?: string\r\n  ): RealtimeSubscription {\r\n    const channelId = `payment-subscriptions-${businessProfileId}${channelSuffix ? `-${channelSuffix}` : ''}`;\r\n    \r\n    return this.subscribeToTable(\r\n      'payment_subscriptions',\r\n      callback,\r\n      {\r\n        event: '*',\r\n        filter: `business_profile_id=eq.${businessProfileId}`\r\n      },\r\n      channelId\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Unsubscribe from a specific channel\r\n   */\r\n  private unsubscribe(channelId: string): void {\r\n    const channel = this.subscriptions.get(channelId);\r\n    if (channel) {\r\n      this.supabase.removeChannel(channel);\r\n      this.subscriptions.delete(channelId);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Unsubscribe from all channels\r\n   */\r\n  unsubscribeAll(): void {\r\n    this.subscriptions.forEach((channel, _channelId) => {\r\n      this.supabase.removeChannel(channel);\r\n    });\r\n    this.subscriptions.clear();\r\n  }\r\n\r\n  /**\r\n   * Get active subscription count (for debugging)\r\n   */\r\n  getActiveSubscriptionCount(): number {\r\n    return this.subscriptions.size;\r\n  }\r\n\r\n  /**\r\n   * Get active subscription channel IDs (for debugging)\r\n   */\r\n  getActiveChannelIds(): string[] {\r\n    return Array.from(this.subscriptions.keys());\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const realtimeService = new RealtimeService();\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAkBA,MAAM;IACI,gBAAgB,IAAI,MAA+B;IACnD,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,IAAI;IAElC;;GAEC,GACD,iBACE,SAAiB,EACjB,QAA6B,EAC7B,UAA+B,CAAC,CAAC,EACjC,eAAwB,EACF;QACtB,MAAM,EACJ,QAAQ,GAAG,EACX,MAAM,EACN,SAAS,QAAQ,EAClB,GAAG;QAEJ,6BAA6B;QAC7B,MAAM,YAAY,mBAAmB,GAAG,UAAU,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAErH,wDAAwD;QACxD,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY;YACrC,IAAI,CAAC,WAAW,CAAC;QACnB;QAEA,MAAM,UAAU,IAAI,CAAC,QAAQ,CAC1B,OAAO,CAAC,WACR,EAAE,CACD,oBACA;YACE;YACA;YACA,OAAO;YACP,GAAI,UAAU;gBAAE;YAAO,CAAC;QAC1B,GACA,UAED,SAAS;QAEZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,WAAW;QAElC,OAAO;YACL,aAAa,IAAM,IAAI,CAAC,WAAW,CAAC;YACpC;QACF;IACF;IAEA;;GAEC,GACD,8BACE,iBAAyB,EACzB,QAA0B,EAC1B,aAAsB,EACA;QACtB,MAAM,YAAY,CAAC,oBAAoB,EAAE,oBAAoB,gBAAgB,CAAC,CAAC,EAAE,eAAe,GAAG,IAAI;QAEvG,OAAO,IAAI,CAAC,gBAAgB,CAC1B,uBACA,UACA;YACE,OAAO;YACP,QAAQ,CAAC,uBAAuB,EAAE,mBAAmB;QACvD,GACA;IAEJ;IAEA;;GAEC,GACD,2BACE,iBAAyB,EACzB,QAA0B,EAC1B,aAAsB,EACA;QACtB,MAAM,YAAY,CAAC,iBAAiB,EAAE,oBAAoB,gBAAgB,CAAC,CAAC,EAAE,eAAe,GAAG,IAAI;QAEpG,OAAO,IAAI,CAAC,gBAAgB,CAC1B,qBACA,UACA;YACE,OAAO;YACP,QAAQ,CAAC,MAAM,EAAE,mBAAmB;QACtC,GACA;IAEJ;IAEA;;GAEC,GACD,0BACE,iBAAyB,EACzB,QAA0B,EAC1B,aAAsB,EACA;QACtB,MAAM,YAAY,CAAC,gBAAgB,EAAE,oBAAoB,gBAAgB,CAAC,CAAC,EAAE,eAAe,GAAG,IAAI;QAEnG,OAAO,IAAI,CAAC,gBAAgB,CAC1B,yBACA,UACA;YACE,OAAO;YACP,QAAQ,CAAC,uBAAuB,EAAE,mBAAmB;QACvD,GACA;IAEJ;IAEA;;GAEC,GACD,gCACE,iBAAyB,EACzB,QAA0B,EAC1B,aAAsB,EACA;QACtB,MAAM,YAAY,CAAC,sBAAsB,EAAE,oBAAoB,gBAAgB,CAAC,CAAC,EAAE,eAAe,GAAG,IAAI;QAEzG,OAAO,IAAI,CAAC,gBAAgB,CAC1B,yBACA,UACA;YACE,OAAO;YACP,QAAQ,CAAC,uBAAuB,EAAE,mBAAmB;QACvD,GACA;IAEJ;IAEA;;GAEC,GACD,AAAQ,YAAY,SAAiB,EAAQ;QAC3C,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACvC,IAAI,SAAS;YACX,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;YAC5B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD,iBAAuB;QACrB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,SAAS;YACnC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;QAC9B;QACA,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEA;;GAEC,GACD,6BAAqC;QACnC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI;IAChC;IAEA;;GAEC,GACD,sBAAgC;QAC9B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;IAC3C;AACF;AAGO,MAAM,kBAAkB,IAAI", "debugId": null}}, {"offset": {"line": 3332, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/sidebar/BusinessAppSidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { SidebarLink } from \"./SidebarLink\";\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarHeader,\r\n  SidebarRail,\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuItem,\r\n  SidebarMenuButton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { NavBusinessUser } from \"./NavBusinessUser\";\r\nimport { ChevronRight } from \"lucide-react\";\r\n// Import the icon map from NavBusinessMain\r\nimport { iconMap } from \"./NavBusinessMain\";\r\nimport { getUnreadActivitiesCount } from \"@/lib/actions/activities\";\r\nimport { realtimeService } from \"@/lib/services/realtimeService\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\nimport { useBusinessProfile } from \"@/contexts/UserDataContext\";\r\n\r\n// Define types for navigation items\r\ninterface NavItem {\r\n  title: string;\r\n  icon?: string;\r\n  url?: string;\r\n  badge?: string;\r\n  badgeVariant?: \"default\" | \"secondary\" | \"destructive\" | \"outline\" | \"upgrade\";\r\n}\r\n\r\ninterface NavSection {\r\n  title: string;\r\n  icon?: string;\r\n  url?: string;\r\n  items: NavItem[];\r\n}\r\n\r\n// Define props if necessary, e.g., business data\r\ninterface BusinessAppSidebarProps extends React.ComponentProps<typeof Sidebar> {\r\n  businessName: string | null;\r\n  logoUrl: string | null;\r\n  memberName: string | null;\r\n  userPlan: string | null;\r\n}\r\n\r\nexport function BusinessAppSidebar({\r\n  businessName: propBusinessName,\r\n  logoUrl: propLogoUrl,\r\n  memberName: propMemberName,\r\n  userPlan: propUserPlan,\r\n  ...props\r\n}: BusinessAppSidebarProps) {\r\n  const [unreadActivitiesCount, setUnreadActivitiesCount] = useState(0);\r\n\r\n  // Use context data with fallback to props\r\n  const { businessProfile } = useBusinessProfile();\r\n\r\n  // Determine actual data to use (context first, then props)\r\n  const businessName = businessProfile?.business_name || propBusinessName;\r\n  const logoUrl = businessProfile?.logo_url || propLogoUrl;\r\n  const memberName = businessProfile?.member_name || propMemberName;\r\n  const userPlan = propUserPlan; // Keep using prop for plan as it comes from subscription data\r\n\r\n  /**\r\n   * Realtime Subscription for Unread Activities Count\r\n   *\r\n   * This effect sets up a Supabase Realtime subscription to keep the unread activities count\r\n   * badge updated in real-time.\r\n   *\r\n   * Important: You need to enable realtime for the business_activities table in Supabase:\r\n   * 1. Go to Supabase Dashboard > Database > Replication\r\n   * 2. Find the \"business_activities\" table in the list\r\n   * 3. Enable realtime by toggling it on\r\n   *\r\n   * The component subscribes to:\r\n   * 1. INSERT events - To increment the count when new activities are added\r\n   * 2. UPDATE events - To refresh the count when activities are marked as read\r\n   */\r\n  useEffect(() => {\r\n    const fetchUnreadCount = async () => {\r\n      const supabase = createClient();\r\n\r\n      // Get the current user\r\n      const { data: { user } } = await supabase.auth.getUser();\r\n\r\n      if (user) {\r\n        // Fetch initial unread count\r\n        const { count } = await getUnreadActivitiesCount(user.id);\r\n        setUnreadActivitiesCount(count);\r\n\r\n        // Set up real-time subscription for new activities\r\n        const insertSubscription = realtimeService.subscribeToBusinessActivities(\r\n          user.id,\r\n          () => {\r\n            // Increment the unread count\r\n            setUnreadActivitiesCount((prev) => prev + 1);\r\n          },\r\n          'sidebar-insert'\r\n        );\r\n\r\n        const updateSubscription = realtimeService.subscribeToTable(\r\n          'business_activities',\r\n          async () => {\r\n            // Refetch the count when activities are marked as read\r\n            const { count } = await getUnreadActivitiesCount(user.id);\r\n            setUnreadActivitiesCount(count);\r\n          },\r\n          {\r\n            event: 'UPDATE',\r\n            filter: `business_profile_id=eq.${user.id} AND is_read=eq.true`\r\n          },\r\n          `sidebar-update-${user.id}`\r\n        );\r\n\r\n        return () => {\r\n          insertSubscription.unsubscribe();\r\n          updateSubscription.unsubscribe();\r\n        };\r\n      }\r\n    };\r\n\r\n    fetchUnreadCount();\r\n  }, []);\r\n\r\n  // Prepare data structure for nav components\r\n  const userData = {\r\n    name: memberName,\r\n    // email: memberEmail, // Assuming email might be available later\r\n    avatar: logoUrl, // Use logoUrl for avatar for now\r\n  };\r\n\r\n  // Define main navigation items for business dashboard with collapsible sections\r\n  const navData: NavSection[] = [\r\n    {\r\n      title: \"Feed\",\r\n      icon: \"LayoutList\",\r\n      url: \"/dashboard/business\",\r\n      items: []\r\n    },\r\n    {\r\n      title: \"Overview\",\r\n      icon: \"LayoutDashboard\",\r\n      url: \"/dashboard/business/overview\",\r\n      items: []\r\n    },\r\n    {\r\n      title: \"Business Management\",\r\n      icon: \"Store\",\r\n      items: [\r\n        {\r\n          title: \"Manage Card\",\r\n          icon: \"CreditCard\",\r\n          url: \"/dashboard/business/card\",\r\n        },\r\n        {\r\n          title: \"Products & Services\",\r\n          icon: \"Package\",\r\n          url: \"/dashboard/business/products\",\r\n        },\r\n        {\r\n          title: \"Gallery\",\r\n          icon: \"Image\",\r\n          url: \"/dashboard/business/gallery\",\r\n          badge: userPlan === \"free\" ? \"1 Photo\" : userPlan === \"basic\" ? \"3 Photos\" : undefined,\r\n          badgeVariant: \"secondary\" as const\r\n        },\r\n      ]\r\n    },\r\n    {\r\n      title: \"Insights\",\r\n      icon: \"BarChart3\",\r\n      items: [\r\n        {\r\n          title: \"Analytics\",\r\n          icon: \"BarChart3\",\r\n          url: \"/dashboard/business/analytics\",\r\n          badge: userPlan === \"free\" ? \"Basic+\" : undefined,\r\n          badgeVariant: \"upgrade\" as const\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      title: \"Social\",\r\n      icon: \"Users\",\r\n      items: [\r\n        {\r\n          title: \"Activities\",\r\n          icon: \"Bell\",\r\n          url: \"/dashboard/business/activities\",\r\n          badge: unreadActivitiesCount > 0\r\n            ? unreadActivitiesCount > 99\r\n              ? \"99+\"\r\n              : unreadActivitiesCount.toString()\r\n            : undefined,\r\n          badgeVariant: \"secondary\" as const\r\n        },\r\n        {\r\n          title: \"Likes\",\r\n          icon: \"Heart\",\r\n          url: \"/dashboard/business/likes\",\r\n        },\r\n        {\r\n          title: \"Subscriptions\",\r\n          icon: \"Users\",\r\n          url: \"/dashboard/business/subscriptions\",\r\n        },\r\n        {\r\n          title: \"Reviews\",\r\n          icon: \"Star\",\r\n          url: \"/dashboard/business/reviews\",\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      title: \"Account\",\r\n      icon: \"User\",\r\n      items: [\r\n        {\r\n          title: \"Manage Plan\",\r\n          icon: \"WalletCards\",\r\n          url: \"/dashboard/business/plan\",\r\n        },\r\n        {\r\n          title: \"Settings\",\r\n          icon: \"Settings\",\r\n          url: \"/dashboard/business/settings\",\r\n        }\r\n      ]\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <Sidebar collapsible=\"icon\" {...props}>\r\n      <SidebarHeader className=\"border-b border-border/50\">\r\n        <div className=\"flex items-center px-2 py-4\">\r\n          <Link href=\"/?view=home\" className=\"flex flex-col group transition-all duration-200 hover:opacity-80\">\r\n            <span className=\"font-bold text-lg text-[var(--brand-gold)]\">\r\n              Dukan<span className=\"text-foreground\">card</span>\r\n            </span>\r\n            <span className=\"text-xs text-muted-foreground\">Business Portal</span>\r\n          </Link>\r\n        </div>\r\n      </SidebarHeader>\r\n      <SidebarContent className=\"px-2\">\r\n        <SidebarGroup>\r\n          <SidebarMenu className=\"space-y-1\">\r\n            {navData.map((section, index) => {\r\n              // For Feed and Overview (first two items), render as direct links\r\n              if (index === 0 || index === 1) {\r\n                return (\r\n                  <SidebarMenuItem key={section.title}>\r\n                    <SidebarMenuButton asChild tooltip={section.title}>\r\n                      <SidebarLink href={section.url || \"#\"}>\r\n                        {section.icon && iconMap[section.icon] && React.createElement(iconMap[section.icon], { className: \"h-4 w-4\" })}\r\n                        <span>{section.title}</span>\r\n                      </SidebarLink>\r\n                    </SidebarMenuButton>\r\n                  </SidebarMenuItem>\r\n                );\r\n              }\r\n\r\n              // For other sections, render as collapsible\r\n              return (\r\n                <Collapsible\r\n                  key={section.title}\r\n                  defaultOpen={index === 2} // Open the Business Management section by default\r\n                  className=\"group/collapsible\"\r\n                >\r\n                  <SidebarMenuItem>\r\n                    <CollapsibleTrigger asChild>\r\n                      <SidebarMenuButton>\r\n                        {section.icon && iconMap[section.icon] && React.createElement(iconMap[section.icon], { className: \"h-4 w-4\" })}\r\n                        <span>{section.title}</span>\r\n                        <ChevronRight className=\"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90\" />\r\n                      </SidebarMenuButton>\r\n                    </CollapsibleTrigger>\r\n                    <CollapsibleContent>\r\n                      <SidebarMenuSub>\r\n                        {section.items.map((item) => (\r\n                          <SidebarMenuSubItem key={item.title}>\r\n                            <SidebarMenuSubButton asChild>\r\n                              <SidebarLink href={item.url || \"#\"} className=\"flex items-center\">\r\n                                {item.icon && iconMap[item.icon] && React.createElement(iconMap[item.icon], { className: \"h-4 w-4 mr-2\" })}\r\n                                <span>{item.title}</span>\r\n                                {item.badge && (\r\n                                  <Badge\r\n                                    variant={item.badgeVariant === \"upgrade\" ? \"default\" : item.badgeVariant || \"default\"}\r\n                                    className={`ml-2 text-xs ${\r\n                                      item.badgeVariant === \"upgrade\" && \"bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300\"\r\n                                    }`}\r\n                                  >\r\n                                    {item.badge}\r\n                                  </Badge>\r\n                                )}\r\n                              </SidebarLink>\r\n                            </SidebarMenuSubButton>\r\n                          </SidebarMenuSubItem>\r\n                        ))}\r\n                      </SidebarMenuSub>\r\n                    </CollapsibleContent>\r\n                  </SidebarMenuItem>\r\n                </Collapsible>\r\n              );\r\n            })}\r\n          </SidebarMenu>\r\n        </SidebarGroup>\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <NavBusinessUser user={userData} businessName={businessName} />\r\n      </SidebarFooter>\r\n      <SidebarRail />\r\n    </Sidebar>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAcA;AAKA;AACA;AACA;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;AAjCA;;;;;;;;;;;;;;;;AA2DO,SAAS,mBAAmB,EACjC,cAAc,gBAAgB,EAC9B,SAAS,WAAW,EACpB,YAAY,cAAc,EAC1B,UAAU,YAAY,EACtB,GAAG,OACqB;IACxB,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,0CAA0C;IAC1C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD;IAE7C,2DAA2D;IAC3D,MAAM,eAAe,iBAAiB,iBAAiB;IACvD,MAAM,UAAU,iBAAiB,YAAY;IAC7C,MAAM,aAAa,iBAAiB,eAAe;IACnD,MAAM,WAAW,cAAc,8DAA8D;IAE7F;;;;;;;;;;;;;;GAcC,GACD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;YAE5B,uBAAuB;YACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAEtD,IAAI,MAAM;gBACR,6BAA6B;gBAC7B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sJAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,EAAE;gBACxD,yBAAyB;gBAEzB,mDAAmD;gBACnD,MAAM,qBAAqB,kIAAA,CAAA,kBAAe,CAAC,6BAA6B,CACtE,KAAK,EAAE,EACP;oBACE,6BAA6B;oBAC7B,yBAAyB,CAAC,OAAS,OAAO;gBAC5C,GACA;gBAGF,MAAM,qBAAqB,kIAAA,CAAA,kBAAe,CAAC,gBAAgB,CACzD,uBACA;oBACE,uDAAuD;oBACvD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sJAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,EAAE;oBACxD,yBAAyB;gBAC3B,GACA;oBACE,OAAO;oBACP,QAAQ,CAAC,uBAAuB,EAAE,KAAK,EAAE,CAAC,oBAAoB,CAAC;gBACjE,GACA,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE;gBAG7B,OAAO;oBACL,mBAAmB,WAAW;oBAC9B,mBAAmB,WAAW;gBAChC;YACF;QACF;QAEA;IACF,GAAG,EAAE;IAEL,4CAA4C;IAC5C,MAAM,WAAW;QACf,MAAM;QACN,iEAAiE;QACjE,QAAQ;IACV;IAEA,gFAAgF;IAChF,MAAM,UAAwB;QAC5B;YACE,OAAO;YACP,MAAM;YACN,KAAK;YACL,OAAO,EAAE;QACX;QACA;YACE,OAAO;YACP,MAAM;YACN,KAAK;YACL,OAAO,EAAE;QACX;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;oBACL,OAAO,aAAa,SAAS,YAAY,aAAa,UAAU,aAAa;oBAC7E,cAAc;gBAChB;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;oBACL,OAAO,aAAa,SAAS,WAAW;oBACxC,cAAc;gBAChB;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;oBACL,OAAO,wBAAwB,IAC3B,wBAAwB,KACtB,QACA,sBAAsB,QAAQ,KAChC;oBACJ,cAAc;gBAChB;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;gBACL;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,KAAK;gBACP;aACD;QACH;KACD;IAED,qBACE,8OAAC,4HAAA,CAAA,UAAO;QAAC,aAAY;QAAQ,GAAG,KAAK;;0BACnC,8OAAC,4HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAc,WAAU;;0CACjC,8OAAC;gCAAK,WAAU;;oCAA6C;kDACtD,8OAAC;wCAAK,WAAU;kDAAkB;;;;;;;;;;;;0CAEzC,8OAAC;gCAAK,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;0BAItD,8OAAC,4HAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,8OAAC,4HAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,4HAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,QAAQ,GAAG,CAAC,CAAC,SAAS;4BACrB,kEAAkE;4BAClE,IAAI,UAAU,KAAK,UAAU,GAAG;gCAC9B,qBACE,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;wCAAC,OAAO;wCAAC,SAAS,QAAQ,KAAK;kDAC/C,cAAA,8OAAC,qIAAA,CAAA,cAAW;4CAAC,MAAM,QAAQ,GAAG,IAAI;;gDAC/B,QAAQ,IAAI,IAAI,yIAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,kBAAI,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,yIAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,EAAE;oDAAE,WAAW;gDAAU;8DAC5G,8OAAC;8DAAM,QAAQ,KAAK;;;;;;;;;;;;;;;;;mCAJJ,QAAQ,KAAK;;;;;4BASvC;4BAEA,4CAA4C;4BAC5C,qBACE,8OAAC,gIAAA,CAAA,cAAW;gCAEV,aAAa,UAAU;gCACvB,WAAU;0CAEV,cAAA,8OAAC,4HAAA,CAAA,kBAAe;;sDACd,8OAAC,gIAAA,CAAA,qBAAkB;4CAAC,OAAO;sDACzB,cAAA,8OAAC,4HAAA,CAAA,oBAAiB;;oDACf,QAAQ,IAAI,IAAI,yIAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,kBAAI,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,yIAAA,CAAA,UAAO,CAAC,QAAQ,IAAI,CAAC,EAAE;wDAAE,WAAW;oDAAU;kEAC5G,8OAAC;kEAAM,QAAQ,KAAK;;;;;;kEACpB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG5B,8OAAC,gIAAA,CAAA,qBAAkB;sDACjB,cAAA,8OAAC,4HAAA,CAAA,iBAAc;0DACZ,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC,4HAAA,CAAA,qBAAkB;kEACjB,cAAA,8OAAC,4HAAA,CAAA,uBAAoB;4DAAC,OAAO;sEAC3B,cAAA,8OAAC,qIAAA,CAAA,cAAW;gEAAC,MAAM,KAAK,GAAG,IAAI;gEAAK,WAAU;;oEAC3C,KAAK,IAAI,IAAI,yIAAA,CAAA,UAAO,CAAC,KAAK,IAAI,CAAC,kBAAI,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,yIAAA,CAAA,UAAO,CAAC,KAAK,IAAI,CAAC,EAAE;wEAAE,WAAW;oEAAe;kFACxG,8OAAC;kFAAM,KAAK,KAAK;;;;;;oEAChB,KAAK,KAAK,kBACT,8OAAC,0HAAA,CAAA,QAAK;wEACJ,SAAS,KAAK,YAAY,KAAK,YAAY,YAAY,KAAK,YAAY,IAAI;wEAC5E,WAAW,CAAC,aAAa,EACvB,KAAK,YAAY,KAAK,aAAa,wEACnC;kFAED,KAAK,KAAK;;;;;;;;;;;;;;;;;uDAZI,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;+BAftC,QAAQ,KAAK;;;;;wBAuCxB;;;;;;;;;;;;;;;;0BAIN,8OAAC,4HAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC,yIAAA,CAAA,kBAAe;oBAAC,MAAM;oBAAU,cAAc;;;;;;;;;;;0BAEjD,8OAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 3767, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/context/PaymentMethodLimitationsContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { createContext, useContext, useState, ReactNode, useEffect } from \"react\";\r\nimport dynamic from \"next/dynamic\";\r\n\r\n// Dynamically import the dialog to prevent SSR issues\r\nconst PaymentMethodLimitationsDialog = dynamic(\r\n  () => import(\"@/app/(dashboard)/dashboard/business/plan/components/PaymentMethodLimitationsDialog\").then(mod => ({ default: mod.PaymentMethodLimitationsDialog })),\r\n  {\r\n    ssr: false,\r\n    loading: () => null // Return null during loading to prevent hydration issues\r\n  }\r\n);\r\n\r\ninterface PaymentMethodLimitationsContextType {\r\n  openDialog: (_paymentMethod?: string, _onContinueAction?: () => void) => void;\r\n}\r\n\r\nconst PaymentMethodLimitationsContext = createContext<\r\n  PaymentMethodLimitationsContextType | undefined\r\n>(undefined);\r\n\r\nexport function PaymentMethodLimitationsProvider({\r\n  children,\r\n}: {\r\n  children: ReactNode;\r\n}) {\r\n  const [isClient, setIsClient] = useState(false);\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [paymentMethod, setPaymentMethod] = useState<string>(\"UPI\");\r\n  const [pendingAction, setPendingAction] = useState<(() => void) | null>(null);\r\n\r\n  // Set client flag to prevent SSR issues\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  const openDialog = (\r\n    method: string = \"UPI\",\r\n    onContinueAction?: () => void\r\n  ) => {\r\n    setPaymentMethod(method);\r\n    setDialogOpen(true);\r\n    if (onContinueAction) {\r\n      setPendingAction(() => onContinueAction);\r\n    }\r\n  };\r\n\r\n  const handleContinue = () => {\r\n    setDialogOpen(false);\r\n    if (pendingAction) {\r\n      pendingAction();\r\n      setPendingAction(null);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <PaymentMethodLimitationsContext.Provider\r\n      value={{\r\n        openDialog,\r\n      }}\r\n    >\r\n      {children}\r\n      {isClient && (\r\n        <PaymentMethodLimitationsDialog\r\n          open={dialogOpen}\r\n          onOpenChange={setDialogOpen}\r\n          onContinue={handleContinue}\r\n          paymentMethod={paymentMethod}\r\n        />\r\n      )}\r\n    </PaymentMethodLimitationsContext.Provider>\r\n  );\r\n}\r\n\r\nexport function usePaymentMethodLimitations() {\r\n  const context = useContext(PaymentMethodLimitationsContext);\r\n  if (context === undefined) {\r\n    throw new Error(\r\n      \"usePaymentMethodLimitations must be used within a PaymentMethodLimitationsProvider\"\r\n    );\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;AAHA;;;;AAKA,sDAAsD;AACtD,MAAM,iCAAiC,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAGzC,KAAK;IACL,SAAS,IAAM,KAAK,yDAAyD;;AAQjF,MAAM,gDAAkC,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAElD;AAEK,SAAS,iCAAiC,EAC/C,QAAQ,EAGT;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAExE,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,aAAa,CACjB,SAAiB,KAAK,EACtB;QAEA,iBAAiB;QACjB,cAAc;QACd,IAAI,kBAAkB;YACpB,iBAAiB,IAAM;QACzB;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI,eAAe;YACjB;YACA,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,gCAAgC,QAAQ;QACvC,OAAO;YACL;QACF;;YAEC;YACA,0BACC,8OAAC;gBACC,MAAM;gBACN,cAAc;gBACd,YAAY;gBACZ,eAAe;;;;;;;;;;;;AAKzB;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3849, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClientLayout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\"; // Removed useState\r\nimport MinimalHeader from \"@/app/components/MinimalHeader\";\r\nimport { ThemeToggle } from \"@/app/components/ThemeToggle\";\r\nimport BottomNav from \"@/app/components/BottomNav\"; // Import BottomNav\r\nimport {\r\n  SidebarProvider,\r\n  SidebarInset,\r\n  SidebarTrigger,\r\n} from \"@/components/ui/sidebar\"; // Added shadcn sidebar imports\r\nimport { BusinessAppSidebar } from \"@/components/sidebar/BusinessAppSidebar\"; // Added new sidebar import\r\nimport { cn } from \"@/lib/utils\";\r\nimport { PaymentMethodLimitationsProvider } from \"@/app/context/PaymentMethodLimitationsContext\";\r\nimport { UserDataProvider } from \"@/contexts/UserDataContext\";\r\n\r\n// Define props type\r\ninterface BusinessDashboardClientLayoutProps {\r\n  children: React.ReactNode;\r\n  businessName: string | null;\r\n  logoUrl: string | null;\r\n  memberName: string | null;\r\n  userPlan: string | null;\r\n}\r\n\r\nexport default function BusinessDashboardClientLayout({\r\n  children,\r\n  businessName,\r\n  logoUrl,\r\n  memberName,\r\n  userPlan,\r\n}: BusinessDashboardClientLayoutProps) {\r\n  // Removed old state and handlers\r\n\r\n  return (\r\n    <UserDataProvider>\r\n      <PaymentMethodLimitationsProvider>\r\n        <SidebarProvider>\r\n        <BusinessAppSidebar\r\n          businessName={businessName}\r\n          logoUrl={logoUrl}\r\n          memberName={memberName}\r\n          userPlan={userPlan}\r\n        />\r\n        <SidebarInset>\r\n          {/* Header is now inside SidebarInset */}\r\n          <MinimalHeader\r\n            businessName={businessName}\r\n            logoUrl={logoUrl}\r\n            userName={memberName} // Pass memberName to userName prop\r\n          >\r\n            {/* Sidebar Trigger replaces old buttons */}\r\n            <SidebarTrigger className=\"ml-auto md:ml-0\" />{\" \"}\r\n            {/* Adjust margin as needed */}\r\n            {/* Removed old Sheet and Desktop Collapse Button */}\r\n            <ThemeToggle variant=\"dashboard\" />\r\n          </MinimalHeader>\r\n\r\n          {/* Main Content Area */}\r\n          <main\r\n            className={cn(\r\n              \"flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto pb-16 md:pb-6\", // Standardized padding across all screen sizes\r\n              \"bg-white dark:bg-black\" // Use white for light mode and black for dark mode\r\n            )}\r\n          >\r\n            {children}\r\n          </main>\r\n          <BottomNav />\r\n        </SidebarInset>\r\n        </SidebarProvider>\r\n      </PaymentMethodLimitationsProvider>\r\n    </UserDataProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA,gOAAoD,mBAAmB;AACvE,0NAIkC,+BAA+B;AACjE,0PAA8E,2BAA2B;AACzG;AACA;AACA;AAdA;;;;;;;;;;AAyBe,SAAS,8BAA8B,EACpD,QAAQ,EACR,YAAY,EACZ,OAAO,EACP,UAAU,EACV,QAAQ,EAC2B;IACnC,iCAAiC;IAEjC,qBACE,8OAAC,4HAAA,CAAA,mBAAgB;kBACf,cAAA,8OAAC,kJAAA,CAAA,mCAAgC;sBAC/B,cAAA,8OAAC,4HAAA,CAAA,kBAAe;;kCAChB,8OAAC,4IAAA,CAAA,qBAAkB;wBACjB,cAAc;wBACd,SAAS;wBACT,YAAY;wBACZ,UAAU;;;;;;kCAEZ,8OAAC,4HAAA,CAAA,eAAY;;0CAEX,8OAAC,mIAAA,CAAA,UAAa;gCACZ,cAAc;gCACd,SAAS;gCACT,UAAU;;kDAGV,8OAAC,4HAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;oCAAqB;kDAG/C,8OAAC,iIAAA,CAAA,cAAW;wCAAC,SAAQ;;;;;;;;;;;;0CAIvB,8OAAC;gCACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6DACA,yBAAyB,mDAAmD;;0CAG7E;;;;;;0CAEH,8OAAC,+HAAA,CAAA,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}]}