"use client";

import React from "react"; // Removed useState
import MinimalHeader from "@/app/components/MinimalHeader";
import { ThemeToggle } from "@/app/components/ThemeToggle";
import BottomNav from "@/app/components/BottomNav"; // Import BottomNav
import {
  SidebarProvider,
  SidebarInset,
  SidebarTrigger,
} from "@/components/ui/sidebar"; // Added shadcn sidebar imports
import { BusinessAppSidebar } from "@/components/sidebar/BusinessAppSidebar"; // Added new sidebar import
import { cn } from "@/lib/utils";
import { PaymentMethodLimitationsProvider } from "@/app/context/PaymentMethodLimitationsContext";
import { UserDataProvider } from "@/contexts/UserDataContext";

// Define props type
interface BusinessDashboardClientLayoutProps {
  children: React.ReactNode;
  businessName: string | null;
  logoUrl: string | null;
  memberName: string | null;
  userPlan: string | null;
}

export default function BusinessDashboardClientLayout({
  children,
  businessName,
  logoUrl,
  memberName,
  userPlan,
}: BusinessDashboardClientLayoutProps) {
  // Removed old state and handlers

  return (
    <UserDataProvider>
      <PaymentMethodLimitationsProvider>
        <SidebarProvider>
        <BusinessAppSidebar
          businessName={businessName}
          logoUrl={logoUrl}
          memberName={memberName}
          userPlan={userPlan}
        />
        <SidebarInset>
          {/* Header is now inside SidebarInset */}
          <MinimalHeader
            businessName={businessName}
            logoUrl={logoUrl}
            userName={memberName} // Pass memberName to userName prop
          >
            {/* Sidebar Trigger replaces old buttons */}
            <SidebarTrigger className="ml-auto md:ml-0" />{" "}
            {/* Adjust margin as needed */}
            {/* Removed old Sheet and Desktop Collapse Button */}
            <ThemeToggle variant="dashboard" />
          </MinimalHeader>

          {/* Main Content Area */}
          <main
            className={cn(
              "flex-grow p-3 sm:p-4 md:p-5 overflow-y-auto pb-16 md:pb-6", // Standardized padding across all screen sizes
              "bg-white dark:bg-black" // Use white for light mode and black for dark mode
            )}
          >
            {children}
          </main>
          <BottomNav />
        </SidebarInset>
        </SidebarProvider>
      </PaymentMethodLimitationsProvider>
    </UserDataProvider>
  );
}
