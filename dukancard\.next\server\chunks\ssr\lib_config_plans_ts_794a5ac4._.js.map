{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/config/plans.ts"], "sourcesContent": ["/**\r\n * Central configuration for all plan-related constants\r\n * This file serves as the single source of truth for plan information\r\n */\r\n\r\n// Plan types\r\nexport type PlanType = \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\";\r\nexport type PlanCycle = \"monthly\" | \"yearly\";\r\nexport type PaymentGateway = \"razorpay\";\r\n\r\n// Plan features\r\nexport interface PlanFeature {\r\n  name: string;\r\n  included: boolean;\r\n  limit?: number | \"unlimited\";\r\n  description?: string;\r\n}\r\n\r\n// Define Razorpay plan IDs\r\n// Different plan IDs for production and development environments\r\nconst RAZORPAY_PLAN_IDS = {\r\n  free: {\r\n    monthly: \"free-plan-monthly\", // Free plan doesn't need a real Razorpay ID\r\n    yearly: \"free-plan-yearly\",   // Free plan doesn't need a real Razorpay ID\r\n  },\r\n  basic: {\r\n    monthly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QO9rDMTSLeT34b\" // Production Basic monthly\r\n      : \"plan_QRgoJF3OfM6mB0\", // Development Basic monthly\r\n    yearly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QO9sfgFBnEFATA\" // Production Basic yearly\r\n      : \"plan_QRgr1XzaksqvSZ\", // Development Basic yearly\r\n  },\r\n  growth: {\r\n    monthly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnOd77S3FVeUc\" // Production Growth monthly\r\n      : \"plan_QbnMGvYCN7BM0V\", // Development Growth monthly\r\n    yearly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnOuE7iogOGTq\" // Production Growth yearly\r\n      : \"plan_QbnMdSbSeFrykv\", // Development Growth yearly\r\n  },\r\n  pro: {\r\n    monthly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnP83wvmzUOqM\" // Production Pro monthly\r\n      : \"plan_QbnN4mwUu6H2Ho\", // Development Pro monthly\r\n    yearly: process.env.NODE_ENV === 'production'\r\n      ? \"plan_QbnPJinNt66Pik\" // Production Pro yearly\r\n      : \"plan_QbnNYfrCExI496\", // Development Pro yearly\r\n  },\r\n  enterprise: {\r\n    monthly: \"enterprise-plan-monthly-razorpay\", // Placeholder for future implementation\r\n    yearly: \"enterprise-plan-yearly-razorpay\", // Placeholder for future implementation\r\n  }\r\n};\r\n\r\n// Set payment gateway to Razorpay\r\nconst _paymentGateway: PaymentGateway = \"razorpay\";\r\n\r\n\r\n// Plan interface\r\nexport interface Plan {\r\n  id: PlanType;\r\n  name: string;\r\n  description: string;\r\n  features: PlanFeature[];\r\n  razorpayPlanIds: {\r\n    monthly: string;\r\n    yearly: string;\r\n  };\r\n  pricing: {\r\n    monthly: number;\r\n    yearly: number;\r\n  };\r\n  recommended?: boolean;\r\n}\r\n\r\n// Plan definitions\r\nexport const PLANS: Plan[] = [\r\n  {\r\n    id: \"free\",\r\n    name: \"Free\",\r\n    description: \"Basic features for individuals and startups\",\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.free,\r\n    pricing: {\r\n      monthly: 0,\r\n      yearly: 0,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Simple digital business card with contact information\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: 5,\r\n        description: \"Showcase your products or services (limited to 5)\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: false,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: false,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: false,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: false,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 1,\r\n        description: \"Upload and display 1 image in your gallery\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: true,\r\n        description: \"Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"basic\",\r\n    name: \"Basic\",\r\n    description: \"Essential features for small businesses\",\r\n    recommended: false,\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.basic,\r\n    pricing: {\r\n      monthly: 99,\r\n      yearly: 999,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Basic digital business card with contact information\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: 15,\r\n        description: \"Showcase your products or services (up to 15)\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: false,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: false,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: false,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 3,\r\n        description: \"Upload and display up to 3 images in your gallery\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: true,\r\n        description: \"Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"growth\",\r\n    name: \"Growth\",\r\n    description: \"Advanced features for growing businesses\",\r\n    recommended: true,\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.growth,\r\n    pricing: {\r\n      monthly: 499,\r\n      yearly: 4990,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Premium digital business card with enhanced features\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: 50,\r\n        description: \"Showcase your products or services (up to 50)\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: false,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: true,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: false,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 10,\r\n        description: \"Upload and display up to 10 images\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: true,\r\n        description: \"Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"pro\",\r\n    name: \"Pro\",\r\n    description: \"Premium features for established businesses\",\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.pro,\r\n    pricing: {\r\n      monthly: 1999,\r\n      yearly: 19990,\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description: \"Elite digital business card with premium features\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: \"unlimited\",\r\n        description: \"Showcase unlimited products or services\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: true,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: true,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: true,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 50,\r\n        description: \"Upload and display up to 50 images\",\r\n      },\r\n      {\r\n        name: \"Priority Support\",\r\n        included: true,\r\n        description: \"Priority email and chat support\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: false,\r\n        description: \"No Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    id: \"enterprise\",\r\n    name: \"Enterprise\",\r\n    description: \"Custom solutions for large businesses\",\r\n    razorpayPlanIds: RAZORPAY_PLAN_IDS.enterprise,\r\n    pricing: {\r\n      monthly: 0, // Will be handled as \"Contact Sales\"\r\n      yearly: 0, // Will be handled as \"Contact Sales\"\r\n    },\r\n    features: [\r\n      {\r\n        name: \"Digital Business Card\",\r\n        included: true,\r\n        description:\r\n          \"Enterprise-grade digital business card with all premium features\",\r\n      },\r\n      {\r\n        name: \"QR Code for Sharing\",\r\n        included: true,\r\n        description: \"Shareable QR code for your business card\",\r\n      },\r\n      {\r\n        name: \"Social Media Links\",\r\n        included: true,\r\n        description: \"Add links to your social media profiles\",\r\n      },\r\n      {\r\n        name: \"Product Listings\",\r\n        included: true,\r\n        limit: \"unlimited\",\r\n        description: \"Showcase unlimited products or services\",\r\n      },\r\n      {\r\n        name: \"Customer Subscriptions\",\r\n        included: true,\r\n        description: \"Allow customers to subscribe to your business\",\r\n      },\r\n      {\r\n        name: \"Ratings & Reviews\",\r\n        included: true,\r\n        description: \"Collect and display customer reviews\",\r\n      },\r\n      {\r\n        name: \"Like Feature\",\r\n        included: true,\r\n        description: \"Let customers like your business card\",\r\n      },\r\n      {\r\n        name: \"Basic Analytics\",\r\n        included: true,\r\n        description: \"View basic metrics like views and clicks\",\r\n      },\r\n      {\r\n        name: \"Default Theme\",\r\n        included: true,\r\n        description: \"Use the default Dukancard theme\",\r\n      },\r\n      {\r\n        name: \"Delivery Hours\",\r\n        included: true,\r\n        description: \"Set and display your delivery hours\",\r\n      },\r\n      {\r\n        name: \"Business Hours\",\r\n        included: true,\r\n        description: \"Set and display your business hours\",\r\n      },\r\n      {\r\n        name: \"Theme Customization\",\r\n        included: true,\r\n        description: \"Customize your card theme and colors\",\r\n      },\r\n\r\n      {\r\n        name: \"Enhanced Analytics\",\r\n        included: true,\r\n        description: \"View detailed metrics including product views\",\r\n      },\r\n      {\r\n        name: \"Advanced Analytics\",\r\n        included: true,\r\n        description: \"Access comprehensive business insights\",\r\n      },\r\n      {\r\n        name: \"Photo Gallery\",\r\n        included: true,\r\n        limit: 100,\r\n        description: \"Upload and display up to 100 images\",\r\n      },\r\n      {\r\n        name: \"Dedicated Account Manager\",\r\n        included: true,\r\n        description: \"Get a dedicated account manager\",\r\n      },\r\n      {\r\n        name: \"Custom Analytics Dashboard\",\r\n        included: true,\r\n        description: \"Get a custom analytics dashboard\",\r\n      },\r\n      {\r\n        name: \"24/7 Priority Support\",\r\n        included: true,\r\n        description: \"24/7 priority support\",\r\n      },\r\n      {\r\n        name: \"White-Label Option\",\r\n        included: true,\r\n        description: \"Use your own branding instead of Dukancard\",\r\n      },\r\n      {\r\n        name: \"Dukancard Branding\",\r\n        included: false,\r\n        description: \"No Dukancard branding on your business card\",\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\n/**\r\n * Get a plan by its ID\r\n * @param planId The plan ID\r\n * @returns The plan object or undefined if not found\r\n */\r\nexport function getPlanById(planId: PlanType): Plan | undefined {\r\n  return PLANS.find((plan) => plan.id === planId);\r\n}\r\n\r\n/**\r\n * Get a plan by its Razorpay plan ID\r\n * @param planId The Razorpay plan ID\r\n * @returns The plan object or undefined if not found\r\n */\r\nexport function getPlanByRazorpayPlanId(\r\n  planId: string\r\n): Plan | undefined {\r\n  return PLANS.find(\r\n    (plan) =>\r\n      plan.razorpayPlanIds.monthly === planId ||\r\n      plan.razorpayPlanIds.yearly === planId\r\n  );\r\n}\r\n\r\n/**\r\n * Map a Razorpay plan ID to a Dukancard plan type\r\n * @param razorpayPlanId The Razorpay plan ID\r\n * @returns The corresponding Dukancard plan type or 'free' if not found\r\n */\r\nexport function mapRazorpayPlanToDukancardPlan(\r\n  razorpayPlanId: string\r\n): PlanType {\r\n  const plan = getPlanByRazorpayPlanId(razorpayPlanId);\r\n  return plan?.id || \"free\";\r\n}\r\n\r\n/**\r\n * Get the Razorpay plan ID for a given plan type and cycle\r\n * @param planType The plan type\r\n * @param planCycle The plan cycle\r\n * @returns The Razorpay plan ID or null if not found\r\n */\r\nexport function getRazorpayPlanId(\r\n  planType: PlanType,\r\n  planCycle: PlanCycle\r\n): string | null {\r\n  const plan = getPlanById(planType);\r\n  if (!plan) {\r\n    return null;\r\n  }\r\n\r\n  const planId = plan.razorpayPlanIds[planCycle];\r\n  return planId || null;\r\n}\r\n\r\n/**\r\n * Get the Razorpay plan ID for subscription actions\r\n * Centralized function for all subscription-related operations\r\n * @param planId The plan ID\r\n * @param planCycle The plan cycle\r\n * @returns The Razorpay plan ID or throws error if invalid\r\n */\r\nexport function getSubscriptionRazorpayPlanId(\r\n  planId: PlanType,\r\n  planCycle: PlanCycle\r\n): string {\r\n  if (planId === \"free\") {\r\n    return planCycle === \"monthly\" ? \"free-plan-monthly\" : \"free-plan-yearly\";\r\n  } else if (planId === \"basic\") {\r\n    return planCycle === \"monthly\"\r\n      ? (process.env.NODE_ENV === 'production' ? \"plan_QO9rDMTSLeT34b\" : \"plan_QRgoJF3OfM6mB0\")\r\n      : (process.env.NODE_ENV === 'production' ? \"plan_QO9sfgFBnEFATA\" : \"plan_QRgr1XzaksqvSZ\");\r\n  } else if (planId === \"growth\") {\r\n    return planCycle === \"monthly\"\r\n      ? (process.env.NODE_ENV === 'production' ? \"plan_QbnOd77S3FVeUc\" : \"plan_QbnMGvYCN7BM0V\")\r\n      : (process.env.NODE_ENV === 'production' ? \"plan_QbnOuE7iogOGTq\" : \"plan_QbnMdSbSeFrykv\");\r\n  } else if (planId === \"pro\") {\r\n    return planCycle === \"monthly\"\r\n      ? (process.env.NODE_ENV === 'production' ? \"plan_QbnP83wvmzUOqM\" : \"plan_QbnN4mwUu6H2Ho\")\r\n      : (process.env.NODE_ENV === 'production' ? \"plan_QbnPJinNt66Pik\" : \"plan_QbnNYfrCExI496\");\r\n  } else {\r\n    throw new Error(`Invalid plan selected: ${planId}`);\r\n  }\r\n}\r\n\r\n/**\r\n * Get the plan ID for a given plan type and cycle\r\n * @param planType The plan type\r\n * @param planCycle The plan cycle\r\n * @returns The plan ID or undefined if not found\r\n */\r\nexport function getPlanId(\r\n  planType: PlanType,\r\n  planCycle: PlanCycle\r\n): string | undefined {\r\n  // Use Razorpay plan IDs since we've migrated to Razorpay\r\n  const planId = getRazorpayPlanId(planType, planCycle);\r\n  return planId === null ? undefined : planId;\r\n}\r\n\r\n/**\r\n * Get the product limit for a given plan type\r\n * @param planType The plan type\r\n * @returns The product limit (number or Infinity for unlimited)\r\n */\r\nexport function getProductLimit(planType: PlanType | null | undefined): number {\r\n  if (!planType) return 0;\r\n\r\n  const plan = getPlanById(planType);\r\n  if (!plan) return 0;\r\n\r\n  const productFeature = plan.features.find(\r\n    (feature) => feature.name === \"Product Listings\"\r\n  );\r\n  if (!productFeature || !productFeature.included) return 0;\r\n\r\n  return productFeature.limit === \"unlimited\"\r\n    ? Infinity\r\n    : productFeature.limit || 0;\r\n}\r\n\r\n/**\r\n * Check if a feature is included in a plan\r\n * @param planType The plan type\r\n * @param featureName The feature name\r\n * @returns True if the feature is included, false otherwise\r\n */\r\nexport function hasFeature(\r\n  planType: PlanType | null | undefined,\r\n  featureName: string\r\n): boolean {\r\n  if (!planType) return false;\r\n\r\n  const plan = getPlanById(planType);\r\n  if (!plan) return false;\r\n\r\n  const feature = plan.features.find((feature) => feature.name === featureName);\r\n  return feature?.included || false;\r\n}\r\n\r\n/**\r\n * Get all plans\r\n * @returns Array of plans\r\n */\r\nexport function pricingPlans(): Plan[] {\r\n  return PLANS;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,aAAa;;;;;;;;;;;;;AAab,2BAA2B;AAC3B,iEAAiE;AACjE,MAAM,oBAAoB;IACxB,MAAM;QACJ,SAAS;QACT,QAAQ;IACV;IACA,OAAO;QACL,SAAS,6EAEL;QACJ,QAAQ,6EAEJ;IACN;IACA,QAAQ;QACN,SAAS,6EAEL;QACJ,QAAQ,6EAEJ;IACN;IACA,KAAK;QACH,SAAS,6EAEL;QACJ,QAAQ,6EAEJ;IACN;IACA,YAAY;QACV,SAAS;QACT,QAAQ;IACV;AACF;AAEA,kCAAkC;AAClC,MAAM,kBAAkC;AAqBjC,MAAM,QAAgB;IAC3B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB,kBAAkB,IAAI;QACvC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,iBAAiB,kBAAkB,KAAK;QACxC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,aAAa;QACb,iBAAiB,kBAAkB,MAAM;QACzC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB,kBAAkB,GAAG;QACtC,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,iBAAiB,kBAAkB,UAAU;QAC7C,SAAS;YACP,SAAS;YACT,QAAQ;QACV;QACA,UAAU;YACR;gBACE,MAAM;gBACN,UAAU;gBACV,aACE;YACJ;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YAEA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;YACA;gBACE,MAAM;gBACN,UAAU;gBACV,aAAa;YACf;SACD;IACH;CACD;AAOM,SAAS,YAAY,MAAgB;IAC1C,OAAO,MAAM,IAAI,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;AAC1C;AAOO,SAAS,wBACd,MAAc;IAEd,OAAO,MAAM,IAAI,CACf,CAAC,OACC,KAAK,eAAe,CAAC,OAAO,KAAK,UACjC,KAAK,eAAe,CAAC,MAAM,KAAK;AAEtC;AAOO,SAAS,+BACd,cAAsB;IAEtB,MAAM,OAAO,wBAAwB;IACrC,OAAO,MAAM,MAAM;AACrB;AAQO,SAAS,kBACd,QAAkB,EAClB,SAAoB;IAEpB,MAAM,OAAO,YAAY;IACzB,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,SAAS,KAAK,eAAe,CAAC,UAAU;IAC9C,OAAO,UAAU;AACnB;AASO,SAAS,8BACd,MAAgB,EAChB,SAAoB;IAEpB,IAAI,WAAW,QAAQ;QACrB,OAAO,cAAc,YAAY,sBAAsB;IACzD,OAAO,IAAI,WAAW,SAAS;QAC7B,OAAO,cAAc,YAChB,6EAAgE,wBAChE,6EAAgE;IACvE,OAAO,IAAI,WAAW,UAAU;QAC9B,OAAO,cAAc,YAChB,6EAAgE,wBAChE,6EAAgE;IACvE,OAAO,IAAI,WAAW,OAAO;QAC3B,OAAO,cAAc,YAChB,6EAAgE,wBAChE,6EAAgE;IACvE,OAAO;QACL,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,QAAQ;IACpD;AACF;AAQO,SAAS,UACd,QAAkB,EAClB,SAAoB;IAEpB,yDAAyD;IACzD,MAAM,SAAS,kBAAkB,UAAU;IAC3C,OAAO,WAAW,OAAO,YAAY;AACvC;AAOO,SAAS,gBAAgB,QAAqC;IACnE,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,OAAO,YAAY;IACzB,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,iBAAiB,KAAK,QAAQ,CAAC,IAAI,CACvC,CAAC,UAAY,QAAQ,IAAI,KAAK;IAEhC,IAAI,CAAC,kBAAkB,CAAC,eAAe,QAAQ,EAAE,OAAO;IAExD,OAAO,eAAe,KAAK,KAAK,cAC5B,WACA,eAAe,KAAK,IAAI;AAC9B;AAQO,SAAS,WACd,QAAqC,EACrC,WAAmB;IAEnB,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,OAAO,YAAY;IACzB,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,UAAU,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,IAAI,KAAK;IACjE,OAAO,SAAS,YAAY;AAC9B;AAMO,SAAS;IACd,OAAO;AACT", "debugId": null}}]}