{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/activities.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n/**\r\n * Database Triggers Documentation\r\n *\r\n * The following triggers are set up in Supabase to automatically track activities:\r\n *\r\n * 1. add_like_activity() - Trigger function for likes\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'like',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is added\r\n * CREATE TRIGGER trigger_add_like_activity\r\n * AFTER INSERT ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_like_activity();\r\n * ```\r\n *\r\n * 1a. delete_like_activity() - Trigger function for removing like activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'like';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is deleted\r\n * CREATE TRIGGER trigger_delete_like_activity\r\n * AFTER DELETE ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_like_activity();\r\n * ```\r\n *\r\n * 2. add_subscription_activity() - Trigger function for subscriptions\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'subscribe',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is added\r\n * CREATE TRIGGER trigger_add_subscription_activity\r\n * AFTER INSERT ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_subscription_activity();\r\n * ```\r\n *\r\n * 2a. delete_subscription_activity() - Trigger function for removing subscription activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'subscribe';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is deleted\r\n * CREATE TRIGGER trigger_delete_subscription_activity\r\n * AFTER DELETE ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_subscription_activity();\r\n * ```\r\n *\r\n * 3. add_rating_activity() - Trigger function for ratings\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Check if this is an update or insert\r\n *   IF TG_OP = 'UPDATE' THEN\r\n *     -- For updates, only add activity if rating changed\r\n *     IF NEW.rating = OLD.rating THEN\r\n *       RETURN NEW;\r\n *     END IF;\r\n *   END IF;\r\n *\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     rating_value,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'rating',\r\n *     NEW.rating,\r\n *     NEW.updated_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is added or updated\r\n * CREATE TRIGGER trigger_add_rating_activity\r\n * AFTER INSERT OR UPDATE OF rating ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_rating_activity();\r\n * ```\r\n *\r\n * 3a. delete_rating_activity() - Trigger function for removing rating activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'rating';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is deleted\r\n * CREATE TRIGGER trigger_delete_rating_activity\r\n * AFTER DELETE ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_rating_activity();\r\n * ```\r\n */\r\n\r\n/**\r\n * Table Structure\r\n *\r\n * The business_activities table is structured as follows:\r\n * ```sql\r\n * CREATE TABLE business_activities (\r\n *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\r\n *   business_profile_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,\r\n *   user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\r\n *   activity_type TEXT NOT NULL CHECK (activity_type IN ('like', 'subscribe', 'rating')),\r\n *   rating_value INTEGER,\r\n *   created_at TIMESTAMPTZ NOT NULL DEFAULT now(),\r\n *   is_read BOOLEAN NOT NULL DEFAULT false,\r\n *\r\n *   -- Add constraint to ensure rating_value is only set for rating activities\r\n *   CONSTRAINT rating_value_only_for_ratings CHECK (\r\n *     (activity_type = 'rating' AND rating_value IS NOT NULL) OR\r\n *     (activity_type != 'rating' AND rating_value IS NULL)\r\n *   )\r\n * );\r\n *\r\n * -- Indexes for better performance\r\n * CREATE INDEX idx_business_activities_business_profile_id ON business_activities(business_profile_id);\r\n * CREATE INDEX idx_business_activities_user_id ON business_activities(user_id);\r\n * CREATE INDEX idx_business_activities_is_read ON business_activities(is_read);\r\n * CREATE INDEX idx_business_activities_activity_type ON business_activities(activity_type);\r\n * CREATE INDEX idx_business_activities_created_at ON business_activities(created_at);\r\n * ```\r\n */\r\n\r\n/**\r\n * Row Level Security (RLS) Policies\r\n *\r\n * The following RLS policies are set up in Supabase to secure the business_activities table:\r\n *\r\n * 1. Select Policy - Allows business owners to read their own activities\r\n * ```sql\r\n * CREATE POLICY business_activities_select_policy ON business_activities\r\n *   FOR SELECT\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n *\r\n * 2. Update Policy - Allows business owners to update their own activities (for marking as read)\r\n * ```sql\r\n * CREATE POLICY business_activities_update_policy ON business_activities\r\n *   FOR UPDATE\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n */\r\n\r\n// Define types for activities\r\nexport interface BusinessActivity {\r\n  id: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  activity_type: \"like\" | \"subscribe\" | \"rating\";\r\n  rating_value: number | null;\r\n  created_at: string;\r\n  is_read: boolean;\r\n  user_profile?: {\r\n    name?: string | null;\r\n    avatar_url?: string | null;\r\n    email?: string | null;\r\n    is_business?: boolean;\r\n    business_name?: string | null;\r\n    business_slug?: string | null;\r\n    logo_url?: string | null;\r\n  };\r\n}\r\n\r\nexport type ActivitySortBy = \"newest\" | \"oldest\" | \"unread_first\";\r\n\r\n/**\r\n * Fetches activities for a business with pagination and sorting\r\n * Optionally marks fetched activities as read automatically\r\n */\r\nexport async function getBusinessActivities({\r\n  businessProfileId,\r\n  page = 1,\r\n  pageSize = 15,\r\n  sortBy = \"newest\",\r\n  filterBy = \"all\",\r\n  autoMarkAsRead = true, // New parameter to control auto-marking as read\r\n}: {\r\n  businessProfileId: string;\r\n  page?: number;\r\n  pageSize?: number;\r\n  sortBy?: ActivitySortBy;\r\n  filterBy?: \"all\" | \"like\" | \"subscribe\" | \"rating\" | \"unread\";\r\n  autoMarkAsRead?: boolean;\r\n}) {\r\n  const supabase = await createClient();\r\n  const supabaseAdmin = createAdminClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { activities: [], count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { activities: [], count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // Calculate pagination\r\n    const from = (page - 1) * pageSize;\r\n    const to = from + pageSize - 1;\r\n\r\n    // Build the query\r\n    let query = supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(\"business_profile_id\", businessProfileId);\r\n\r\n    // Apply filter\r\n    if (filterBy === \"like\") {\r\n      query = query.eq(\"activity_type\", \"like\");\r\n    } else if (filterBy === \"subscribe\") {\r\n      query = query.eq(\"activity_type\", \"subscribe\");\r\n    } else if (filterBy === \"rating\") {\r\n      query = query.eq(\"activity_type\", \"rating\");\r\n    } else if (filterBy === \"unread\") {\r\n      query = query.eq(\"is_read\", false);\r\n    }\r\n\r\n    // Apply sorting\r\n    switch (sortBy) {\r\n      case \"oldest\":\r\n        query = query.order(\"created_at\", { ascending: true });\r\n        break;\r\n      case \"unread_first\":\r\n        query = query.order(\"is_read\", { ascending: true }).order(\"created_at\", { ascending: false });\r\n        break;\r\n      case \"newest\":\r\n      default:\r\n        query = query.order(\"created_at\", { ascending: false });\r\n        break;\r\n    }\r\n\r\n    // Apply pagination\r\n    query = query.range(from, to);\r\n\r\n    // Execute the query\r\n    const { data: activities, error, count } = await query;\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching business activities:\", error);\r\n      return { activities: [], count: 0, error: error.message };\r\n    }\r\n\r\n    // Get user profiles for the activities\r\n    const userIds = activities.map((activity) => activity.user_id);\r\n\r\n    // Fetch both customer and business profiles\r\n    const [customerProfiles, businessProfiles] = await Promise.all([\r\n      supabaseAdmin\r\n        .from(\"customer_profiles\")\r\n        .select(\"id, name, avatar_url, email\")\r\n        .in(\"id\", userIds),\r\n      supabaseAdmin\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_name, business_slug, logo_url\")\r\n        .in(\"id\", userIds),\r\n    ]);\r\n\r\n    // Combine the profiles\r\n    const userProfiles = new Map();\r\n\r\n    // Add customer profiles to the map\r\n    customerProfiles.data?.forEach((profile) => {\r\n      userProfiles.set(profile.id, {\r\n        name: profile.name,\r\n        avatar_url: profile.avatar_url,\r\n        email: profile.email,\r\n        is_business: false,\r\n      });\r\n    });\r\n\r\n    // Add business profiles to the map, overriding customer profiles if both exist\r\n    businessProfiles.data?.forEach((profile) => {\r\n      const existingProfile = userProfiles.get(profile.id) || {};\r\n      userProfiles.set(profile.id, {\r\n        ...existingProfile,\r\n        business_name: profile.business_name,\r\n        business_slug: profile.business_slug,\r\n        logo_url: profile.logo_url,\r\n        is_business: true,\r\n      });\r\n    });\r\n\r\n    // Attach user profiles to activities\r\n    const activitiesWithProfiles = activities.map((activity) => ({\r\n      ...activity,\r\n      user_profile: userProfiles.get(activity.user_id) || {},\r\n    }));\r\n\r\n    // Auto-mark fetched activities as read if enabled\r\n    if (autoMarkAsRead && activities.length > 0) {\r\n      // Get IDs of unread activities\r\n      const unreadActivityIds = activities\r\n        .filter(activity => !activity.is_read)\r\n        .map(activity => activity.id);\r\n\r\n      // Only proceed if there are unread activities\r\n      if (unreadActivityIds.length > 0) {\r\n        // Mark these activities as read\r\n        const { error: markError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", unreadActivityIds);\r\n\r\n        if (markError) {\r\n          console.error(\"Error auto-marking activities as read:\", markError);\r\n        } else {\r\n          // Update the activities in our result to reflect they're now read\r\n          activitiesWithProfiles.forEach(activity => {\r\n            if (unreadActivityIds.includes(activity.id)) {\r\n              activity.is_read = true;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      activities: activitiesWithProfiles,\r\n      count: count || 0,\r\n      error: null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching business activities:\", error);\r\n    return {\r\n      activities: [],\r\n      count: 0,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Marks activities as read\r\n * Handles pagination for large numbers of activities to work around Supabase's 1000 row limit\r\n */\r\nexport async function markActivitiesAsRead({\r\n  businessProfileId,\r\n  activityIds,\r\n}: {\r\n  businessProfileId: string;\r\n  activityIds: string[] | \"all\";\r\n}) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { success: false, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { success: false, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // If marking specific activities as read\r\n    if (activityIds !== \"all\") {\r\n      // Handle case where we have specific activity IDs\r\n      const { error } = await supabase\r\n        .from(\"business_activities\")\r\n        .update({ is_read: true })\r\n        .eq(\"business_profile_id\", businessProfileId)\r\n        .in(\"id\", activityIds);\r\n\r\n      if (error) {\r\n        console.error(\"Error marking specific activities as read:\", error);\r\n        return { success: false, error: error.message };\r\n      }\r\n    } else {\r\n      // Handle \"mark all as read\" with pagination to work around Supabase's 1000 row limit\r\n      const BATCH_SIZE = 1000; // Maximum number of rows to update at once\r\n      let hasMore = true;\r\n      let processedCount = 0;\r\n\r\n      while (hasMore) {\r\n        // Get a batch of unread activity IDs\r\n        const { data: unreadActivities, error: fetchError } = await supabase\r\n          .from(\"business_activities\")\r\n          .select(\"id\")\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .eq(\"is_read\", false)\r\n          .limit(BATCH_SIZE);\r\n\r\n        if (fetchError) {\r\n          console.error(\"Error fetching unread activities:\", fetchError);\r\n          return { success: false, error: fetchError.message };\r\n        }\r\n\r\n        // If no more unread activities, we're done\r\n        if (!unreadActivities || unreadActivities.length === 0) {\r\n          hasMore = false;\r\n          break;\r\n        }\r\n\r\n        // Extract IDs from the batch\r\n        const batchIds = unreadActivities.map(activity => activity.id);\r\n\r\n        // Mark this batch as read\r\n        const { error: updateError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", batchIds);\r\n\r\n        if (updateError) {\r\n          console.error(\"Error marking batch as read:\", updateError);\r\n          return { success: false, error: updateError.message };\r\n        }\r\n\r\n        // Update processed count and check if we need to continue\r\n        processedCount += batchIds.length;\r\n        hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more\r\n      }\r\n\r\n      console.log(`Marked ${processedCount} activities as read`);\r\n    }\r\n\r\n    // Revalidate the activities page\r\n    revalidatePath(\"/dashboard/business/activities\");\r\n\r\n    return { success: true, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error marking activities as read:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the count of unread activities\r\n */\r\nexport async function getUnreadActivitiesCount(businessProfileId: string) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    const { count, error } = await supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\", head: true })\r\n      .eq(\"business_profile_id\", businessProfileId)\r\n      .eq(\"is_read\", false);\r\n\r\n    if (error) {\r\n      console.error(\"Error getting unread activities count:\", error);\r\n      return { count: 0, error: error.message };\r\n    }\r\n\r\n    return { count: count || 0, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error getting unread activities count:\", error);\r\n    return { count: 0, error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;;;;;AAuPO,eAAe,sBAAsB,EAC1C,iBAAiB,EACjB,OAAO,CAAC,EACR,WAAW,EAAE,EACb,SAAS,QAAQ,EACjB,WAAW,KAAK,EAChB,iBAAiB,IAAI,EAQtB;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IAEtC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,YAAY,EAAE;YAAE,OAAO;YAAG,OAAO;QAAoB;IAChE;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,YAAY,EAAE;YAAE,OAAO;YAAG,OAAO;QAAe;IAC3D;IAEA,IAAI;QACF,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,WAAW;QAE7B,kBAAkB;QAClB,IAAI,QAAQ,SACT,IAAI,CAAC,uBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ,GAC7B,EAAE,CAAC,uBAAuB;QAE7B,eAAe;QACf,IAAI,aAAa,QAAQ;YACvB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,aAAa;YACnC,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,UAAU;YAChC,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,UAAU;YAChC,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,gBAAgB;QAChB,OAAQ;YACN,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBACpD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,WAAW;oBAAE,WAAW;gBAAK,GAAG,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAC3F;YACF,KAAK;YACL;gBACE,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBACrD;QACJ;QAEA,mBAAmB;QACnB,QAAQ,MAAM,KAAK,CAAC,MAAM;QAE1B,oBAAoB;QACpB,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAEjD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,YAAY,EAAE;gBAAE,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1D;QAEA,uCAAuC;QACvC,MAAM,UAAU,WAAW,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO;QAE7D,4CAA4C;QAC5C,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7D,cACG,IAAI,CAAC,qBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM;YACZ,cACG,IAAI,CAAC,qBACL,MAAM,CAAC,8CACP,EAAE,CAAC,MAAM;SACb;QAED,uBAAuB;QACvB,MAAM,eAAe,IAAI;QAEzB,mCAAmC;QACnC,iBAAiB,IAAI,EAAE,QAAQ,CAAC;YAC9B,aAAa,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC3B,MAAM,QAAQ,IAAI;gBAClB,YAAY,QAAQ,UAAU;gBAC9B,OAAO,QAAQ,KAAK;gBACpB,aAAa;YACf;QACF;QAEA,+EAA+E;QAC/E,iBAAiB,IAAI,EAAE,QAAQ,CAAC;YAC9B,MAAM,kBAAkB,aAAa,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;YACzD,aAAa,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC3B,GAAG,eAAe;gBAClB,eAAe,QAAQ,aAAa;gBACpC,eAAe,QAAQ,aAAa;gBACpC,UAAU,QAAQ,QAAQ;gBAC1B,aAAa;YACf;QACF;QAEA,qCAAqC;QACrC,MAAM,yBAAyB,WAAW,GAAG,CAAC,CAAC,WAAa,CAAC;gBAC3D,GAAG,QAAQ;gBACX,cAAc,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC;YACvD,CAAC;QAED,kDAAkD;QAClD,IAAI,kBAAkB,WAAW,MAAM,GAAG,GAAG;YAC3C,+BAA+B;YAC/B,MAAM,oBAAoB,WACvB,MAAM,CAAC,CAAA,WAAY,CAAC,SAAS,OAAO,EACpC,GAAG,CAAC,CAAA,WAAY,SAAS,EAAE;YAE9B,8CAA8C;YAC9C,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,gCAAgC;gBAChC,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,uBACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;gBAEZ,IAAI,WAAW;oBACb,QAAQ,KAAK,CAAC,0CAA0C;gBAC1D,OAAO;oBACL,kEAAkE;oBAClE,uBAAuB,OAAO,CAAC,CAAA;wBAC7B,IAAI,kBAAkB,QAAQ,CAAC,SAAS,EAAE,GAAG;4BAC3C,SAAS,OAAO,GAAG;wBACrB;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACL,YAAY;YACZ,OAAO,SAAS;YAChB,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;YACL,YAAY,EAAE;YACd,OAAO;YACP,OAAO;QACT;IACF;AACF;AAMO,eAAe,qBAAqB,EACzC,iBAAiB,EACjB,WAAW,EAIZ;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoB;IACtD;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;IAEA,IAAI;QACF,yCAAyC;QACzC,IAAI,gBAAgB,OAAO;YACzB,kDAAkD;YAClD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC;gBAAE,SAAS;YAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,8CAA8C;gBAC5D,OAAO;oBAAE,SAAS;oBAAO,OAAO,MAAM,OAAO;gBAAC;YAChD;QACF,OAAO;YACL,qFAAqF;YACrF,MAAM,aAAa,MAAM,2CAA2C;YACpE,IAAI,UAAU;YACd,IAAI,iBAAiB;YAErB,MAAO,QAAS;gBACd,qCAAqC;gBACrC,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,uBACL,MAAM,CAAC,MACP,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,WAAW,OACd,KAAK,CAAC;gBAET,IAAI,YAAY;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,OAAO;wBAAE,SAAS;wBAAO,OAAO,WAAW,OAAO;oBAAC;gBACrD;gBAEA,2CAA2C;gBAC3C,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;oBACtD,UAAU;oBACV;gBACF;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,iBAAiB,GAAG,CAAC,CAAA,WAAY,SAAS,EAAE;gBAE7D,0BAA0B;gBAC1B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,uBACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;gBAEZ,IAAI,aAAa;oBACf,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,OAAO;wBAAE,SAAS;wBAAO,OAAO,YAAY,OAAO;oBAAC;gBACtD;gBAEA,0DAA0D;gBAC1D,kBAAkB,SAAS,MAAM;gBACjC,UAAU,SAAS,MAAM,KAAK,YAAY,8CAA8C;YAC1F;YAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,eAAe,mBAAmB,CAAC;QAC3D;QAEA,iCAAiC;QACjC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAKO,eAAe,yBAAyB,iBAAyB;IACtE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;YAAG,OAAO;QAAoB;IAChD;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,OAAO;YAAG,OAAO;QAAe;IAC3C;IAEA,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAC5B,IAAI,CAAC,uBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,WAAW;QAEjB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBAAE,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1C;QAEA,OAAO;YAAE,OAAO,SAAS;YAAG,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YAAE,OAAO;YAAG,OAAO;QAA+B;IAC3D;AACF;;;IAlTsB;IA4KA;IAqGA;;AAjRA,+OAAA;AA4KA,+OAAA;AAqGA,+OAAA", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { unstable_noStore as noStore } from \"next/cache\";\r\n\r\n/**\r\n * Structure for the analytics data returned by getVisitAnalytics\r\n *\r\n * This data is used to display visitor metrics on the analytics page.\r\n * The data comes from three sources:\r\n * 1. Pre-aggregated metrics in the business_profiles table\r\n * 2. Monthly metrics in the monthly_visit_metrics table\r\n * 3. Trend data calculated by database functions\r\n *\r\n * Database Implementation:\r\n * - total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days\r\n *   are stored in the business_profiles table and updated by triggers\r\n * - currentMonthUniqueVisits, previousMonthUniqueVisits are stored in the monthly_visit_metrics table\r\n *   and updated by the update_monthly_visit_counts() function via the handle_new_visit_monthly trigger\r\n * - dailyTrend7Days, dailyTrend30Days, hourlyTrendToday, monthlyTrend are calculated\r\n *   by database functions when requested\r\n *\r\n * Timezone Handling:\r\n * - All visit metrics are calculated based on IST (Indian Standard Time, UTC+5:30)\r\n * - The visited_at column in card_visits stores timestamps in UTC\r\n * - All database functions convert these timestamps to IST using 'visited_at AT TIME ZONE 'Asia/Kolkata''\r\n * - The update_visit_counts() function uses the most recent visit date in IST as \"today\"\r\n * - This ensures accurate metrics regardless of when the functions run\r\n *\r\n * Monthly Metrics Implementation:\r\n * - Monthly metrics are stored in the monthly_visit_metrics table with year and month columns\r\n * - The update_monthly_visit_counts() function updates the monthly metrics when a new visit is recorded\r\n * - The update_all_monthly_visit_metrics() function updates monthly metrics for all businesses\r\n *   and is scheduled to run on the 1st day of each month\r\n * - The populate_historical_monthly_data() function populates historical monthly data\r\n *   from existing card_visits records\r\n * - The get_monthly_unique_visits() function gets monthly unique visits for a specific month and year\r\n * - The get_monthly_unique_visit_trend() function gets monthly trend data for a date range\r\n * - The get_available_years_for_monthly_metrics() function gets available years from monthly_visit_metrics table\r\n *\r\n * Real-time Updates:\r\n * - The component uses realtime database for all metrics:\r\n *   - Total unique visits, today's visits, yesterday's visits, 7-day visits, and 30-day visits from business_profiles table\r\n *   - Total likes, total subscriptions, and average rating from business_profiles table\r\n *   - Monthly unique visits from monthly_visit_metrics table\r\n * - When changes occur in any of these tables, the UI updates automatically with animations\r\n * - Note: card_visits table does not have realtime enabled; instead, we rely on the business_profiles table\r\n *   which has pre-aggregated metrics that are updated by triggers when new visits are recorded\r\n */\r\nexport interface VisitAnalyticsData {\r\n  totalUniqueVisits: number;  // Total lifetime unique visitors\r\n  todayUniqueVisits: number;  // Unique visitors today (based on IST timezone)\r\n  yesterdayUniqueVisits: number;  // Unique visitors yesterday (based on IST timezone)\r\n  visits7Days: number;  // Unique visitors in the last 7 days\r\n  visits30Days: number;  // Unique visitors in the last 30 days\r\n  currentMonthUniqueVisits: number;  // Unique visitors in the current month\r\n  previousMonthUniqueVisits: number;  // Unique visitors in the previous month\r\n  currentYear: number;  // Current year (for UI display)\r\n  currentMonth: number;  // Current month (for UI display)\r\n  dailyTrend7Days: { date: string; visits: number }[];  // Daily trend for the last 7 days\r\n  dailyTrend30Days: { date: string; visits: number }[];  // Daily trend for the last 30 days\r\n  hourlyTrendToday: { hour: number; visits: number }[];  // Hourly trend for today\r\n  monthlyTrend: { year: number; month: number; visits: number }[];  // Monthly trend data\r\n  availableYears: number[];  // Available years for monthly trend selection\r\n}\r\n\r\nexport interface DailyTrendData {\r\n  dailyTrend7Days: { date: string; visits: number }[];\r\n  dailyTrend30Days: { date: string; visits: number }[];\r\n}\r\n\r\nexport interface HourlyTrendData {\r\n  hourlyTrendToday: { hour: number; visits: number }[];\r\n}\r\n\r\n// Helper function to get date strings in 'Asia/Kolkata' timezone (YYYY-MM-DD)\r\nconst getKolkataDateString = (offsetDays: number = 0): string => {\r\n  // Get current time in UTC\r\n  const now = new Date();\r\n  // Calculate the offset for IST (UTC+5:30) in milliseconds\r\n  const istOffset = 5.5 * 60 * 60 * 1000;\r\n  // Create a new Date object representing the time in IST\r\n  const istDate = new Date(now.getTime() + istOffset);\r\n  // Apply the day offset in UTC to avoid issues around midnight IST\r\n  istDate.setUTCDate(istDate.getUTCDate() - offsetDays);\r\n  // Format as YYYY-MM-DD based on the IST date parts\r\n  const year = istDate.getUTCFullYear();\r\n  const month = String(istDate.getUTCMonth() + 1).padStart(2, \"0\");\r\n  const day = String(istDate.getUTCDate()).padStart(2, \"0\");\r\n  return `${year}-${month}-${day}`;\r\n};\r\n\r\n// Removed unused getStartOfDayUTC function\r\n\r\n/**\r\n * Gets visit analytics data for the current user's business profile\r\n *\r\n * This function:\r\n * 1. Gets the current user's business profile ID\r\n * 2. Fetches pre-aggregated metrics from the business_profiles table\r\n * 3. Fetches monthly metrics from the monthly_visit_metrics table\r\n * 4. Fetches trend data using database functions\r\n * 5. Falls back to calculating metrics if pre-aggregated data is not available\r\n *\r\n * Database Functions Used:\r\n * - get_daily_unique_visit_trend: Gets daily trend data for a date range based on IST dates\r\n * - get_hourly_unique_visit_trend: Gets hourly trend data for a specific date based on IST hours\r\n * - get_monthly_unique_visits: Gets monthly unique visits for a specific month and year\r\n * - get_monthly_unique_visit_trend: Gets monthly trend data for a date range\r\n * - get_total_unique_visits: Gets total unique visits (fallback)\r\n * - get_daily_unique_visits: Gets daily unique visits (fallback)\r\n * - get_period_unique_visits: Gets period unique visits (fallback)\r\n *\r\n * Pre-aggregated Metrics:\r\n * - total_visits: Updated by update_visit_counts() trigger function\r\n * - today_visits: Updated by update_visit_counts() trigger function based on the most recent visit date in IST\r\n * - yesterday_visits: Updated by update_visit_counts() trigger function based on the day before the most recent visit date\r\n * - visits_7_days: Updated by update_visit_counts() trigger function\r\n * - visits_30_days: Updated by update_visit_counts() trigger function\r\n *\r\n * Monthly Metrics:\r\n * - currentMonthUniqueVisits: Stored in monthly_visit_metrics table\r\n * - previousMonthUniqueVisits: Stored in monthly_visit_metrics table\r\n * - monthlyTrend: Calculated by get_monthly_unique_visit_trend() function\r\n *\r\n * These metrics are managed by scheduled cron jobs:\r\n * - reset-visit-counts: Runs at midnight IST (6:30 PM UTC) to reset daily counts\r\n * - clean-card-visits: Runs at 1 AM IST (7:30 PM UTC) to delete records older than 31 days\r\n * - update-monthly-visit-metrics: Runs at midnight IST on the 1st day of each month\r\n *\r\n * @returns Object containing analytics data or error\r\n */\r\nexport async function getVisitAnalytics(userPlan?: string | null): Promise<{\r\n  data?: VisitAnalyticsData;\r\n  error?: string;\r\n}> {\r\n  // Check if user has access to premium analytics features\r\n  const isPremiumUser = userPlan === \"growth\" || userPlan === \"pro\" || userPlan === \"enterprise\";\r\n  noStore(); // Ensure data is fetched dynamically\r\n  const supabase = await createClient();\r\n\r\n  // 1. Get current user ID\r\n  const {\r\n    data: { user },\r\n    error: userError,\r\n  } = await supabase.auth.getUser();\r\n  if (userError || !user) {\r\n    return { error: \"Authentication required.\" };\r\n  }\r\n\r\n  // 2. Get business profile ID (assuming it's the same as user ID for business users)\r\n  // Adjust if business_profile_id is stored differently or needs a separate lookup\r\n  const businessProfileId = user.id;\r\n\r\n  try {\r\n    // First, get the pre-aggregated visit counts from business_profiles\r\n    const { data: profile, error: profileError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    // --- Fetch Aggregated Data using NEW RPC Functions ---\r\n    const todayStr = getKolkataDateString(0);\r\n    const yesterdayStr = getKolkataDateString(1);\r\n    const sevenDaysAgoStr = getKolkataDateString(7);\r\n    const thirtyDaysAgoStr = getKolkataDateString(30);\r\n\r\n    // Get current date in IST for monthly metrics\r\n    const now = new Date();\r\n    const istOffset = 5.5 * 60 * 60 * 1000;\r\n    const istDate = new Date(now.getTime() + istOffset);\r\n    const currentYear = istDate.getUTCFullYear();\r\n    const currentMonth = istDate.getUTCMonth() + 1; // 1-12\r\n\r\n    // Calculate previous month and year\r\n    let previousMonth = currentMonth - 1;\r\n    let previousYear = currentYear;\r\n    if (previousMonth === 0) {\r\n      previousMonth = 12;\r\n      previousYear = currentYear - 1;\r\n    }\r\n\r\n    // Initialize premium data with empty arrays for basic plan users\r\n    let trend7Data: { date: string; visits: number }[] = [];\r\n    let trend30Data: { date: string; visits: number }[] = [];\r\n    let hourlyTrendData: { hour: number; visits: number }[] = [];\r\n\r\n    // Only fetch premium analytics data for premium users\r\n    if (isPremiumUser) {\r\n      // Daily Trend (Last 7 Days)\r\n      const { data: trend7Result, error: trend7Error } = await supabase.rpc(\r\n        \"get_daily_unique_visit_trend\",\r\n        {\r\n          business_id: businessProfileId,\r\n          start_date: sevenDaysAgoStr,\r\n          end_date: todayStr,\r\n        }\r\n      );\r\n      if (trend7Error) throw new Error(`Failed to fetch 7-day trend: ${trend7Error.message}`);\r\n      trend7Data = trend7Result;\r\n\r\n      // Daily Trend (Last 30 Days)\r\n      const { data: trend30Result, error: trend30Error } = await supabase.rpc(\r\n        \"get_daily_unique_visit_trend\",\r\n        {\r\n          business_id: businessProfileId,\r\n          start_date: thirtyDaysAgoStr,\r\n          end_date: todayStr,\r\n        }\r\n      );\r\n      if (trend30Error) throw new Error(`Failed to fetch 30-day trend: ${trend30Error.message}`);\r\n      trend30Data = trend30Result;\r\n\r\n      // Hourly Trend (Today)\r\n      const { data: hourlyTrendResult, error: hourlyTrendError } = await supabase.rpc(\r\n        \"get_hourly_unique_visit_trend\",\r\n        {\r\n          business_id: businessProfileId,\r\n          target_date: todayStr,\r\n        }\r\n      );\r\n      if (hourlyTrendError) throw new Error(`Failed to fetch hourly trend: ${hourlyTrendError.message}`);\r\n      hourlyTrendData = hourlyTrendResult;\r\n    }\r\n\r\n    // Get current month's unique visits\r\n    const { data: currentMonthData, error: currentMonthError } = await supabase.rpc(\r\n      \"get_monthly_unique_visits\",\r\n      {\r\n        business_id: businessProfileId,\r\n        target_year: currentYear,\r\n        target_month: currentMonth,\r\n      }\r\n    );\r\n    if (currentMonthError) throw new Error(`Failed to fetch current month visits: ${currentMonthError.message}`);\r\n\r\n    // Get previous month's unique visits\r\n    const { data: previousMonthData, error: previousMonthError } = await supabase.rpc(\r\n      \"get_monthly_unique_visits\",\r\n      {\r\n        business_id: businessProfileId,\r\n        target_year: previousYear,\r\n        target_month: previousMonth,\r\n      }\r\n    );\r\n    if (previousMonthError) throw new Error(`Failed to fetch previous month visits: ${previousMonthError.message}`);\r\n\r\n    // Initialize monthly trend data with empty arrays for basic plan users\r\n    let availableYears: number[] = [currentYear];\r\n    let monthlyTrendData: { year: number; month: number; visits: number }[] = [];\r\n\r\n    // Only fetch monthly trend data for premium users\r\n    if (isPremiumUser) {\r\n      // Get available years from monthly_visit_metrics\r\n      const { data: availableYearsData, error: availableYearsError } = await supabase.rpc(\r\n        \"get_available_years_for_monthly_metrics\",\r\n        {\r\n          business_id: businessProfileId\r\n        }\r\n      );\r\n      if (availableYearsError) throw new Error(`Failed to fetch available years: ${availableYearsError.message}`);\r\n\r\n      // Create array of available years, with current year as fallback\r\n      if (availableYearsData && Array.isArray(availableYearsData) && availableYearsData.length > 0) {\r\n        availableYears = availableYearsData.map(item => item.year);\r\n      } else {\r\n        // If no years available, use current year as fallback\r\n        availableYears = [currentYear];\r\n      }\r\n\r\n      // Get monthly trend data for all available years\r\n      const { data: monthlyTrendResult, error: monthlyTrendError } = await supabase.rpc(\r\n        \"get_monthly_unique_visit_trend\",\r\n        {\r\n          business_id: businessProfileId,\r\n          start_year: Math.min(...availableYears),\r\n          start_month: 1,\r\n          end_year: currentYear,\r\n          end_month: currentMonth,\r\n        }\r\n      );\r\n      if (monthlyTrendError) throw new Error(`Failed to fetch monthly trend: ${monthlyTrendError.message}`);\r\n      monthlyTrendData = monthlyTrendResult;\r\n    }\r\n\r\n    // If we have pre-aggregated data from business_profiles, use it\r\n    // Otherwise, calculate from trend data\r\n    let totalVisits = profile?.total_visits ?? 0;\r\n    let todayVisits = profile?.today_visits ?? 0;\r\n    let yesterdayVisits = profile?.yesterday_visits ?? 0;\r\n    let visits7Days = profile?.visits_7_days ?? 0;\r\n    let visits30Days = profile?.visits_30_days ?? 0;\r\n\r\n    // If we don't have pre-aggregated data, calculate from trend data\r\n    if (!profile || profileError) {\r\n      console.warn(\"Using calculated visit metrics due to missing pre-aggregated data\");\r\n\r\n      // Calculate Today's and Yesterday's visits from the 7-day trend data\r\n      todayVisits = trend7Data?.find((d: { date: string; visits: number }) => d.date === todayStr)?.visits ?? 0;\r\n      yesterdayVisits = trend7Data?.find((d: { date: string; visits: number }) => d.date === yesterdayStr)?.visits ?? 0;\r\n\r\n      // Calculate total visits (fallback)\r\n      const { data: totalCount, error: totalError } = await supabase.rpc(\r\n        \"get_total_unique_visits\",\r\n        { business_id: businessProfileId }\r\n      );\r\n      if (totalError) throw new Error(`Failed to fetch total visits: ${totalError.message}`);\r\n      totalVisits = totalCount ?? 0;\r\n\r\n      // Calculate 7-day and 30-day visits\r\n      visits7Days = trend7Data?.reduce((sum: number, day: { visits: number }) => sum + day.visits, 0) ?? 0;\r\n      visits30Days = trend30Data?.reduce((sum: number, day: { visits: number }) => sum + day.visits, 0) ?? 0;\r\n    }\r\n\r\n    // --- Clicks Data Removed ---\r\n\r\n    const analyticsData: VisitAnalyticsData = {\r\n      totalUniqueVisits: Number(totalVisits),\r\n      todayUniqueVisits: Number(todayVisits),\r\n      yesterdayUniqueVisits: Number(yesterdayVisits),\r\n      visits7Days: Number(visits7Days),\r\n      visits30Days: Number(visits30Days),\r\n      currentMonthUniqueVisits: Number(currentMonthData || 0),\r\n      previousMonthUniqueVisits: Number(previousMonthData || 0),\r\n      currentYear: currentYear,\r\n      currentMonth: currentMonth,\r\n      dailyTrend7Days: trend7Data ?? [],\r\n      dailyTrend30Days: trend30Data ?? [],\r\n      hourlyTrendToday: hourlyTrendData ?? [],\r\n      monthlyTrend: monthlyTrendData ?? [],\r\n      availableYears: availableYears,\r\n    };\r\n\r\n    return { data: analyticsData };\r\n\r\n  } catch (error) {\r\n    console.error(\"getVisitAnalytics Error:\", error);\r\n    // Ensure error message is always a string\r\n    return {\r\n      error:\r\n        error instanceof Error ? error.message : \"An unknown error occurred.\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;;AAwEA,8EAA8E;AAC9E,MAAM,uBAAuB,CAAC,aAAqB,CAAC;IAClD,0BAA0B;IAC1B,MAAM,MAAM,IAAI;IAChB,0DAA0D;IAC1D,MAAM,YAAY,MAAM,KAAK,KAAK;IAClC,wDAAwD;IACxD,MAAM,UAAU,IAAI,KAAK,IAAI,OAAO,KAAK;IACzC,kEAAkE;IAClE,QAAQ,UAAU,CAAC,QAAQ,UAAU,KAAK;IAC1C,mDAAmD;IACnD,MAAM,OAAO,QAAQ,cAAc;IACnC,MAAM,QAAQ,OAAO,QAAQ,WAAW,KAAK,GAAG,QAAQ,CAAC,GAAG;IAC5D,MAAM,MAAM,OAAO,QAAQ,UAAU,IAAI,QAAQ,CAAC,GAAG;IACrD,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;AAClC;AA0CO,eAAe,kBAAkB,QAAwB;IAI9D,yDAAyD;IACzD,MAAM,gBAAgB,aAAa,YAAY,aAAa,SAAS,aAAa;IAClF,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD,KAAK,qCAAqC;IAChD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,yBAAyB;IACzB,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,OAAO;QAA2B;IAC7C;IAEA,oFAAoF;IACpF,iFAAiF;IACjF,MAAM,oBAAoB,KAAK,EAAE;IAEjC,IAAI;QACF,oEAAoE;QACpE,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,qBACL,MAAM,CAAC,+EACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,wDAAwD;QACxD,MAAM,WAAW,qBAAqB;QACtC,MAAM,eAAe,qBAAqB;QAC1C,MAAM,kBAAkB,qBAAqB;QAC7C,MAAM,mBAAmB,qBAAqB;QAE9C,8CAA8C;QAC9C,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,MAAM,KAAK,KAAK;QAClC,MAAM,UAAU,IAAI,KAAK,IAAI,OAAO,KAAK;QACzC,MAAM,cAAc,QAAQ,cAAc;QAC1C,MAAM,eAAe,QAAQ,WAAW,KAAK,GAAG,OAAO;QAEvD,oCAAoC;QACpC,IAAI,gBAAgB,eAAe;QACnC,IAAI,eAAe;QACnB,IAAI,kBAAkB,GAAG;YACvB,gBAAgB;YAChB,eAAe,cAAc;QAC/B;QAEA,iEAAiE;QACjE,IAAI,aAAiD,EAAE;QACvD,IAAI,cAAkD,EAAE;QACxD,IAAI,kBAAsD,EAAE;QAE5D,sDAAsD;QACtD,IAAI,eAAe;YACjB,4BAA4B;YAC5B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAAS,GAAG,CACnE,gCACA;gBACE,aAAa;gBACb,YAAY;gBACZ,UAAU;YACZ;YAEF,IAAI,aAAa,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,YAAY,OAAO,EAAE;YACtF,aAAa;YAEb,6BAA6B;YAC7B,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAAS,GAAG,CACrE,gCACA;gBACE,aAAa;gBACb,YAAY;gBACZ,UAAU;YACZ;YAEF,IAAI,cAAc,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,aAAa,OAAO,EAAE;YACzF,cAAc;YAEd,uBAAuB;YACvB,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,gBAAgB,EAAE,GAAG,MAAM,SAAS,GAAG,CAC7E,iCACA;gBACE,aAAa;gBACb,aAAa;YACf;YAEF,IAAI,kBAAkB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,iBAAiB,OAAO,EAAE;YACjG,kBAAkB;QACpB;QAEA,oCAAoC;QACpC,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAAS,GAAG,CAC7E,6BACA;YACE,aAAa;YACb,aAAa;YACb,cAAc;QAChB;QAEF,IAAI,mBAAmB,MAAM,IAAI,MAAM,CAAC,sCAAsC,EAAE,kBAAkB,OAAO,EAAE;QAE3G,qCAAqC;QACrC,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,kBAAkB,EAAE,GAAG,MAAM,SAAS,GAAG,CAC/E,6BACA;YACE,aAAa;YACb,aAAa;YACb,cAAc;QAChB;QAEF,IAAI,oBAAoB,MAAM,IAAI,MAAM,CAAC,uCAAuC,EAAE,mBAAmB,OAAO,EAAE;QAE9G,uEAAuE;QACvE,IAAI,iBAA2B;YAAC;SAAY;QAC5C,IAAI,mBAAsE,EAAE;QAE5E,kDAAkD;QAClD,IAAI,eAAe;YACjB,iDAAiD;YACjD,MAAM,EAAE,MAAM,kBAAkB,EAAE,OAAO,mBAAmB,EAAE,GAAG,MAAM,SAAS,GAAG,CACjF,2CACA;gBACE,aAAa;YACf;YAEF,IAAI,qBAAqB,MAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,oBAAoB,OAAO,EAAE;YAE1G,iEAAiE;YACjE,IAAI,sBAAsB,MAAM,OAAO,CAAC,uBAAuB,mBAAmB,MAAM,GAAG,GAAG;gBAC5F,iBAAiB,mBAAmB,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;YAC3D,OAAO;gBACL,sDAAsD;gBACtD,iBAAiB;oBAAC;iBAAY;YAChC;YAEA,iDAAiD;YACjD,MAAM,EAAE,MAAM,kBAAkB,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAAS,GAAG,CAC/E,kCACA;gBACE,aAAa;gBACb,YAAY,KAAK,GAAG,IAAI;gBACxB,aAAa;gBACb,UAAU;gBACV,WAAW;YACb;YAEF,IAAI,mBAAmB,MAAM,IAAI,MAAM,CAAC,+BAA+B,EAAE,kBAAkB,OAAO,EAAE;YACpG,mBAAmB;QACrB;QAEA,gEAAgE;QAChE,uCAAuC;QACvC,IAAI,cAAc,SAAS,gBAAgB;QAC3C,IAAI,cAAc,SAAS,gBAAgB;QAC3C,IAAI,kBAAkB,SAAS,oBAAoB;QACnD,IAAI,cAAc,SAAS,iBAAiB;QAC5C,IAAI,eAAe,SAAS,kBAAkB;QAE9C,kEAAkE;QAClE,IAAI,CAAC,WAAW,cAAc;YAC5B,QAAQ,IAAI,CAAC;YAEb,qEAAqE;YACrE,cAAc,YAAY,KAAK,CAAC,IAAwC,EAAE,IAAI,KAAK,WAAW,UAAU;YACxG,kBAAkB,YAAY,KAAK,CAAC,IAAwC,EAAE,IAAI,KAAK,eAAe,UAAU;YAEhH,oCAAoC;YACpC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAAS,GAAG,CAChE,2BACA;gBAAE,aAAa;YAAkB;YAEnC,IAAI,YAAY,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,WAAW,OAAO,EAAE;YACrF,cAAc,cAAc;YAE5B,oCAAoC;YACpC,cAAc,YAAY,OAAO,CAAC,KAAa,MAA4B,MAAM,IAAI,MAAM,EAAE,MAAM;YACnG,eAAe,aAAa,OAAO,CAAC,KAAa,MAA4B,MAAM,IAAI,MAAM,EAAE,MAAM;QACvG;QAEA,8BAA8B;QAE9B,MAAM,gBAAoC;YACxC,mBAAmB,OAAO;YAC1B,mBAAmB,OAAO;YAC1B,uBAAuB,OAAO;YAC9B,aAAa,OAAO;YACpB,cAAc,OAAO;YACrB,0BAA0B,OAAO,oBAAoB;YACrD,2BAA2B,OAAO,qBAAqB;YACvD,aAAa;YACb,cAAc;YACd,iBAAiB,cAAc,EAAE;YACjC,kBAAkB,eAAe,EAAE;YACnC,kBAAkB,mBAAmB,EAAE;YACvC,cAAc,oBAAoB,EAAE;YACpC,gBAAgB;QAClB;QAEA,OAAO;YAAE,MAAM;QAAc;IAE/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,0CAA0C;QAC1C,OAAO;YACL,OACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC7C;IACF;AACF;;;IApNsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/business/analytics/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00a78b43259bdfa35946a0918da66b9382dcd7b4dc'} from 'ACTIONS_MODULE0'\nexport {getUnreadActivitiesCount as '40f389eb27483c521497eadb1dbe197d2328544a4a'} from 'ACTIONS_MODULE1'\nexport {getVisitAnalytics as '4042e35d74021c2263524beeeb9dccf18638b4ed58'} from 'ACTIONS_MODULE2'\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n/**\r\n * Cleans and formats phone number from Supabase auth.users table format\r\n * Handles various formats: +918458060663, 918458060663, 8458060663\r\n * Returns clean 10-digit phone number or null if invalid\r\n *\r\n * @param phone - Phone number from Supabase auth.users table\r\n * @returns Clean 10-digit phone number or null if invalid\r\n */\r\nexport function cleanPhoneFromAuth(phone: string | null | undefined): string | null {\r\n  if (!phone) return null;\r\n\r\n  let processedPhone = phone.trim();\r\n\r\n  // Remove +91 prefix if present\r\n  if (processedPhone.startsWith('+91')) {\r\n    processedPhone = processedPhone.substring(3);\r\n  }\r\n  // Remove 91 prefix if it's a 12-digit number starting with 91\r\n  else if (processedPhone.length === 12 && processedPhone.startsWith('91')) {\r\n    processedPhone = processedPhone.substring(2);\r\n  }\r\n\r\n  // Validate it's a 10-digit number\r\n  if (/^\\d{10}$/.test(processedPhone)) {\r\n    return processedPhone;\r\n  }\r\n\r\n  return null; // Invalid format\r\n}\r\n\r\n/**\r\n * Masks a phone number, showing first and last two digits.\r\n * Example: 9123456789 -> 91******89\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskPhoneNumber(phone: string | null | undefined): string {\r\n  if (!phone || phone.length < 4) {\r\n    return \"Invalid Phone\"; // Or return empty string or original if preferred\r\n  }\r\n  const firstTwo = phone.substring(0, 2);\r\n  const lastTwo = phone.substring(phone.length - 2);\r\n  const maskedPart = \"*\".repeat(phone.length - 4);\r\n  return `${firstTwo}${maskedPart}${lastTwo}`;\r\n}\r\n\r\n/**\r\n * Masks an email address.\r\n * Example: <EMAIL> -> ex****@do****.com\r\n * Handles null/undefined/empty strings.\r\n */\r\nexport function maskEmail(email: string | null | undefined): string {\r\n  if (!email || !email.includes(\"@\")) {\r\n    return \"Invalid Email\"; // Or return empty string or original\r\n  }\r\n  const parts = email.split(\"@\");\r\n  const username = parts[0];\r\n  const domain = parts[1];\r\n\r\n  if (username.length <= 2 || domain.length <= 2 || !domain.includes(\".\")) {\r\n    return \"Email Hidden\"; // Simple mask for very short/invalid emails\r\n  }\r\n\r\n  const maskedUsername =\r\n    username.substring(0, 2) + \"*\".repeat(username.length - 2);\r\n\r\n  const domainParts = domain.split(\".\");\r\n  const domainName = domainParts[0];\r\n  const domainTld = domainParts.slice(1).join(\".\"); // Handle multiple parts like .co.uk\r\n\r\n  const maskedDomainName =\r\n    domainName.substring(0, 2) + \"*\".repeat(domainName.length - 2);\r\n\r\n  return `${maskedUsername}@${maskedDomainName}.${domainTld}`;\r\n}\r\n\r\n/**\r\n * Formats a number using the Indian numbering system with short notations.\r\n * Supports: K (Thousand), L (Lakh), Cr (Crore), Ar (Arab), Khar (Kharab), Neel, Padma, Shankh, etc.\r\n * Examples:\r\n *   1_200 -> \"1.2K\"\r\n *   1_20_000 -> \"1.2L\"\r\n *   1_20_00_000 -> \"1.2Cr\"\r\n *   1_20_00_00_000 -> \"1.2Ar\"\r\n *   1_20_00_00_00_000 -> \"1.2Khar\"\r\n *   1_20_00_00_00_00_000 -> \"1.2Neel\"\r\n *   1_20_00_00_00_00_00_000 -> \"1.2Padma\"\r\n *   1_20_00_00_00_00_00_00_000 -> \"1.2Shankh\"\r\n */\r\nexport function formatIndianNumberShort(num: number): string {\r\n  if (num === null || num === undefined || isNaN(num)) return \"0\";\r\n  const absNum = Math.abs(num);\r\n\r\n  // Indian units and their values\r\n  const units = [\r\n    { value: 1e5, symbol: \"L\" }, // Lakh\r\n    { value: 1e7, symbol: \"Cr\" }, // Crore\r\n    { value: 1e9, symbol: \"Ar\" }, // Arab\r\n    { value: 1e11, symbol: \"Khar\" }, // Kharab\r\n    { value: 1e13, symbol: \"Neel\" }, // Neel\r\n    { value: 1e15, symbol: \"Padma\" }, // Padma\r\n    { value: 1e17, symbol: \"Shankh\" }, // Shankh\r\n  ];\r\n\r\n  // For thousands (K), use western style for sub-lakh\r\n  if (absNum < 1e5) {\r\n    if (absNum >= 1e3) {\r\n      return (num / 1e3).toFixed(1).replace(/\\.0$/, \"\") + \"K\";\r\n    }\r\n    return num.toString();\r\n  }\r\n\r\n  // Find the largest unit that fits\r\n  for (let i = units.length - 1; i >= 0; i--) {\r\n    if (absNum >= units[i].value) {\r\n      return (\r\n        (num / units[i].value).toFixed(1).replace(/\\.0$/, \"\") + units[i].symbol\r\n      );\r\n    }\r\n  }\r\n\r\n  // Fallback (should not reach here)\r\n  return num.toString();\r\n}\r\n\r\n/**\r\n * Formats an address from BusinessCardData into a single string\r\n */\r\nexport function formatAddress(data: BusinessCardData): string {\r\n  const addressParts = [\r\n    data.address_line,\r\n    data.locality,\r\n    data.city,\r\n    data.state,\r\n    data.pincode,\r\n  ].filter(Boolean);\r\n\r\n  return addressParts.join(\", \") || \"Address not available\";\r\n}\r\n\r\n/**\r\n * Formats a date in a user-friendly format with Indian Standard Time (IST)\r\n * @param date The date to format\r\n * @param includeTime Whether to include time in the formatted string\r\n * @returns Formatted date string in IST\r\n */\r\nexport function formatDate(date: Date, includeTime: boolean = false): string {\r\n  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {\r\n    return \"Invalid date\";\r\n  }\r\n\r\n  const options: Intl.DateTimeFormatOptions = {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n    timeZone: \"Asia/Kolkata\", // Explicitly set timezone to IST\r\n  };\r\n\r\n  if (includeTime) {\r\n    options.hour = \"2-digit\";\r\n    options.minute = \"2-digit\";\r\n    options.hour12 = true;\r\n  }\r\n\r\n  return date.toLocaleString(\"en-IN\", options);\r\n}\r\n\r\n/**\r\n * Formats a currency amount with the appropriate currency symbol\r\n * @param amount The amount to format\r\n * @param currency The currency code (e.g., INR, USD)\r\n * @returns Formatted currency string\r\n */\r\nexport function formatCurrency(\r\n  amount: number,\r\n  currency: string = \"INR\"\r\n): string {\r\n  if (amount === null || amount === undefined || isNaN(amount)) {\r\n    return \"Invalid amount\";\r\n  }\r\n\r\n  try {\r\n    return new Intl.NumberFormat(\"en-IN\", {\r\n      style: \"currency\",\r\n      currency: currency,\r\n      minimumFractionDigits: 0,\r\n      maximumFractionDigits: 2,\r\n    }).format(amount);\r\n  } catch {\r\n    // Catch any error without using the error variable\r\n    // Fallback in case of invalid currency code\r\n    return `${currency} ${amount.toFixed(2)}`;\r\n  }\r\n}\r\n\r\n/**\r\n * Formats a string to title case (first letter of each word capitalized)\r\n * @param text The text to format\r\n * @returns The text in title case\r\n */\r\nexport function toTitleCase(text: string): string {\r\n  if (!text) return \"\";\r\n\r\n  return text\r\n    .toLowerCase()\r\n    .replace(/\\b\\w/g, (char) => char.toUpperCase());\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAUO,SAAS,mBAAmB,KAAgC;IACjE,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,iBAAiB,MAAM,IAAI;IAE/B,+BAA+B;IAC/B,IAAI,eAAe,UAAU,CAAC,QAAQ;QACpC,iBAAiB,eAAe,SAAS,CAAC;IAC5C,OAEK,IAAI,eAAe,MAAM,KAAK,MAAM,eAAe,UAAU,CAAC,OAAO;QACxE,iBAAiB,eAAe,SAAS,CAAC;IAC5C;IAEA,kCAAkC;IAClC,IAAI,WAAW,IAAI,CAAC,iBAAiB;QACnC,OAAO;IACT;IAEA,OAAO,MAAM,iBAAiB;AAChC;AAOO,SAAS,gBAAgB,KAAgC;IAC9D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO,iBAAiB,kDAAkD;IAC5E;IACA,MAAM,WAAW,MAAM,SAAS,CAAC,GAAG;IACpC,MAAM,UAAU,MAAM,SAAS,CAAC,MAAM,MAAM,GAAG;IAC/C,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,MAAM,GAAG;IAC7C,OAAO,GAAG,WAAW,aAAa,SAAS;AAC7C;AAOO,SAAS,UAAU,KAAgC;IACxD,IAAI,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,MAAM;QAClC,OAAO,iBAAiB,qCAAqC;IAC/D;IACA,MAAM,QAAQ,MAAM,KAAK,CAAC;IAC1B,MAAM,WAAW,KAAK,CAAC,EAAE;IACzB,MAAM,SAAS,KAAK,CAAC,EAAE;IAEvB,IAAI,SAAS,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK,CAAC,OAAO,QAAQ,CAAC,MAAM;QACvE,OAAO,gBAAgB,4CAA4C;IACrE;IAEA,MAAM,iBACJ,SAAS,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG;IAE1D,MAAM,cAAc,OAAO,KAAK,CAAC;IACjC,MAAM,aAAa,WAAW,CAAC,EAAE;IACjC,MAAM,YAAY,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,oCAAoC;IAEtF,MAAM,mBACJ,WAAW,SAAS,CAAC,GAAG,KAAK,IAAI,MAAM,CAAC,WAAW,MAAM,GAAG;IAE9D,OAAO,GAAG,eAAe,CAAC,EAAE,iBAAiB,CAAC,EAAE,WAAW;AAC7D;AAeO,SAAS,wBAAwB,GAAW;IACjD,IAAI,QAAQ,QAAQ,QAAQ,aAAa,MAAM,MAAM,OAAO;IAC5D,MAAM,SAAS,KAAK,GAAG,CAAC;IAExB,gCAAgC;IAChC,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAK,QAAQ;QAAI;QAC1B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAK,QAAQ;QAAK;QAC3B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAO;QAC9B;YAAE,OAAO;YAAM,QAAQ;QAAQ;QAC/B;YAAE,OAAO;YAAM,QAAQ;QAAS;KACjC;IAED,oDAAoD;IACpD,IAAI,SAAS,KAAK;QAChB,IAAI,UAAU,KAAK;YACjB,OAAO,CAAC,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM;QACtD;QACA,OAAO,IAAI,QAAQ;IACrB;IAEA,kCAAkC;IAClC,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE;YAC5B,OACE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC,QAAQ,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM;QAE3E;IACF;IAEA,mCAAmC;IACnC,OAAO,IAAI,QAAQ;AACrB;AAKO,SAAS,cAAc,IAAsB;IAClD,MAAM,eAAe;QACnB,KAAK,YAAY;QACjB,KAAK,QAAQ;QACb,KAAK,IAAI;QACT,KAAK,KAAK;QACV,KAAK,OAAO;KACb,CAAC,MAAM,CAAC;IAET,OAAO,aAAa,IAAI,CAAC,SAAS;AACpC;AAQO,SAAS,WAAW,IAAU,EAAE,cAAuB,KAAK;IACjE,IAAI,CAAC,QAAQ,CAAC,CAAC,gBAAgB,IAAI,KAAK,MAAM,KAAK,OAAO,KAAK;QAC7D,OAAO;IACT;IAEA,MAAM,UAAsC;QAC1C,MAAM;QACN,OAAO;QACP,KAAK;QACL,UAAU;IACZ;IAEA,IAAI,aAAa;QACf,QAAQ,IAAI,GAAG;QACf,QAAQ,MAAM,GAAG;QACjB,QAAQ,MAAM,GAAG;IACnB;IAEA,OAAO,KAAK,cAAc,CAAC,SAAS;AACtC;AAQO,SAAS,eACd,MAAc,EACd,WAAmB,KAAK;IAExB,IAAI,WAAW,QAAQ,WAAW,aAAa,MAAM,SAAS;QAC5D,OAAO;IACT;IAEA,IAAI;QACF,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ,EAAE,OAAM;QACN,mDAAmD;QACnD,4CAA4C;QAC5C,OAAO,GAAG,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC,IAAI;IAC3C;AACF;AAOO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAS,KAAK,WAAW;AAChD", "debugId": null}}, {"offset": {"line": 810, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/EnhancedAnalyticsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/analytics/components/EnhancedAnalyticsPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/analytics/components/EnhancedAnalyticsPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2V,GACxX,yHACA", "debugId": null}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/EnhancedAnalyticsPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/analytics/components/EnhancedAnalyticsPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/analytics/components/EnhancedAnalyticsPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuU,GACpW,qGACA", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/page.tsx"], "sourcesContent": ["import { getVisitAnalytics } from \"./actions\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { AlertTriangle } from \"lucide-react\";\r\nimport { Metadata } from \"next\";\r\nimport { redirect } from \"next/navigation\";\r\nimport EnhancedAnalyticsPageClient from \"./components/EnhancedAnalyticsPageClient\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Analytics - Dukancard Business\",\r\n  robots: \"noindex, nofollow\",\r\n};\r\n\r\nexport default async function AnalyticsPage() {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return redirect(\"/login?message=Authentication required\");\r\n  }\r\n\r\n  // Fetch the business profile including interaction metrics\r\n  const { data: profile, error: profileError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"total_likes, total_subscriptions, average_rating, total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (profileError || !profile) {\r\n    return redirect(\"/login?message=Profile fetch error\");\r\n  }\r\n\r\n  // Fetch subscription data to get the plan_id\r\n  const { data: subscription, error: _subscriptionError } = await supabase\r\n    .from(\"payment_subscriptions\")\r\n    .select(\"plan_id\")\r\n    .eq(\"business_profile_id\", user.id)\r\n    .order(\"created_at\", { ascending: false })\r\n    .limit(1)\r\n    .maybeSingle();\r\n\r\n  const planId = subscription?.plan_id || \"free\";\r\n\r\n  const { data: analyticsData, error } = await getVisitAnalytics(planId);\r\n\r\n  if (error) {\r\n    return (\r\n      <Alert variant=\"destructive\">\r\n        <AlertTriangle className=\"h-4 w-4\" />\r\n        <AlertTitle>Error Fetching Analytics</AlertTitle>\r\n        <AlertDescription>{error}</AlertDescription>\r\n      </Alert>\r\n    );\r\n  }\r\n\r\n  if (!analyticsData) {\r\n    // Should ideally not happen if no error, but good practice\r\n    return <p>No analytics data available.</p>;\r\n  }\r\n\r\n  return (\r\n    <EnhancedAnalyticsPageClient\r\n      analyticsData={analyticsData}\r\n      userId={user.id}\r\n      userPlan={planId}\r\n      initialProfile={{\r\n        total_likes: profile?.total_likes || 0,\r\n        total_subscriptions: profile?.total_subscriptions || 0,\r\n        average_rating: profile?.average_rating || 0,\r\n        total_visits: profile?.total_visits || 0,\r\n        today_visits: profile?.today_visits || 0,\r\n        yesterday_visits: profile?.yesterday_visits || 0,\r\n        visits_7_days: profile?.visits_7_days || 0,\r\n        visits_30_days: profile?.visits_30_days || 0,\r\n      }}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AAAA;AACA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,QAAQ;AACV;AAEe,eAAe;IAC5B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAClB;IAEA,2DAA2D;IAC3D,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,qBACL,MAAM,CAAC,iIACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,gBAAgB,CAAC,SAAS;QAC5B,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;IAClB;IAEA,6CAA6C;IAC7C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,kBAAkB,EAAE,GAAG,MAAM,SAC7D,IAAI,CAAC,yBACL,MAAM,CAAC,WACP,EAAE,CAAC,uBAAuB,KAAK,EAAE,EACjC,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC,GACN,WAAW;IAEd,MAAM,SAAS,cAAc,WAAW;IAExC,MAAM,EAAE,MAAM,aAAa,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD,EAAE;IAE/D,IAAI,OAAO;QACT,qBACE,8OAAC,0HAAA,CAAA,QAAK;YAAC,SAAQ;;8BACb,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC,0HAAA,CAAA,aAAU;8BAAC;;;;;;8BACZ,8OAAC,0HAAA,CAAA,mBAAgB;8BAAE;;;;;;;;;;;;IAGzB;IAEA,IAAI,CAAC,eAAe;QAClB,2DAA2D;QAC3D,qBAAO,8OAAC;sBAAE;;;;;;IACZ;IAEA,qBACE,8OAAC,wMAAA,CAAA,UAA2B;QAC1B,eAAe;QACf,QAAQ,KAAK,EAAE;QACf,UAAU;QACV,gBAAgB;YACd,aAAa,SAAS,eAAe;YACrC,qBAAqB,SAAS,uBAAuB;YACrD,gBAAgB,SAAS,kBAAkB;YAC3C,cAAc,SAAS,gBAAgB;YACvC,cAAc,SAAS,gBAAgB;YACvC,kBAAkB,SAAS,oBAAoB;YAC/C,eAAe,SAAS,iBAAiB;YACzC,gBAAgB,SAAS,kBAAkB;QAC7C;;;;;;AAGN", "debugId": null}}]}