{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/AnimatedMetricCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { LucideIcon } from \"lucide-react\";\r\n\r\ninterface AnimatedMetricCardProps {\r\n  title: string;\r\n  value: string | number;\r\n  icon: LucideIcon;\r\n  description: string;\r\n  color: \"rose\" | \"blue\" | \"amber\" | \"red\" | \"yellow\";\r\n  isUpdated?: boolean;\r\n}\r\n\r\nexport default function AnimatedMetricCard({\r\n  title,\r\n  value,\r\n  icon: Icon,\r\n  description,\r\n  color,\r\n  isUpdated = false,\r\n}: AnimatedMetricCardProps) {\r\n  // Simple, clean color variants without glow effects\r\n  const iconColors = {\r\n    rose: \"text-rose-600 dark:text-rose-400\",\r\n    blue: \"text-blue-600 dark:text-blue-400\",\r\n    amber: \"text-amber-600 dark:text-amber-400\",\r\n    red: \"text-red-600 dark:text-red-400\",\r\n    yellow: \"text-yellow-600 dark:text-yellow-400\",\r\n  };\r\n\r\n  const iconColor = iconColors[color];\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 300,\r\n        damping: 24,\r\n      },\r\n    },\r\n  };\r\n\r\n  const counterVariants = {\r\n    initial: { scale: 1 },\r\n    update: {\r\n      scale: 1.05,\r\n      transition: { duration: 0.3 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      className=\"group relative overflow-hidden rounded-xl p-6 bg-white dark:bg-black border border-border hover:border-border/80 shadow-sm hover:shadow-md transition-all duration-300\"\r\n    >\r\n      {/* Content */}\r\n      <div className=\"flex flex-col items-center text-center space-y-4\">\r\n        {/* Icon */}\r\n        <div className=\"p-3 rounded-xl bg-muted\">\r\n          <Icon className={`w-6 h-6 ${iconColor}`} />\r\n        </div>\r\n\r\n        {/* Value */}\r\n        <div className=\"space-y-1\">\r\n          <motion.div\r\n            className=\"text-2xl font-bold text-foreground\"\r\n            variants={counterVariants}\r\n            initial=\"initial\"\r\n            animate={isUpdated ? \"update\" : \"initial\"}\r\n          >\r\n            {value}\r\n          </motion.div>\r\n          <div className=\"text-sm font-medium text-muted-foreground\">\r\n            {title}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Description */}\r\n        <p className=\"text-xs text-muted-foreground\">\r\n          {description}\r\n        </p>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAce,SAAS,mBAAmB,EACzC,KAAK,EACL,KAAK,EACL,MAAM,IAAI,EACV,WAAW,EACX,KAAK,EACL,YAAY,KAAK,EACO;IACxB,oDAAoD;IACpD,MAAM,aAAa;QACjB,MAAM;QACN,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ;IACV;IAEA,MAAM,YAAY,UAAU,CAAC,MAAM;IAEnC,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,SAAS;YAAE,OAAO;QAAE;QACpB,QAAQ;YACN,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,WAAU;kBAGV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,WAAW,CAAC,QAAQ,EAAE,WAAW;;;;;;;;;;;8BAIzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;4BACV,SAAQ;4BACR,SAAS,YAAY,WAAW;sCAE/B;;;;;;sCAEH,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;8BAKL,8OAAC;oBAAE,WAAU;8BACV;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/DashboardOverviewClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { createClient } from \"@/utils/supabase/client\";\r\nimport type { RealtimePostgresChangesPayload } from \"@supabase/supabase-js\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport { Heart, Star, Users } from \"lucide-react\";\r\nimport AnimatedMetricCard from \"./AnimatedMetricCard\";\r\n\r\n/**\r\n * Dashboard Overview Client Component\r\n *\r\n * This component displays real-time metrics for a business profile:\r\n * - Total Likes\r\n * - Total Subscriptions\r\n * - Average Rating\r\n *\r\n * Database Implementation:\r\n * - All metrics are stored as pre-aggregated values in the business_profiles table\r\n * - These values are automatically updated by database triggers:\r\n *   1. update_total_likes() - Updates total_likes when likes are added/removed\r\n *   2. update_total_subscriptions() - Updates total_subscriptions when subscriptions change\r\n *   3. update_average_rating() - Updates average_rating when ratings change\r\n *\r\n * Real-time Updates:\r\n * - The component listens for changes to the business_profiles table\r\n * - When changes occur, the UI updates automatically with animations\r\n * - This approach is efficient for handling large numbers of likes/subscriptions\r\n */\r\n\r\n// Define types for our data\r\ninterface BusinessProfile {\r\n  business_name: string;\r\n  total_likes: number;\r\n  total_subscriptions: number;\r\n  average_rating: number;\r\n}\r\n\r\ninterface DashboardOverviewClientProps {\r\n  initialProfile: BusinessProfile;\r\n  userId: string;\r\n  userPlan: string | null;\r\n}\r\n\r\nexport default function DashboardOverviewClient({\r\n  initialProfile,\r\n  userId,\r\n}: DashboardOverviewClientProps) {\r\n  // State for real-time data\r\n  const [profile, setProfile] = useState<BusinessProfile>(initialProfile);\r\n  const [isLikesUpdated, setIsLikesUpdated] = useState(false);\r\n  const [isSubscriptionsUpdated, setIsSubscriptionsUpdated] = useState(false);\r\n  const [isRatingUpdated, setIsRatingUpdated] = useState(false);\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  /**\r\n   * Set up Supabase real-time subscriptions\r\n   *\r\n   * This effect sets up a real-time subscription to the business_profiles table\r\n   * to listen for changes to the metrics (total_likes, total_subscriptions, average_rating).\r\n   *\r\n   * When changes are detected:\r\n   * 1. The UI is updated with the new values\r\n   * 2. Animations are triggered to highlight the changes\r\n   *\r\n   * Note: We only listen to the business_profiles table because:\r\n   * - The metrics are pre-aggregated in this table\r\n   * - Database triggers (update_total_likes, update_total_subscriptions, update_average_rating)\r\n   *   automatically update these values when the underlying data changes\r\n   * - This approach is more efficient than listening to individual tables or counting records\r\n   */\r\n  useEffect(() => {\r\n    const supabase = createClient();\r\n\r\n    const dashboardChannel = supabase\r\n      .channel(\"business-dashboard\")\r\n      .on<BusinessProfile>(\r\n        \"postgres_changes\" as const,\r\n        {\r\n          event: \"*\",\r\n          schema: \"public\",\r\n          table: \"business_profiles\",\r\n          filter: `id=eq.${userId}`,\r\n        },\r\n        (payload: RealtimePostgresChangesPayload<BusinessProfile>) => {\r\n          const newData = payload.new as BusinessProfile;\r\n          if (newData) {\r\n            // Check which values have changed and trigger animations accordingly\r\n            if (newData.total_likes !== profile.total_likes) {\r\n              setIsLikesUpdated(true);\r\n              setTimeout(() => setIsLikesUpdated(false), 1000);\r\n            }\r\n\r\n            if (newData.total_subscriptions !== profile.total_subscriptions) {\r\n              setIsSubscriptionsUpdated(true);\r\n              setTimeout(() => setIsSubscriptionsUpdated(false), 1000);\r\n            }\r\n\r\n            if (newData.average_rating !== profile.average_rating) {\r\n              setIsRatingUpdated(true);\r\n              setTimeout(() => setIsRatingUpdated(false), 1000);\r\n            }\r\n\r\n            // Update all profile data\r\n            setProfile((prev) => ({\r\n              ...prev,\r\n              total_likes: newData.total_likes,\r\n              total_subscriptions: newData.total_subscriptions,\r\n              average_rating: newData.average_rating,\r\n            }));\r\n          }\r\n        }\r\n      )\r\n      .subscribe();\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      supabase.removeChannel(dashboardChannel);\r\n    };\r\n  }, [userId, profile]);\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className=\"space-y-6\"\r\n    >\r\n      {/* Hero Stats Section */}\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4\">\r\n        {/* Likes Card */}\r\n        <AnimatedMetricCard\r\n          title=\"Likes\"\r\n          value={formatIndianNumberShort(profile.total_likes)}\r\n          icon={Heart}\r\n          description=\"People who liked your card\"\r\n          color=\"red\"\r\n          isUpdated={isLikesUpdated}\r\n        />\r\n\r\n        {/* Subscribers Card */}\r\n        <AnimatedMetricCard\r\n          title=\"Subscribers\"\r\n          value={formatIndianNumberShort(profile.total_subscriptions)}\r\n          icon={Users}\r\n          description=\"People subscribed to updates\"\r\n          color=\"blue\"\r\n          isUpdated={isSubscriptionsUpdated}\r\n        />\r\n\r\n        {/* Rating Card */}\r\n        <AnimatedMetricCard\r\n          title=\"Rating\"\r\n          value={`${profile.average_rating?.toFixed(1) || \"0.0\"}/5.0`}\r\n          icon={Star}\r\n          description=\"Average customer rating\"\r\n          color=\"yellow\"\r\n          isUpdated={isRatingUpdated}\r\n        />\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AACA;AARA;;;;;;;;AA6Ce,SAAS,wBAAwB,EAC9C,cAAc,EACd,MAAM,EACuB;IAC7B,2BAA2B;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA;;;;;;;;;;;;;;;GAeC,GACD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAE5B,MAAM,mBAAmB,SACtB,OAAO,CAAC,sBACR,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,MAAM,EAAE,QAAQ;QAC3B,GACA,CAAC;YACC,MAAM,UAAU,QAAQ,GAAG;YAC3B,IAAI,SAAS;gBACX,qEAAqE;gBACrE,IAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,EAAE;oBAC/C,kBAAkB;oBAClB,WAAW,IAAM,kBAAkB,QAAQ;gBAC7C;gBAEA,IAAI,QAAQ,mBAAmB,KAAK,QAAQ,mBAAmB,EAAE;oBAC/D,0BAA0B;oBAC1B,WAAW,IAAM,0BAA0B,QAAQ;gBACrD;gBAEA,IAAI,QAAQ,cAAc,KAAK,QAAQ,cAAc,EAAE;oBACrD,mBAAmB;oBACnB,WAAW,IAAM,mBAAmB,QAAQ;gBAC9C;gBAEA,0BAA0B;gBAC1B,WAAW,CAAC,OAAS,CAAC;wBACpB,GAAG,IAAI;wBACP,aAAa,QAAQ,WAAW;wBAChC,qBAAqB,QAAQ,mBAAmB;wBAChD,gBAAgB,QAAQ,cAAc;oBACxC,CAAC;YACH;QACF,GAED,SAAS;QAEZ,mBAAmB;QACnB,OAAO;YACL,SAAS,aAAa,CAAC;QACzB;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;kBAGV,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,kLAAA,CAAA,UAAkB;oBACjB,OAAM;oBACN,OAAO,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,WAAW;oBAClD,MAAM,oMAAA,CAAA,QAAK;oBACX,aAAY;oBACZ,OAAM;oBACN,WAAW;;;;;;8BAIb,8OAAC,kLAAA,CAAA,UAAkB;oBACjB,OAAM;oBACN,OAAO,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,mBAAmB;oBAC1D,MAAM,oMAAA,CAAA,QAAK;oBACX,aAAY;oBACZ,OAAM;oBACN,WAAW;;;;;;8BAIb,8OAAC,kLAAA,CAAA,UAAkB;oBACjB,OAAM;oBACN,OAAO,GAAG,QAAQ,cAAc,EAAE,QAAQ,MAAM,MAAM,IAAI,CAAC;oBAC3D,MAAM,kMAAA,CAAA,OAAI;oBACV,aAAY;oBACZ,OAAM;oBACN,WAAW;;;;;;;;;;;;;;;;;AAKrB", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/FlipTimer.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useMemo } from \"react\";\r\nimport { intervalToDuration } from \"date-fns\";\r\nimport { CalendarClock } from \"lucide-react\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\ninterface FlipTimerProps {\r\n  endDate: string | Date;\r\n  label?: string;\r\n  tooltipText?: string;\r\n  className?: string;\r\n}\r\n\r\ninterface TimeUnit {\r\n  label: string;\r\n  value: number;\r\n}\r\n\r\nconst FlipTimer: React.FC<FlipTimerProps> = ({\r\n  endDate,\r\n  label = \"Trial ends in:\",\r\n  tooltipText = \"Your trial will expire soon. Upgrade to continue using all features.\",\r\n  className,\r\n}) => {\r\n  const [now, setNow] = useState(new Date());\r\n  const [timeUnits, setTimeUnits] = useState<TimeUnit[]>([]);\r\n\r\n  const targetDate = useMemo(() => {\r\n    // Parse the date and ensure it's treated as IST (UTC+5:30)\r\n    let date: Date;\r\n\r\n    if (typeof endDate === \"string\") {\r\n      // For string dates, create a new Date object\r\n      date = new Date(endDate);\r\n    } else {\r\n      // For Date objects, use as is\r\n      date = endDate;\r\n    }\r\n\r\n    // Validate the date\r\n    return !isNaN(date.getTime()) ? date : null;\r\n  }, [endDate]);\r\n\r\n  useEffect(() => {\r\n    if (!targetDate) return;\r\n\r\n    const updateTime = () => {\r\n      const currentTime = new Date();\r\n      setNow(currentTime);\r\n\r\n      if (targetDate && currentTime < targetDate) {\r\n        const duration = intervalToDuration({\r\n          start: currentTime,\r\n          end: targetDate,\r\n        });\r\n\r\n        const units: TimeUnit[] = [];\r\n\r\n        // Always show a complete breakdown of time units\r\n        // Calculate years, months, days, hours, minutes, seconds\r\n        const diffMs = targetDate.getTime() - currentTime.getTime();\r\n\r\n        // Calculate years (approximate)\r\n        const millisecondsInYear = 1000 * 60 * 60 * 24 * 365.25; // Account for leap years\r\n        const years = Math.floor(diffMs / millisecondsInYear);\r\n        let remainder = diffMs % millisecondsInYear;\r\n\r\n        // Calculate months (approximate)\r\n        const millisecondsInMonth = 1000 * 60 * 60 * 24 * 30.44; // Average month length\r\n        const months = Math.floor(remainder / millisecondsInMonth);\r\n        remainder = remainder % millisecondsInMonth;\r\n\r\n        // Calculate days\r\n        const millisecondsInDay = 1000 * 60 * 60 * 24;\r\n        const days = Math.floor(remainder / millisecondsInDay);\r\n        remainder = remainder % millisecondsInDay;\r\n\r\n        // Hours, minutes, seconds from duration object\r\n        const hours = duration.hours || 0;\r\n        const minutes = duration.minutes || 0;\r\n        const seconds = duration.seconds || 0;\r\n\r\n        // Add time units to the display, but limit to 5 units maximum to avoid overcrowding\r\n        const allUnits = [];\r\n\r\n        if (years > 0) {\r\n          allUnits.push({ label: \"years\", value: years });\r\n        }\r\n\r\n        if (months > 0) {\r\n          allUnits.push({ label: \"months\", value: months });\r\n        }\r\n\r\n        if (days > 0) {\r\n          allUnits.push({ label: \"days\", value: days });\r\n        }\r\n\r\n        // Always include hours, minutes, and seconds\r\n        allUnits.push(\r\n          { label: \"hrs\", value: hours },\r\n          { label: \"min\", value: minutes },\r\n          { label: \"sec\", value: seconds }\r\n        );\r\n\r\n        // Always include seconds and take up to 5 other units\r\n        // Extract seconds first\r\n        const secondsUnit = allUnits.pop(); // This is the seconds unit\r\n\r\n        // Take up to 5 other units\r\n        units.push(...allUnits.slice(0, 5));\r\n\r\n        // Always add seconds at the end\r\n        if (secondsUnit) {\r\n          units.push(secondsUnit);\r\n        }\r\n\r\n        setTimeUnits(units);\r\n      }\r\n    };\r\n\r\n    // Initial update\r\n    updateTime();\r\n\r\n    // Set up interval\r\n    const intervalId = setInterval(updateTime, 1000);\r\n\r\n    // Cleanup\r\n    return () => clearInterval(intervalId);\r\n  }, [targetDate]);\r\n\r\n  if (!targetDate || now >= targetDate) {\r\n    return null; // Don't render if date is invalid or trial ended\r\n  }\r\n\r\n  // Determine if this is a short or long countdown\r\n  const isLongCountdown = timeUnits.some(\r\n    (unit) => unit.label === \"months\" || unit.label === \"years\"\r\n  );\r\n\r\n  // Determine color theme based on time remaining\r\n  const getColorTheme = () => {\r\n    if (isLongCountdown) {\r\n      return {\r\n        gradient:\r\n          \"from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-900/20\",\r\n        border: \"border-blue-200 dark:border-blue-800/50\",\r\n        text: \"text-blue-600 dark:text-blue-400\",\r\n        shadow: \"shadow-blue-200/20 dark:shadow-blue-900/20\",\r\n        glow: \"after:bg-blue-500/10 dark:after:bg-blue-400/10\",\r\n      };\r\n    }\r\n\r\n    // Less than a day remaining\r\n    if (!timeUnits.some((unit) => unit.label === \"days\")) {\r\n      return {\r\n        gradient:\r\n          \"from-amber-100 to-amber-50 dark:from-amber-900/40 dark:to-amber-900/20\",\r\n        border: \"border-amber-200 dark:border-amber-800/50\",\r\n        text: \"text-amber-600 dark:text-amber-400\",\r\n        shadow: \"shadow-amber-200/20 dark:shadow-amber-900/20\",\r\n        glow: \"after:bg-amber-500/10 dark:after:bg-amber-400/10\",\r\n      };\r\n    }\r\n\r\n    // Default (days remaining)\r\n    return {\r\n      gradient:\r\n        \"from-[var(--brand-gold)]/20 to-[var(--brand-gold)]/5 dark:from-[var(--brand-gold)]/30 dark:to-[var(--brand-gold)]/10\",\r\n      border:\r\n        \"border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/20\",\r\n      text: \"text-[var(--brand-gold)] dark:text-[var(--brand-gold)]\",\r\n      shadow:\r\n        \"shadow-[var(--brand-gold)]/10 dark:shadow-[var(--brand-gold)]/10\",\r\n      glow: \"after:bg-[var(--brand-gold)]/10 dark:after:bg-[var(--brand-gold)]/10\",\r\n    };\r\n  };\r\n\r\n  const colorTheme = getColorTheme();\r\n\r\n  return (\r\n    <div className={`flex flex-col items-center w-full ${className || \"\"}`}>\r\n      <TooltipProvider delayDuration={100}>\r\n        <Tooltip>\r\n          <TooltipTrigger asChild>\r\n            <div className=\"flex items-center gap-1.5 mb-1.5\">\r\n              <div className=\"transition-transform hover:scale-110\">\r\n                <CalendarClock className={`w-4 h-4 ${colorTheme.text}`} />\r\n              </div>\r\n              <span className=\"text-xs font-medium text-muted-foreground\">\r\n                {label}\r\n              </span>\r\n            </div>\r\n          </TooltipTrigger>\r\n          <TooltipContent\r\n            side=\"bottom\"\r\n            className=\"bg-neutral-800/95 dark:bg-neutral-950/95 backdrop-blur-sm border border-neutral-700/50 dark:border-[var(--brand-gold)]/20 text-white text-xs p-3 rounded-lg shadow-lg\"\r\n          >\r\n            <p>{tooltipText}</p>\r\n          </TooltipContent>\r\n        </Tooltip>\r\n      </TooltipProvider>\r\n\r\n      <div className=\"flex gap-1 xs:gap-1.5 sm:gap-2 flex-wrap justify-center\">\r\n        {timeUnits.map((unit, index) => (\r\n          <div key={index} className=\"flex flex-col items-center\">\r\n            <div\r\n              className={`relative w-8 h-8 xs:w-9 xs:h-9 sm:w-10 sm:h-10 md:w-11 md:h-11 overflow-hidden rounded-md ${colorTheme.shadow} shadow-md after:absolute after:inset-0 after:rounded-md after:opacity-30 after:blur-xl ${colorTheme.glow}`}\r\n            >\r\n              {/* Top half static overlay (shadow effect) */}\r\n              <div className=\"absolute top-0 left-0 right-0 h-[1px] bg-white/40 dark:bg-white/10 z-10\"></div>\r\n\r\n              {/* Bottom half static overlay (shadow effect) */}\r\n              <div className=\"absolute bottom-0 left-0 right-0 h-[1px] bg-black/10 dark:bg-black/20 z-10\"></div>\r\n\r\n              {/* Horizontal divider line */}\r\n              <div className=\"absolute top-1/2 left-0 right-0 h-[1px] bg-black/10 dark:bg-white/10 z-10\"></div>\r\n\r\n              <div\r\n                key={`${unit.label}-${unit.value}`}\r\n                className={`absolute inset-0 flex items-center justify-center bg-gradient-to-b ${colorTheme.gradient} rounded-md border ${colorTheme.border}`}\r\n              >\r\n                <span className=\"text-xs xs:text-sm sm:text-base md:text-lg font-mono font-bold text-neutral-800 dark:text-neutral-200\">\r\n                  {unit.value.toString().padStart(2, \"0\")}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <span className=\"text-[7px] xs:text-[8px] sm:text-[9px] font-medium text-muted-foreground mt-0.5\">\r\n              {unit.label}\r\n            </span>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FlipTimer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAwBA,MAAM,YAAsC,CAAC,EAC3C,OAAO,EACP,QAAQ,gBAAgB,EACxB,cAAc,sEAAsE,EACpF,SAAS,EACV;IACC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAEzD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,2DAA2D;QAC3D,IAAI;QAEJ,IAAI,OAAO,YAAY,UAAU;YAC/B,6CAA6C;YAC7C,OAAO,IAAI,KAAK;QAClB,OAAO;YACL,8BAA8B;YAC9B,OAAO;QACT;QAEA,oBAAoB;QACpB,OAAO,CAAC,MAAM,KAAK,OAAO,MAAM,OAAO;IACzC,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY;QAEjB,MAAM,aAAa;YACjB,MAAM,cAAc,IAAI;YACxB,OAAO;YAEP,IAAI,cAAc,cAAc,YAAY;gBAC1C,MAAM,WAAW,CAAA,GAAA,kJAAA,CAAA,qBAAkB,AAAD,EAAE;oBAClC,OAAO;oBACP,KAAK;gBACP;gBAEA,MAAM,QAAoB,EAAE;gBAE5B,iDAAiD;gBACjD,yDAAyD;gBACzD,MAAM,SAAS,WAAW,OAAO,KAAK,YAAY,OAAO;gBAEzD,gCAAgC;gBAChC,MAAM,qBAAqB,OAAO,KAAK,KAAK,KAAK,QAAQ,yBAAyB;gBAClF,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;gBAClC,IAAI,YAAY,SAAS;gBAEzB,iCAAiC;gBACjC,MAAM,sBAAsB,OAAO,KAAK,KAAK,KAAK,OAAO,uBAAuB;gBAChF,MAAM,SAAS,KAAK,KAAK,CAAC,YAAY;gBACtC,YAAY,YAAY;gBAExB,iBAAiB;gBACjB,MAAM,oBAAoB,OAAO,KAAK,KAAK;gBAC3C,MAAM,OAAO,KAAK,KAAK,CAAC,YAAY;gBACpC,YAAY,YAAY;gBAExB,+CAA+C;gBAC/C,MAAM,QAAQ,SAAS,KAAK,IAAI;gBAChC,MAAM,UAAU,SAAS,OAAO,IAAI;gBACpC,MAAM,UAAU,SAAS,OAAO,IAAI;gBAEpC,oFAAoF;gBACpF,MAAM,WAAW,EAAE;gBAEnB,IAAI,QAAQ,GAAG;oBACb,SAAS,IAAI,CAAC;wBAAE,OAAO;wBAAS,OAAO;oBAAM;gBAC/C;gBAEA,IAAI,SAAS,GAAG;oBACd,SAAS,IAAI,CAAC;wBAAE,OAAO;wBAAU,OAAO;oBAAO;gBACjD;gBAEA,IAAI,OAAO,GAAG;oBACZ,SAAS,IAAI,CAAC;wBAAE,OAAO;wBAAQ,OAAO;oBAAK;gBAC7C;gBAEA,6CAA6C;gBAC7C,SAAS,IAAI,CACX;oBAAE,OAAO;oBAAO,OAAO;gBAAM,GAC7B;oBAAE,OAAO;oBAAO,OAAO;gBAAQ,GAC/B;oBAAE,OAAO;oBAAO,OAAO;gBAAQ;gBAGjC,sDAAsD;gBACtD,wBAAwB;gBACxB,MAAM,cAAc,SAAS,GAAG,IAAI,2BAA2B;gBAE/D,2BAA2B;gBAC3B,MAAM,IAAI,IAAI,SAAS,KAAK,CAAC,GAAG;gBAEhC,gCAAgC;gBAChC,IAAI,aAAa;oBACf,MAAM,IAAI,CAAC;gBACb;gBAEA,aAAa;YACf;QACF;QAEA,iBAAiB;QACjB;QAEA,kBAAkB;QAClB,MAAM,aAAa,YAAY,YAAY;QAE3C,UAAU;QACV,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAW;IAEf,IAAI,CAAC,cAAc,OAAO,YAAY;QACpC,OAAO,MAAM,iDAAiD;IAChE;IAEA,iDAAiD;IACjD,MAAM,kBAAkB,UAAU,IAAI,CACpC,CAAC,OAAS,KAAK,KAAK,KAAK,YAAY,KAAK,KAAK,KAAK;IAGtD,gDAAgD;IAChD,MAAM,gBAAgB;QACpB,IAAI,iBAAiB;YACnB,OAAO;gBACL,UACE;gBACF,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;QACF;QAEA,4BAA4B;QAC5B,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,OAAS,KAAK,KAAK,KAAK,SAAS;YACpD,OAAO;gBACL,UACE;gBACF,QAAQ;gBACR,MAAM;gBACN,QAAQ;gBACR,MAAM;YACR;QACF;QAEA,2BAA2B;QAC3B,OAAO;YACL,UACE;YACF,QACE;YACF,MAAM;YACN,QACE;YACF,MAAM;QACR;IACF;IAEA,MAAM,aAAa;IAEnB,qBACE,8OAAC;QAAI,WAAW,CAAC,kCAAkC,EAAE,aAAa,IAAI;;0BACpE,8OAAC,4HAAA,CAAA,kBAAe;gBAAC,eAAe;0BAC9B,cAAA,8OAAC,4HAAA,CAAA,UAAO;;sCACN,8OAAC,4HAAA,CAAA,iBAAc;4BAAC,OAAO;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAW,CAAC,QAAQ,EAAE,WAAW,IAAI,EAAE;;;;;;;;;;;kDAExD,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;;sCAIP,8OAAC,4HAAA,CAAA,iBAAc;4BACb,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCACC,WAAW,CAAC,0FAA0F,EAAE,WAAW,MAAM,CAAC,wFAAwF,EAAE,WAAW,IAAI,EAAE;;kDAGrO,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC;wCAAI,WAAU;;;;;;kDAEf,8OAAC;wCAEC,WAAW,CAAC,mEAAmE,EAAE,WAAW,QAAQ,CAAC,mBAAmB,EAAE,WAAW,MAAM,EAAE;kDAE7I,cAAA,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;uCAJhC,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;0CAQtC,8OAAC;gCAAK,WAAU;0CACb,KAAK,KAAK;;;;;;;uBAvBL;;;;;;;;;;;;;;;;AA8BpB;uCAEe", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-constants.ts"], "sourcesContent": ["// Subscription status constants - IMMUT<PERSON>LE SINGLE SOURCE OF TRUTH\r\nexport const SUBSCRIPTION_STATUS = {\r\n  ACTIVE: 'active',\r\n  AUTHENTICATED: 'authenticated',\r\n  TRIAL: 'trial',\r\n  PENDING: 'pending',\r\n  HALTED: 'halted',\r\n  CANCELLED: 'cancelled',\r\n  EXPIRED: 'expired',\r\n  COMPLETED: 'completed',\r\n  PAYMENT_FAILED: 'payment_failed',\r\n  CANCELLATION_SCHEDULED: 'cancellation_scheduled'\r\n} as const;\r\n\r\nexport type SubscriptionStatus = typeof SUBSCRIPTION_STATUS[keyof typeof SUBSCRIPTION_STATUS];\r\n\r\n// Plan ID constants for consistency\r\nexport const PLAN_IDS = {\r\n  FREE: 'free',\r\n  BASIC: 'basic',\r\n  GROWTH: 'growth',\r\n  PRO: 'pro',\r\n  ENTERPRISE: 'enterprise'\r\n} as const;\r\n\r\nexport type PlanId = typeof PLAN_IDS[keyof typeof PLAN_IDS];"], "names": [], "mappings": "AAAA,mEAAmE;;;;;AAC5D,MAAM,sBAAsB;IACjC,QAAQ;IACR,eAAe;IACf,OAAO;IACP,SAAS;IACT,QAAQ;IACR,WAAW;IACX,SAAS;IACT,WAAW;IACX,gBAAgB;IAChB,wBAAwB;AAC1B;AAKO,MAAM,WAAW;IACtB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,YAAY;AACd", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-state-manager.ts"], "sourcesContent": ["import { PLAN_IDS, SUBSCRIPTION_STATUS } from \"./subscription-constants\";\r\n\r\n/**\r\n * CENTRALIZED SUBSCRIPTION STATE MANAGER\r\n *\r\n * This class provides the single source of truth for ALL subscription logic.\r\n * Use this instead of scattered functions throughout the codebase.\r\n *\r\n * CRITICAL: This is the ONLY place where subscription access logic should be defined.\r\n */\r\nexport class SubscriptionStateManager {\r\n  /**\r\n   * MASTER FUNCTION: Determines if a user should have active subscription access\r\n   * This is the ONLY function that should be used for subscription access control\r\n   *\r\n   * @param status The subscription status\r\n   * @param planId The plan ID (required for accurate determination)\r\n   * @returns true if the user should have active subscription features\r\n   */\r\n  static shouldHaveActiveSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {\r\n    // Free plan users NEVER have \"active subscription\" - they're on free tier\r\n    if (planId === PLAN_IDS.FREE) {\r\n      return false;\r\n    }\r\n\r\n    // Trial users NEVER have \"active subscription\" - they're testing, not paying\r\n    if (status === SUBSCRIPTION_STATUS.TRIAL) {\r\n      return false;\r\n    }\r\n\r\n    // Only ACTIVE status on PAID plans counts as \"active subscription\"\r\n    // Authenticated users have selected a plan but haven't paid yet\r\n    const activeStatuses: string[] = [\r\n      SUBSCRIPTION_STATUS.ACTIVE\r\n    ];\r\n\r\n    return activeStatuses.includes(status);\r\n  }\r\n\r\n  /**\r\n   * Determines if a subscription status is considered terminal (final state)\r\n   * Terminal states cannot transition to active states without creating new subscription\r\n   */\r\n  static isTerminalStatus(status: string): boolean {\r\n    const terminalStatuses: string[] = [\r\n      SUBSCRIPTION_STATUS.CANCELLED,\r\n      SUBSCRIPTION_STATUS.EXPIRED,\r\n      SUBSCRIPTION_STATUS.COMPLETED\r\n    ];\r\n    return terminalStatuses.includes(status);\r\n  }\r\n\r\n  /**\r\n   * Determines if a subscription status indicates trial period\r\n   */\r\n  static isTrialStatus(status: string): boolean {\r\n    return status === SUBSCRIPTION_STATUS.TRIAL;\r\n  }\r\n\r\n  /**\r\n   * Determines if a plan is a free plan\r\n   */\r\n  static isFreeStatus(status: string, planId?: string): boolean {\r\n    return planId === PLAN_IDS.FREE || status === 'free';\r\n  }\r\n\r\n  /**\r\n   * Get user's access level based on subscription state\r\n   */\r\n  static getAccessLevel(status: string, planId: string = PLAN_IDS.FREE): 'free' | 'trial' | 'paid' {\r\n    if (planId === PLAN_IDS.FREE) return 'free';\r\n    if (status === SUBSCRIPTION_STATUS.TRIAL) return 'trial';\r\n    if (this.shouldHaveActiveSubscription(status, planId)) return 'paid';\r\n    return 'free';\r\n  }\r\n\r\n  /**\r\n   * Determines if a subscription status indicates an active paid subscription\r\n   */\r\n  static isActivePaidSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {\r\n    return this.shouldHaveActiveSubscription(status, planId);\r\n  }\r\n\r\n  /**\r\n   * Validates if a status transition is allowed\r\n   */\r\n  static isValidStatusTransition(fromStatus: string, toStatus: string): boolean {\r\n    // Terminal states cannot transition to non-terminal states\r\n    if (this.isTerminalStatus(fromStatus) && !this.isTerminalStatus(toStatus)) {\r\n      return false;\r\n    }\r\n\r\n    // All other transitions are allowed\r\n    return true;\r\n  }\r\n}\r\n\r\n// Legacy function wrappers for backward compatibility - DEPRECATED\r\n// Use SubscriptionStateManager instead for new code\r\nexport function shouldHaveActiveSubscription(status: string): boolean {\r\n  console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');\r\n  return SubscriptionStateManager.shouldHaveActiveSubscription(status, PLAN_IDS.FREE);\r\n}\r\n\r\nexport function isTerminalStatus(status: string): boolean {\r\n  return SubscriptionStateManager.isTerminalStatus(status);\r\n}\r\n\r\nexport function isActivePaidSubscription(status: string): boolean {\r\n  console.warn('[DEPRECATED] Use SubscriptionStateManager.isActivePaidSubscription(status, planId) instead');\r\n  return SubscriptionStateManager.isActivePaidSubscription(status, PLAN_IDS.FREE);\r\n}\r\n\r\nexport function isTrialStatus(status: string): boolean {\r\n  return SubscriptionStateManager.isTrialStatus(status);\r\n}\r\n\r\nexport function isFreeStatus(status: string, planId?: string): boolean {\r\n  return SubscriptionStateManager.isFreeStatus(status, planId);\r\n}\r\n\r\n/**\r\n * LEGACY FUNCTION - Use SubscriptionStateManager.shouldHaveActiveSubscription instead\r\n *\r\n * @deprecated Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead\r\n */\r\nexport function shouldHaveActiveSubscriptionByPlan(status: string, planId: string): boolean {\r\n  console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');\r\n  return SubscriptionStateManager.shouldHaveActiveSubscription(status, planId);\r\n}"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAUO,MAAM;IACX;;;;;;;GAOC,GACD,OAAO,6BAA6B,MAAc,EAAE,SAAiB,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAW;QAC3F,0EAA0E;QAC1E,IAAI,WAAW,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAE;YAC5B,OAAO;QACT;QAEA,6EAA6E;QAC7E,IAAI,WAAW,oKAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;YACxC,OAAO;QACT;QAEA,mEAAmE;QACnE,gEAAgE;QAChE,MAAM,iBAA2B;YAC/B,oKAAA,CAAA,sBAAmB,CAAC,MAAM;SAC3B;QAED,OAAO,eAAe,QAAQ,CAAC;IACjC;IAEA;;;GAGC,GACD,OAAO,iBAAiB,MAAc,EAAW;QAC/C,MAAM,mBAA6B;YACjC,oKAAA,CAAA,sBAAmB,CAAC,SAAS;YAC7B,oKAAA,CAAA,sBAAmB,CAAC,OAAO;YAC3B,oKAAA,CAAA,sBAAmB,CAAC,SAAS;SAC9B;QACD,OAAO,iBAAiB,QAAQ,CAAC;IACnC;IAEA;;GAEC,GACD,OAAO,cAAc,MAAc,EAAW;QAC5C,OAAO,WAAW,oKAAA,CAAA,sBAAmB,CAAC,KAAK;IAC7C;IAEA;;GAEC,GACD,OAAO,aAAa,MAAc,EAAE,MAAe,EAAW;QAC5D,OAAO,WAAW,oKAAA,CAAA,WAAQ,CAAC,IAAI,IAAI,WAAW;IAChD;IAEA;;GAEC,GACD,OAAO,eAAe,MAAc,EAAE,SAAiB,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAA6B;QAC/F,IAAI,WAAW,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAE,OAAO;QACrC,IAAI,WAAW,oKAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE,OAAO;QACjD,IAAI,IAAI,CAAC,4BAA4B,CAAC,QAAQ,SAAS,OAAO;QAC9D,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,yBAAyB,MAAc,EAAE,SAAiB,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAW;QACvF,OAAO,IAAI,CAAC,4BAA4B,CAAC,QAAQ;IACnD;IAEA;;GAEC,GACD,OAAO,wBAAwB,UAAkB,EAAE,QAAgB,EAAW;QAC5E,2DAA2D;QAC3D,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW;YACzE,OAAO;QACT;QAEA,oCAAoC;QACpC,OAAO;IACT;AACF;AAIO,SAAS,6BAA6B,MAAc;IACzD,QAAQ,IAAI,CAAC;IACb,OAAO,yBAAyB,4BAA4B,CAAC,QAAQ,oKAAA,CAAA,WAAQ,CAAC,IAAI;AACpF;AAEO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,yBAAyB,gBAAgB,CAAC;AACnD;AAEO,SAAS,yBAAyB,MAAc;IACrD,QAAQ,IAAI,CAAC;IACb,OAAO,yBAAyB,wBAAwB,CAAC,QAAQ,oKAAA,CAAA,WAAQ,CAAC,IAAI;AAChF;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,yBAAyB,aAAa,CAAC;AAChD;AAEO,SAAS,aAAa,MAAc,EAAE,MAAe;IAC1D,OAAO,yBAAyB,YAAY,CAAC,QAAQ;AACvD;AAOO,SAAS,mCAAmC,MAAc,EAAE,MAAc;IAC/E,QAAQ,IAAI,CAAC;IACb,OAAO,yBAAyB,4BAA4B,CAAC,QAAQ;AACvE", "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 736, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-state-validator.ts"], "sourcesContent": ["import { SUBSCRIPTION_STATUS } from \"./subscription-constants\";\r\nimport { SubscriptionStateManager } from \"./subscription-state-manager\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\n\r\n/**\r\n * CENTRALIZED SUBSCRIPTION STATE VALIDATION\r\n *\r\n * Validates the entire subscription state for consistency.\r\n * This function should be used whenever we need to verify subscription integrity.\r\n */\r\nexport interface SubscriptionStateValidation {\r\n  isValid: boolean;\r\n  hasActiveSubscription: boolean;\r\n  accessLevel: 'free' | 'trial' | 'paid';\r\n  warnings: string[];\r\n  errors: string[];\r\n}\r\n\r\n/**\r\n * Validates subscription state for consistency across business_profiles and payment_subscriptions\r\n * Uses the centralized SubscriptionStateManager for all logic\r\n */\r\nexport function validateSubscriptionState(\r\n  businessProfile: {\r\n    has_active_subscription: boolean;\r\n    trial_end_date: string | null;\r\n  },\r\n  paymentSubscription: {\r\n    subscription_status: string;\r\n    plan_id: string;\r\n  } | null\r\n): SubscriptionStateValidation {\r\n  const warnings: string[] = [];\r\n  const errors: string[] = [];\r\n\r\n  // Determine expected state based on payment subscription using centralized manager\r\n  let expectedHasActiveSubscription = false;\r\n  let accessLevel: 'free' | 'trial' | 'paid' = 'free';\r\n\r\n  if (paymentSubscription) {\r\n    expectedHasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(\r\n      paymentSubscription.subscription_status,\r\n      paymentSubscription.plan_id\r\n    );\r\n\r\n    accessLevel = SubscriptionStateManager.getAccessLevel(\r\n      paymentSubscription.subscription_status,\r\n      paymentSubscription.plan_id\r\n    );\r\n  }\r\n\r\n  // Check for inconsistencies\r\n  if (businessProfile.has_active_subscription !== expectedHasActiveSubscription) {\r\n    errors.push(\r\n      `has_active_subscription mismatch: business_profiles=${businessProfile.has_active_subscription}, expected=${expectedHasActiveSubscription}`\r\n    );\r\n  }\r\n\r\n  // Check trial state consistency\r\n  if (paymentSubscription?.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {\r\n    if (!businessProfile.trial_end_date) {\r\n      warnings.push('Trial status but no trial_end_date set');\r\n    } else {\r\n      const trialEnd = new Date(businessProfile.trial_end_date);\r\n      const now = new Date();\r\n      if (trialEnd <= now) {\r\n        warnings.push('Trial status but trial period has expired');\r\n      }\r\n    }\r\n  }\r\n\r\n  return {\r\n    isValid: errors.length === 0,\r\n    hasActiveSubscription: expectedHasActiveSubscription,\r\n    accessLevel,\r\n    warnings,\r\n    errors\r\n  };\r\n}\r\n\r\n/**\r\n * ENHANCED SUBSCRIPTION STATE VALIDATOR\r\n *\r\n * Validates and fixes subscription state inconsistencies across tables\r\n */\r\nexport async function validateAndFixSubscriptionState(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; message: string; fixed: boolean }> {\r\n  try {\r\n    const adminClient = createAdminClient();\r\n\r\n    // Get current state from both tables\r\n    const [profileResult, subscriptionResult] = await Promise.all([\r\n      adminClient\r\n        .from('business_profiles')\r\n        .select('has_active_subscription, trial_end_date')\r\n        .eq('id', businessProfileId)\r\n        .maybeSingle(),\r\n      adminClient\r\n        .from('payment_subscriptions')\r\n        .select('subscription_status, plan_id')\r\n        .eq('business_profile_id', businessProfileId)\r\n        .order('created_at', { ascending: false })\r\n        .limit(1)\r\n        .maybeSingle()\r\n    ]);\r\n\r\n    if (profileResult.error) {\r\n      return { success: false, message: `Profile fetch error: ${profileResult.error.message}`, fixed: false };\r\n    }\r\n\r\n    if (!profileResult.data) {\r\n      return { success: false, message: 'Business profile not found', fixed: false };\r\n    }\r\n\r\n    // Use centralized validation\r\n    const validation = validateSubscriptionState(\r\n      profileResult.data,\r\n      subscriptionResult.data\r\n    );\r\n\r\n    if (validation.isValid) {\r\n      return { success: true, message: 'Subscription state is consistent', fixed: false };\r\n    }\r\n\r\n    // Fix inconsistencies\r\n    const expectedHasActiveSubscription = validation.hasActiveSubscription;\r\n\r\n    if (profileResult.data.has_active_subscription !== expectedHasActiveSubscription) {\r\n      const { error: fixError } = await adminClient\r\n        .from('business_profiles')\r\n        .update({\r\n          has_active_subscription: expectedHasActiveSubscription,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', businessProfileId);\r\n\r\n      if (fixError) {\r\n        return {\r\n          success: false,\r\n          message: `Failed to fix inconsistency: ${fixError.message}`,\r\n          fixed: false\r\n        };\r\n      }\r\n\r\n      console.log(`[STATE_VALIDATOR] Fixed has_active_subscription for ${businessProfileId}: ${profileResult.data.has_active_subscription} -> ${expectedHasActiveSubscription}`);\r\n      return {\r\n        success: true,\r\n        message: `Fixed subscription state inconsistency`,\r\n        fixed: true\r\n      };\r\n    }\r\n\r\n    return { success: true, message: 'No fixes needed', fixed: false };\r\n  } catch (error) {\r\n    console.error(`[STATE_VALIDATOR] Exception:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Validation exception: ${error instanceof Error ? error.message : String(error)}`,\r\n      fixed: false\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAoBO,SAAS,0BACd,eAGC,EACD,mBAGQ;IAER,MAAM,WAAqB,EAAE;IAC7B,MAAM,SAAmB,EAAE;IAE3B,mFAAmF;IACnF,IAAI,gCAAgC;IACpC,IAAI,cAAyC;IAE7C,IAAI,qBAAqB;QACvB,gCAAgC,2KAAA,CAAA,2BAAwB,CAAC,4BAA4B,CACnF,oBAAoB,mBAAmB,EACvC,oBAAoB,OAAO;QAG7B,cAAc,2KAAA,CAAA,2BAAwB,CAAC,cAAc,CACnD,oBAAoB,mBAAmB,EACvC,oBAAoB,OAAO;IAE/B;IAEA,4BAA4B;IAC5B,IAAI,gBAAgB,uBAAuB,KAAK,+BAA+B;QAC7E,OAAO,IAAI,CACT,CAAC,oDAAoD,EAAE,gBAAgB,uBAAuB,CAAC,WAAW,EAAE,+BAA+B;IAE/I;IAEA,gCAAgC;IAChC,IAAI,qBAAqB,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;QAC1E,IAAI,CAAC,gBAAgB,cAAc,EAAE;YACnC,SAAS,IAAI,CAAC;QAChB,OAAO;YACL,MAAM,WAAW,IAAI,KAAK,gBAAgB,cAAc;YACxD,MAAM,MAAM,IAAI;YAChB,IAAI,YAAY,KAAK;gBACnB,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B,uBAAuB;QACvB;QACA;QACA;IACF;AACF;AAOO,eAAe,gCACpB,iBAAyB;IAEzB,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEpC,qCAAqC;QACrC,MAAM,CAAC,eAAe,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC5D,YACG,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,mBACT,WAAW;YACd,YACG,IAAI,CAAC,yBACL,MAAM,CAAC,gCACP,EAAE,CAAC,uBAAuB,mBAC1B,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC,GACN,WAAW;SACf;QAED,IAAI,cAAc,KAAK,EAAE;YACvB,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,qBAAqB,EAAE,cAAc,KAAK,CAAC,OAAO,EAAE;gBAAE,OAAO;YAAM;QACxG;QAEA,IAAI,CAAC,cAAc,IAAI,EAAE;YACvB,OAAO;gBAAE,SAAS;gBAAO,SAAS;gBAA8B,OAAO;YAAM;QAC/E;QAEA,6BAA6B;QAC7B,MAAM,aAAa,0BACjB,cAAc,IAAI,EAClB,mBAAmB,IAAI;QAGzB,IAAI,WAAW,OAAO,EAAE;YACtB,OAAO;gBAAE,SAAS;gBAAM,SAAS;gBAAoC,OAAO;YAAM;QACpF;QAEA,sBAAsB;QACtB,MAAM,gCAAgC,WAAW,qBAAqB;QAEtE,IAAI,cAAc,IAAI,CAAC,uBAAuB,KAAK,+BAA+B;YAChF,MAAM,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,YAC/B,IAAI,CAAC,qBACL,MAAM,CAAC;gBACN,yBAAyB;gBACzB,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,UAAU;gBACZ,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,6BAA6B,EAAE,SAAS,OAAO,EAAE;oBAC3D,OAAO;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,kBAAkB,EAAE,EAAE,cAAc,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,+BAA+B;YACzK,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,sCAAsC,CAAC;gBACjD,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,SAAS;YAAmB,OAAO;QAAM;IACnE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAC9C,OAAO;YACL,SAAS;YACT,SAAS,CAAC,sBAAsB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;YAC1F,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/types.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Webhook Event Types\r\n *\r\n * This file contains the types for Razorpay subscription webhook events.\r\n */\r\n\r\n// Subscription webhook event types\r\nexport enum RazorpaySubscriptionEventType {\r\n  // Subscription events\r\n  _SUBSCRIPTION_AUTHENTICATED = \"subscription.authenticated\",\r\n  _SUBSCRIPTION_ACTIVATED = \"subscription.activated\",\r\n  _SUBSCRIPTION_CHARGED = \"subscription.charged\",\r\n  _SUBSCRIPTION_PENDING = \"subscription.pending\",\r\n  _SUBSCRIPTION_HALTED = \"subscription.halted\", // This is the event for paused subscriptions\r\n  _SUBSCRIPTION_CANCELLED = \"subscription.cancelled\",\r\n  _SUBSCRIPTION_COMPLETED = \"subscription.completed\",\r\n  _SUBSCRIPTION_EXPIRED = \"subscription.expired\",\r\n  _SUBSCRIPTION_UPDATED = \"subscription.updated\",\r\n\r\n  // Payment events\r\n  _PAYMENT_AUTHORIZED = \"payment.authorized\",\r\n  _PAYMENT_CAPTURED = \"payment.captured\",\r\n  _PAYMENT_FAILED = \"payment.failed\",\r\n\r\n  // Invoice events\r\n  _INVOICE_PAID = \"invoice.paid\",\r\n\r\n  // Refund events\r\n  _REFUND_CREATED = \"refund.created\",\r\n  _REFUND_PROCESSED = \"refund.processed\",\r\n  _REFUND_FAILED = \"refund.failed\"\r\n}\r\n\r\n// Subscription status values\r\nexport enum RazorpaySubscriptionStatus {\r\n  _CREATED = \"created\",\r\n  _AUTHENTICATED = \"authenticated\",\r\n  _ACTIVE = \"active\",\r\n  _PENDING = \"pending\",\r\n  _HALTED = \"halted\", // This is the status for paused subscriptions\r\n  _CANCELLED = \"cancelled\",\r\n  _COMPLETED = \"completed\",\r\n  _EXPIRED = \"expired\"\r\n}\r\n\r\n// Payment status values\r\nexport enum RazorpayPaymentStatus {\r\n  _CREATED = \"created\",\r\n  _AUTHORIZED = \"authorized\",\r\n  _CAPTURED = \"captured\",\r\n  _REFUNDED = \"refunded\",\r\n  _FAILED = \"failed\"\r\n}\r\n\r\n// Refund status values\r\nexport enum RazorpayRefundStatus {\r\n  _CREATED = \"created\",\r\n  _PROCESSED = \"processed\",\r\n  _FAILED = \"failed\"\r\n}\r\n\r\n// Supabase subscription status types (reused from existing implementation)\r\nexport enum SupabaseSubscriptionStatus {\r\n  _ACTIVE = \"active\",\r\n  _PENDING = \"pending\",\r\n  _HALTED = \"halted\", // This is the status for paused subscriptions\r\n  _CANCELLED = \"cancelled\",\r\n  _COMPLETED = \"completed\",\r\n  _EXPIRED = \"expired\",\r\n  _PAYMENT_FAILED = \"payment_failed\",\r\n  _AUTHENTICATED = \"authenticated\"\r\n}\r\n\r\n/**\r\n * Maps Razorpay subscription status to Supabase subscription status\r\n * @param razorpayStatus The Razorpay subscription status\r\n * @returns The corresponding Supabase subscription status\r\n */\r\nexport function mapRazorpayStatusToSupabase(razorpayStatus: string): SupabaseSubscriptionStatus {\r\n  switch (razorpayStatus) {\r\n    case RazorpaySubscriptionStatus._ACTIVE:\r\n      return SupabaseSubscriptionStatus._ACTIVE;\r\n    case RazorpaySubscriptionStatus._PENDING:\r\n      return SupabaseSubscriptionStatus._PENDING;\r\n    case RazorpaySubscriptionStatus._HALTED:\r\n      return SupabaseSubscriptionStatus._HALTED;\r\n    case RazorpaySubscriptionStatus._CANCELLED:\r\n      return SupabaseSubscriptionStatus._CANCELLED;\r\n    case RazorpaySubscriptionStatus._COMPLETED:\r\n      return SupabaseSubscriptionStatus._COMPLETED;\r\n    case RazorpaySubscriptionStatus._AUTHENTICATED:\r\n      return SupabaseSubscriptionStatus._AUTHENTICATED;\r\n    case RazorpaySubscriptionStatus._EXPIRED:\r\n      return SupabaseSubscriptionStatus._EXPIRED;\r\n    default:\r\n      // Default to pending for unknown statuses\r\n      console.warn(`Unknown Razorpay status: ${razorpayStatus}, defaulting to pending`);\r\n      return SupabaseSubscriptionStatus._PENDING;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mCAAmC;;;;;;;;;AAC5B,IAAA,AAAK,uDAAA;IACV,sBAAsB;;;;;;;;;;IAWtB,iBAAiB;;;;IAKjB,iBAAiB;;IAGjB,gBAAgB;;;;WApBN;;AA2BL,IAAA,AAAK,oDAAA;;;;;;;;;WAAA;;AAYL,IAAA,AAAK,+CAAA;;;;;;WAAA;;AASL,IAAA,AAAK,8CAAA;;;;WAAA;;AAOL,IAAA,AAAK,oDAAA;;;;;;;;;WAAA;;AAgBL,SAAS,4BAA4B,cAAsB;IAChE,OAAQ;QACN;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE,0CAA0C;YAC1C,QAAQ,IAAI,CAAC,CAAC,yBAAyB,EAAE,eAAe,uBAAuB,CAAC;YAChF;IACJ;AACF", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-db-updater.ts"], "sourcesContent": ["import { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { SupabaseSubscriptionStatus } from \"../types\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { SubscriptionStateManager } from \"./subscription-state-manager\";\r\n\r\n/**\r\n * CENTRALIZED SUBSCRIPTION UPDATE FUNCTION\r\n * \r\n * This function implements the single source of truth for subscription status handling.\r\n * All subscription updates must go through this function to ensure consistency.\r\n * \r\n * @param supabase The Supabase client (admin client will be used internally)\r\n * @param subscriptionId The Razorpay subscription ID\r\n * @param status The new subscription status\r\n * @param additionalData Additional data to update\r\n * @returns The result of the update operation\r\n */\r\nexport async function updateSubscription(\r\n  _supabase: SupabaseClient, // Original client not used, using admin client instead\r\n  subscriptionId: string,\r\n  status: SupabaseSubscriptionStatus,\r\n  additionalData: Record<string, unknown> = {}\r\n): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    // Get admin client to bypass RLS\r\n    const adminClient = createAdminClient();\r\n\r\n    // Get subscription details from Razorpay to find the business_profile_id and other details\r\n    const { getSubscription } = await import('@/lib/razorpay/services/subscription');\r\n    const subscriptionDetails = await getSubscription(subscriptionId);\r\n\r\n    if (!subscriptionDetails.success || !subscriptionDetails.data) {\r\n      console.error(`[RAZORPAY_WEBHOOK] Failed to get subscription details from Razorpay for ${subscriptionId}`);\r\n\r\n      // ENHANCED: Try to get business_profile_id from our database instead\r\n      const { data: localSubscription, error: localError } = await adminClient\r\n        .from('payment_subscriptions')\r\n        .select('business_profile_id, plan_id, plan_cycle')\r\n        .eq('razorpay_subscription_id', subscriptionId)\r\n        .maybeSingle();\r\n\r\n      if (localError || !localSubscription) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Also failed to get subscription from local database for ${subscriptionId}:`, localError);\r\n        return { success: false, message: `Failed to get subscription details from both Razorpay and local database` };\r\n      }\r\n\r\n      console.log(`[RAZORPAY_WEBHOOK] Using local subscription data for ${subscriptionId} since Razorpay API failed`);\r\n\r\n      // Create a mock subscription details object using local data\r\n      const mockSubscriptionDetails = {\r\n        success: true,\r\n        data: {\r\n          id: subscriptionId,\r\n          plan_id: `${localSubscription.plan_id}_${localSubscription.plan_cycle}`,\r\n          customer_id: null,\r\n          current_start: null,\r\n          current_end: null,\r\n          charge_at: null,\r\n          start_at: null,\r\n          notes: {\r\n            business_profile_id: localSubscription.business_profile_id,\r\n            plan_type: localSubscription.plan_id,\r\n            plan_cycle: localSubscription.plan_cycle\r\n          }\r\n        }\r\n      };\r\n\r\n      // Use the mock data for the rest of the function\r\n      const subscriptionDetailsToUse = mockSubscriptionDetails;\r\n      return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetailsToUse.data);\r\n    }\r\n\r\n    // Continue with normal processing using Razorpay data\r\n    return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetails.data);\r\n  } catch (error) {\r\n    console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process subscription update with given subscription data\r\n * This function contains the main logic extracted from updateSubscription\r\n */\r\nasync function processSubscriptionUpdate(\r\n  adminClient: SupabaseClient,\r\n  subscriptionId: string,\r\n  status: SupabaseSubscriptionStatus,\r\n  additionalData: Record<string, unknown>,\r\n  subscriptionData: {\r\n    id: string;\r\n    plan_id: string;\r\n    customer_id: string | null;\r\n    current_start: number | null;\r\n    current_end: number | null;\r\n    charge_at: number | null;\r\n    start_at: number | null;\r\n    notes: {\r\n      business_profile_id?: string;\r\n      user_id?: string;\r\n      plan_type?: string;\r\n      plan_cycle?: string;\r\n    };\r\n  }\r\n): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    // Extract business_profile_id from notes\r\n    const businessProfileId = subscriptionData.notes?.business_profile_id ||\r\n                             subscriptionData.notes?.user_id;\r\n\r\n    if (!businessProfileId) {\r\n      console.error(`[RAZORPAY_WEBHOOK] No business_profile_id found in subscription notes for ${subscriptionId}`);\r\n      return { success: false, message: `No business_profile_id found in subscription notes` };\r\n    }\r\n\r\n    // Extract plan details from notes or plan_id\r\n    let planType = subscriptionData.notes?.plan_type;\r\n    let planCycle = subscriptionData.notes?.plan_cycle;\r\n\r\n    // If plan type and cycle are not in notes, try to determine from plan_id\r\n    if (!planType || !planCycle) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Plan type or cycle not found in notes, determining from plan_id: ${subscriptionData.plan_id}`);\r\n\r\n      // Use centralized plan configuration to map Razorpay plan ID to plan details\r\n      const { getPlanByRazorpayPlanId } = await import('@/lib/config/plans');\r\n      const planDetails = getPlanByRazorpayPlanId(subscriptionData.plan_id);\r\n\r\n      if (planDetails) {\r\n        planType = planDetails.id;\r\n        // Determine cycle by checking which Razorpay plan ID matches\r\n        planCycle = planDetails.razorpayPlanIds.monthly === subscriptionData.plan_id ? \"monthly\" : \"yearly\";\r\n        console.log(`[RAZORPAY_WEBHOOK] Determined plan type: ${planType}, cycle: ${planCycle} from plan_id using centralized config`);\r\n      } else {\r\n        // Default to basic monthly if we can't determine\r\n        planType = \"basic\";\r\n        planCycle = \"monthly\";\r\n        console.log(`[RAZORPAY_WEBHOOK] Could not determine plan type and cycle from plan_id: ${subscriptionData.plan_id}, defaulting to basic monthly`);\r\n      }\r\n    }\r\n\r\n    // CENTRALIZED LOGIC: Use SubscriptionStateManager to determine has_active_subscription\r\n    const hasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(status, planType || 'free');\r\n\r\n    // Create a copy of additionalData to avoid modifying the original\r\n    const additionalDataCopy = { ...additionalData };\r\n\r\n    // Remove has_active_subscription from additionalData if it exists\r\n    // This ensures we always set it based on the status, not what's passed in\r\n    if ('has_active_subscription' in additionalDataCopy) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Removing has_active_subscription from additionalData for subscription with status ${status}`);\r\n      delete additionalDataCopy.has_active_subscription;\r\n    }\r\n\r\n    // Extract subscription dates from Razorpay subscription details\r\n    let subscriptionStartDate = subscriptionData.current_start\r\n      ? new Date(subscriptionData.current_start * 1000).toISOString()\r\n      : null;\r\n\r\n    const subscriptionExpiryTime = subscriptionData.current_end\r\n      ? new Date(subscriptionData.current_end * 1000).toISOString()\r\n      : null;\r\n\r\n    const subscriptionChargeTime = subscriptionData.charge_at\r\n      ? new Date(subscriptionData.charge_at * 1000).toISOString()\r\n      : null;\r\n\r\n    // Check if the status is authenticated or active\r\n    const isValidStatus = status === SupabaseSubscriptionStatus._AUTHENTICATED ||\r\n                         status === SupabaseSubscriptionStatus._ACTIVE;\r\n\r\n    if (!isValidStatus) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Skipping creation/update of subscription record for ${subscriptionId} with status ${status} - only handling authenticated or active statuses`);\r\n      return { success: true, message: `Skipped creation/update of subscription record with status ${status}` };\r\n    }\r\n\r\n    // For authenticated subscriptions, ensure we're setting the correct dates\r\n    // This is especially important for trial users where start_at is in the future\r\n    if (status === SupabaseSubscriptionStatus._AUTHENTICATED) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Authenticated subscription detected, ensuring dates are set correctly`);\r\n\r\n      // For authenticated subscriptions, we need to check if start_at is set\r\n      // If it is, we should use that for subscription_start_date instead of current_start\r\n      if (subscriptionData.start_at) {\r\n        const startAt = new Date(subscriptionData.start_at * 1000).toISOString();\r\n        console.log(`[RAZORPAY_WEBHOOK] Using start_at (${startAt}) for subscription_start_date`);\r\n\r\n        // Override the subscription_start_date with start_at\r\n        subscriptionStartDate = startAt;\r\n      }\r\n    }\r\n\r\n    // Find existing subscription record\r\n    const { data: subscription, error: findError } = await adminClient\r\n      .from('payment_subscriptions')\r\n      .select('id, business_profile_id, razorpay_subscription_id')\r\n      .eq('razorpay_subscription_id', subscriptionId)\r\n      .maybeSingle();\r\n\r\n    if (findError) {\r\n      console.error(`[RAZORPAY_WEBHOOK] Error finding subscription ${subscriptionId}:`, findError);\r\n      return { success: false, message: `Error finding subscription: ${findError.message}` };\r\n    }\r\n\r\n    // If no subscription record exists for this Razorpay subscription ID, check if there's an existing record for the business\r\n    if (!subscription) {\r\n      console.log(`[RAZORPAY_WEBHOOK] No subscription found with ID ${subscriptionId}, checking for existing subscription for business ${businessProfileId}`);\r\n\r\n      // Check if there's an existing subscription for this business\r\n      const { data: existingSubscriptions, error: existingError } = await adminClient\r\n        .from('payment_subscriptions')\r\n        .select('id, razorpay_subscription_id, subscription_status')\r\n        .eq('business_profile_id', businessProfileId);\r\n\r\n      if (existingError) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Error checking for existing subscriptions for business ${businessProfileId}:`, existingError);\r\n        return { success: false, message: `Error checking for existing subscriptions: ${existingError.message}` };\r\n      }\r\n\r\n      // If there's an existing subscription for this business, update it instead of creating a new one\r\n      if (existingSubscriptions && existingSubscriptions.length > 0) {\r\n        console.log(`[RAZORPAY_WEBHOOK] Found existing subscription for business ${businessProfileId}, updating instead of creating new one`);\r\n\r\n        const existingSubscription = existingSubscriptions[0];\r\n\r\n        // Create the update data object\r\n        const updateData = {\r\n          razorpay_subscription_id: subscriptionId,\r\n          razorpay_customer_id: subscriptionData.customer_id || null,\r\n          subscription_status: status,\r\n          plan_id: planType,\r\n          plan_cycle: planCycle,\r\n          subscription_start_date: subscriptionStartDate,\r\n          subscription_expiry_time: subscriptionExpiryTime,\r\n          subscription_charge_time: subscriptionChargeTime,\r\n          updated_at: new Date().toISOString(),\r\n          ...additionalDataCopy\r\n        };\r\n\r\n        // Update the existing subscription record\r\n        const { error: updateError } = await adminClient\r\n          .from('payment_subscriptions')\r\n          .update(updateData)\r\n          .eq('id', existingSubscription.id);\r\n\r\n        if (updateError) {\r\n          console.error(`[RAZORPAY_WEBHOOK] Error updating existing subscription ${existingSubscription.id}:`, updateError);\r\n          return { success: false, message: `Error updating existing subscription: ${updateError.message}` };\r\n        }\r\n\r\n        console.log(`[RAZORPAY_WEBHOOK] Updated existing subscription ${existingSubscription.id} with new Razorpay ID ${subscriptionId} and status ${status}`);\r\n\r\n        // Use transaction utility to ensure consistency\r\n        const transactionResult = await updateSubscriptionWithBusinessProfile({\r\n          subscription_id: subscriptionId,\r\n          business_profile_id: businessProfileId,\r\n          subscription_status: status,\r\n          has_active_subscription: hasActiveSubscription,\r\n          additional_data: updateData\r\n        });\r\n\r\n        if (!transactionResult.success) {\r\n          console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);\r\n          return { success: false, message: `Transaction failed: ${transactionResult.message}` };\r\n        }\r\n\r\n        return { success: true, message: `Updated existing subscription with new Razorpay ID and status ${status}` };\r\n      }\r\n\r\n      // If no existing subscription for this business, create a new one\r\n      console.log(`[RAZORPAY_WEBHOOK] No existing subscription found for business ${businessProfileId}, creating new one`);\r\n\r\n      const insertData = {\r\n        business_profile_id: businessProfileId,\r\n        razorpay_subscription_id: subscriptionId,\r\n        razorpay_customer_id: subscriptionData.customer_id || null,\r\n        subscription_status: status,\r\n        plan_id: planType,\r\n        plan_cycle: planCycle,\r\n        subscription_start_date: subscriptionStartDate,\r\n        subscription_expiry_time: subscriptionExpiryTime,\r\n        subscription_charge_time: subscriptionChargeTime,\r\n        created_at: new Date().toISOString(),\r\n        updated_at: new Date().toISOString(),\r\n        ...additionalDataCopy\r\n      };\r\n\r\n      const { data: _newSubscription, error: insertError } = await adminClient\r\n        .from('payment_subscriptions')\r\n        .insert(insertData)\r\n        .select('id')\r\n        .single();\r\n\r\n      if (insertError) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Error creating subscription record for ${subscriptionId}:`, insertError);\r\n        return { success: false, message: `Error creating subscription record: ${insertError.message}` };\r\n      }\r\n\r\n      console.log(`[RAZORPAY_WEBHOOK] Created new subscription record for ${subscriptionId}`);\r\n\r\n      // Use transaction utility to ensure consistency\r\n      const transactionResult = await updateSubscriptionWithBusinessProfile({\r\n        subscription_id: subscriptionId,\r\n        business_profile_id: businessProfileId,\r\n        subscription_status: status,\r\n        has_active_subscription: hasActiveSubscription,\r\n        additional_data: insertData\r\n      });\r\n\r\n      if (!transactionResult.success) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Transaction failed for new subscription ${subscriptionId}:`, transactionResult.message);\r\n        return { success: false, message: `Transaction failed: ${transactionResult.message}` };\r\n      }\r\n\r\n      console.log(`[RAZORPAY_WEBHOOK] Created subscription for ${subscriptionId} with status ${status}`);\r\n      return { success: true, message: `Created subscription with status ${status}` };\r\n    }\r\n\r\n    // If subscription exists, update it\r\n    const updateData = {\r\n      subscription_status: status,\r\n      subscription_start_date: subscriptionStartDate,\r\n      subscription_expiry_time: subscriptionExpiryTime,\r\n      subscription_charge_time: subscriptionChargeTime,\r\n      updated_at: new Date().toISOString(),\r\n      ...additionalDataCopy\r\n    };\r\n\r\n    // ENHANCED: Use atomic RPC function for transaction safety\r\n    const transactionResult = await updateSubscriptionWithBusinessProfile({\r\n      subscription_id: subscriptionId,\r\n      business_profile_id: businessProfileId,\r\n      subscription_status: status,\r\n      has_active_subscription: hasActiveSubscription,\r\n      additional_data: updateData\r\n    });\r\n\r\n    if (!transactionResult.success) {\r\n      console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);\r\n      return { success: false, message: `Transaction failed: ${transactionResult.message}` };\r\n    }\r\n\r\n    console.log(`[RAZORPAY_WEBHOOK] Updated subscription ${subscription.id} with status ${status}`);\r\n    return { success: true, message: `Updated subscription with status ${status}` };\r\n  } catch (error) {\r\n    console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * ATOMIC TRANSACTION UTILITY\r\n *\r\n * Ensures atomic updates between payment_subscriptions and business_profiles tables.\r\n * This prevents data inconsistencies that could cause users to lose access.\r\n */\r\ninterface TransactionParams {\r\n  subscription_id: string;\r\n  business_profile_id: string;\r\n  subscription_status: string;\r\n  has_active_subscription: boolean;\r\n  additional_data?: Record<string, unknown>;\r\n}\r\n\r\nexport async function updateSubscriptionWithBusinessProfile(\r\n  params: TransactionParams\r\n): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    const adminClient = createAdminClient();\r\n\r\n    console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${params.subscription_id}`);\r\n\r\n    // ENHANCED: Use atomic RPC function for true transaction safety\r\n    const { data: result, error } = await adminClient.rpc('update_subscription_atomic', {\r\n      p_subscription_id: params.subscription_id,\r\n      p_new_status: params.subscription_status,\r\n      p_business_profile_id: params.business_profile_id,\r\n      p_has_active_subscription: params.has_active_subscription,\r\n      p_additional_data: params.additional_data || {},\r\n      p_webhook_timestamp: params.additional_data?.last_webhook_timestamp || null\r\n    });\r\n\r\n    if (error) {\r\n      console.error(`[ATOMIC_TRANSACTION] RPC error for ${params.subscription_id}:`, error);\r\n      return {\r\n        success: false,\r\n        message: `RPC error: ${error.message}`\r\n      };\r\n    }\r\n\r\n    if (!result?.success) {\r\n      console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${params.subscription_id}:`, result);\r\n      return {\r\n        success: false,\r\n        message: result?.error || 'Unknown RPC error'\r\n      };\r\n    }\r\n\r\n    console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${params.subscription_id} atomically`);\r\n\r\n    return { success: true, message: 'Atomic transaction completed successfully via RPC' };\r\n  } catch (error) {\r\n    console.error(`[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Atomic transaction exception: ${error instanceof Error ? error.message : String(error)}`\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAcO,eAAe,mBACpB,SAAyB,EACzB,cAAsB,EACtB,MAAkC,EAClC,iBAA0C,CAAC,CAAC;IAE5C,IAAI;QACF,iCAAiC;QACjC,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEpC,2FAA2F;QAC3F,MAAM,EAAE,eAAe,EAAE,GAAG;QAC5B,MAAM,sBAAsB,MAAM,gBAAgB;QAElD,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,oBAAoB,IAAI,EAAE;YAC7D,QAAQ,KAAK,CAAC,CAAC,wEAAwE,EAAE,gBAAgB;YAEzG,qEAAqE;YACrE,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,YAC1D,IAAI,CAAC,yBACL,MAAM,CAAC,4CACP,EAAE,CAAC,4BAA4B,gBAC/B,WAAW;YAEd,IAAI,cAAc,CAAC,mBAAmB;gBACpC,QAAQ,KAAK,CAAC,CAAC,2EAA2E,EAAE,eAAe,CAAC,CAAC,EAAE;gBAC/G,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,wEAAwE,CAAC;gBAAC;YAC/G;YAEA,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,eAAe,0BAA0B,CAAC;YAE9G,6DAA6D;YAC7D,MAAM,0BAA0B;gBAC9B,SAAS;gBACT,MAAM;oBACJ,IAAI;oBACJ,SAAS,GAAG,kBAAkB,OAAO,CAAC,CAAC,EAAE,kBAAkB,UAAU,EAAE;oBACvE,aAAa;oBACb,eAAe;oBACf,aAAa;oBACb,WAAW;oBACX,UAAU;oBACV,OAAO;wBACL,qBAAqB,kBAAkB,mBAAmB;wBAC1D,WAAW,kBAAkB,OAAO;wBACpC,YAAY,kBAAkB,UAAU;oBAC1C;gBACF;YACF;YAEA,iDAAiD;YACjD,MAAM,2BAA2B;YACjC,OAAO,MAAM,0BAA0B,aAAa,gBAAgB,QAAQ,gBAAgB,yBAAyB,IAAI;QAC3H;QAEA,sDAAsD;QACtD,OAAO,MAAM,0BAA0B,aAAa,gBAAgB,QAAQ,gBAAgB,oBAAoB,IAAI;IACtH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mDAAmD,EAAE,eAAe,CAAC,CAAC,EAAE;QACvF,OAAO;YACL,SAAS;YACT,SAAS,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;QACvG;IACF;AACF;AAEA;;;CAGC,GACD,eAAe,0BACb,WAA2B,EAC3B,cAAsB,EACtB,MAAkC,EAClC,cAAuC,EACvC,gBAcC;IAED,IAAI;QACF,yCAAyC;QACzC,MAAM,oBAAoB,iBAAiB,KAAK,EAAE,uBACzB,iBAAiB,KAAK,EAAE;QAEjD,IAAI,CAAC,mBAAmB;YACtB,QAAQ,KAAK,CAAC,CAAC,0EAA0E,EAAE,gBAAgB;YAC3G,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,kDAAkD,CAAC;YAAC;QACzF;QAEA,6CAA6C;QAC7C,IAAI,WAAW,iBAAiB,KAAK,EAAE;QACvC,IAAI,YAAY,iBAAiB,KAAK,EAAE;QAExC,yEAAyE;QACzE,IAAI,CAAC,YAAY,CAAC,WAAW;YAC3B,QAAQ,GAAG,CAAC,CAAC,oFAAoF,EAAE,iBAAiB,OAAO,EAAE;YAE7H,6EAA6E;YAC7E,MAAM,EAAE,uBAAuB,EAAE,GAAG;YACpC,MAAM,cAAc,wBAAwB,iBAAiB,OAAO;YAEpE,IAAI,aAAa;gBACf,WAAW,YAAY,EAAE;gBACzB,6DAA6D;gBAC7D,YAAY,YAAY,eAAe,CAAC,OAAO,KAAK,iBAAiB,OAAO,GAAG,YAAY;gBAC3F,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,SAAS,SAAS,EAAE,UAAU,sCAAsC,CAAC;YAC/H,OAAO;gBACL,iDAAiD;gBACjD,WAAW;gBACX,YAAY;gBACZ,QAAQ,GAAG,CAAC,CAAC,yEAAyE,EAAE,iBAAiB,OAAO,CAAC,6BAA6B,CAAC;YACjJ;QACF;QAEA,uFAAuF;QACvF,MAAM,wBAAwB,2KAAA,CAAA,2BAAwB,CAAC,4BAA4B,CAAC,QAAQ,YAAY;QAExG,kEAAkE;QAClE,MAAM,qBAAqB;YAAE,GAAG,cAAc;QAAC;QAE/C,kEAAkE;QAClE,0EAA0E;QAC1E,IAAI,6BAA6B,oBAAoB;YACnD,QAAQ,GAAG,CAAC,CAAC,qGAAqG,EAAE,QAAQ;YAC5H,OAAO,mBAAmB,uBAAuB;QACnD;QAEA,gEAAgE;QAChE,IAAI,wBAAwB,iBAAiB,aAAa,GACtD,IAAI,KAAK,iBAAiB,aAAa,GAAG,MAAM,WAAW,KAC3D;QAEJ,MAAM,yBAAyB,iBAAiB,WAAW,GACvD,IAAI,KAAK,iBAAiB,WAAW,GAAG,MAAM,WAAW,KACzD;QAEJ,MAAM,yBAAyB,iBAAiB,SAAS,GACrD,IAAI,KAAK,iBAAiB,SAAS,GAAG,MAAM,WAAW,KACvD;QAEJ,iDAAiD;QACjD,MAAM,gBAAgB,WAAW,oIAAA,CAAA,6BAA0B,CAAC,cAAc,IACrD,WAAW,oIAAA,CAAA,6BAA0B,CAAC,OAAO;QAElE,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC,CAAC,uEAAuE,EAAE,eAAe,aAAa,EAAE,OAAO,iDAAiD,CAAC;YAC7K,OAAO;gBAAE,SAAS;gBAAM,SAAS,CAAC,2DAA2D,EAAE,QAAQ;YAAC;QAC1G;QAEA,0EAA0E;QAC1E,+EAA+E;QAC/E,IAAI,WAAW,oIAAA,CAAA,6BAA0B,CAAC,cAAc,EAAE;YACxD,QAAQ,GAAG,CAAC,CAAC,wFAAwF,CAAC;YAEtG,uEAAuE;YACvE,oFAAoF;YACpF,IAAI,iBAAiB,QAAQ,EAAE;gBAC7B,MAAM,UAAU,IAAI,KAAK,iBAAiB,QAAQ,GAAG,MAAM,WAAW;gBACtE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,6BAA6B,CAAC;gBAExF,qDAAqD;gBACrD,wBAAwB;YAC1B;QACF;QAEA,oCAAoC;QACpC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,YACpD,IAAI,CAAC,yBACL,MAAM,CAAC,qDACP,EAAE,CAAC,4BAA4B,gBAC/B,WAAW;QAEd,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,eAAe,CAAC,CAAC,EAAE;YAClF,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,4BAA4B,EAAE,UAAU,OAAO,EAAE;YAAC;QACvF;QAEA,2HAA2H;QAC3H,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,eAAe,kDAAkD,EAAE,mBAAmB;YAEtJ,8DAA8D;YAC9D,MAAM,EAAE,MAAM,qBAAqB,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,YACjE,IAAI,CAAC,yBACL,MAAM,CAAC,qDACP,EAAE,CAAC,uBAAuB;YAE7B,IAAI,eAAe;gBACjB,QAAQ,KAAK,CAAC,CAAC,0EAA0E,EAAE,kBAAkB,CAAC,CAAC,EAAE;gBACjH,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,2CAA2C,EAAE,cAAc,OAAO,EAAE;gBAAC;YAC1G;YAEA,iGAAiG;YACjG,IAAI,yBAAyB,sBAAsB,MAAM,GAAG,GAAG;gBAC7D,QAAQ,GAAG,CAAC,CAAC,4DAA4D,EAAE,kBAAkB,sCAAsC,CAAC;gBAEpI,MAAM,uBAAuB,qBAAqB,CAAC,EAAE;gBAErD,gCAAgC;gBAChC,MAAM,aAAa;oBACjB,0BAA0B;oBAC1B,sBAAsB,iBAAiB,WAAW,IAAI;oBACtD,qBAAqB;oBACrB,SAAS;oBACT,YAAY;oBACZ,yBAAyB;oBACzB,0BAA0B;oBAC1B,0BAA0B;oBAC1B,YAAY,IAAI,OAAO,WAAW;oBAClC,GAAG,kBAAkB;gBACvB;gBAEA,0CAA0C;gBAC1C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,YAClC,IAAI,CAAC,yBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,qBAAqB,EAAE;gBAEnC,IAAI,aAAa;oBACf,QAAQ,KAAK,CAAC,CAAC,wDAAwD,EAAE,qBAAqB,EAAE,CAAC,CAAC,CAAC,EAAE;oBACrG,OAAO;wBAAE,SAAS;wBAAO,SAAS,CAAC,sCAAsC,EAAE,YAAY,OAAO,EAAE;oBAAC;gBACnG;gBAEA,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,qBAAqB,EAAE,CAAC,sBAAsB,EAAE,eAAe,YAAY,EAAE,QAAQ;gBAErJ,gDAAgD;gBAChD,MAAM,oBAAoB,MAAM,sCAAsC;oBACpE,iBAAiB;oBACjB,qBAAqB;oBACrB,qBAAqB;oBACrB,yBAAyB;oBACzB,iBAAiB;gBACnB;gBAEA,IAAI,CAAC,kBAAkB,OAAO,EAAE;oBAC9B,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,eAAe,CAAC,CAAC,EAAE,kBAAkB,OAAO;oBACpH,OAAO;wBAAE,SAAS;wBAAO,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,OAAO,EAAE;oBAAC;gBACvF;gBAEA,OAAO;oBAAE,SAAS;oBAAM,SAAS,CAAC,8DAA8D,EAAE,QAAQ;gBAAC;YAC7G;YAEA,kEAAkE;YAClE,QAAQ,GAAG,CAAC,CAAC,+DAA+D,EAAE,kBAAkB,kBAAkB,CAAC;YAEnH,MAAM,aAAa;gBACjB,qBAAqB;gBACrB,0BAA0B;gBAC1B,sBAAsB,iBAAiB,WAAW,IAAI;gBACtD,qBAAqB;gBACrB,SAAS;gBACT,YAAY;gBACZ,yBAAyB;gBACzB,0BAA0B;gBAC1B,0BAA0B;gBAC1B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;gBAClC,GAAG,kBAAkB;YACvB;YAEA,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,YAC1D,IAAI,CAAC,yBACL,MAAM,CAAC,YACP,MAAM,CAAC,MACP,MAAM;YAET,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,eAAe,CAAC,CAAC,EAAE;gBAC9F,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,oCAAoC,EAAE,YAAY,OAAO,EAAE;gBAAC;YACjG;YAEA,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,gBAAgB;YAEtF,gDAAgD;YAChD,MAAM,oBAAoB,MAAM,sCAAsC;gBACpE,iBAAiB;gBACjB,qBAAqB;gBACrB,qBAAqB;gBACrB,yBAAyB;gBACzB,iBAAiB;YACnB;YAEA,IAAI,CAAC,kBAAkB,OAAO,EAAE;gBAC9B,QAAQ,KAAK,CAAC,CAAC,2DAA2D,EAAE,eAAe,CAAC,CAAC,EAAE,kBAAkB,OAAO;gBACxH,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,OAAO,EAAE;gBAAC;YACvF;YAEA,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,eAAe,aAAa,EAAE,QAAQ;YACjG,OAAO;gBAAE,SAAS;gBAAM,SAAS,CAAC,iCAAiC,EAAE,QAAQ;YAAC;QAChF;QAEA,oCAAoC;QACpC,MAAM,aAAa;YACjB,qBAAqB;YACrB,yBAAyB;YACzB,0BAA0B;YAC1B,0BAA0B;YAC1B,YAAY,IAAI,OAAO,WAAW;YAClC,GAAG,kBAAkB;QACvB;QAEA,2DAA2D;QAC3D,MAAM,oBAAoB,MAAM,sCAAsC;YACpE,iBAAiB;YACjB,qBAAqB;YACrB,qBAAqB;YACrB,yBAAyB;YACzB,iBAAiB;QACnB;QAEA,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC9B,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,eAAe,CAAC,CAAC,EAAE,kBAAkB,OAAO;YACpH,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,OAAO,EAAE;YAAC;QACvF;QAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,aAAa,EAAE,CAAC,aAAa,EAAE,QAAQ;QAC9F,OAAO;YAAE,SAAS;YAAM,SAAS,CAAC,iCAAiC,EAAE,QAAQ;QAAC;IAChF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mDAAmD,EAAE,eAAe,CAAC,CAAC,EAAE;QACvF,OAAO;YACL,SAAS;YACT,SAAS,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;QACvG;IACF;AACF;AAgBO,eAAe,sCACpB,MAAyB;IAEzB,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEpC,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,OAAO,eAAe,EAAE;QAE9F,gEAAgE;QAChE,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,YAAY,GAAG,CAAC,8BAA8B;YAClF,mBAAmB,OAAO,eAAe;YACzC,cAAc,OAAO,mBAAmB;YACxC,uBAAuB,OAAO,mBAAmB;YACjD,2BAA2B,OAAO,uBAAuB;YACzD,mBAAmB,OAAO,eAAe,IAAI,CAAC;YAC9C,qBAAqB,OAAO,eAAe,EAAE,0BAA0B;QACzE;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,eAAe,CAAC,CAAC,CAAC,EAAE;YAC/E,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;YACxC;QACF;QAEA,IAAI,CAAC,QAAQ,SAAS;YACpB,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,OAAO,eAAe,CAAC,CAAC,CAAC,EAAE;YACjG,OAAO;gBACL,SAAS;gBACT,SAAS,QAAQ,SAAS;YAC5B;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,OAAO,eAAe,CAAC,WAAW,CAAC;QAEzG,OAAO;YAAE,SAAS;YAAM,SAAS;QAAoD;IACvF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,wEAAwE,CAAC,EAAE;QAC1F,OAAO;YACL,SAAS;YACT,SAAS,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;QACpG;IACF;AACF", "debugId": null}}, {"offset": {"line": 1285, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/webhook-utils.ts"], "sourcesContent": ["import { SubscriptionStatus, SUBSCRIPTION_STATUS } from \"./subscription-constants\";\r\n\r\n/**\r\n * ENHANCED WEBHOOK TIMESTAMP EXTRACTION\r\n *\r\n * Extracts webhook timestamp from Razorpay payload for sequence validation.\r\n * This function is critical for preventing out-of-order webhook processing.\r\n *\r\n * @param payload The webhook payload from Razorpay\r\n * @returns Unix timestamp in seconds, or current time if not available\r\n */\r\nexport function extractWebhookTimestamp(payload: any): number { // eslint-disable-line @typescript-eslint/no-explicit-any\r\n  try {\r\n    // Priority order for timestamp extraction (most reliable first)\r\n    const timestampSources = [\r\n      // 1. Main event timestamp (most reliable)\r\n      () => payload.created_at,\r\n\r\n      // 2. Subscription entity timestamps\r\n      () => payload.payload?.subscription?.entity?.created_at,\r\n      () => payload.payload?.subscription?.entity?.updated_at,\r\n\r\n      // 3. Payment entity timestamps\r\n      () => payload.payload?.payment?.entity?.created_at,\r\n      () => payload.payload?.payment?.entity?.updated_at,\r\n\r\n      // 4. Invoice entity timestamps\r\n      () => payload.payload?.invoice?.entity?.created_at,\r\n      () => payload.payload?.invoice?.entity?.updated_at,\r\n\r\n      // 5. Generic entity timestamps\r\n      () => payload.payload?.entity?.created_at,\r\n      () => payload.payload?.entity?.updated_at,\r\n\r\n      // 6. Event-specific timestamps\r\n      () => payload.event_timestamp,\r\n      () => payload.timestamp\r\n    ];\r\n\r\n    // Try each timestamp source in order\r\n    for (const getTimestamp of timestampSources) {\r\n      try {\r\n        const timestamp = getTimestamp();\r\n        if (timestamp && typeof timestamp === 'number' && timestamp > 0) {\r\n          // Validate timestamp is reasonable (not too far in past/future)\r\n          const now = Math.floor(Date.now() / 1000);\r\n          const maxAge = 24 * 60 * 60; // 24 hours\r\n          const maxFuture = 5 * 60; // 5 minutes\r\n\r\n          if (timestamp >= (now - maxAge) && timestamp <= (now + maxFuture)) {\r\n            return timestamp;\r\n          } else {\r\n            console.warn(`[WEBHOOK_TIMESTAMP] Timestamp ${timestamp} outside reasonable range, trying next source`);\r\n          }\r\n        }\r\n      } catch (_sourceError) {\r\n        // Continue to next source if this one fails\r\n        continue;\r\n      }\r\n    }\r\n\r\n    // Fallback to current time with warning\r\n    console.warn('[WEBHOOK_TIMESTAMP] Could not extract valid timestamp from payload, using current time');\r\n    console.warn('[WEBHOOK_TIMESTAMP] Payload structure:', JSON.stringify(payload, null, 2));\r\n    return Math.floor(Date.now() / 1000);\r\n\r\n  } catch (error) {\r\n    console.error('[WEBHOOK_TIMESTAMP] Error extracting timestamp from payload:', error);\r\n    return Math.floor(Date.now() / 1000);\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the appropriate subscription status for a user based on their current state.\r\n * This is used to determine the correct status when creating or updating subscriptions.\r\n *\r\n * @param hasActiveSubscription Current has_active_subscription flag (should be false for trial/free)\r\n * @param trialEndDate Trial end date if any\r\n * @param planId Current plan ID\r\n * @returns The appropriate subscription status\r\n */\r\nexport function getAppropriateSubscriptionStatus(\r\n  hasActiveSubscription: boolean,\r\n  trialEndDate: string | null,\r\n  planId: string\r\n): SubscriptionStatus {\r\n  // If on free plan, always active status but has_active_subscription should be false\r\n  if (planId === 'free') {\r\n    return SUBSCRIPTION_STATUS.ACTIVE;\r\n  }\r\n\r\n  // Check if user is in trial period\r\n  if (trialEndDate) {\r\n    const trialEnd = new Date(trialEndDate);\r\n    const now = new Date();\r\n\r\n    if (trialEnd > now) {\r\n      // User is in trial period - status is trial, has_active_subscription should be false\r\n      return SUBSCRIPTION_STATUS.TRIAL;\r\n    }\r\n  }\r\n\r\n  // If has active subscription flag, it means they have a paid subscription\r\n  if (hasActiveSubscription) {\r\n    return SUBSCRIPTION_STATUS.ACTIVE;\r\n  }\r\n\r\n  // Default to pending if no active subscription and not in trial\r\n  return SUBSCRIPTION_STATUS.PENDING;\r\n}"], "names": [], "mappings": ";;;;AAAA;;AAWO,SAAS,wBAAwB,OAAY;IAClD,IAAI;QACF,gEAAgE;QAChE,MAAM,mBAAmB;YACvB,0CAA0C;YAC1C,IAAM,QAAQ,UAAU;YAExB,oCAAoC;YACpC,IAAM,QAAQ,OAAO,EAAE,cAAc,QAAQ;YAC7C,IAAM,QAAQ,OAAO,EAAE,cAAc,QAAQ;YAE7C,+BAA+B;YAC/B,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YACxC,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YAExC,+BAA+B;YAC/B,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YACxC,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YAExC,+BAA+B;YAC/B,IAAM,QAAQ,OAAO,EAAE,QAAQ;YAC/B,IAAM,QAAQ,OAAO,EAAE,QAAQ;YAE/B,+BAA+B;YAC/B,IAAM,QAAQ,eAAe;YAC7B,IAAM,QAAQ,SAAS;SACxB;QAED,qCAAqC;QACrC,KAAK,MAAM,gBAAgB,iBAAkB;YAC3C,IAAI;gBACF,MAAM,YAAY;gBAClB,IAAI,aAAa,OAAO,cAAc,YAAY,YAAY,GAAG;oBAC/D,gEAAgE;oBAChE,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;oBACpC,MAAM,SAAS,KAAK,KAAK,IAAI,WAAW;oBACxC,MAAM,YAAY,IAAI,IAAI,YAAY;oBAEtC,IAAI,aAAc,MAAM,UAAW,aAAc,MAAM,WAAY;wBACjE,OAAO;oBACT,OAAO;wBACL,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,UAAU,6CAA6C,CAAC;oBACxG;gBACF;YACF,EAAE,OAAO,cAAc;gBAErB;YACF;QACF;QAEA,wCAAwC;QACxC,QAAQ,IAAI,CAAC;QACb,QAAQ,IAAI,CAAC,0CAA0C,KAAK,SAAS,CAAC,SAAS,MAAM;QACrF,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IAEjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gEAAgE;QAC9E,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACjC;AACF;AAWO,SAAS,iCACd,qBAA8B,EAC9B,YAA2B,EAC3B,MAAc;IAEd,oFAAoF;IACpF,IAAI,WAAW,QAAQ;QACrB,OAAO,oKAAA,CAAA,sBAAmB,CAAC,MAAM;IACnC;IAEA,mCAAmC;IACnC,IAAI,cAAc;QAChB,MAAM,WAAW,IAAI,KAAK;QAC1B,MAAM,MAAM,IAAI;QAEhB,IAAI,WAAW,KAAK;YAClB,qFAAqF;YACrF,OAAO,oKAAA,CAAA,sBAAmB,CAAC,KAAK;QAClC;IACF;IAEA,0EAA0E;IAC1E,IAAI,uBAAuB;QACzB,OAAO,oKAAA,CAAA,sBAAmB,CAAC,MAAM;IACnC;IAEA,gEAAgE;IAChE,OAAO,oKAAA,CAAA,sBAAmB,CAAC,OAAO;AACpC", "debugId": null}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/utils.ts"], "sourcesContent": ["export * from './subscription-constants';\r\nexport * from './subscription-state-manager';\r\nexport * from './subscription-state-validator';\r\nexport * from './subscription-db-updater';\r\nexport * from './webhook-utils';"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/AnimatedSubscriptionStatus.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\n// Alert components not used in this file\r\nimport { ArrowRight, Crown, ShieldCheck } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { PricingPlan } from \"@/lib/PricingPlans\";\r\nimport FlipTimer from \"./FlipTimer\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { SUBSCRIPTION_STATUS } from \"@/lib/razorpay/webhooks/handlers/utils\";\r\n\r\ninterface AnimatedSubscriptionStatusProps {\r\n  subscriptionStatus: \"active\" | \"trial\" | \"inactive\";\r\n  planDetails?: PricingPlan;\r\n  trialEndDate?: string | null;\r\n  planCycle?: string | null;\r\n  subscription?: {\r\n    subscription_status?: string | null;\r\n    plan_id?: string | null;\r\n    plan_cycle?: string | null;\r\n  } | null;\r\n}\r\n\r\nexport default function AnimatedSubscriptionStatus({\r\n  subscriptionStatus,\r\n  planDetails,\r\n  trialEndDate,\r\n  planCycle,\r\n  subscription,\r\n}: AnimatedSubscriptionStatusProps) {\r\n\r\n  // Get plan icon based on plan ID - used in badge rendering\r\n  const getPlanIcon = () => {\r\n    if (!planDetails) return <ShieldCheck className=\"w-3.5 h-3.5 mr-1\" />;\r\n\r\n    switch (planDetails.id) {\r\n      case \"premium\":\r\n      case \"enterprise\":\r\n        return <Crown className=\"w-3.5 h-3.5 mr-1\" />;\r\n      case \"pro\":\r\n        return <ShieldCheck className=\"w-3.5 h-3.5 mr-1\" />;\r\n      case \"growth\":\r\n        return <ShieldCheck className=\"w-3.5 h-3.5 mr-1\" />;\r\n      case \"basic\":\r\n        return <ShieldCheck className=\"w-3.5 h-3.5 mr-1\" />;\r\n      case \"free\":\r\n        return <ShieldCheck className=\"w-3.5 h-3.5 mr-1\" />;\r\n      default:\r\n        return <ShieldCheck className=\"w-3.5 h-3.5 mr-1\" />;\r\n    }\r\n  };\r\n\r\n  // Get badge styles based on plan ID\r\n  const getPlanBadgeStyles = () => {\r\n    if (!planDetails) return \"\";\r\n\r\n    switch (planDetails.id) {\r\n      case \"premium\":\r\n      case \"enterprise\":\r\n        return \"border-[var(--brand-gold)]/50 text-[var(--brand-gold)]\";\r\n      case \"pro\":\r\n        return \"border-blue-500/50 text-blue-500 dark:border-blue-400/50 dark:text-blue-400\";\r\n      case \"growth\":\r\n        return \"border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400\";\r\n      case \"basic\":\r\n        return \"border-purple-500/50 text-purple-500 dark:border-purple-400/50 dark:text-purple-400\";\r\n      case \"free\":\r\n        return \"border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400\";\r\n      default:\r\n        return \"border-green-500/50 text-green-500 dark:border-green-400/50 dark:text-green-400\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full\">\r\n      <div\r\n        className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-2 xs:p-3 sm:p-4 md:p-5 transition-all duration-300 hover:shadow-lg overflow-hidden h-full flex flex-col relative\"\r\n      >\r\n\r\n        <div className=\"relative z-10 flex flex-col h-full\">\r\n          <div className=\"flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <div className=\"p-1.5 sm:p-2 rounded-lg bg-primary/10 text-primary\">\r\n                <ShieldCheck className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n              </div>\r\n              <h3 className=\"text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate\">\r\n                Subscription Status\r\n              </h3>\r\n            </div>\r\n            {subscriptionStatus === \"active\" && (\r\n              <div className=\"w-3 h-3 rounded-full bg-green-500 animate-pulse\" />\r\n            )}\r\n            {subscriptionStatus === \"trial\" && (\r\n              <div className=\"w-3 h-3 rounded-full bg-amber-500 animate-pulse\" />\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"flex-1 flex flex-col justify-center\">\r\n            {subscriptionStatus === \"active\" && planDetails && (\r\n              <div className=\"flex flex-col items-center justify-center text-center\">\r\n                <div>\r\n                  <Badge\r\n                    variant=\"outline\"\r\n                    className={cn(\"border px-2 py-1 mb-2\", getPlanBadgeStyles())}\r\n                  >\r\n                    {getPlanIcon()}\r\n                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? \"Paused\" : \"Active\"}\r\n                  </Badge>\r\n                </div>\r\n\r\n                <h3 className=\"text-base sm:text-lg font-medium mb-1\">\r\n                  {planDetails.name}\r\n                </h3>\r\n                {/* Only show plan cycle for paid plans, not for free plan */}\r\n                {planDetails.id !== \"free\" && (\r\n                  <p className=\"text-xs sm:text-sm font-medium text-primary mb-1\">\r\n                    {planCycle === \"yearly\" ? \"Yearly Plan\" : \"Monthly Plan\"}\r\n                  </p>\r\n                )}\r\n\r\n                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? (\r\n                  <p className=\"text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3\">\r\n                    Your subscription is currently paused. Your business card is offline until you resume your subscription.\r\n                  </p>\r\n                ) : (\r\n                  <p className=\"text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3\">\r\n                    Your subscription is active and all features are enabled\r\n                  </p>\r\n                )}\r\n\r\n                <div className=\"mt-2 w-full px-2 mb-2\">\r\n                  <Button asChild size=\"sm\" variant=\"outline\" className=\"w-full sm:w-auto\">\r\n                    <Link href=\"/dashboard/business/plan\" className=\"flex items-center justify-center gap-1\">\r\n                      <span className=\"whitespace-nowrap\">Manage Plan</span> <ArrowRight className=\"w-3.5 h-3.5 flex-shrink-0\" />\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {subscriptionStatus === \"trial\" && trialEndDate && (\r\n              <div className=\"flex flex-col items-center justify-center text-center\">\r\n                <div>\r\n                  <Badge\r\n                    variant=\"outline\"\r\n                    className=\"border border-[var(--brand-gold)]/50 text-[var(--brand-gold)] px-2 py-1 mb-2\"\r\n                  >\r\n                    <Crown className=\"w-3.5 h-3.5 mr-1\" />\r\n                    Trial\r\n                  </Badge>\r\n                </div>\r\n\r\n                <h3 className=\"text-base sm:text-lg font-medium mb-1\">\r\n                  {planDetails ? planDetails.name : \"Premium\"} Trial\r\n                </h3>\r\n                {/* Only show plan cycle for paid plans, not for free plan */}\r\n                {planDetails && planDetails.id !== \"free\" && (\r\n                  <p className=\"text-xs sm:text-sm font-medium text-[var(--brand-gold)] mb-2\">\r\n                    {planCycle === \"yearly\" ? \"Yearly Plan\" : \"Monthly Plan\"}\r\n                  </p>\r\n                )}\r\n\r\n                <div className=\"w-full overflow-x-auto px-1\">\r\n                  <FlipTimer endDate={trialEndDate} label=\"\" tooltipText=\"Your trial will expire soon. Choose a plan to continue using all features.\" />\r\n                </div>\r\n\r\n                <div className=\"mt-4 w-full px-2 mb-2\">\r\n                  <div className=\"relative group\">\r\n                    {/* Strong inner glow effect that fills only the button */}\r\n                    <div className=\"absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none\" />\r\n\r\n                    {/* Border glow effect - matches button size */}\r\n                    <div\r\n                      className=\"absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300\"\r\n                      style={{\r\n                        boxShadow: `inset 0 0 20px rgba(194, 157, 91, 0.2), 0 0 20px rgba(194, 157, 91, 0.3)`\r\n                      }}\r\n                    />\r\n\r\n                    {/* Strong decorative colored glow elements - positioned relative to button */}\r\n                    <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none\" />\r\n                    <div className=\"absolute -bottom-1 -left-1 w-4 h-4 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none\" />\r\n\r\n                    <Button\r\n                      asChild\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"gap-1 w-full sm:w-auto border-amber-200/50 dark:border-amber-700/50 shadow-amber-500/40 shadow-lg hover:shadow-xl hover:shadow-amber-500/40 transition-all duration-300 text-amber-500 dark:text-amber-400 hover:bg-amber-500/5 dark:hover:bg-amber-500/10 rounded-xl\"\r\n                    >\r\n                      <Link href=\"/dashboard/business/plan\" className=\"flex items-center justify-center relative\">\r\n                        <span className=\"whitespace-nowrap\">Choose a Plan</span> <ArrowRight className=\"w-3.5 h-3.5 ml-1 flex-shrink-0\" />\r\n\r\n                        {/* Shimmer effect - only on hover */}\r\n                        <div className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300\" />\r\n                      </Link>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n\r\n              </div>\r\n            )}\r\n\r\n            {subscriptionStatus === \"inactive\" && (\r\n              <div className=\"flex flex-col items-center justify-center text-center\">\r\n                <div>\r\n                  <Badge\r\n                    variant=\"outline\"\r\n                    className={cn(\"border px-2 py-1 mb-2\",\r\n                      subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails\r\n                        ? getPlanBadgeStyles()\r\n                        : \"border-neutral-500/50 text-neutral-500 dark:border-neutral-400/50 dark:text-neutral-400\"\r\n                    )}\r\n                  >\r\n                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails\r\n                      ? getPlanIcon()\r\n                      : <ShieldCheck className=\"w-3.5 h-3.5 mr-1\" />\r\n                    }\r\n                    {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? \"Paused\" : \"Free Plan\"}\r\n                  </Badge>\r\n                </div>\r\n\r\n                <h3 className=\"text-base sm:text-lg font-medium mb-1\">\r\n                  {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails\r\n                    ? planDetails.name\r\n                    : \"Free Plan\"\r\n                  }\r\n                </h3>\r\n\r\n                {/* Only show plan cycle for paid plans with halted subscription */}\r\n                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED && planDetails && planDetails.id !== \"free\" && (\r\n                  <p className=\"text-xs sm:text-sm font-medium text-primary mb-1\">\r\n                    {planCycle === \"yearly\" ? \"Yearly Plan\" : \"Monthly Plan\"}\r\n                  </p>\r\n                )}\r\n\r\n                {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? (\r\n                  <p className=\"text-xs sm:text-sm text-amber-600 dark:text-amber-400 max-w-[250px] mx-auto px-2 mb-3\">\r\n                    Your subscription is currently paused. Your business card is offline until you resume your subscription.\r\n                  </p>\r\n                ) : (\r\n                  <p className=\"text-xs sm:text-sm text-muted-foreground max-w-[250px] mx-auto px-2 mb-3\">\r\n                    You are on the free plan with limited features. Upgrade to access more features.\r\n                  </p>\r\n                )}\r\n\r\n                <div className=\"mt-2 w-full px-2 mb-2\">\r\n                  <div className=\"relative group\">\r\n                    {/* Strong inner glow effect that fills only the button */}\r\n                    <div className=\"absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none\" />\r\n\r\n                    {/* Border glow effect - matches button size */}\r\n                    <div\r\n                      className=\"absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300\"\r\n                      style={{\r\n                        boxShadow: `inset 0 0 20px rgba(194, 157, 91, 0.2), 0 0 20px rgba(194, 157, 91, 0.3)`\r\n                      }}\r\n                    />\r\n\r\n                    {/* Strong decorative colored glow elements - positioned relative to button */}\r\n                    <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none\" />\r\n                    <div className=\"absolute -bottom-1 -left-1 w-4 h-4 bg-amber-500/30 rounded-full shadow-amber-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none\" />\r\n\r\n                    <Button\r\n                      asChild\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"w-full sm:w-auto border-amber-200/50 dark:border-amber-700/50 shadow-amber-500/40 shadow-lg hover:shadow-xl hover:shadow-amber-500/40 transition-all duration-300 text-amber-500 dark:text-amber-400 hover:bg-amber-500/5 dark:hover:bg-amber-500/10 rounded-xl\"\r\n                    >\r\n                      <Link href=\"/dashboard/business/plan\" className=\"flex items-center justify-center gap-1 relative\">\r\n                        <span className=\"whitespace-nowrap\">\r\n                          {subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED ? \"Manage Plan\" : \"Upgrade Plan\"}\r\n                        </span>\r\n                        <ArrowRight className=\"w-3.5 h-3.5 flex-shrink-0\" />\r\n\r\n                        {/* Shimmer effect - only on hover */}\r\n                        <div className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300\" />\r\n                      </Link>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,yCAAyC;AACzC;AAAA;AAAA;AACA;AAEA;AACA;AACA;AAAA;AAVA;;;;;;;;;AAwBe,SAAS,2BAA2B,EACjD,kBAAkB,EAClB,WAAW,EACX,YAAY,EACZ,SAAS,EACT,YAAY,EACoB;IAEhC,2DAA2D;IAC3D,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,qBAAO,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAEhD,OAAQ,YAAY,EAAE;YACpB,KAAK;YACL,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,oCAAoC;IACpC,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,OAAO;QAEzB,OAAQ,YAAY,EAAE;YACpB,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAU;sBAGV,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCAAG,WAAU;kDAAqF;;;;;;;;;;;;4BAIpG,uBAAuB,0BACtB,8OAAC;gCAAI,WAAU;;;;;;4BAEhB,uBAAuB,yBACtB,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAInB,8OAAC;wBAAI,WAAU;;4BACZ,uBAAuB,YAAY,6BAClC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDACC,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;;gDAEtC;gDACA,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,GAAG,WAAW;;;;;;;;;;;;kDAInF,8OAAC;wCAAG,WAAU;kDACX,YAAY,IAAI;;;;;;oCAGlB,YAAY,EAAE,KAAK,wBAClB,8OAAC;wCAAE,WAAU;kDACV,cAAc,WAAW,gBAAgB;;;;;;oCAI7C,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,iBAC/D,8OAAC;wCAAE,WAAU;kDAAwF;;;;;6DAIrG,8OAAC;wCAAE,WAAU;kDAA2E;;;;;;kDAK1F,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,MAAK;4CAAK,SAAQ;4CAAU,WAAU;sDACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAA2B,WAAU;;kEAC9C,8OAAC;wDAAK,WAAU;kEAAoB;;;;;;oDAAkB;kEAAC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOtF,uBAAuB,WAAW,8BACjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDACC,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;;8DAEV,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqB;;;;;;;;;;;;kDAK1C,8OAAC;wCAAG,WAAU;;4CACX,cAAc,YAAY,IAAI,GAAG;4CAAU;;;;;;;oCAG7C,eAAe,YAAY,EAAE,KAAK,wBACjC,8OAAC;wCAAE,WAAU;kDACV,cAAc,WAAW,gBAAgB;;;;;;kDAI9C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,yKAAA,CAAA,UAAS;4CAAC,SAAS;4CAAc,OAAM;4CAAG,aAAY;;;;;;;;;;;kDAGzD,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,WAAW,CAAC,wEAAwE,CAAC;oDACvF;;;;;;8DAIF,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DAEf,8OAAC,2HAAA,CAAA,SAAM;oDACL,OAAO;oDACP,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA2B,WAAU;;0EAC9C,8OAAC;gEAAK,WAAU;0EAAoB;;;;;;4DAAoB;0EAAC,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EAG/E,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAU1B,uBAAuB,4BACtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDACC,cAAA,8OAAC,0HAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBACZ,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,IAAI,cAChE,uBACA;;gDAGL,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,IAAI,cACjE,8BACA,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;gDAE1B,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,GAAG,WAAW;;;;;;;;;;;;kDAInF,8OAAC;wCAAG,WAAU;kDACX,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,IAAI,cACjE,YAAY,IAAI,GAChB;;;;;;oCAKL,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,IAAI,eAAe,YAAY,EAAE,KAAK,wBACrG,8OAAC;wCAAE,WAAU;kDACV,cAAc,WAAW,gBAAgB;;;;;;oCAI7C,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,iBAC/D,8OAAC;wCAAE,WAAU;kDAAwF;;;;;6DAIrG,8OAAC;wCAAE,WAAU;kDAA2E;;;;;;kDAK1F,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;;;;;8DAGf,8OAAC;oDACC,WAAU;oDACV,OAAO;wDACL,WAAW,CAAC,wEAAwE,CAAC;oDACvF;;;;;;8DAIF,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DAEf,8OAAC,2HAAA,CAAA,SAAM;oDACL,OAAO;oDACP,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAA2B,WAAU;;0EAC9C,8OAAC;gEAAK,WAAU;0EACb,cAAc,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,MAAM,GAAG,gBAAgB;;;;;;0EAEtF,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EAGtB,8OAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvC", "debugId": null}}, {"offset": {"line": 2008, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/EnhancedQuickActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON>ard, Package, Heart, Bell } from \"lucide-react\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport Link from \"next/link\";\r\n\r\ninterface EnhancedQuickActionsProps {\r\n  userPlan: string | null;\r\n}\r\n\r\nexport default function EnhancedQuickActions({ userPlan: _userPlan }: EnhancedQuickActionsProps) {\r\n  return (\r\n    <div\r\n      className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-5 transition-all duration-300 hover:shadow-lg h-full relative overflow-hidden\"\r\n    >\r\n\r\n      <div className=\"relative z-10\">\r\n        <div className=\"flex items-center justify-between gap-2 mb-4 pb-3 border-b border-neutral-100 dark:border-neutral-800\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"p-1.5 sm:p-2 rounded-lg bg-primary/10 text-primary\">\r\n              <CreditCard className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n            </div>\r\n            <h3 className=\"text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate\">\r\n              Quick Actions\r\n            </h3>\r\n          </div>\r\n          <div className=\"w-2 h-2 rounded-full bg-green-500 animate-pulse\" />\r\n        </div>\r\n\r\n        <div className=\"flex flex-col gap-2 sm:gap-3 overflow-hidden\">\r\n          <div className=\"relative group\">\r\n            {/* Inner glow effect only */}\r\n            <div className=\"absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none\" />\r\n\r\n            <Button\r\n              asChild\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden\"\r\n            >\r\n              <Link href=\"/dashboard/business/card\">\r\n                <CreditCard className=\"w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300\" />\r\n                Edit Business Card\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"relative group\">\r\n            {/* Inner glow effect only */}\r\n            <div className=\"absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none\" />\r\n\r\n            <Button\r\n              asChild\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden\"\r\n            >\r\n              <Link href=\"/dashboard/business/products\">\r\n                <Package className=\"w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300\" />\r\n                Manage Products\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n\r\n\r\n\r\n          <div className=\"relative group\">\r\n            {/* Inner glow effect only */}\r\n            <div className=\"absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none\" />\r\n\r\n            <Button\r\n              asChild\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden\"\r\n            >\r\n              <Link href=\"/dashboard/business/analytics\">\r\n                <BarChart3 className=\"w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300\" />\r\n                View Analytics\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"relative group\">\r\n            {/* Inner glow effect only */}\r\n            <div className=\"absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none\" />\r\n\r\n            <Button\r\n              asChild\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden\"\r\n            >\r\n              <Link href=\"/dashboard/business/likes\">\r\n                <Heart className=\"w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300\" />\r\n                My Likes\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"relative group\">\r\n            {/* Inner glow effect only */}\r\n            <div className=\"absolute inset-0 bg-amber-500/5 dark:bg-amber-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-lg pointer-events-none\" />\r\n\r\n            <Button\r\n              asChild\r\n              variant=\"outline\"\r\n              className=\"w-full justify-start border-border/50 hover:border-[var(--brand-gold)]/50 hover:text-[var(--brand-gold)] group cursor-pointer text-xs sm:text-sm py-1.5 sm:py-2 px-2 sm:px-3 h-auto min-h-9 relative overflow-hidden\"\r\n            >\r\n              <Link href=\"/dashboard/business/subscriptions\">\r\n                <Bell className=\"w-4 h-4 mr-2 group-hover:text-[var(--brand-gold)] transition-colors duration-300\" />\r\n                My Subscriptions\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAUe,SAAS,qBAAqB,EAAE,UAAU,SAAS,EAA6B;IAC7F,qBACE,8OAAC;QACC,WAAU;kBAGV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAG,WAAU;8CAAqF;;;;;;;;;;;;sCAIrG,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,2HAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAqF;;;;;;;;;;;;;;;;;;sCAMjH,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,2HAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAqF;;;;;;;;;;;;;;;;;;sCAQ9G,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,2HAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAqF;;;;;;;;;;;;;;;;;;sCAMhH,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,2HAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAqF;;;;;;;;;;;;;;;;;;sCAM5G,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,2HAAA,CAAA,SAAM;oCACL,OAAO;oCACP,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;;0DACT,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAqF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrH", "debugId": null}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/activities.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n/**\r\n * Database Triggers Documentation\r\n *\r\n * The following triggers are set up in Supabase to automatically track activities:\r\n *\r\n * 1. add_like_activity() - Trigger function for likes\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'like',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is added\r\n * CREATE TRIGGER trigger_add_like_activity\r\n * AFTER INSERT ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_like_activity();\r\n * ```\r\n *\r\n * 1a. delete_like_activity() - Trigger function for removing like activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'like';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is deleted\r\n * CREATE TRIGGER trigger_delete_like_activity\r\n * AFTER DELETE ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_like_activity();\r\n * ```\r\n *\r\n * 2. add_subscription_activity() - Trigger function for subscriptions\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'subscribe',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is added\r\n * CREATE TRIGGER trigger_add_subscription_activity\r\n * AFTER INSERT ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_subscription_activity();\r\n * ```\r\n *\r\n * 2a. delete_subscription_activity() - Trigger function for removing subscription activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'subscribe';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is deleted\r\n * CREATE TRIGGER trigger_delete_subscription_activity\r\n * AFTER DELETE ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_subscription_activity();\r\n * ```\r\n *\r\n * 3. add_rating_activity() - Trigger function for ratings\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Check if this is an update or insert\r\n *   IF TG_OP = 'UPDATE' THEN\r\n *     -- For updates, only add activity if rating changed\r\n *     IF NEW.rating = OLD.rating THEN\r\n *       RETURN NEW;\r\n *     END IF;\r\n *   END IF;\r\n *\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     rating_value,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'rating',\r\n *     NEW.rating,\r\n *     NEW.updated_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is added or updated\r\n * CREATE TRIGGER trigger_add_rating_activity\r\n * AFTER INSERT OR UPDATE OF rating ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_rating_activity();\r\n * ```\r\n *\r\n * 3a. delete_rating_activity() - Trigger function for removing rating activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'rating';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is deleted\r\n * CREATE TRIGGER trigger_delete_rating_activity\r\n * AFTER DELETE ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_rating_activity();\r\n * ```\r\n */\r\n\r\n/**\r\n * Table Structure\r\n *\r\n * The business_activities table is structured as follows:\r\n * ```sql\r\n * CREATE TABLE business_activities (\r\n *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\r\n *   business_profile_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,\r\n *   user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\r\n *   activity_type TEXT NOT NULL CHECK (activity_type IN ('like', 'subscribe', 'rating')),\r\n *   rating_value INTEGER,\r\n *   created_at TIMESTAMPTZ NOT NULL DEFAULT now(),\r\n *   is_read BOOLEAN NOT NULL DEFAULT false,\r\n *\r\n *   -- Add constraint to ensure rating_value is only set for rating activities\r\n *   CONSTRAINT rating_value_only_for_ratings CHECK (\r\n *     (activity_type = 'rating' AND rating_value IS NOT NULL) OR\r\n *     (activity_type != 'rating' AND rating_value IS NULL)\r\n *   )\r\n * );\r\n *\r\n * -- Indexes for better performance\r\n * CREATE INDEX idx_business_activities_business_profile_id ON business_activities(business_profile_id);\r\n * CREATE INDEX idx_business_activities_user_id ON business_activities(user_id);\r\n * CREATE INDEX idx_business_activities_is_read ON business_activities(is_read);\r\n * CREATE INDEX idx_business_activities_activity_type ON business_activities(activity_type);\r\n * CREATE INDEX idx_business_activities_created_at ON business_activities(created_at);\r\n * ```\r\n */\r\n\r\n/**\r\n * Row Level Security (RLS) Policies\r\n *\r\n * The following RLS policies are set up in Supabase to secure the business_activities table:\r\n *\r\n * 1. Select Policy - Allows business owners to read their own activities\r\n * ```sql\r\n * CREATE POLICY business_activities_select_policy ON business_activities\r\n *   FOR SELECT\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n *\r\n * 2. Update Policy - Allows business owners to update their own activities (for marking as read)\r\n * ```sql\r\n * CREATE POLICY business_activities_update_policy ON business_activities\r\n *   FOR UPDATE\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n */\r\n\r\n// Define types for activities\r\nexport interface BusinessActivity {\r\n  id: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  activity_type: \"like\" | \"subscribe\" | \"rating\";\r\n  rating_value: number | null;\r\n  created_at: string;\r\n  is_read: boolean;\r\n  user_profile?: {\r\n    name?: string | null;\r\n    avatar_url?: string | null;\r\n    email?: string | null;\r\n    is_business?: boolean;\r\n    business_name?: string | null;\r\n    business_slug?: string | null;\r\n    logo_url?: string | null;\r\n  };\r\n}\r\n\r\nexport type ActivitySortBy = \"newest\" | \"oldest\" | \"unread_first\";\r\n\r\n/**\r\n * Fetches activities for a business with pagination and sorting\r\n * Optionally marks fetched activities as read automatically\r\n */\r\nexport async function getBusinessActivities({\r\n  businessProfileId,\r\n  page = 1,\r\n  pageSize = 15,\r\n  sortBy = \"newest\",\r\n  filterBy = \"all\",\r\n  autoMarkAsRead = true, // New parameter to control auto-marking as read\r\n}: {\r\n  businessProfileId: string;\r\n  page?: number;\r\n  pageSize?: number;\r\n  sortBy?: ActivitySortBy;\r\n  filterBy?: \"all\" | \"like\" | \"subscribe\" | \"rating\" | \"unread\";\r\n  autoMarkAsRead?: boolean;\r\n}) {\r\n  const supabase = await createClient();\r\n  const supabaseAdmin = createAdminClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { activities: [], count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { activities: [], count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // Calculate pagination\r\n    const from = (page - 1) * pageSize;\r\n    const to = from + pageSize - 1;\r\n\r\n    // Build the query\r\n    let query = supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(\"business_profile_id\", businessProfileId);\r\n\r\n    // Apply filter\r\n    if (filterBy === \"like\") {\r\n      query = query.eq(\"activity_type\", \"like\");\r\n    } else if (filterBy === \"subscribe\") {\r\n      query = query.eq(\"activity_type\", \"subscribe\");\r\n    } else if (filterBy === \"rating\") {\r\n      query = query.eq(\"activity_type\", \"rating\");\r\n    } else if (filterBy === \"unread\") {\r\n      query = query.eq(\"is_read\", false);\r\n    }\r\n\r\n    // Apply sorting\r\n    switch (sortBy) {\r\n      case \"oldest\":\r\n        query = query.order(\"created_at\", { ascending: true });\r\n        break;\r\n      case \"unread_first\":\r\n        query = query.order(\"is_read\", { ascending: true }).order(\"created_at\", { ascending: false });\r\n        break;\r\n      case \"newest\":\r\n      default:\r\n        query = query.order(\"created_at\", { ascending: false });\r\n        break;\r\n    }\r\n\r\n    // Apply pagination\r\n    query = query.range(from, to);\r\n\r\n    // Execute the query\r\n    const { data: activities, error, count } = await query;\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching business activities:\", error);\r\n      return { activities: [], count: 0, error: error.message };\r\n    }\r\n\r\n    // Get user profiles for the activities\r\n    const userIds = activities.map((activity) => activity.user_id);\r\n\r\n    // Fetch both customer and business profiles\r\n    const [customerProfiles, businessProfiles] = await Promise.all([\r\n      supabaseAdmin\r\n        .from(\"customer_profiles\")\r\n        .select(\"id, name, avatar_url, email\")\r\n        .in(\"id\", userIds),\r\n      supabaseAdmin\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_name, business_slug, logo_url\")\r\n        .in(\"id\", userIds),\r\n    ]);\r\n\r\n    // Combine the profiles\r\n    const userProfiles = new Map();\r\n\r\n    // Add customer profiles to the map\r\n    customerProfiles.data?.forEach((profile) => {\r\n      userProfiles.set(profile.id, {\r\n        name: profile.name,\r\n        avatar_url: profile.avatar_url,\r\n        email: profile.email,\r\n        is_business: false,\r\n      });\r\n    });\r\n\r\n    // Add business profiles to the map, overriding customer profiles if both exist\r\n    businessProfiles.data?.forEach((profile) => {\r\n      const existingProfile = userProfiles.get(profile.id) || {};\r\n      userProfiles.set(profile.id, {\r\n        ...existingProfile,\r\n        business_name: profile.business_name,\r\n        business_slug: profile.business_slug,\r\n        logo_url: profile.logo_url,\r\n        is_business: true,\r\n      });\r\n    });\r\n\r\n    // Attach user profiles to activities\r\n    const activitiesWithProfiles = activities.map((activity) => ({\r\n      ...activity,\r\n      user_profile: userProfiles.get(activity.user_id) || {},\r\n    }));\r\n\r\n    // Auto-mark fetched activities as read if enabled\r\n    if (autoMarkAsRead && activities.length > 0) {\r\n      // Get IDs of unread activities\r\n      const unreadActivityIds = activities\r\n        .filter(activity => !activity.is_read)\r\n        .map(activity => activity.id);\r\n\r\n      // Only proceed if there are unread activities\r\n      if (unreadActivityIds.length > 0) {\r\n        // Mark these activities as read\r\n        const { error: markError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", unreadActivityIds);\r\n\r\n        if (markError) {\r\n          console.error(\"Error auto-marking activities as read:\", markError);\r\n        } else {\r\n          // Update the activities in our result to reflect they're now read\r\n          activitiesWithProfiles.forEach(activity => {\r\n            if (unreadActivityIds.includes(activity.id)) {\r\n              activity.is_read = true;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      activities: activitiesWithProfiles,\r\n      count: count || 0,\r\n      error: null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching business activities:\", error);\r\n    return {\r\n      activities: [],\r\n      count: 0,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Marks activities as read\r\n * Handles pagination for large numbers of activities to work around Supabase's 1000 row limit\r\n */\r\nexport async function markActivitiesAsRead({\r\n  businessProfileId,\r\n  activityIds,\r\n}: {\r\n  businessProfileId: string;\r\n  activityIds: string[] | \"all\";\r\n}) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { success: false, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { success: false, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // If marking specific activities as read\r\n    if (activityIds !== \"all\") {\r\n      // Handle case where we have specific activity IDs\r\n      const { error } = await supabase\r\n        .from(\"business_activities\")\r\n        .update({ is_read: true })\r\n        .eq(\"business_profile_id\", businessProfileId)\r\n        .in(\"id\", activityIds);\r\n\r\n      if (error) {\r\n        console.error(\"Error marking specific activities as read:\", error);\r\n        return { success: false, error: error.message };\r\n      }\r\n    } else {\r\n      // Handle \"mark all as read\" with pagination to work around Supabase's 1000 row limit\r\n      const BATCH_SIZE = 1000; // Maximum number of rows to update at once\r\n      let hasMore = true;\r\n      let processedCount = 0;\r\n\r\n      while (hasMore) {\r\n        // Get a batch of unread activity IDs\r\n        const { data: unreadActivities, error: fetchError } = await supabase\r\n          .from(\"business_activities\")\r\n          .select(\"id\")\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .eq(\"is_read\", false)\r\n          .limit(BATCH_SIZE);\r\n\r\n        if (fetchError) {\r\n          console.error(\"Error fetching unread activities:\", fetchError);\r\n          return { success: false, error: fetchError.message };\r\n        }\r\n\r\n        // If no more unread activities, we're done\r\n        if (!unreadActivities || unreadActivities.length === 0) {\r\n          hasMore = false;\r\n          break;\r\n        }\r\n\r\n        // Extract IDs from the batch\r\n        const batchIds = unreadActivities.map(activity => activity.id);\r\n\r\n        // Mark this batch as read\r\n        const { error: updateError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", batchIds);\r\n\r\n        if (updateError) {\r\n          console.error(\"Error marking batch as read:\", updateError);\r\n          return { success: false, error: updateError.message };\r\n        }\r\n\r\n        // Update processed count and check if we need to continue\r\n        processedCount += batchIds.length;\r\n        hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more\r\n      }\r\n\r\n      console.log(`Marked ${processedCount} activities as read`);\r\n    }\r\n\r\n    // Revalidate the activities page\r\n    revalidatePath(\"/dashboard/business/activities\");\r\n\r\n    return { success: true, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error marking activities as read:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the count of unread activities\r\n */\r\nexport async function getUnreadActivitiesCount(businessProfileId: string) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    const { count, error } = await supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\", head: true })\r\n      .eq(\"business_profile_id\", businessProfileId)\r\n      .eq(\"is_read\", false);\r\n\r\n    if (error) {\r\n      console.error(\"Error getting unread activities count:\", error);\r\n      return { count: 0, error: error.message };\r\n    }\r\n\r\n    return { count: count || 0, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error getting unread activities count:\", error);\r\n    return { count: 0, error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA2PsB,wBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 2327, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/RecentActivities.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n/**\r\n * Realtime Subscription Notes\r\n *\r\n * This component uses Supabase Realtime to listen for new activities.\r\n *\r\n * Important: You need to enable realtime for the business_activities table in Supabase:\r\n * 1. Go to Supabase Dashboard > Database > Replication\r\n * 2. Find the \"business_activities\" table in the list\r\n * 3. Enable realtime by toggling it on\r\n *\r\n * The component subscribes to INSERT events on the business_activities table\r\n * with a filter for the specific business_profile_id:\r\n *\r\n * ```javascript\r\n * supabase\r\n *   .channel('dashboard-activities-changes')\r\n *   .on(\r\n *     'postgres_changes',\r\n *     {\r\n *       event: 'INSERT',\r\n *       schema: 'public',\r\n *       table: 'business_activities',\r\n *       filter: `business_profile_id=eq.${businessProfileId}`,\r\n *     },\r\n *     async () => {\r\n *       // Handle new activity\r\n *     }\r\n *   )\r\n *   .subscribe();\r\n * ```\r\n */\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Bell, Heart, Star, Users, ArrowRight } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { formatDistanceToNow } from \"date-fns\";\r\nimport Link from \"next/link\";\r\nimport { BusinessActivity, getBusinessActivities } from \"@/lib/actions/activities\";\r\nimport { realtimeService } from \"@/lib/services/realtimeService\";\r\n\r\ninterface RecentActivitiesProps {\r\n  businessProfileId: string;\r\n  unreadCount: number;\r\n}\r\n\r\nexport default function RecentActivities({\r\n  businessProfileId,\r\n  unreadCount,\r\n}: RecentActivitiesProps) {\r\n  const [activities, setActivities] = useState<BusinessActivity[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  // Fetch recent activities\r\n  useEffect(() => {\r\n    const fetchActivities = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const { activities } = await getBusinessActivities({\r\n          businessProfileId,\r\n          page: 1,\r\n          pageSize: 5,\r\n          sortBy: \"newest\",\r\n          filterBy: \"all\",\r\n        });\r\n\r\n        setActivities(activities || []);\r\n      } catch (error) {\r\n        console.error(\"Error fetching recent activities:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchActivities();\r\n  }, [businessProfileId]);\r\n\r\n  // Set up real-time subscription for new activities\r\n  useEffect(() => {\r\n    if (!businessProfileId) return;\r\n\r\n    const subscription = realtimeService.subscribeToBusinessActivities(\r\n      businessProfileId,\r\n      async () => {\r\n        // Fetch the updated activities\r\n        const { activities: newActivities } = await getBusinessActivities({\r\n          businessProfileId,\r\n          page: 1,\r\n          pageSize: 5,\r\n          sortBy: \"newest\",\r\n          filterBy: \"all\",\r\n        });\r\n\r\n        setActivities(newActivities || []);\r\n      },\r\n      'recent-activities'\r\n    );\r\n\r\n    return () => {\r\n      subscription.unsubscribe();\r\n    };\r\n  }, [businessProfileId]);\r\n\r\n  // Container animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-4 transition-all duration-300 h-full\"\r\n    >\r\n      <div className=\"flex items-center justify-between mb-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <div className=\"flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900\">\r\n            <Bell className=\"w-4 h-4 text-blue-600 dark:text-blue-300\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold\">Recent Activities</h3>\r\n          {unreadCount > 0 && (\r\n            <Badge variant=\"secondary\" className=\"ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\r\n              {unreadCount > 99 ? \"99+\" : unreadCount} new\r\n            </Badge>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"relative group\">\r\n          {/* Strong inner glow effect that fills only the button */}\r\n          <div className=\"absolute inset-0 bg-blue-500/5 dark:bg-blue-500/10 opacity-80 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none\" />\r\n\r\n          {/* Border glow effect - matches button size */}\r\n          <div\r\n            className=\"absolute inset-0 rounded-xl pointer-events-none opacity-50 group-hover:opacity-70 transition-opacity duration-300\"\r\n            style={{\r\n              boxShadow: `inset 0 0 20px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.3)`\r\n            }}\r\n          />\r\n\r\n          {/* Strong decorative colored glow elements - positioned relative to button */}\r\n          <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-blue-500/30 rounded-full shadow-blue-500/60 opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md pointer-events-none\" />\r\n          <div className=\"absolute -bottom-1 -left-1 w-4 h-4 bg-blue-500/30 rounded-full shadow-blue-500/60 opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md pointer-events-none\" />\r\n\r\n          <Button\r\n            asChild\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            className=\"border-blue-200/50 dark:border-blue-700/50 shadow-blue-500/40 shadow-lg hover:shadow-xl hover:shadow-blue-500/40 transition-all duration-300 text-blue-500 dark:text-blue-400 hover:bg-blue-500/5 dark:hover:bg-blue-500/10 rounded-xl relative overflow-hidden\"\r\n          >\r\n            <Link href=\"/dashboard/business/activities\" className=\"flex items-center gap-1 relative\">\r\n              View All <ArrowRight className=\"w-3 h-3 ml-1\" />\r\n            </Link>\r\n          </Button>\r\n\r\n          {/* Shimmer effect - only on hover, positioned relative to button */}\r\n          <div className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent pointer-events-none opacity-0 group-hover:opacity-100 group-hover:animate-[shimmer_0.6s_ease-in-out] transition-opacity duration-300\" />\r\n        </div>\r\n      </div>\r\n\r\n      {loading ? (\r\n        <div className=\"space-y-3\">\r\n          <Skeleton className=\"h-16 w-full rounded-lg\" />\r\n          <Skeleton className=\"h-16 w-full rounded-lg\" />\r\n          <Skeleton className=\"h-16 w-full rounded-lg\" />\r\n        </div>\r\n      ) : activities.length === 0 ? (\r\n        <div className=\"flex flex-col items-center justify-center py-8 text-center\">\r\n          <Bell className=\"w-10 h-10 text-neutral-300 dark:text-neutral-600 mb-3\" />\r\n          <h4 className=\"text-base font-medium text-neutral-700 dark:text-neutral-300\">No activities yet</h4>\r\n          <p className=\"text-sm text-neutral-500 dark:text-neutral-400 mt-1\">\r\n            When someone interacts with your business, it will appear here.\r\n          </p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"space-y-3\">\r\n          {activities.map((activity) => (\r\n            <RecentActivityItem key={activity.id} activity={activity} />\r\n          ))}\r\n        </div>\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n\r\nfunction RecentActivityItem({ activity }: { activity: BusinessActivity }) {\r\n  // Determine if the user is a business or customer\r\n  const isBusiness = activity.user_profile?.is_business || false;\r\n\r\n  // Get the appropriate name and avatar\r\n  const displayName = isBusiness\r\n    ? activity.user_profile?.business_name\r\n    : activity.user_profile?.name;\r\n\r\n  const avatarUrl = isBusiness\r\n    ? activity.user_profile?.logo_url\r\n    : activity.user_profile?.avatar_url;\r\n\r\n  // Get initials for avatar fallback\r\n  const getInitials = (name?: string | null) => {\r\n    if (!name) return \"?\";\r\n    return name\r\n      .split(\" \")\r\n      .map((n) => n[0])\r\n      .join(\"\")\r\n      .toUpperCase()\r\n      .substring(0, 2);\r\n  };\r\n\r\n  const initials = getInitials(displayName);\r\n\r\n  // Format the time\r\n  const timeAgo = activity.created_at\r\n    ? formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })\r\n    : \"recently\";\r\n\r\n  // Get activity icon and text\r\n  const getActivityDetails = () => {\r\n    switch (activity.activity_type) {\r\n      case \"like\":\r\n        return {\r\n          icon: <Heart className=\"w-4 h-4 text-rose-500\" />,\r\n          text: \"liked your business\",\r\n          color: \"bg-rose-50 text-rose-700 dark:bg-rose-900/20 dark:text-rose-300\",\r\n        };\r\n      case \"subscribe\":\r\n        return {\r\n          icon: <Users className=\"w-4 h-4 text-blue-500\" />,\r\n          text: \"subscribed to your business\",\r\n          color: \"bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300\",\r\n        };\r\n      case \"rating\":\r\n        return {\r\n          icon: <Star className=\"w-4 h-4 text-amber-500\" />,\r\n          text: `rated your business ${activity.rating_value}/5`,\r\n          color: \"bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300\",\r\n        };\r\n      default:\r\n        return {\r\n          icon: <Star className=\"w-4 h-4\" />,\r\n          text: \"interacted with your business\",\r\n          color: \"bg-neutral-50 text-neutral-700 dark:bg-neutral-900/20 dark:text-neutral-300\",\r\n        };\r\n    }\r\n  };\r\n\r\n  const { icon, text, color } = getActivityDetails();\r\n\r\n  // Get profile link\r\n  const profileLink = isBusiness && activity.user_profile?.business_slug\r\n    ? `/${activity.user_profile.business_slug}`\r\n    : \"#\";\r\n\r\n  // Item animation variants\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 10 },\r\n    visible: { opacity: 1, y: 0 },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={itemVariants}\r\n      className={cn(\r\n        \"relative rounded-lg border p-3 transition-all duration-300\",\r\n        activity.is_read\r\n          ? \"border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black\"\r\n          : \"border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/10\"\r\n      )}\r\n    >\r\n      {/* Unread indicator */}\r\n      {!activity.is_read && (\r\n        <div className=\"absolute top-3 right-3 w-2 h-2 rounded-full bg-blue-500\" />\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-3\">\r\n        {/* Avatar - Only link for business users */}\r\n        {isBusiness ? (\r\n          <Link href={profileLink} className=\"shrink-0\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n            <Avatar className=\"h-8 w-8 rounded-lg border border-amber-200 dark:border-amber-800\">\r\n              {avatarUrl ? (\r\n                <AvatarImage src={avatarUrl} alt={displayName || \"User\"} />\r\n              ) : null}\r\n              <AvatarFallback className=\"rounded-lg text-xs bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300\">\r\n                {initials}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          </Link>\r\n        ) : (\r\n          <div className=\"shrink-0\">\r\n            <Avatar className=\"h-8 w-8 rounded-lg border border-neutral-200 dark:border-neutral-700\">\r\n              {avatarUrl ? (\r\n                <AvatarImage src={avatarUrl} alt={displayName || \"User\"} />\r\n              ) : null}\r\n              <AvatarFallback className=\"rounded-lg text-xs bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300\">\r\n                {initials}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n          </div>\r\n        )}\r\n\r\n        {/* Content */}\r\n        <div className=\"flex-1 min-w-0\">\r\n          <div className=\"flex flex-wrap items-center gap-1\">\r\n            {/* Only link for business users */}\r\n            {isBusiness ? (\r\n              <Link\r\n                href={profileLink}\r\n                className=\"font-medium hover:underline truncate text-sm\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n              >\r\n                {displayName || \"Anonymous User\"}\r\n              </Link>\r\n            ) : (\r\n              <span className=\"font-medium truncate text-sm\">\r\n                {displayName || \"Anonymous User\"}\r\n              </span>\r\n            )}\r\n\r\n            {/* Business badge */}\r\n            {isBusiness && (\r\n              <Badge variant=\"outline\" className=\"bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-800 flex items-center gap-1 py-0 h-4 text-[10px]\">\r\n                Business\r\n              </Badge>\r\n            )}\r\n\r\n            <span className=\"text-neutral-500 dark:text-neutral-400 text-xs\">\r\n              {text}\r\n            </span>\r\n          </div>\r\n\r\n          <div className=\"flex items-center justify-between mt-1\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Badge variant=\"secondary\" className={cn(\"flex items-center gap-1 h-5 text-xs\", color)}>\r\n                {icon}\r\n                <span className=\"text-[10px] capitalize\">{activity.activity_type}</span>\r\n              </Badge>\r\n\r\n              <span className=\"text-[10px] text-neutral-500 dark:text-neutral-400\">\r\n                {timeAgo}\r\n              </span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA8BC,GAED;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA7CA;;;;;;;;;;;;;;AAoDe,SAAS,iBAAiB,EACvC,iBAAiB,EACjB,WAAW,EACW;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,WAAW;YACX,IAAI;gBACF,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,CAAA,GAAA,sJAAA,CAAA,wBAAqB,AAAD,EAAE;oBACjD;oBACA,MAAM;oBACN,UAAU;oBACV,QAAQ;oBACR,UAAU;gBACZ;gBAEA,cAAc,cAAc,EAAE;YAChC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAkB;IAEtB,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB;QAExB,MAAM,eAAe,kIAAA,CAAA,kBAAe,CAAC,6BAA6B,CAChE,mBACA;YACE,+BAA+B;YAC/B,MAAM,EAAE,YAAY,aAAa,EAAE,GAAG,MAAM,CAAA,GAAA,sJAAA,CAAA,wBAAqB,AAAD,EAAE;gBAChE;gBACA,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,UAAU;YACZ;YAEA,cAAc,iBAAiB,EAAE;QACnC,GACA;QAGF,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG;QAAC;KAAkB;IAEtB,+BAA+B;IAC/B,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;4BACrC,cAAc,mBACb,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC,cAAc,KAAK,QAAQ;oCAAY;;;;;;;;;;;;;kCAK9C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCACC,WAAU;gCACV,OAAO;oCACL,WAAW,CAAC,wEAAwE,CAAC;gCACvF;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,2HAAA,CAAA,SAAM;gCACL,OAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAiC,WAAU;;wCAAmC;sDAC9E,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKnC,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;YAIlB,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;uBAEpB,WAAW,MAAM,KAAK,kBACxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAC7E,8OAAC;wBAAE,WAAU;kCAAsD;;;;;;;;;;;qCAKrE,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wBAAqC,UAAU;uBAAvB,SAAS,EAAE;;;;;;;;;;;;;;;;AAMhD;AAEA,SAAS,mBAAmB,EAAE,QAAQ,EAAkC;IACtE,kDAAkD;IAClD,MAAM,aAAa,SAAS,YAAY,EAAE,eAAe;IAEzD,sCAAsC;IACtC,MAAM,cAAc,aAChB,SAAS,YAAY,EAAE,gBACvB,SAAS,YAAY,EAAE;IAE3B,MAAM,YAAY,aACd,SAAS,YAAY,EAAE,WACvB,SAAS,YAAY,EAAE;IAE3B,mCAAmC;IACnC,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EACf,IAAI,CAAC,IACL,WAAW,GACX,SAAS,CAAC,GAAG;IAClB;IAEA,MAAM,WAAW,YAAY;IAE7B,kBAAkB;IAClB,MAAM,UAAU,SAAS,UAAU,GAC/B,CAAA,GAAA,mJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,SAAS,UAAU,GAAG;QAAE,WAAW;IAAK,KACrE;IAEJ,6BAA6B;IAC7B,MAAM,qBAAqB;QACzB,OAAQ,SAAS,aAAa;YAC5B,KAAK;gBACH,OAAO;oBACL,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,MAAM;oBACN,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;oBACvB,MAAM;oBACN,OAAO;gBACT;YACF,KAAK;gBACH,OAAO;oBACL,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,MAAM,CAAC,oBAAoB,EAAE,SAAS,YAAY,CAAC,EAAE,CAAC;oBACtD,OAAO;gBACT;YACF;gBACE,OAAO;oBACL,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACtB,MAAM;oBACN,OAAO;gBACT;QACJ;IACF;IAEA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IAE9B,mBAAmB;IACnB,MAAM,cAAc,cAAc,SAAS,YAAY,EAAE,gBACrD,CAAC,CAAC,EAAE,SAAS,YAAY,CAAC,aAAa,EAAE,GACzC;IAEJ,0BAA0B;IAC1B,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8DACA,SAAS,OAAO,GACZ,sEACA;;YAIL,CAAC,SAAS,OAAO,kBAChB,8OAAC;gBAAI,WAAU;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;oBAEZ,2BACC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM;wBAAa,WAAU;wBAAW,QAAO;wBAAS,KAAI;kCAChE,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;;gCACf,0BACC,8OAAC,2HAAA,CAAA,cAAW;oCAAC,KAAK;oCAAW,KAAK,eAAe;;;;;2CAC/C;8CACJ,8OAAC,2HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB;;;;;;;;;;;;;;;;6CAKP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BAAC,WAAU;;gCACf,0BACC,8OAAC,2HAAA,CAAA,cAAW;oCAAC,KAAK;oCAAW,KAAK,eAAe;;;;;2CAC/C;8CACJ,8OAAC,2HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB;;;;;;;;;;;;;;;;;kCAOT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAEZ,2BACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,QAAO;wCACP,KAAI;kDAEH,eAAe;;;;;6DAGlB,8OAAC;wCAAK,WAAU;kDACb,eAAe;;;;;;oCAKnB,4BACC,8OAAC,0HAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAA0J;;;;;;kDAK/L,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;0CAIL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0HAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;;gDAC7E;8DACD,8OAAC;oDAAK,WAAU;8DAA0B,SAAS,aAAa;;;;;;;;;;;;sDAGlE,8OAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB", "debugId": null}}, {"offset": {"line": 2941, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3006, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessStatusAlert.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON>, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { AlertCircle, ArrowRight } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { motion } from \"framer-motion\";\r\n\r\n/**\r\n * BusinessStatusAlert Component\r\n * \r\n * This component displays an alert when a business profile is set to offline status.\r\n * It informs the user that their business card is not visible to customers and\r\n * provides a direct link to the card settings page to change the status.\r\n */\r\nexport default function BusinessStatusAlert() {\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: -10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3 }}\r\n      className=\"mb-6\"\r\n    >\r\n      <Alert \r\n        variant=\"destructive\"\r\n        className=\"bg-red-50 border-red-200 dark:bg-red-950/50 dark:border-red-800/50\"\r\n      >\r\n        <AlertCircle className=\"h-5 w-5 text-red-600 dark:text-red-400\" />\r\n        <AlertTitle className=\"text-red-800 dark:text-red-300\">\r\n          Your Business Card is Offline\r\n        </AlertTitle>\r\n        <AlertDescription className=\"text-red-700 dark:text-red-400\">\r\n          <p className=\"mb-3\">\r\n            Your business card is currently set to offline status and won&apos;t appear in search results or discovery pages.\r\n          </p>\r\n          <Button \r\n            asChild \r\n            size=\"sm\" \r\n            variant=\"outline\"\r\n            className=\"border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900/30\"\r\n          >\r\n            <Link href=\"/dashboard/business/card\" className=\"flex items-center gap-1\">\r\n              <span>Go Online Now</span>\r\n              <ArrowRight className=\"h-3.5 w-3.5\" />\r\n            </Link>\r\n          </Button>\r\n        </AlertDescription>\r\n      </Alert>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAee,SAAS;IACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,8OAAC,0HAAA,CAAA,QAAK;YACJ,SAAQ;YACR,WAAU;;8BAEV,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC,0HAAA,CAAA,aAAU;oBAAC,WAAU;8BAAiC;;;;;;8BAGvD,8OAAC,0HAAA,CAAA,mBAAgB;oBAAC,WAAU;;sCAC1B,8OAAC;4BAAE,WAAU;sCAAO;;;;;;sCAGpB,8OAAC,2HAAA,CAAA,SAAM;4BACL,OAAO;4BACP,MAAK;4BACL,SAAQ;4BACR,WAAU;sCAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2B,WAAU;;kDAC9C,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 3125, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/components/BusinessDashboardClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  LayoutDashboard,\r\n  CreditCard,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport DashboardOverviewClient from \"./DashboardOverviewClient\";\r\nimport AnimatedSubscriptionStatus from \"./AnimatedSubscriptionStatus\";\r\nimport EnhancedQuickActions from \"./EnhancedQuickActions\";\r\nimport RecentActivities from \"./RecentActivities\";\r\nimport BusinessStatusAlert from \"./BusinessStatusAlert\";\r\nimport { PricingPlan } from \"@/lib/PricingPlans\";\r\nimport { getUnreadActivitiesCount } from \"@/lib/actions/activities\";\r\n\r\ninterface BusinessDashboardClientProps {\r\n  initialProfile: {\r\n    business_name: string;\r\n    business_slug: string;\r\n    plan_id: string | null;\r\n    plan_cycle: string | null;\r\n    has_active_subscription: boolean | null;\r\n    trial_end_date: string | null;\r\n    total_likes: number;\r\n    total_subscriptions: number;\r\n    average_rating: number;\r\n    logo_url: string | null;\r\n    title: string | null;\r\n    status: string; // \"online\" or \"offline\"\r\n  };\r\n  userId: string;\r\n  subscriptionStatus: \"active\" | \"trial\" | \"inactive\";\r\n  planDetails: PricingPlan | undefined;\r\n  subscription?: {\r\n    subscription_status: string | null;\r\n    plan_id: string | null;\r\n    plan_cycle: string | null;\r\n  } | null;\r\n}\r\n\r\nexport default function BusinessDashboardClient({\r\n  initialProfile,\r\n  userId,\r\n  subscriptionStatus,\r\n  planDetails,\r\n  subscription,\r\n}: BusinessDashboardClientProps) {\r\n  const [unreadActivitiesCount, setUnreadActivitiesCount] = useState(0);\r\n\r\n  /**\r\n   * Fetch Unread Activities Count\r\n   *\r\n   * This effect fetches the initial unread activities count and sets up an interval\r\n   * to refresh it every minute. This ensures the dashboard shows accurate counts\r\n   * even if the realtime subscription misses any events.\r\n   *\r\n   * Note: For realtime updates of the activities count, you need to enable realtime\r\n   * for the business_activities table in Supabase:\r\n   * 1. Go to Supabase Dashboard > Database > Replication\r\n   * 2. Find the \"business_activities\" table in the list\r\n   * 3. Enable realtime by toggling it on\r\n   */\r\n  useEffect(() => {\r\n    const fetchUnreadCount = async () => {\r\n      const { count } = await getUnreadActivitiesCount(userId);\r\n      setUnreadActivitiesCount(count);\r\n    };\r\n\r\n    fetchUnreadCount();\r\n\r\n    // Set up interval to refresh count every minute\r\n    const intervalId = setInterval(fetchUnreadCount, 60000);\r\n\r\n    return () => clearInterval(intervalId);\r\n  }, [userId]);\r\n\r\n  // Animation variants for staggered animations\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: { opacity: 1, y: 0, transition: { duration: 0.4 } },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      variants={containerVariants}\r\n      className=\"space-y-8\"\r\n    >\r\n      {/* Welcome Section - Full Width */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6\">\r\n        <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n          <LayoutDashboard className=\"w-6 h-6 text-foreground\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h1 className=\"text-2xl font-bold text-foreground\">\r\n            Welcome, {initialProfile.business_name || 'Business Owner'}\r\n          </h1>\r\n          <p className=\"text-muted-foreground mt-1\">\r\n            Manage your business profile and track performance\r\n          </p>\r\n        </div>\r\n        <Button\r\n          asChild\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n        >\r\n          <Link href=\"/dashboard/business/card\" className=\"flex items-center\">\r\n            <CreditCard className=\"mr-2 h-4 w-4\" />\r\n            Manage Card\r\n          </Link>\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Business Status Alert - Show only when status is offline */}\r\n      {initialProfile.status === \"offline\" && (\r\n        <motion.div variants={itemVariants}>\r\n          <BusinessStatusAlert />\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Dashboard Overview Client - Full Width */}\r\n      <motion.div variants={itemVariants}>\r\n        <DashboardOverviewClient\r\n          initialProfile={initialProfile}\r\n          userId={userId}\r\n          userPlan={initialProfile.plan_id}\r\n        />\r\n      </motion.div>\r\n\r\n      {/* Subscription Status and Quick Actions - Full Width */}\r\n      <motion.div variants={itemVariants}>\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6\">\r\n          {/* Animated Subscription Status */}\r\n          <AnimatedSubscriptionStatus\r\n            subscriptionStatus={subscriptionStatus}\r\n            planDetails={planDetails}\r\n            trialEndDate={initialProfile.trial_end_date}\r\n            planCycle={initialProfile.plan_cycle}\r\n            subscription={subscription}\r\n          />\r\n\r\n          {/* Enhanced Quick Actions */}\r\n          <EnhancedQuickActions userPlan={initialProfile.plan_id} />\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Recent Activities - Full Width */}\r\n      <motion.div variants={itemVariants}>\r\n        <RecentActivities\r\n          businessProfileId={userId}\r\n          unreadCount={unreadActivitiesCount}\r\n        />\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AAEA;AAhBA;;;;;;;;;;;;;AA2Ce,SAAS,wBAAwB,EAC9C,cAAc,EACd,MAAM,EACN,kBAAkB,EAClB,WAAW,EACX,YAAY,EACiB;IAC7B,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE;;;;;;;;;;;;GAYC,GACD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,sJAAA,CAAA,2BAAwB,AAAD,EAAE;YACjD,yBAAyB;QAC3B;QAEA;QAEA,gDAAgD;QAChD,MAAM,aAAa,YAAY,kBAAkB;QAEjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAO;IAEX,8CAA8C;IAC9C,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,YAAY;gBAAE,UAAU;YAAI;QAAE;IAC7D;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAQ;QACR,SAAQ;QACR,UAAU;QACV,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4NAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;kCAE7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAqC;oCACvC,eAAe,aAAa,IAAI;;;;;;;0CAE5C,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAI5C,8OAAC,2HAAA,CAAA,SAAM;wBACL,OAAO;wBACP,SAAQ;wBACR,MAAK;kCAEL,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAA2B,WAAU;;8CAC9C,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YAO5C,eAAe,MAAM,KAAK,2BACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,8OAAC,mLAAA,CAAA,UAAmB;;;;;;;;;;0BAKxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,8OAAC,uLAAA,CAAA,UAAuB;oBACtB,gBAAgB;oBAChB,QAAQ;oBACR,UAAU,eAAe,OAAO;;;;;;;;;;;0BAKpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,UAA0B;4BACzB,oBAAoB;4BACpB,aAAa;4BACb,cAAc,eAAe,cAAc;4BAC3C,WAAW,eAAe,UAAU;4BACpC,cAAc;;;;;;sCAIhB,8OAAC,oLAAA,CAAA,UAAoB;4BAAC,UAAU,eAAe,OAAO;;;;;;;;;;;;;;;;;0BAK1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,8OAAC,gLAAA,CAAA,UAAgB;oBACf,mBAAmB;oBACnB,aAAa;;;;;;;;;;;;;;;;;AAKvB", "debugId": null}}]}