{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/utils/scrollToError.ts"], "sourcesContent": ["/**\r\n * Scrolls to the first form error with enhanced smooth scrolling behavior\r\n * @param formId The ID of the form containing the errors\r\n */\r\nexport function scrollToFirstError(formId: string = 'business-card-form'): void {\r\n  // Wait for the DOM to update with error messages\r\n  setTimeout(() => {\r\n    // Find the form\r\n    const form = document.getElementById(formId);\r\n    if (!form) return;\r\n\r\n    // Find all error messages within the form\r\n    const errorElements = form.querySelectorAll('[role=\"alert\"]');\r\n    if (!errorElements || errorElements.length === 0) {\r\n      // If no error elements with role=\"alert\", try to find form fields with errors\r\n      const errorFields = form.querySelectorAll('.error-field, [aria-invalid=\"true\"]');\r\n      if (errorFields && errorFields.length > 0) {\r\n        smoothScrollToElement(errorFields[0]);\r\n        return;\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Get the first error element\r\n    const firstError = errorElements[0];\r\n\r\n    // Use custom smooth scroll\r\n    smoothScrollToElement(firstError);\r\n  }, 100); // Small delay to ensure DOM has updated\r\n}\r\n\r\n/**\r\n * Performs a smoother scroll animation to an element\r\n * @param element The element to scroll to\r\n */\r\nfunction smoothScrollToElement(element: Element): void {\r\n  // Get the element's position\r\n  const rect = element.getBoundingClientRect();\r\n  const targetPosition = window.scrollY + rect.top - 150; // 150px offset from top\r\n  const startPosition = window.scrollY;\r\n  const distance = targetPosition - startPosition;\r\n\r\n  // Add a subtle highlight effect to draw attention\r\n  element.classList.add('error-highlight');\r\n  setTimeout(() => {\r\n    element.classList.remove('error-highlight');\r\n  }, 3000); // Longer highlight duration\r\n\r\n  // Use a more sophisticated easing function for smoother animation\r\n  const easeOutCubic = (t: number): number => 1 - Math.pow(1 - t, 3);\r\n\r\n  // Animation parameters\r\n  const duration = 800; // Longer duration for smoother effect\r\n  const startTime = performance.now();\r\n\r\n  // Animation function\r\n  function animateScroll(currentTime: number) {\r\n    const elapsedTime = currentTime - startTime;\r\n    const progress = Math.min(elapsedTime / duration, 1);\r\n    const easedProgress = easeOutCubic(progress);\r\n\r\n    window.scrollTo({\r\n      top: startPosition + distance * easedProgress,\r\n      behavior: 'auto' // We're handling the animation manually\r\n    });\r\n\r\n    if (progress < 1) {\r\n      requestAnimationFrame(animateScroll);\r\n    }\r\n  }\r\n\r\n  // Start the animation\r\n  requestAnimationFrame(animateScroll);\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,mBAAmB,SAAiB,oBAAoB;IACtE,iDAAiD;IACjD,WAAW;QACT,gBAAgB;QAChB,MAAM,OAAO,SAAS,cAAc,CAAC;QACrC,IAAI,CAAC,MAAM;QAEX,0CAA0C;QAC1C,MAAM,gBAAgB,KAAK,gBAAgB,CAAC;QAC5C,IAAI,CAAC,iBAAiB,cAAc,MAAM,KAAK,GAAG;YAChD,8EAA8E;YAC9E,MAAM,cAAc,KAAK,gBAAgB,CAAC;YAC1C,IAAI,eAAe,YAAY,MAAM,GAAG,GAAG;gBACzC,sBAAsB,WAAW,CAAC,EAAE;gBACpC;YACF;YACA;QACF;QAEA,8BAA8B;QAC9B,MAAM,aAAa,aAAa,CAAC,EAAE;QAEnC,2BAA2B;QAC3B,sBAAsB;IACxB,GAAG,MAAM,wCAAwC;AACnD;AAEA;;;CAGC,GACD,SAAS,sBAAsB,OAAgB;IAC7C,6BAA6B;IAC7B,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,MAAM,iBAAiB,OAAO,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,wBAAwB;IAChF,MAAM,gBAAgB,OAAO,OAAO;IACpC,MAAM,WAAW,iBAAiB;IAElC,kDAAkD;IAClD,QAAQ,SAAS,CAAC,GAAG,CAAC;IACtB,WAAW;QACT,QAAQ,SAAS,CAAC,MAAM,CAAC;IAC3B,GAAG,OAAO,4BAA4B;IAEtC,kEAAkE;IAClE,MAAM,eAAe,CAAC,IAAsB,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG;IAEhE,uBAAuB;IACvB,MAAM,WAAW,KAAK,sCAAsC;IAC5D,MAAM,YAAY,YAAY,GAAG;IAEjC,qBAAqB;IACrB,SAAS,cAAc,WAAmB;QACxC,MAAM,cAAc,cAAc;QAClC,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc,UAAU;QAClD,MAAM,gBAAgB,aAAa;QAEnC,OAAO,QAAQ,CAAC;YACd,KAAK,gBAAgB,WAAW;YAChC,UAAU,OAAO,wCAAwC;QAC3D;QAEA,IAAI,WAAW,GAAG;YAChB,sBAAsB;QACxB;IACF;IAEA,sBAAsB;IACtB,sBAAsB;AACxB", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/schema.ts"], "sourcesContent": ["import * as z from \"zod\";\r\nimport { IndianMobileSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Regular expression for validating hex color codes (e.g., #RRGGBB, #RGB)\r\n// const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/; // Removed as theme_color is removed\r\n\r\n// Zod schema for business card data validation (Phase 1)\r\nexport const businessCardSchema = z.object({\r\n  // Optional fields first\r\n  logo_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for logo/profile photo.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .nullable(), // Allow empty string, null, or valid URL\r\n  established_year: z\r\n    .number()\r\n    .int({ message: \"Established year must be a whole number.\" })\r\n    .min(1800, { message: \"Established year must be after 1800.\" })\r\n    .max(new Date().getFullYear(), { message: \"Established year cannot be in the future.\" })\r\n    .optional()\r\n    .nullable(),\r\n  // Address broken down - NOW REQUIRED (from onboarding)\r\n  address_line: z\r\n    .string()\r\n    .min(1, { message: \"Address line is required.\" })\r\n    .max(100, { message: \"Address line cannot exceed 100 characters.\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality/area is required.\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required.\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required.\" }),\r\n  pincode: z\r\n    .string()\r\n    .min(6, { message: \"Pincode must be 6 digits.\" })\r\n    .max(6, { message: \"Pincode must be 6 digits.\" })\r\n    .regex(/^\\d+$/, { message: \"Pincode must contain only digits.\" }),\r\n  phone: IndianMobileSchema, // Primary display phone - NOW REQUIRED\r\n  // timing_info removed\r\n  // delivery_info removed\r\n  // website_url removed\r\n  instagram_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Instagram.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  facebook_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Facebook.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // linkedin_url removed\r\n  // twitter_url removed\r\n  // youtube_url removed\r\n  whatsapp_number: IndianMobileSchema // For wa.me link\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // call_number removed\r\n  about_bio: z\r\n    .string()\r\n    .max(100, { message: \"Bio cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  theme_color: z // Added for Growth plan\r\n    .string()\r\n    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {\r\n      message: \"Invalid hex color format (e.g., #RRGGBB or #RGB).\",\r\n    })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // card_texture field removed as it doesn't exist in the database\r\n  business_hours: z.any().optional().nullable(), // Added for Growth plan - Using z.any() for now, refine if specific structure needed\r\n  delivery_info: z // Added for Growth plan\r\n    .string()\r\n    .max(100, { message: \"Delivery info cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  business_category: z\r\n    .string()\r\n    .min(1, { message: \"Business category is required.\" }),\r\n  google_maps_url: z\r\n    .string()\r\n    .url({ message: \"Please enter a valid Google Maps URL.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .refine((url) => {\r\n      if (!url || url === \"\") return true; // Allow empty\r\n      // Validate Google Maps URL patterns\r\n      const googleMapsPatterns = [\r\n        /^https:\\/\\/maps\\.app\\.goo\\.gl\\/[a-zA-Z0-9]+$/,\r\n        /^https:\\/\\/www\\.google\\.com\\/maps\\//,\r\n        /^https:\\/\\/goo\\.gl\\/maps\\//,\r\n        /^https:\\/\\/maps\\.google\\.com\\//\r\n      ];\r\n      return googleMapsPatterns.some(pattern => pattern.test(url));\r\n    }, {\r\n      message: \"Please enter a valid Google Maps URL (e.g., https://maps.app.goo.gl/... or https://www.google.com/maps/...)\"\r\n    }),\r\n  status: z.enum([\"online\", \"offline\"]).default(\"offline\"),\r\n  // Custom branding fields for Pro/Enterprise users\r\n  custom_branding: z.object({\r\n    custom_header_text: z.string().max(50).optional().or(z.literal(\"\")),\r\n    custom_header_image_url: z.string().url().optional().or(z.literal(\"\")), // Legacy field\r\n    custom_header_image_light_url: z.string().url().optional().or(z.literal(\"\")), // Light theme\r\n    custom_header_image_dark_url: z.string().url().optional().or(z.literal(\"\")), // Dark theme\r\n    hide_dukancard_branding: z.boolean().optional(),\r\n    // File objects for pending uploads (not saved to database)\r\n    pending_light_header_file: z.any().optional(), // File object for light theme\r\n    pending_dark_header_file: z.any().optional(), // File object for dark theme\r\n  }).optional()\r\n  .refine((data) => {\r\n    // Only require custom_header_text OR any header image if hide_dukancard_branding is explicitly true\r\n    if (data?.hide_dukancard_branding === true) {\r\n      const hasText = data?.custom_header_text && data.custom_header_text.trim() !== \"\";\r\n      const hasLegacyImage = data?.custom_header_image_url && data.custom_header_image_url.trim() !== \"\";\r\n      const hasLightImage = data?.custom_header_image_light_url && data.custom_header_image_light_url.trim() !== \"\";\r\n      const hasDarkImage = data?.custom_header_image_dark_url && data.custom_header_image_dark_url.trim() !== \"\";\r\n\r\n      if (!hasText && !hasLegacyImage && !hasLightImage && !hasDarkImage) {\r\n        return false;\r\n      }\r\n    }\r\n    return true;\r\n  }, {\r\n    message: \"Custom header text or image is required when hiding Dukancard branding\",\r\n    path: [\"custom_header_text\"]\r\n  }),\r\n  // Custom ads for Pro/Enterprise users\r\n  custom_ads: z.object({\r\n    enabled: z.boolean().optional(),\r\n    image_url: z.string().url().optional().or(z.literal(\"\")),\r\n    link_url: z.string().url().optional().or(z.literal(\"\")),\r\n    uploaded_at: z.string().optional().or(z.literal(\"\")).nullable(),\r\n  }).optional(),\r\n  business_slug: z\r\n    .string()\r\n    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {\r\n      message:\r\n        \"Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen.\",\r\n    })\r\n    .min(3, { message: \"Slug must be at least 3 characters long.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n\r\n  // Required fields\r\n  member_name: z\r\n    .string()\r\n    .min(1, { message: \"Member name is required.\" })\r\n    .max(50, { message: \"Name cannot exceed 50 characters.\" }),\r\n  title: z\r\n    .string()\r\n    .min(1, { message: \"Title/Designation is required.\" })\r\n    .max(50, { message: \"Title cannot exceed 50 characters.\" }),\r\n  business_name: z\r\n    .string()\r\n    .min(1, { message: \"Business name is required.\" })\r\n    .max(100, { message: \"Business name cannot exceed 100 characters.\" }),\r\n\r\n  // Read-only/managed fields (keep for type safety if needed)\r\n  id: z.string().uuid().optional(),\r\n  contact_email: z.string().email({ message: \"Please enter a valid email address\" }).min(1, { message: \"Contact email is required\" }),\r\n  has_active_subscription: z.boolean().optional(),\r\n  trial_end_date: z.string().optional().nullable(), // Database returns string, not Date\r\n  created_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n  updated_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n\r\n  // Interaction fields (added in Phase 2) - make optional as they might not always be fetched\r\n  total_likes: z.number().int().nonnegative().optional(),\r\n  total_subscriptions: z.number().int().nonnegative().optional(),\r\n  average_rating: z.number().nonnegative().optional(),\r\n  total_visits: z.number().int().nonnegative().optional(),\r\n});\r\n\r\n// TypeScript type inferred from the Zod schema\r\nexport type BusinessCardData = z.infer<typeof businessCardSchema>;\r\n\r\n// Default values for initializing the form or preview (Phase 1)\r\nexport const defaultBusinessCardData: Partial<BusinessCardData> = {\r\n  member_name: \"\",\r\n  title: \"\",\r\n  business_name: \"\",\r\n  logo_url: null,\r\n  established_year: null,\r\n  address_line: \"\",\r\n  locality: \"\",\r\n  city: \"\",\r\n  state: \"\",\r\n  pincode: \"\",\r\n  phone: \"\",\r\n  instagram_url: \"\",\r\n  facebook_url: \"\",\r\n  whatsapp_number: \"\",\r\n  about_bio: \"\",\r\n  theme_color: \"\",\r\n  business_hours: null,\r\n  delivery_info: \"\",\r\n  business_category: \"\",\r\n  google_maps_url: \"\",\r\n  status: \"offline\",\r\n  business_slug: \"\",\r\n  contact_email: \"\", // Added contact_email field\r\n  custom_branding: {\r\n    custom_header_text: \"\",\r\n    custom_header_image_url: \"\", // Legacy field\r\n    custom_header_image_light_url: \"\", // Light theme\r\n    custom_header_image_dark_url: \"\", // Dark theme\r\n    hide_dukancard_branding: false,\r\n    pending_light_header_file: null, // File object for light theme\r\n    pending_dark_header_file: null, // File object for dark theme\r\n  },\r\n  custom_ads: {\r\n    enabled: false,\r\n    image_url: \"\",\r\n    link_url: \"\",\r\n    uploaded_at: null,\r\n  },\r\n};\r\n\r\n// Define which fields are strictly required to go online\r\nexport const requiredFieldsForOnline: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\",\r\n  \"contact_email\", // Added contact_email as required for online status\r\n  \"business_category\" // Added business_category as required for online status\r\n];\r\n\r\n// Define which fields are required for saving regardless of status (all onboarding fields except plan)\r\nexport const requiredFieldsForSaving: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"contact_email\",\r\n  \"business_category\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\"\r\n];\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAMO,MAAM,qBAAqB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;IACzC,wBAAwB;IACxB,UAAU,CAAA,GAAA,uIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA6C,GAC5D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE,KACb,QAAQ;IACX,kBAAkB,CAAA,GAAA,uIAAA,CAAA,SACT,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA2C,GAC1D,GAAG,CAAC,MAAM;QAAE,SAAS;IAAuC,GAC5D,GAAG,CAAC,IAAI,OAAO,WAAW,IAAI;QAAE,SAAS;IAA4C,GACrF,QAAQ,GACR,QAAQ;IACX,uDAAuD;IACvD,cAAc,CAAA,GAAA,uIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA6C;IACpE,UAAU,CAAA,GAAA,uIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;IAClD,MAAM,CAAA,GAAA,uIAAA,CAAA,SACG,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB;IACzC,OAAO,CAAA,GAAA,uIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAqB;IAC1C,SAAS,CAAA,GAAA,uIAAA,CAAA,SACA,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,KAAK,CAAC,SAAS;QAAE,SAAS;IAAoC;IACjE,OAAO,gIAAA,CAAA,qBAAkB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,sBAAsB;IACtB,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAoC,GACnD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,CAAA,GAAA,uIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAmC,GAClD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,iBAAiB,iIAAmB,iBAAiB;IAApC,CAAA,qBAAkB,CAChC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,sBAAsB;IACtB,WAAW,CAAA,GAAA,uIAAA,CAAA,SACF,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAAoC,GACxD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,aAAa,CAAA,GAAA,uIAAA,CAAA,SACJ,AAAD,IACL,KAAK,CAAC,sCAAsC;QAC3C,SAAS;IACX,GACC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,iEAAiE;IACjE,gBAAgB,CAAA,GAAA,uIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C,GAClE,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,mBAAmB,CAAA,GAAA,uIAAA,CAAA,SACV,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC;IACtD,iBAAiB,CAAA,GAAA,uIAAA,CAAA,SACR,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAwC,GACvD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE,KACb,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,OAAO,QAAQ,IAAI,OAAO,MAAM,cAAc;QACnD,oCAAoC;QACpC,MAAM,qBAAqB;YACzB;YACA;YACA;YACA;SACD;QACD,OAAO,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;IACzD,GAAG;QACD,SAAS;IACX;IACF,QAAQ,CAAA,GAAA,uIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAU;KAAU,EAAE,OAAO,CAAC;IAC9C,kDAAkD;IAClD,iBAAiB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;QACxB,oBAAoB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QAC/D,yBAAyB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QAClE,+BAA+B,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACxE,8BAA8B,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACvE,yBAAyB,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7C,2DAA2D;QAC3D,2BAA2B,CAAA,GAAA,uIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;QAC3C,0BAA0B,CAAA,GAAA,uIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;IAC5C,GAAG,QAAQ,GACV,MAAM,CAAC,CAAC;QACP,oGAAoG;QACpG,IAAI,MAAM,4BAA4B,MAAM;YAC1C,MAAM,UAAU,MAAM,sBAAsB,KAAK,kBAAkB,CAAC,IAAI,OAAO;YAC/E,MAAM,iBAAiB,MAAM,2BAA2B,KAAK,uBAAuB,CAAC,IAAI,OAAO;YAChG,MAAM,gBAAgB,MAAM,iCAAiC,KAAK,6BAA6B,CAAC,IAAI,OAAO;YAC3G,MAAM,eAAe,MAAM,gCAAgC,KAAK,4BAA4B,CAAC,IAAI,OAAO;YAExG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,cAAc;gBAClE,OAAO;YACT;QACF;QACA,OAAO;IACT,GAAG;QACD,SAAS;QACT,MAAM;YAAC;SAAqB;IAC9B;IACA,sCAAsC;IACtC,YAAY,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,EAAE;QACnB,SAAS,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7B,WAAW,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACpD,UAAU,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;QACnD,aAAa,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE,KAAK,QAAQ;IAC/D,GAAG,QAAQ;IACX,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,KAAK,CAAC,8BAA8B;QACnC,SACE;IACJ,GACC,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,EAAE;IAEhB,kBAAkB;IAClB,aAAa,CAAA,GAAA,uIAAA,CAAA,SACJ,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAC7C,GAAG,CAAC,IAAI;QAAE,SAAS;IAAoC;IAC1D,OAAO,CAAA,GAAA,uIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC,GACnD,GAAG,CAAC,IAAI;QAAE,SAAS;IAAqC;IAC3D,eAAe,CAAA,GAAA,uIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B,GAC/C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C;IAErE,4DAA4D;IAC5D,IAAI,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,GAAG,QAAQ;IAC9B,eAAe,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAAqC,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B;IACjI,yBAAyB,CAAA,GAAA,uIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;IAC7C,gBAAgB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC9C,YAAY,CAAA,GAAA,uIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,uIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IACA,YAAY,CAAA,GAAA,uIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,uIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IAEA,4FAA4F;IAC5F,aAAa,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IACpD,qBAAqB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IAC5D,gBAAgB,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,WAAW,GAAG,QAAQ;IACjD,cAAc,CAAA,GAAA,uIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;AACvD;AAMO,MAAM,0BAAqD;IAChE,aAAa;IACb,OAAO;IACP,eAAe;IACf,UAAU;IACV,kBAAkB;IAClB,cAAc;IACd,UAAU;IACV,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IACnB,iBAAiB;IACjB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,iBAAiB;QACf,oBAAoB;QACpB,yBAAyB;QACzB,+BAA+B;QAC/B,8BAA8B;QAC9B,yBAAyB;QACzB,2BAA2B;QAC3B,0BAA0B;IAC5B;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,aAAa;IACf;AACF;AAGO,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB,wDAAwD;CAC7E;AAGM,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 333, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions.ts"], "sourcesContent": ["// Re-export all actions from organized modules\r\nexport * from \"./business-card/updateBusinessCard\";\r\nexport * from \"./business-card/getBusinessCardData\";\r\nexport * from \"./logo/logoActions\";\r\nexport * from \"./slug/slugUtils\";\r\nexport * from \"./public/publicCardActions\";\r\n"], "names": [], "mappings": "AAAA,+CAA+C", "debugId": null}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/business-card/updateBusinessCard.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { BusinessCardData } from \"../schema\";\r\nimport { validateBusinessCardData } from \"../validation/businessCardValidation\";\r\nimport { checkSubscriptionStatus } from \"../data/subscriptionChecker\";\r\nimport { generateUniqueSlug } from \"../slug/slugUtils\";\r\nimport { processBusinessHours } from \"../utils/businessHoursProcessor\";\r\nimport { uploadThemeHeaderImage, deleteThemeHeaderImage, cleanupOldThemeHeaderImages } from \"../actions/themeHeaderActions\";\r\n\r\n/**\r\n * Updates business card data with validation and processing\r\n * @param formData - The business card data to update\r\n * @returns Success/error response with updated data\r\n */\r\nexport async function updateBusinessCard(\r\n  formData: BusinessCardData\r\n): Promise<{ success: boolean; error?: string; data?: BusinessCardData }> {\r\n  const supabase = await createClient();\r\n\r\n  // 1. Validate the incoming data\r\n  const validatedFields = validateBusinessCardData(formData);\r\n\r\n  if (!validatedFields.success) {\r\n    console.error(\r\n      \"Validation Error:\",\r\n      validatedFields.error.flatten().fieldErrors\r\n    );\r\n    return {\r\n      success: false,\r\n      error: \"Invalid data provided. Please check the form fields.\",\r\n    };\r\n  }\r\n\r\n  // 2. Get the authenticated user\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    console.error(\"Auth Error:\", authError);\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Get existing profile to compare phone numbers\r\n  const { data: existingProfile, error: profileError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"phone\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (profileError) {\r\n    console.error(\"Profile fetch error:\", profileError);\r\n    return { success: false, error: \"Failed to fetch existing profile.\" };\r\n  }\r\n\r\n  // 3. Check subscription status if going online\r\n  if (validatedFields.data.status === \"online\") {\r\n    const subscriptionCheck = await checkSubscriptionStatus(user.id);\r\n    if (!subscriptionCheck.canGoOnline) {\r\n      return {\r\n        success: false,\r\n        error: subscriptionCheck.error || \"Cannot set card to online status.\"\r\n      };\r\n    }\r\n  }\r\n\r\n  // 4. Handle Slug Logic if going online\r\n  let finalSlug = validatedFields.data.business_slug;\r\n\r\n  if (validatedFields.data.status === \"online\") {\r\n    const slugResult = await generateUniqueSlug(\r\n      validatedFields.data.business_name,\r\n      finalSlug || \"\",\r\n      user.id\r\n    );\r\n\r\n    if (!slugResult.success) {\r\n      return {\r\n        success: false,\r\n        error: slugResult.error || \"Failed to generate unique slug.\"\r\n      };\r\n    }\r\n\r\n    finalSlug = slugResult.slug;\r\n  } else {\r\n    finalSlug = validatedFields.data.business_slug;\r\n  }\r\n\r\n  // 5. Handle theme-specific header image uploads\r\n  const updatedCustomBranding = { ...validatedFields.data.custom_branding };\r\n\r\n  // Handle light theme header upload\r\n  if (validatedFields.data.custom_branding?.pending_light_header_file) {\r\n    const lightFile = validatedFields.data.custom_branding.pending_light_header_file as File;\r\n    const lightUploadResult = await uploadThemeHeaderImage(lightFile, 'light');\r\n\r\n    if (lightUploadResult.success && lightUploadResult.url) {\r\n      // Clean up old light theme images\r\n      if (updatedCustomBranding.custom_header_image_light_url) {\r\n        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_light_url);\r\n      }\r\n      await cleanupOldThemeHeaderImages(user.id, 'light', lightUploadResult.url);\r\n\r\n      updatedCustomBranding.custom_header_image_light_url = lightUploadResult.url;\r\n    } else {\r\n      console.error(\"Light theme header upload failed:\", lightUploadResult.error);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload light theme header: ${lightUploadResult.error}`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // Handle dark theme header upload\r\n  if (validatedFields.data.custom_branding?.pending_dark_header_file) {\r\n    const darkFile = validatedFields.data.custom_branding.pending_dark_header_file as File;\r\n    const darkUploadResult = await uploadThemeHeaderImage(darkFile, 'dark');\r\n\r\n    if (darkUploadResult.success && darkUploadResult.url) {\r\n      // Clean up old dark theme images\r\n      if (updatedCustomBranding.custom_header_image_dark_url) {\r\n        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_dark_url);\r\n      }\r\n      await cleanupOldThemeHeaderImages(user.id, 'dark', darkUploadResult.url);\r\n\r\n      updatedCustomBranding.custom_header_image_dark_url = darkUploadResult.url;\r\n    } else {\r\n      console.error(\"Dark theme header upload failed:\", darkUploadResult.error);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload dark theme header: ${darkUploadResult.error}`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // Handle deletion of theme-specific headers (when URL is empty but no new file)\r\n  if (validatedFields.data.custom_branding?.custom_header_image_light_url === \"\" &&\r\n      !validatedFields.data.custom_branding?.pending_light_header_file) {\r\n    // Get current light URL from database to delete\r\n    const { data: currentProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_branding\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (currentProfile?.custom_branding?.custom_header_image_light_url) {\r\n      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_light_url);\r\n    }\r\n    updatedCustomBranding.custom_header_image_light_url = \"\";\r\n  }\r\n\r\n  if (validatedFields.data.custom_branding?.custom_header_image_dark_url === \"\" &&\r\n      !validatedFields.data.custom_branding?.pending_dark_header_file) {\r\n    // Get current dark URL from database to delete\r\n    const { data: currentProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_branding\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (currentProfile?.custom_branding?.custom_header_image_dark_url) {\r\n      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_dark_url);\r\n    }\r\n    updatedCustomBranding.custom_header_image_dark_url = \"\";\r\n  }\r\n\r\n  // Remove pending file fields from the data to be saved to database\r\n  delete updatedCustomBranding.pending_light_header_file;\r\n  delete updatedCustomBranding.pending_dark_header_file;\r\n\r\n  // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n  // 7. Prepare data for Supabase update\r\n  const businessHoursData = processBusinessHours(validatedFields.data.business_hours);\r\n\r\n  const dataToUpdate: Partial<BusinessCardData> = {\r\n    business_name: validatedFields.data.business_name,\r\n    member_name: validatedFields.data.member_name,\r\n    title: validatedFields.data.title,\r\n    logo_url: validatedFields.data.logo_url,\r\n    established_year: validatedFields.data.established_year,\r\n    address_line: validatedFields.data.address_line,\r\n    city: validatedFields.data.city,\r\n    state: validatedFields.data.state,\r\n    pincode: validatedFields.data.pincode,\r\n    phone: validatedFields.data.phone,\r\n    delivery_info: validatedFields.data.delivery_info,\r\n    google_maps_url: validatedFields.data.google_maps_url,\r\n    instagram_url: validatedFields.data.instagram_url,\r\n    facebook_url: validatedFields.data.facebook_url,\r\n    whatsapp_number: validatedFields.data.whatsapp_number,\r\n    about_bio: validatedFields.data.about_bio,\r\n    locality: validatedFields.data.locality,\r\n    theme_color: validatedFields.data.theme_color,\r\n    business_hours: businessHoursData,\r\n    status: validatedFields.data.status,\r\n    business_slug: finalSlug,\r\n    contact_email: validatedFields.data.contact_email,\r\n    business_category: validatedFields.data.business_category,\r\n    custom_branding: updatedCustomBranding,\r\n    custom_ads: validatedFields.data.custom_ads,\r\n  };\r\n\r\n  // 7. Update the business profile in Supabase\r\n  const { data: updatedProfile, error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update(dataToUpdate)\r\n    .eq(\"id\", user.id)\r\n    .select(\r\n      `\r\n      id, business_name, member_name, title, logo_url, address_line, city, state, pincode, locality,\r\n      phone, instagram_url, facebook_url, whatsapp_number, about_bio, status, business_slug,\r\n      theme_color, delivery_info, business_hours, contact_email, has_active_subscription,\r\n      trial_end_date, created_at, updated_at, total_likes, total_subscriptions, average_rating,\r\n      business_category, custom_branding, custom_ads, google_maps_url, established_year\r\n    `\r\n    )\r\n    .single();\r\n\r\n  if (updateError) {\r\n    console.error(\"Supabase Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  if (!updatedProfile) {\r\n    return {\r\n      success: false,\r\n      error: \"Failed to update profile. Profile not found after update.\",\r\n    };\r\n  }\r\n\r\n  // 8. Update phone in Supabase auth.users table if phone was changed\r\n  if (\r\n    validatedFields.data.phone &&\r\n    validatedFields.data.phone !== existingProfile.phone\r\n  ) {\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${validatedFields.data.phone}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.warn('Failed to update auth phone field:', authUpdateError.message);\r\n      // Don't fail the operation for this, just log the warning\r\n      // The business_profiles table is updated successfully\r\n    }\r\n  }\r\n\r\n  // 9. Revalidate paths\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  if (dataToUpdate.status === \"online\" && dataToUpdate.business_slug) {\r\n    revalidatePath(`/(main)/card/${dataToUpdate.business_slug}`, \"page\");\r\n  }\r\n\r\n  // 10. Return success response with the updated data\r\n  return { success: true, data: updatedProfile as BusinessCardData };\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgBsB,qBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 373, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/logo/logoActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { getProfileImagePath, getScalableUserPath } from \"@/lib/utils/storage-paths\";\r\nimport {\r\n  LOGO_MAX_SIZE_MB,\r\n  ALLOWED_IMAGE_TYPES,\r\n  STORAGE_BUCKET\r\n} from \"../utils/constants\";\r\n\r\n/**\r\n * Updates only the logo URL in the database\r\n * @param logoUrl - The new logo URL\r\n * @returns Success/error response\r\n */\r\nexport async function updateLogoUrl(\r\n  logoUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Logo URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Deletes logo from storage and updates the database\r\n * @returns Success/error response\r\n */\r\nexport async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // First, get the current logo URL to extract the path\r\n  const { data: profile, error: fetchError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"logo_url\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching profile for logo deletion:\", fetchError);\r\n    return { success: false, error: \"Failed to fetch profile information.\" };\r\n  }\r\n\r\n  // If there's a logo URL, delete the file from storage\r\n  if (profile?.logo_url) {\r\n    try {\r\n      // Extract the file path from the URL\r\n      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');\r\n      if (urlParts.length === 2) {\r\n        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters\r\n\r\n        // Use admin client to delete from storage (required to bypass RLS)\r\n        const adminSupabase = createAdminClient();\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove([filePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting logo from storage:\", deleteError);\r\n          // Continue with database update even if storage deletion fails\r\n        } else {\r\n          console.log(\"Successfully deleted logo from storage:\", filePath);\r\n        }\r\n      } else {\r\n        console.warn(\"Could not parse logo URL for storage deletion:\", profile.logo_url);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing logo URL for deletion:\", error);\r\n      // Continue with database update even if storage deletion fails\r\n    }\r\n  }\r\n\r\n  // Update the database to remove the logo URL\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: null, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Error updating profile after logo deletion:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile after logo deletion: ${updateError.message}`\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Uploads logo file and returns public URL\r\n * @param formData - Form data containing the logo file\r\n * @returns Success/error response with URL\r\n */\r\nexport async function uploadLogoAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"logoFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No logo file provided.\" };\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation\r\n  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {\r\n    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };\r\n  }\r\n\r\n  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);\r\n  const fullPath = getProfileImagePath(userId, timestamp);\r\n\r\n  try {\r\n    // Use admin client for cleanup operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Clean up existing logos in the profile folder\r\n    const userPath = getScalableUserPath(userId);\r\n    const profileFolderPath = `${userPath}/profile/`;\r\n\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .list(profileFolderPath, { limit: 10 });\r\n\r\n    if (!listError && existingFiles && existingFiles.length > 0) {\r\n      const filesToDelete = existingFiles\r\n        .filter(f => f.name.startsWith('logo_'))\r\n        .map(f => `${profileFolderPath}${f.name}`);\r\n\r\n      if (filesToDelete.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove(filesToDelete);\r\n        if (deleteError) {\r\n          console.warn(`Error deleting existing logos:`, deleteError.message);\r\n        }\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Exception during logo deletion check:\", e);\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use the file's original type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Logo Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload logo: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8HsB,sBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/logo/logoActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { getProfileImagePath, getScalableUserPath } from \"@/lib/utils/storage-paths\";\r\nimport {\r\n  LOGO_MAX_SIZE_MB,\r\n  ALLOWED_IMAGE_TYPES,\r\n  STORAGE_BUCKET\r\n} from \"../utils/constants\";\r\n\r\n/**\r\n * Updates only the logo URL in the database\r\n * @param logoUrl - The new logo URL\r\n * @returns Success/error response\r\n */\r\nexport async function updateLogoUrl(\r\n  logoUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Logo URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Deletes logo from storage and updates the database\r\n * @returns Success/error response\r\n */\r\nexport async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // First, get the current logo URL to extract the path\r\n  const { data: profile, error: fetchError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"logo_url\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching profile for logo deletion:\", fetchError);\r\n    return { success: false, error: \"Failed to fetch profile information.\" };\r\n  }\r\n\r\n  // If there's a logo URL, delete the file from storage\r\n  if (profile?.logo_url) {\r\n    try {\r\n      // Extract the file path from the URL\r\n      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');\r\n      if (urlParts.length === 2) {\r\n        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters\r\n\r\n        // Use admin client to delete from storage (required to bypass RLS)\r\n        const adminSupabase = createAdminClient();\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove([filePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting logo from storage:\", deleteError);\r\n          // Continue with database update even if storage deletion fails\r\n        } else {\r\n          console.log(\"Successfully deleted logo from storage:\", filePath);\r\n        }\r\n      } else {\r\n        console.warn(\"Could not parse logo URL for storage deletion:\", profile.logo_url);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing logo URL for deletion:\", error);\r\n      // Continue with database update even if storage deletion fails\r\n    }\r\n  }\r\n\r\n  // Update the database to remove the logo URL\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: null, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Error updating profile after logo deletion:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile after logo deletion: ${updateError.message}`\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Uploads logo file and returns public URL\r\n * @param formData - Form data containing the logo file\r\n * @returns Success/error response with URL\r\n */\r\nexport async function uploadLogoAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"logoFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No logo file provided.\" };\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation\r\n  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {\r\n    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };\r\n  }\r\n\r\n  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);\r\n  const fullPath = getProfileImagePath(userId, timestamp);\r\n\r\n  try {\r\n    // Use admin client for cleanup operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Clean up existing logos in the profile folder\r\n    const userPath = getScalableUserPath(userId);\r\n    const profileFolderPath = `${userPath}/profile/`;\r\n\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .list(profileFolderPath, { limit: 10 });\r\n\r\n    if (!listError && existingFiles && existingFiles.length > 0) {\r\n      const filesToDelete = existingFiles\r\n        .filter(f => f.name.startsWith('logo_'))\r\n        .map(f => `${profileFolderPath}${f.name}`);\r\n\r\n      if (filesToDelete.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove(filesToDelete);\r\n        if (deleteError) {\r\n          console.warn(`Error deleting existing logos:`, deleteError.message);\r\n        }\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Exception during logo deletion check:\", e);\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use the file's original type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Logo Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload logo: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAiBsB,gBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/logo/logoActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { getProfileImagePath, getScalableUserPath } from \"@/lib/utils/storage-paths\";\r\nimport {\r\n  LOGO_MAX_SIZE_MB,\r\n  ALLOWED_IMAGE_TYPES,\r\n  STORAGE_BUCKET\r\n} from \"../utils/constants\";\r\n\r\n/**\r\n * Updates only the logo URL in the database\r\n * @param logoUrl - The new logo URL\r\n * @returns Success/error response\r\n */\r\nexport async function updateLogoUrl(\r\n  logoUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Logo URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Deletes logo from storage and updates the database\r\n * @returns Success/error response\r\n */\r\nexport async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // First, get the current logo URL to extract the path\r\n  const { data: profile, error: fetchError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"logo_url\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching profile for logo deletion:\", fetchError);\r\n    return { success: false, error: \"Failed to fetch profile information.\" };\r\n  }\r\n\r\n  // If there's a logo URL, delete the file from storage\r\n  if (profile?.logo_url) {\r\n    try {\r\n      // Extract the file path from the URL\r\n      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');\r\n      if (urlParts.length === 2) {\r\n        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters\r\n\r\n        // Use admin client to delete from storage (required to bypass RLS)\r\n        const adminSupabase = createAdminClient();\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove([filePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting logo from storage:\", deleteError);\r\n          // Continue with database update even if storage deletion fails\r\n        } else {\r\n          console.log(\"Successfully deleted logo from storage:\", filePath);\r\n        }\r\n      } else {\r\n        console.warn(\"Could not parse logo URL for storage deletion:\", profile.logo_url);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing logo URL for deletion:\", error);\r\n      // Continue with database update even if storage deletion fails\r\n    }\r\n  }\r\n\r\n  // Update the database to remove the logo URL\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: null, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Error updating profile after logo deletion:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile after logo deletion: ${updateError.message}`\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Uploads logo file and returns public URL\r\n * @param formData - Form data containing the logo file\r\n * @returns Success/error response with URL\r\n */\r\nexport async function uploadLogoAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"logoFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No logo file provided.\" };\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation\r\n  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {\r\n    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };\r\n  }\r\n\r\n  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);\r\n  const fullPath = getProfileImagePath(userId, timestamp);\r\n\r\n  try {\r\n    // Use admin client for cleanup operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Clean up existing logos in the profile folder\r\n    const userPath = getScalableUserPath(userId);\r\n    const profileFolderPath = `${userPath}/profile/`;\r\n\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .list(profileFolderPath, { limit: 10 });\r\n\r\n    if (!listError && existingFiles && existingFiles.length > 0) {\r\n      const filesToDelete = existingFiles\r\n        .filter(f => f.name.startsWith('logo_'))\r\n        .map(f => `${profileFolderPath}${f.name}`);\r\n\r\n      if (filesToDelete.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove(filesToDelete);\r\n        if (deleteError) {\r\n          console.warn(`Error deleting existing logos:`, deleteError.message);\r\n        }\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Exception during logo deletion check:\", e);\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use the file's original type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Logo Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload logo: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAmDsB,gBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/hooks/useLogoUpload.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useTransition } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport { uploadLogoAndGetUrl, updateLogoUrl, deleteLogoUrl } from \"../../actions\";\r\nimport { compressImageUltraAggressiveClient } from \"@/lib/utils/client-image-compression\";\r\n\r\nexport type LogoUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface UseLogoUploadOptions {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  initialLogoUrl?: string;\r\n  onUpdateCardData: (_data: Partial<BusinessCardData>) => void;\r\n}\r\n\r\nexport function useLogoUpload({\r\n  form,\r\n  initialLogoUrl = \"\",\r\n  onUpdateCardData\r\n}: UseLogoUploadOptions) {\r\n  const [logoUploadStatus, setLogoUploadStatus] = useState<LogoUploadStatus>(\"idle\");\r\n  const [logoUploadError, setLogoUploadError] = useState<string | null>(null);\r\n  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(null);\r\n  const [isLogoUploading, startLogoUploadTransition] = useTransition();\r\n  const [imageToCrop, setImageToCrop] = useState<string | null>(null);\r\n  const [originalFile, setOriginalFile] = useState<File | null>(null);\r\n\r\n  // File selection handler\r\n  const onFileSelect = (file: File | null) => {\r\n    if (localPreviewUrl) {\r\n      URL.revokeObjectURL(localPreviewUrl);\r\n      setLocalPreviewUrl(null);\r\n    }\r\n\r\n    if (file) {\r\n      if (file.size > 15 * 1024 * 1024) {\r\n        toast.error(\"Image too large\", {\r\n          description: \"Please select an image smaller than 15MB\"\r\n        });\r\n        form.setValue(\"logo_url\", null, { shouldDirty: true });\r\n        onUpdateCardData({ logo_url: null });\r\n        setLogoUploadStatus(\"idle\");\r\n        setLogoUploadError(\"File size must be less than 15MB.\");\r\n        setLocalPreviewUrl(null);\r\n        return;\r\n      }\r\n\r\n      // Prepare for cropping\r\n      setOriginalFile(file);\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        setImageToCrop(reader.result as string);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    } else {\r\n      form.setValue(\"logo_url\", null, { shouldDirty: true });\r\n      onUpdateCardData({ logo_url: null });\r\n      setLogoUploadStatus(\"idle\");\r\n      setLogoUploadError(null);\r\n      setLocalPreviewUrl(null);\r\n    }\r\n  };\r\n\r\n  // Logo upload handler\r\n  const handleLogoUpload = async (file: File) => {\r\n    setLogoUploadStatus(\"uploading\");\r\n    setLogoUploadError(null);\r\n\r\n    startLogoUploadTransition(async () => {\r\n      try {\r\n        // Compress image on client-side first\r\n        const compressionResult = await compressImageUltraAggressiveClient(file, {\r\n          maxDimension: 500,\r\n          targetSizeKB: 60\r\n        });\r\n\r\n        // Convert blob to file for upload\r\n        const compressedFile = new File([compressionResult.blob], file.name, {\r\n          type: compressionResult.blob.type\r\n        });\r\n\r\n        const formData = new FormData();\r\n        formData.append(\"logoFile\", compressedFile);\r\n\r\n        const result = await uploadLogoAndGetUrl(formData);\r\n\r\n        if (result.success && result.url) {\r\n          const newLogoUrl = result.url;\r\n\r\n          // Update form and preview\r\n          form.setValue(\"logo_url\", newLogoUrl, { shouldDirty: true, shouldTouch: true });\r\n          onUpdateCardData({ logo_url: newLogoUrl });\r\n          setLogoUploadStatus(\"success\");\r\n\r\n          // Clean up preview URL\r\n          setLocalPreviewUrl(null);\r\n          if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n\r\n          toast.success(\"Logo uploaded successfully!\");\r\n\r\n          // Save URL to DB immediately\r\n          try {\r\n            const updateResult = await updateLogoUrl(newLogoUrl);\r\n            if (!updateResult.success) {\r\n              toast.error(`Logo uploaded, but failed to save URL: ${updateResult.error}`);\r\n            }\r\n\r\n            // Update form state after successful DB save\r\n            if (updateResult.success) {\r\n              onUpdateCardData({ logo_url: newLogoUrl });\r\n              form.reset({ ...form.getValues(), logo_url: newLogoUrl });\r\n            }\r\n          } catch (err) {\r\n            console.error(\"Error saving logo URL:\", err);\r\n            toast.error(\"Error saving logo URL after upload.\");\r\n          }\r\n        } else {\r\n          setLogoUploadStatus(\"error\");\r\n          const errorMessage = result.error || \"Failed to upload logo.\";\r\n          setLogoUploadError(errorMessage);\r\n          setLocalPreviewUrl(null);\r\n          if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n\r\n          // Show user-friendly error message\r\n          if (errorMessage.includes(\"File size must be less than 15MB\")) {\r\n            toast.error(\"Image too large\", {\r\n              description: \"Please select an image smaller than 15MB\"\r\n            });\r\n          } else if (errorMessage.includes(\"Invalid file type\")) {\r\n            toast.error(\"Invalid file type\", {\r\n              description: \"Please select a JPG, PNG, WebP, or GIF image\"\r\n            });\r\n          } else {\r\n            toast.error(\"Upload failed\", {\r\n              description: errorMessage\r\n            });\r\n          }\r\n\r\n          form.setValue(\"logo_url\", initialLogoUrl || \"\", { shouldDirty: false });\r\n        }\r\n      } catch (compressionError) {\r\n        console.error(\"Image compression failed:\", compressionError);\r\n        setLogoUploadStatus(\"error\");\r\n        setLogoUploadError(\"Failed to process image. Please try a different image.\");\r\n        toast.error(\"Image processing failed\", {\r\n          description: \"Please try a different image or reduce the file size.\"\r\n        });\r\n      }\r\n    });\r\n  };\r\n\r\n  // Handle crop completion\r\n  const handleCropComplete = (croppedBlob: Blob | null) => {\r\n    setImageToCrop(null); // Close dialog\r\n\r\n    if (croppedBlob && originalFile) {\r\n      // Convert blob to file and upload\r\n      const croppedFile = new File([croppedBlob], originalFile.name, { type: 'image/webp' });\r\n      const previewUrl = URL.createObjectURL(croppedFile);\r\n      setLocalPreviewUrl(previewUrl);\r\n      handleLogoUpload(croppedFile);\r\n    } else {\r\n      // Handle crop cancellation or error\r\n      console.log(\"Cropping cancelled or failed.\");\r\n      setOriginalFile(null);\r\n      const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\r\n      if (fileInput) fileInput.value = \"\";\r\n    }\r\n  };\r\n\r\n  // Handle crop dialog close\r\n  const handleCropDialogClose = () => {\r\n    setImageToCrop(null);\r\n    setOriginalFile(null);\r\n    // Clear the file input\r\n    const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\r\n    if (fileInput) fileInput.value = \"\";\r\n  };\r\n\r\n  // State for delete confirmation dialog\r\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n\r\n  // Open delete confirmation dialog\r\n  const openDeleteDialog = () => {\r\n    if (!form.getValues(\"logo_url\")) {\r\n      return; // No logo to delete\r\n    }\r\n    setIsDeleteDialogOpen(true);\r\n  };\r\n\r\n  // Close delete confirmation dialog\r\n  const closeDeleteDialog = () => {\r\n    if (!isDeleting) {\r\n      setIsDeleteDialogOpen(false);\r\n    }\r\n  };\r\n\r\n  // Handle logo deletion after confirmation\r\n  const confirmLogoDelete = async () => {\r\n    setIsDeleting(true);\r\n    setLogoUploadStatus(\"uploading\"); // Reuse the uploading status for deletion\r\n    setLogoUploadError(null);\r\n\r\n    startLogoUploadTransition(async () => {\r\n      try {\r\n        const result = await deleteLogoUrl();\r\n\r\n        if (result.success) {\r\n          // Update form and preview with null instead of empty string\r\n          form.setValue(\"logo_url\", null, { shouldDirty: true, shouldTouch: true });\r\n          onUpdateCardData({ logo_url: null });\r\n          setLogoUploadStatus(\"idle\");\r\n\r\n          // Clean up preview URL\r\n          setLocalPreviewUrl(null);\r\n          if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n\r\n          // Reset file input to ensure it can be used again\r\n          const fileInput = document.querySelector('input[type=\"file\"]') as HTMLInputElement;\r\n          if (fileInput) fileInput.value = \"\";\r\n\r\n          toast.success(\"Logo deleted successfully!\");\r\n\r\n          // Reset form with updated values\r\n          form.reset({ ...form.getValues(), logo_url: null });\r\n        } else {\r\n          setLogoUploadStatus(\"error\");\r\n          setLogoUploadError(result.error || \"Failed to delete logo.\");\r\n          toast.error(result.error || \"Failed to delete logo.\");\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error deleting logo:\", err);\r\n        setLogoUploadStatus(\"error\");\r\n        setLogoUploadError(\"An unexpected error occurred while deleting the logo.\");\r\n        toast.error(\"An unexpected error occurred while deleting the logo.\");\r\n      } finally {\r\n        setIsDeleting(false);\r\n        setIsDeleteDialogOpen(false);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Logo error display component\r\n  const logoErrorDisplay = logoUploadStatus === \"error\" && logoUploadError ? logoUploadError : null;\r\n\r\n  return {\r\n    logoUploadStatus,\r\n    logoUploadError,\r\n    localPreviewUrl,\r\n    isLogoUploading,\r\n    imageToCrop,\r\n    onFileSelect,\r\n    handleLogoUpload,\r\n    handleCropComplete,\r\n    handleCropDialogClose,\r\n    handleLogoDelete: openDeleteDialog, // Renamed for backward compatibility\r\n    logoErrorDisplay,\r\n    isDeleteDialogOpen,\r\n    isDeleting,\r\n    openDeleteDialog,\r\n    closeDeleteDialog,\r\n    confirmLogoDelete\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAGA;AAAA;AAAA;AAAA;AACA;;AAPA;;;;;AAiBO,SAAS,cAAc,EAC5B,IAAI,EACJ,iBAAiB,EAAE,EACnB,gBAAgB,EACK;;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,yBAAyB;IACzB,MAAM,eAAe,CAAC;QACpB,IAAI,iBAAiB;YACnB,IAAI,eAAe,CAAC;YACpB,mBAAmB;QACrB;QAEA,IAAI,MAAM;YACR,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;gBAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mBAAmB;oBAC7B,aAAa;gBACf;gBACA,KAAK,QAAQ,CAAC,YAAY,MAAM;oBAAE,aAAa;gBAAK;gBACpD,iBAAiB;oBAAE,UAAU;gBAAK;gBAClC,oBAAoB;gBACpB,mBAAmB;gBACnB,mBAAmB;gBACnB;YACF;YAEA,uBAAuB;YACvB,gBAAgB;YAChB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,GAAG;gBACjB,eAAe,OAAO,MAAM;YAC9B;YACA,OAAO,aAAa,CAAC;QACvB,OAAO;YACL,KAAK,QAAQ,CAAC,YAAY,MAAM;gBAAE,aAAa;YAAK;YACpD,iBAAiB;gBAAE,UAAU;YAAK;YAClC,oBAAoB;YACpB,mBAAmB;YACnB,mBAAmB;QACrB;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,OAAO;QAC9B,oBAAoB;QACpB,mBAAmB;QAEnB,0BAA0B;YACxB,IAAI;gBACF,sCAAsC;gBACtC,MAAM,oBAAoB,MAAM,CAAA,GAAA,iJAAA,CAAA,qCAAkC,AAAD,EAAE,MAAM;oBACvE,cAAc;oBACd,cAAc;gBAChB;gBAEA,kCAAkC;gBAClC,MAAM,iBAAiB,IAAI,KAAK;oBAAC,kBAAkB,IAAI;iBAAC,EAAE,KAAK,IAAI,EAAE;oBACnE,MAAM,kBAAkB,IAAI,CAAC,IAAI;gBACnC;gBAEA,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,YAAY;gBAE5B,MAAM,SAAS,MAAM,CAAA,GAAA,wMAAA,CAAA,sBAAmB,AAAD,EAAE;gBAEzC,IAAI,OAAO,OAAO,IAAI,OAAO,GAAG,EAAE;oBAChC,MAAM,aAAa,OAAO,GAAG;oBAE7B,0BAA0B;oBAC1B,KAAK,QAAQ,CAAC,YAAY,YAAY;wBAAE,aAAa;wBAAM,aAAa;oBAAK;oBAC7E,iBAAiB;wBAAE,UAAU;oBAAW;oBACxC,oBAAoB;oBAEpB,uBAAuB;oBACvB,mBAAmB;oBACnB,IAAI,iBAAiB,IAAI,eAAe,CAAC;oBAEzC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAEd,6BAA6B;oBAC7B,IAAI;wBACF,MAAM,eAAe,MAAM,CAAA,GAAA,wMAAA,CAAA,gBAAa,AAAD,EAAE;wBACzC,IAAI,CAAC,aAAa,OAAO,EAAE;4BACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uCAAuC,EAAE,aAAa,KAAK,EAAE;wBAC5E;wBAEA,6CAA6C;wBAC7C,IAAI,aAAa,OAAO,EAAE;4BACxB,iBAAiB;gCAAE,UAAU;4BAAW;4BACxC,KAAK,KAAK,CAAC;gCAAE,GAAG,KAAK,SAAS,EAAE;gCAAE,UAAU;4BAAW;wBACzD;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,0BAA0B;wBACxC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF,OAAO;oBACL,oBAAoB;oBACpB,MAAM,eAAe,OAAO,KAAK,IAAI;oBACrC,mBAAmB;oBACnB,mBAAmB;oBACnB,IAAI,iBAAiB,IAAI,eAAe,CAAC;oBAEzC,mCAAmC;oBACnC,IAAI,aAAa,QAAQ,CAAC,qCAAqC;wBAC7D,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,mBAAmB;4BAC7B,aAAa;wBACf;oBACF,OAAO,IAAI,aAAa,QAAQ,CAAC,sBAAsB;wBACrD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,qBAAqB;4BAC/B,aAAa;wBACf;oBACF,OAAO;wBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB;4BAC3B,aAAa;wBACf;oBACF;oBAEA,KAAK,QAAQ,CAAC,YAAY,kBAAkB,IAAI;wBAAE,aAAa;oBAAM;gBACvE;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,oBAAoB;gBACpB,mBAAmB;gBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;oBACrC,aAAa;gBACf;YACF;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAqB,CAAC;QAC1B,eAAe,OAAO,eAAe;QAErC,IAAI,eAAe,cAAc;YAC/B,kCAAkC;YAClC,MAAM,cAAc,IAAI,KAAK;gBAAC;aAAY,EAAE,aAAa,IAAI,EAAE;gBAAE,MAAM;YAAa;YACpF,MAAM,aAAa,IAAI,eAAe,CAAC;YACvC,mBAAmB;YACnB,iBAAiB;QACnB,OAAO;YACL,oCAAoC;YACpC,QAAQ,GAAG,CAAC;YACZ,gBAAgB;YAChB,MAAM,YAAY,SAAS,aAAa,CAAC;YACzC,IAAI,WAAW,UAAU,KAAK,GAAG;QACnC;IACF;IAEA,2BAA2B;IAC3B,MAAM,wBAAwB;QAC5B,eAAe;QACf,gBAAgB;QAChB,uBAAuB;QACvB,MAAM,YAAY,SAAS,aAAa,CAAC;QACzC,IAAI,WAAW,UAAU,KAAK,GAAG;IACnC;IAEA,uCAAuC;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI,CAAC,KAAK,SAAS,CAAC,aAAa;YAC/B,QAAQ,oBAAoB;QAC9B;QACA,sBAAsB;IACxB;IAEA,mCAAmC;IACnC,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY;YACf,sBAAsB;QACxB;IACF;IAEA,0CAA0C;IAC1C,MAAM,oBAAoB;QACxB,cAAc;QACd,oBAAoB,cAAc,0CAA0C;QAC5E,mBAAmB;QAEnB,0BAA0B;YACxB,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,wMAAA,CAAA,gBAAa,AAAD;gBAEjC,IAAI,OAAO,OAAO,EAAE;oBAClB,4DAA4D;oBAC5D,KAAK,QAAQ,CAAC,YAAY,MAAM;wBAAE,aAAa;wBAAM,aAAa;oBAAK;oBACvE,iBAAiB;wBAAE,UAAU;oBAAK;oBAClC,oBAAoB;oBAEpB,uBAAuB;oBACvB,mBAAmB;oBACnB,IAAI,iBAAiB,IAAI,eAAe,CAAC;oBAEzC,kDAAkD;oBAClD,MAAM,YAAY,SAAS,aAAa,CAAC;oBACzC,IAAI,WAAW,UAAU,KAAK,GAAG;oBAEjC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAEd,iCAAiC;oBACjC,KAAK,KAAK,CAAC;wBAAE,GAAG,KAAK,SAAS,EAAE;wBAAE,UAAU;oBAAK;gBACnD,OAAO;oBACL,oBAAoB;oBACpB,mBAAmB,OAAO,KAAK,IAAI;oBACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;gBAC9B;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,oBAAoB;gBACpB,mBAAmB;gBACnB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,cAAc;gBACd,sBAAsB;YACxB;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,qBAAqB,WAAW,kBAAkB,kBAAkB;IAE7F,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,kBAAkB;QAClB;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzPgB;;QAQuC,6JAAA,CAAA,gBAAa", "debugId": null}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/hooks/usePincodeDetails.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport { getPincodeDetails } from \"@/lib/actions/location\";\r\n\r\ninterface UsePincodeDetailsOptions {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  initialPincode?: string;\r\n  initialLocality?: string;\r\n}\r\n\r\nexport function usePincodeDetails({\r\n  form,\r\n  initialPincode,\r\n  initialLocality\r\n}: UsePincodeDetailsOptions) {\r\n  const [isPincodeLoading, setIsPincodeLoading] = useState(false);\r\n  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);\r\n\r\n  // Pincode change handler\r\n  const handlePincodeChange = useCallback(async (pincode: string) => {\r\n    if (pincode.length !== 6) return;\r\n\r\n    setIsPincodeLoading(true);\r\n    setAvailableLocalities([]);\r\n\r\n    // Reset form fields\r\n    form.setValue(\"locality\", \"\");\r\n    form.setValue(\"city\", \"\");\r\n    form.setValue(\"state\", \"\");\r\n\r\n    const result = await getPincodeDetails(pincode);\r\n    setIsPincodeLoading(false);\r\n\r\n    if (result.error) {\r\n      toast.error(result.error);\r\n    } else if (result.city && result.state && result.localities) {\r\n      // Set city and state\r\n      form.setValue(\"city\", result.city, { shouldValidate: true });\r\n      form.setValue(\"state\", result.state, { shouldValidate: true });\r\n\r\n      // Update localities\r\n      setAvailableLocalities(result.localities);\r\n\r\n      // If only one locality, auto-select it\r\n      if (result.localities.length === 1) {\r\n        form.setValue(\"locality\", result.localities[0], {\r\n          shouldValidate: true,\r\n          shouldDirty: false // Don't mark as dirty on auto-fill\r\n        });\r\n      }\r\n\r\n      toast.success(\"City and State auto-filled. Please select your locality.\");\r\n    }\r\n  }, [form]);\r\n\r\n  // Effect to fetch localities on initial load if pincode exists\r\n  useEffect(() => {\r\n    if (!initialPincode || initialPincode.length !== 6) return;\r\n\r\n    const fetchAndValidateLocalities = async (pincode: string) => {\r\n      setIsPincodeLoading(true);\r\n      setAvailableLocalities([]);\r\n\r\n      try {\r\n        const result = await getPincodeDetails(pincode);\r\n\r\n        if (result.error) {\r\n          toast.error(`Failed to fetch details for pincode ${pincode}: ${result.error}`);\r\n          setAvailableLocalities([]);\r\n        } else if (result.city && result.state && result.localities) {\r\n          // Set city/state\r\n          form.setValue(\"city\", result.city, { shouldValidate: true });\r\n          form.setValue(\"state\", result.state, { shouldValidate: true });\r\n          setAvailableLocalities(result.localities);\r\n\r\n          if (initialLocality) {\r\n            const savedLocalityLower = initialLocality.trim().toLowerCase();\r\n            const isValid = result.localities.some(\r\n              (loc) => loc.trim().toLowerCase() === savedLocalityLower\r\n            );\r\n\r\n            if (!isValid) {\r\n              // Saved locality is invalid for this pincode\r\n              toast.warning(\r\n                `Saved locality \"${initialLocality}\" is not valid for pincode ${pincode}. Please re-select.`\r\n              );\r\n              form.setValue(\"locality\", \"\", {\r\n                shouldValidate: true,\r\n                shouldDirty: false // Don't mark as dirty on validation correction\r\n              });\r\n            }\r\n          } else if (result.localities.length === 1) {\r\n            // If no locality saved, but only one option, pre-select it\r\n            form.setValue(\"locality\", result.localities[0], {\r\n              shouldValidate: true,\r\n              shouldDirty: false // Don't mark as dirty on auto-fill\r\n            });\r\n          }\r\n        } else {\r\n          setAvailableLocalities([]);\r\n          toast.warning(`No localities found for pincode ${pincode}.`);\r\n          if (initialLocality) {\r\n            form.setValue(\"locality\", \"\", {\r\n              shouldValidate: true,\r\n              shouldDirty: false // Don't mark as dirty on validation correction\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching pincode details:\", error);\r\n        toast.error(\"An unexpected error occurred while fetching pincode details.\");\r\n        setAvailableLocalities([]);\r\n        if (initialLocality) {\r\n          form.setValue(\"locality\", \"\", {\r\n            shouldValidate: true,\r\n            shouldDirty: false // Don't mark as dirty on validation correction\r\n          });\r\n        }\r\n      } finally {\r\n        setIsPincodeLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchAndValidateLocalities(initialPincode);\r\n  }, [initialPincode, initialLocality, form]);\r\n\r\n  return {\r\n    isPincodeLoading,\r\n    availableLocalities,\r\n    handlePincodeChange\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAGA;;AANA;;;;AAcO,SAAS,kBAAkB,EAChC,IAAI,EACJ,cAAc,EACd,eAAe,EACU;;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE3E,yBAAyB;IACzB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE,OAAO;YAC7C,IAAI,QAAQ,MAAM,KAAK,GAAG;YAE1B,oBAAoB;YACpB,uBAAuB,EAAE;YAEzB,oBAAoB;YACpB,KAAK,QAAQ,CAAC,YAAY;YAC1B,KAAK,QAAQ,CAAC,QAAQ;YACtB,KAAK,QAAQ,CAAC,SAAS;YAEvB,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;YACvC,oBAAoB;YAEpB,IAAI,OAAO,KAAK,EAAE;gBAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK;YAC1B,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU,EAAE;gBAC3D,qBAAqB;gBACrB,KAAK,QAAQ,CAAC,QAAQ,OAAO,IAAI,EAAE;oBAAE,gBAAgB;gBAAK;gBAC1D,KAAK,QAAQ,CAAC,SAAS,OAAO,KAAK,EAAE;oBAAE,gBAAgB;gBAAK;gBAE5D,oBAAoB;gBACpB,uBAAuB,OAAO,UAAU;gBAExC,uCAAuC;gBACvC,IAAI,OAAO,UAAU,CAAC,MAAM,KAAK,GAAG;oBAClC,KAAK,QAAQ,CAAC,YAAY,OAAO,UAAU,CAAC,EAAE,EAAE;wBAC9C,gBAAgB;wBAChB,aAAa,MAAM,mCAAmC;oBACxD;gBACF;gBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF;6DAAG;QAAC;KAAK;IAET,+DAA+D;IAC/D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,kBAAkB,eAAe,MAAM,KAAK,GAAG;YAEpD,MAAM;0EAA6B,OAAO;oBACxC,oBAAoB;oBACpB,uBAAuB,EAAE;oBAEzB,IAAI;wBACF,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;wBAEvC,IAAI,OAAO,KAAK,EAAE;4BAChB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,oCAAoC,EAAE,QAAQ,EAAE,EAAE,OAAO,KAAK,EAAE;4BAC7E,uBAAuB,EAAE;wBAC3B,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU,EAAE;4BAC3D,iBAAiB;4BACjB,KAAK,QAAQ,CAAC,QAAQ,OAAO,IAAI,EAAE;gCAAE,gBAAgB;4BAAK;4BAC1D,KAAK,QAAQ,CAAC,SAAS,OAAO,KAAK,EAAE;gCAAE,gBAAgB;4BAAK;4BAC5D,uBAAuB,OAAO,UAAU;4BAExC,IAAI,iBAAiB;gCACnB,MAAM,qBAAqB,gBAAgB,IAAI,GAAG,WAAW;gCAC7D,MAAM,UAAU,OAAO,UAAU,CAAC,IAAI;sGACpC,CAAC,MAAQ,IAAI,IAAI,GAAG,WAAW,OAAO;;gCAGxC,IAAI,CAAC,SAAS;oCACZ,6CAA6C;oCAC7C,2IAAA,CAAA,QAAK,CAAC,OAAO,CACX,CAAC,gBAAgB,EAAE,gBAAgB,2BAA2B,EAAE,QAAQ,mBAAmB,CAAC;oCAE9F,KAAK,QAAQ,CAAC,YAAY,IAAI;wCAC5B,gBAAgB;wCAChB,aAAa,MAAM,+CAA+C;oCACpE;gCACF;4BACF,OAAO,IAAI,OAAO,UAAU,CAAC,MAAM,KAAK,GAAG;gCACzC,2DAA2D;gCAC3D,KAAK,QAAQ,CAAC,YAAY,OAAO,UAAU,CAAC,EAAE,EAAE;oCAC9C,gBAAgB;oCAChB,aAAa,MAAM,mCAAmC;gCACxD;4BACF;wBACF,OAAO;4BACL,uBAAuB,EAAE;4BACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC;4BAC3D,IAAI,iBAAiB;gCACnB,KAAK,QAAQ,CAAC,YAAY,IAAI;oCAC5B,gBAAgB;oCAChB,aAAa,MAAM,+CAA+C;gCACpE;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;wBACjD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACZ,uBAAuB,EAAE;wBACzB,IAAI,iBAAiB;4BACnB,KAAK,QAAQ,CAAC,YAAY,IAAI;gCAC5B,gBAAgB;gCAChB,aAAa,MAAM,+CAA+C;4BACpE;wBACF;oBACF,SAAU;wBACR,oBAAoB;oBACtB;gBACF;;YAEA,2BAA2B;QAC7B;sCAAG;QAAC;QAAgB;QAAiB;KAAK;IAE1C,OAAO;QACL;QACA;QACA;IACF;AACF;GAzHgB", "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/BasicInfoSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport { User, Building2, MessageSquare, Upload, Info, Briefcase, Mail, Tag, Loader2, Trash2, Calendar } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { CategoryCombobox } from \"@/components/ui/category-combobox\";\r\nimport Image from \"next/image\";\r\n\r\ninterface BasicInfoSectionProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  onFileSelect: (_file: File | null) => void;\r\n  isLogoUploading?: boolean;\r\n  onLogoDelete?: () => void;\r\n}\r\n\r\nexport default function BasicInfoSection({ form, onFileSelect, isLogoUploading = false, onLogoDelete }: BasicInfoSectionProps) {\r\n  const bioLength = form.watch(\"about_bio\")?.length || 0;\r\n\r\n  return (\r\n    <div className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n          <User className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Basic Information\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            Let&apos;s start with the essential details for your business card\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 sm:gap-6\">\r\n        {/* Name and Title Fields - 2 columns on tablet and desktop */}\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6\">\r\n          {/* Name Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"member_name\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <User className=\"h-3.5 w-3.5 text-primary\" />\r\n                  Your Name\r\n                  <span className=\"text-red-500\">*</span>\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      placeholder=\"e.g., Rajesh Mahapatra\"\r\n                      {...field}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                      maxLength={50}\r\n                    />\r\n                    <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500\">\r\n                      {field.value?.length || 0}/50\r\n                    </div>\r\n                  </div>\r\n                </FormControl>\r\n                <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                  Your full name as it will appear on the card\r\n                </FormDescription>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* Title Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"title\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <Briefcase className=\"h-3.5 w-3.5 text-primary\" />\r\n                  Your Title/Designation\r\n                  <span className=\"text-red-500\">*</span>\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      placeholder=\"e.g., Owner, Manager, Developer\"\r\n                      {...field}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                      maxLength={50}\r\n                    />\r\n                    <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500\">\r\n                      {field.value?.length || 0}/50\r\n                    </div>\r\n                  </div>\r\n                </FormControl>\r\n                <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                  Your position or role within the business\r\n                </FormDescription>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        {/* Business Name Field - Full Width */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"business_name\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Building2 className=\"h-3.5 w-3.5 text-primary\" />\r\n                Business Name\r\n                <span className=\"text-red-500\">*</span>\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"relative\">\r\n                  <Input\r\n                    placeholder=\"e.g., Mahapatra Kirana & General Store\"\r\n                    {...field}\r\n                    className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                    maxLength={100}\r\n                  />\r\n                  <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500\">\r\n                    {field.value?.length || 0}/100\r\n                  </div>\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                The name of your business or organization\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Business Category Field - Full Width */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"business_category\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Tag className=\"h-3.5 w-3.5 text-primary\" />\r\n                Business Category\r\n                <span className=\"text-red-500\">*</span>\r\n              </FormLabel>\r\n              <FormControl>\r\n                <CategoryCombobox\r\n                  value={field.value || \"\"}\r\n                  onChange={field.onChange}\r\n                  placeholder=\"Select a business category\"\r\n                  className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                />\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                The category that best describes your business\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Established Year Field - Full Width */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"established_year\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Calendar className=\"h-3.5 w-3.5 text-primary\" />\r\n                Established Year\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"relative\">\r\n                  <Input\r\n                    placeholder=\"e.g., 2015\"\r\n                    {...field}\r\n                    type=\"number\"\r\n                    min=\"1800\"\r\n                    max={new Date().getFullYear()}\r\n                    value={field.value || \"\"}\r\n                    onChange={(e) => {\r\n                      const value = e.target.value;\r\n                      field.onChange(value === \"\" ? null : parseInt(value, 10));\r\n                    }}\r\n                    className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                  />\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                The year your business was established (will be displayed on your card)\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Contact Email Field - Full Width */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"contact_email\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Mail className=\"h-3.5 w-3.5 text-primary\" />\r\n                Contact Email\r\n                <span className=\"text-red-500\">*</span>\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"relative\">\r\n                  <Input\r\n                    placeholder=\"e.g., <EMAIL>\"\r\n                    {...field}\r\n                    type=\"email\"\r\n                    className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                  />\r\n                  <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500\">\r\n                    {field.value?.length || 0}/100\r\n                  </div>\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                Email address for customers to contact you (required)\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Logo Upload Field - Full Width */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"logo_url\"\r\n          render={() => {\r\n            // Get the current logo URL from the form state\r\n            const currentLogoUrl = form.watch(\"logo_url\");\r\n            // Only consider non-empty strings as valid URLs\r\n            const validLogoUrl = currentLogoUrl && currentLogoUrl.trim() !== \"\" ? currentLogoUrl : null;\r\n            const currentFileName = validLogoUrl ? validLogoUrl.split(\"/\").pop()?.split(\"?\")[0] : null;\r\n\r\n            return (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <Upload className=\"h-3.5 w-3.5 text-primary\" />\r\n                  Logo / Profile Photo\r\n                </FormLabel>\r\n\r\n                {/* Display current logo preview if it exists */}\r\n                {currentFileName && (\r\n                  <div className=\"flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg bg-gradient-to-r from-neutral-50 to-neutral-100 dark:from-neutral-800/50 dark:to-neutral-800 border border-neutral-200 dark:border-neutral-700\">\r\n                    {validLogoUrl && (\r\n                      <div className=\"h-10 w-10 sm:h-12 sm:w-12 rounded-md border border-neutral-200 dark:border-neutral-700 overflow-hidden bg-white dark:bg-black flex items-center justify-center shadow-sm\">\r\n                        <Image\r\n                          src={validLogoUrl}\r\n                          alt=\"Current logo\"\r\n                          className=\"h-full w-full object-contain\"\r\n                          width={48}\r\n                          height={48}\r\n                          priority\r\n                        />\r\n                      </div>\r\n                    )}\r\n                    <div className=\"flex-1 min-w-0\">\r\n                      <p className=\"text-xs font-medium text-neutral-800 dark:text-neutral-200 truncate\">\r\n                        {currentFileName}\r\n                      </p>\r\n                      <p className=\"text-xs text-neutral-500 dark:text-neutral-400\">\r\n                        Current logo\r\n                      </p>\r\n                    </div>\r\n                    {onLogoDelete && !isLogoUploading && (\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={onLogoDelete}\r\n                        className=\"p-1.5 rounded-full bg-red-50 dark:bg-red-900/20 text-red-500 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors cursor-pointer\"\r\n                        title=\"Delete logo\"\r\n                        aria-label=\"Delete logo\"\r\n                      >\r\n                        <Trash2 className=\"h-3.5 w-3.5\" />\r\n                      </button>\r\n                    )}\r\n                    {isLogoUploading && (\r\n                      <div className=\"p-1.5 rounded-full bg-neutral-100 dark:bg-neutral-800\">\r\n                        <Loader2 className=\"h-3.5 w-3.5 animate-spin text-neutral-500 dark:text-neutral-400\" />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <label className={`flex flex-col items-center justify-center w-full h-20 sm:h-24 border-2 border-dashed ${isLogoUploading ? 'border-primary/30 bg-primary/5' : 'border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/50 hover:bg-neutral-100 dark:hover:bg-neutral-800'} rounded-lg ${isLogoUploading ? 'cursor-wait' : 'cursor-pointer'} transition-all duration-300`}>\r\n                      <div className=\"flex flex-col items-center justify-center pt-3 pb-3 sm:pt-4 sm:pb-4\">\r\n                        {isLogoUploading ? (\r\n                          <>\r\n                            <div className=\"p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2\">\r\n                              <Loader2 className=\"w-4 h-4 sm:w-5 sm:h-5 text-primary animate-spin\" />\r\n                            </div>\r\n                            <p className=\"text-xs text-primary font-medium\">\r\n                              Uploading logo...\r\n                            </p>\r\n                            <p className=\"text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4\">\r\n                              Please wait while we process your image\r\n                            </p>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <div className=\"p-1.5 sm:p-2 rounded-full bg-primary/10 mb-1 sm:mb-2 hover:bg-primary/20 transition-colors\">\r\n                              <Upload className=\"w-4 h-4 sm:w-5 sm:h-5 text-primary\" />\r\n                            </div>\r\n                            <p className=\"text-xs text-neutral-700 dark:text-neutral-300 font-medium\">\r\n                              {validLogoUrl ? \"Replace logo\" : \"Drop your logo here\"}\r\n                            </p>\r\n                            <p className=\"text-xs text-neutral-500 dark:text-neutral-400 text-center mt-0.5 max-w-xs px-4\">\r\n                              PNG, JPG, GIF or WEBP (Max. 15MB)\r\n                            </p>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                      <Input\r\n                        type=\"file\"\r\n                        accept=\"image/png, image/jpeg, image/gif, image/webp\"\r\n                        className=\"hidden\"\r\n                        onChange={(e) => onFileSelect(e.target.files?.[0] || null)}\r\n                        value={undefined}\r\n                        disabled={isLogoUploading}\r\n                      />\r\n                    </label>\r\n                  </div>\r\n                </FormControl>\r\n\r\n                <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1\">\r\n                  <Info className=\"w-3 h-3\" />\r\n                  Your logo will be displayed prominently on your business card\r\n                </FormDescription>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            );\r\n          }}\r\n        />\r\n\r\n        {/* About/Bio Field */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"about_bio\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <MessageSquare className=\"h-3.5 w-3.5 text-primary\" />\r\n                About / Bio\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"relative\">\r\n                  <Textarea\r\n                    placeholder=\"Tell us about your business or yourself...\"\r\n                    {...field}\r\n                    className=\"min-h-[80px] sm:min-h-[100px] rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 p-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary resize-none transition-all\"\r\n                    maxLength={100}\r\n                  />\r\n                  <div className=\"absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500\">\r\n                    {bioLength}/100\r\n                  </div>\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                A short description that will be displayed on your card\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n\r\n      {/* Pro Tip section */}\r\n      <div className=\"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm\">\r\n        <div className=\"flex items-start gap-2 sm:gap-3\">\r\n        <div className=\"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm\">\r\n            <Info className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n          </div>\r\n          <div>\r\n          <p className=\"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300\">\r\n            Pro Tip\r\n            </p>\r\n            <p className=\"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed\">\r\n              Keep your business name and title concise for better readability on your digital business card.\r\n              A clear, professional photo or logo helps with brand recognition and creates a memorable first impression.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAQA;AACA;AAhBA;;;;;;;;AAyBe,SAAS,iBAAiB,EAAE,IAAI,EAAE,YAAY,EAAE,kBAAkB,KAAK,EAAE,YAAY,EAAyB;IAC3H,MAAM,YAAY,KAAK,KAAK,CAAC,cAAc,UAAU;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAG1F,6LAAC;gCAAE,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAMzE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAA6B;kEAE7C,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DACJ,aAAY;4DACX,GAAG,KAAK;4DACT,WAAU;4DACV,WAAW;;;;;;sEAEb,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,KAAK,EAAE,UAAU;gEAAE;;;;;;;;;;;;;;;;;;0DAIhC,6LAAC,4HAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAsD;;;;;;0DAGjF,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;0CAM7B,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAA6B;kEAElD,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DACJ,aAAY;4DACX,GAAG,KAAK;4DACT,WAAU;4DACV,WAAW;;;;;;sEAEb,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,KAAK,EAAE,UAAU;gEAAE;;;;;;;;;;;;;;;;;;0DAIhC,6LAAC,4HAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAsD;;;;;;0DAGjF,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA6B;0DAElD,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEjC,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDACJ,aAAY;oDACX,GAAG,KAAK;oDACT,WAAU;oDACV,WAAW;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,KAAK,EAAE,UAAU;wDAAE;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAA6B;0DAE5C,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEjC,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,4IAAA,CAAA,mBAAgB;4CACf,OAAO,MAAM,KAAK,IAAI;4CACtB,UAAU,MAAM,QAAQ;4CACxB,aAAY;4CACZ,WAAU;;;;;;;;;;;kDAGd,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA6B;;;;;;;kDAGnD,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACX,GAAG,KAAK;gDACT,MAAK;gDACL,KAAI;gDACJ,KAAK,IAAI,OAAO,WAAW;gDAC3B,OAAO,MAAM,KAAK,IAAI;gDACtB,UAAU,CAAC;oDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAC5B,MAAM,QAAQ,CAAC,UAAU,KAAK,OAAO,SAAS,OAAO;gDACvD;gDACA,WAAU;;;;;;;;;;;;;;;;kDAIhB,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAA6B;0DAE7C,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEjC,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6HAAA,CAAA,QAAK;oDACJ,aAAY;oDACX,GAAG,KAAK;oDACT,MAAK;oDACL,WAAU;;;;;;8DAEZ,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,KAAK,EAAE,UAAU;wDAAE;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ;4BACN,+CAA+C;4BAC/C,MAAM,iBAAiB,KAAK,KAAK,CAAC;4BAClC,gDAAgD;4BAChD,MAAM,eAAe,kBAAkB,eAAe,IAAI,OAAO,KAAK,iBAAiB;4BACvF,MAAM,kBAAkB,eAAe,aAAa,KAAK,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI,CAAC,EAAE,GAAG;4BAEtF,qBACE,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAA6B;;;;;;;oCAKhD,iCACC,6LAAC;wCAAI,WAAU;;4CACZ,8BACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAI;oDACJ,WAAU;oDACV,OAAO;oDACP,QAAQ;oDACR,QAAQ;;;;;;;;;;;0DAId,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV;;;;;;kEAEH,6LAAC;wDAAE,WAAU;kEAAiD;;;;;;;;;;;;4CAI/D,gBAAgB,CAAC,iCAChB,6LAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;gDACV,OAAM;gDACN,cAAW;0DAEX,cAAA,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;4CAGrB,iCACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAM3B,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAM,WAAW,CAAC,qFAAqF,EAAE,kBAAkB,mCAAmC,iIAAiI,YAAY,EAAE,kBAAkB,gBAAgB,iBAAiB,4BAA4B,CAAC;;kEAC5X,6LAAC;wDAAI,WAAU;kEACZ,gCACC;;8EACE,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;8EAErB,6LAAC;oEAAE,WAAU;8EAAmC;;;;;;8EAGhD,6LAAC;oEAAE,WAAU;8EAAkF;;;;;;;yFAKjG;;8EACE,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;8EAEpB,6LAAC;oEAAE,WAAU;8EACV,eAAe,iBAAiB;;;;;;8EAEnC,6LAAC;oEAAE,WAAU;8EAAkF;;;;;;;;;;;;;kEAMrG,6LAAC,6HAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,QAAO;wDACP,WAAU;wDACV,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;wDACrD,OAAO;wDACP,UAAU;;;;;;;;;;;;;;;;;;;;;;kDAMlB,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;;0DACzB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;wBAG7B;;;;;;kCAIF,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAA6B;;;;;;;kDAGxD,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,gIAAA,CAAA,WAAQ;oDACP,aAAY;oDACX,GAAG,KAAK;oDACT,WAAU;oDACV,WAAW;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;wDACZ;wDAAU;;;;;;;;;;;;;;;;;;kDAIjB,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACf,6LAAC;4BAAI,WAAU;sCACX,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;;8CACD,6LAAC;oCAAE,WAAU;8CAAsE;;;;;;8CAGjF,6LAAC;oCAAE,WAAU;8CAA8E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvG;KAzXwB", "debugId": null}}, {"offset": {"line": 1882, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/ContactLocationSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport { MapPin, Phone, Loader2, Globe, Building2, Info, Navigation } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\n\r\ninterface ContactLocationSectionProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  isPincodeLoading: boolean;\r\n  availableLocalities: string[];\r\n  onPincodeChange: (_pincode: string) => void;\r\n}\r\n\r\nexport default function ContactLocationSection({\r\n  form,\r\n  isPincodeLoading,\r\n  availableLocalities,\r\n  onPincodeChange,\r\n}: ContactLocationSectionProps) {\r\n  return (\r\n    <div className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n          <MapPin className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Contact & Location\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            Add your contact information and business location details\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 sm:gap-6\">\r\n        {/* Phone and Address Line Fields - 2 columns on tablet and desktop */}\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6\">\r\n          {/* Phone Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"phone\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <Phone className=\"h-3.5 w-3.5 text-primary\" />\r\n                  Primary Phone\r\n                  <span className=\"text-red-500\">*</span>\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      placeholder=\"9876543210\"\r\n                      type=\"tel\"\r\n                      pattern=\"[0-9]*\"\r\n                      inputMode=\"numeric\"\r\n                      {...field}\r\n                      onChange={(e) => {\r\n                        // Remove any +91 prefix if user enters it\r\n                        let value = e.target.value.replace(/^\\+91/, '');\r\n                        // Only allow numeric input and limit to 10 digits\r\n                        value = value.replace(/\\D/g, '');\r\n                        if (value.length <= 10) {\r\n                          field.onChange(value);\r\n                        }\r\n                      }}\r\n                      onKeyDown={(e) => {\r\n                        // Prevent non-numeric input\r\n                        const isNumeric = /^[0-9]$/.test(e.key);\r\n                        const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);\r\n                        if (!isNumeric && !isControl) {\r\n                          e.preventDefault();\r\n                        }\r\n                      }}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                    />\r\n                  </div>\r\n                </FormControl>\r\n                <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                  Your primary contact number for customers\r\n                </FormDescription>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* Address Line Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"address_line\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <Building2 className=\"h-3.5 w-3.5 text-primary\" />\r\n                  Address Line\r\n                  <span className=\"text-red-500\">*</span>\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      placeholder=\"e.g., Shop No. 12, Main Road\"\r\n                      {...field}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                      maxLength={100}\r\n                    />\r\n                    <div className=\"absolute right-2 top-1/2 transform -translate-y-1/2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500\">\r\n                      {field.value?.length || 0}/100\r\n                    </div>\r\n                  </div>\r\n                </FormControl>\r\n                <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                  Your street address or landmark\r\n                </FormDescription>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        {/* Pincode Field */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"pincode\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Globe className=\"h-3.5 w-3.5 text-primary\" />\r\n                Pincode\r\n                <span className=\"text-red-500\">*</span>\r\n              </FormLabel>\r\n              <div className=\"flex items-center gap-2 sm:gap-3\">\r\n                <FormControl className=\"flex-1\">\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      placeholder=\"e.g., 751001\"\r\n                      {...field}\r\n                      value={field.value ?? \"\"}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                      maxLength={6}\r\n                      type=\"number\"\r\n                      onChange={(e) => {\r\n                        field.onChange(e);\r\n                        if (e.target.value.length === 6) {\r\n                          onPincodeChange(e.target.value);\r\n                        }\r\n                      }}\r\n                      onInput={(e) => {\r\n                        const target = e.target as HTMLInputElement;\r\n                        target.value = target.value.replace(/[^0-9]/g, \"\");\r\n                      }}\r\n                    />\r\n                  </div>\r\n                </FormControl>\r\n                {isPincodeLoading && (\r\n                  <div className=\"p-1.5 rounded-md bg-neutral-100 dark:bg-neutral-800\">\r\n                    <Loader2 className=\"h-4 w-4 sm:h-5 sm:w-5 animate-spin text-primary\" />\r\n                  </div>\r\n                )}\r\n              </div>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                6-digit pincode to auto-fill city and state\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* City and State fields in a responsive grid */}\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6\">\r\n          {/* City Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"city\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <MapPin className=\"h-3.5 w-3.5 text-primary/50\" />\r\n                  City\r\n                  <span className=\"text-red-500\">*</span>\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      placeholder=\"Auto-filled from Pincode\"\r\n                      {...field}\r\n                      value={field.value ?? \"\"}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed\"\r\n                      readOnly\r\n                    />\r\n                  </div>\r\n                </FormControl>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* State Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"state\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <MapPin className=\"h-3.5 w-3.5 text-primary/50\" />\r\n                  State\r\n                  <span className=\"text-red-500\">*</span>\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <Input\r\n                      placeholder=\"Auto-filled from Pincode\"\r\n                      {...field}\r\n                      value={field.value ?? \"\"}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-100/80 dark:bg-neutral-800/40 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 cursor-not-allowed\"\r\n                      readOnly\r\n                    />\r\n                  </div>\r\n                </FormControl>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        {/* Locality Field */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"locality\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <MapPin className=\"h-3.5 w-3.5 text-primary\" />\r\n                Locality / Area\r\n                <span className=\"text-red-500\">*</span>\r\n              </FormLabel>\r\n              <Select\r\n                onValueChange={field.onChange}\r\n                value={field.value ?? \"\"}\r\n                disabled={availableLocalities.length === 0}\r\n              >\r\n                <FormControl>\r\n                  <SelectTrigger\r\n                    className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                    disabled={availableLocalities.length === 0}\r\n                  >\r\n                    <SelectValue\r\n                      placeholder={\r\n                        availableLocalities.length === 0\r\n                          ? \"Enter Pincode first\"\r\n                          : \"Select your locality\"\r\n                      }\r\n                    />\r\n                  </SelectTrigger>\r\n                </FormControl>\r\n                <SelectContent className=\"border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-lg\">\r\n                  {availableLocalities.map((loc) => (\r\n                    <SelectItem key={loc} value={loc} className=\"text-sm focus:bg-primary/10 focus:text-primary dark:focus:bg-primary/20\">\r\n                      {loc}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                Select the specific area within the pincode\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Google Maps URL Field */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"google_maps_url\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Navigation className=\"h-3.5 w-3.5 text-primary\" />\r\n                Google Maps Location\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"relative\">\r\n                  <Input\r\n                    placeholder=\"e.g., https://maps.app.goo.gl/ABC123...\"\r\n                    {...field}\r\n                    value={field.value ?? \"\"}\r\n                    className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 px-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                  />\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                Share your Google Maps location URL to help customers find you easily\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n\r\n      {/* Tip Section */}\r\n      <div className=\"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm\">\r\n        <div className=\"flex items-start gap-2 sm:gap-3\">\r\n          <div className=\"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm\">\r\n            <Info className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300\">\r\n              Location Tip\r\n            </p>\r\n            <p className=\"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed\">\r\n              Adding accurate location details helps customers find you easily. Pincode auto-fills city and state for consistency. Add your Google Maps URL to show a &quot;Get Directions&quot; button on your public card.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAQA;AAdA;;;;;;AA6Be,SAAS,uBAAuB,EAC7C,IAAI,EACJ,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACa;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAG1F,6LAAC;gCAAE,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAMzE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA6B;kEAE9C,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,MAAK;wDACL,SAAQ;wDACR,WAAU;wDACT,GAAG,KAAK;wDACT,UAAU,CAAC;4DACT,0CAA0C;4DAC1C,IAAI,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS;4DAC5C,kDAAkD;4DAClD,QAAQ,MAAM,OAAO,CAAC,OAAO;4DAC7B,IAAI,MAAM,MAAM,IAAI,IAAI;gEACtB,MAAM,QAAQ,CAAC;4DACjB;wDACF;wDACA,WAAW,CAAC;4DACV,4BAA4B;4DAC5B,MAAM,YAAY,UAAU,IAAI,CAAC,EAAE,GAAG;4DACtC,MAAM,YAAY;gEAAC;gEAAa;gEAAU;gEAAa;gEAAc;6DAAM,CAAC,QAAQ,CAAC,EAAE,GAAG;4DAC1F,IAAI,CAAC,aAAa,CAAC,WAAW;gEAC5B,EAAE,cAAc;4DAClB;wDACF;wDACA,WAAU;;;;;;;;;;;;;;;;0DAIhB,6LAAC,4HAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAsD;;;;;;0DAGjF,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;0CAM7B,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAA6B;kEAElD,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6HAAA,CAAA,QAAK;4DACJ,aAAY;4DACX,GAAG,KAAK;4DACT,WAAU;4DACV,WAAW;;;;;;sEAEb,6LAAC;4DAAI,WAAU;;gEACZ,MAAM,KAAK,EAAE,UAAU;gEAAE;;;;;;;;;;;;;;;;;;0DAIhC,6LAAC,4HAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAsD;;;;;;0DAGjF,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAA6B;0DAE9C,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;wDACJ,aAAY;wDACX,GAAG,KAAK;wDACT,OAAO,MAAM,KAAK,IAAI;wDACtB,WAAU;wDACV,WAAW;wDACX,MAAK;wDACL,UAAU,CAAC;4DACT,MAAM,QAAQ,CAAC;4DACf,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;gEAC/B,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAChC;wDACF;wDACA,SAAS,CAAC;4DACR,MAAM,SAAS,EAAE,MAAM;4DACvB,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,OAAO,CAAC,WAAW;wDACjD;;;;;;;;;;;;;;;;4CAIL,kCACC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIzB,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAgC;kEAElD,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;wDACJ,aAAY;wDACX,GAAG,KAAK;wDACT,OAAO,MAAM,KAAK,IAAI;wDACtB,WAAU;wDACV,QAAQ;;;;;;;;;;;;;;;;0DAId,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;0CAM7B,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAgC;kEAElD,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEjC,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;wDACJ,aAAY;wDACX,GAAG,KAAK;wDACT,OAAO,MAAM,KAAK,IAAI;wDACtB,WAAU;wDACV,QAAQ;;;;;;;;;;;;;;;;0DAId,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAA6B;0DAE/C,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEjC,6LAAC,8HAAA,CAAA,SAAM;wCACL,eAAe,MAAM,QAAQ;wCAC7B,OAAO,MAAM,KAAK,IAAI;wCACtB,UAAU,oBAAoB,MAAM,KAAK;;0DAEzC,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC,8HAAA,CAAA,gBAAa;oDACZ,WAAU;oDACV,UAAU,oBAAoB,MAAM,KAAK;8DAEzC,cAAA,6LAAC,8HAAA,CAAA,cAAW;wDACV,aACE,oBAAoB,MAAM,KAAK,IAC3B,wBACA;;;;;;;;;;;;;;;;0DAKZ,6LAAC,8HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACtB,oBAAoB,GAAG,CAAC,CAAC,oBACxB,6LAAC,8HAAA,CAAA,aAAU;wDAAW,OAAO;wDAAK,WAAU;kEACzC;uDADc;;;;;;;;;;;;;;;;kDAMvB,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,iNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAA6B;;;;;;;kDAGrD,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;gDACJ,aAAY;gDACX,GAAG,KAAK;gDACT,OAAO,MAAM,KAAK,IAAI;gDACtB,WAAU;;;;;;;;;;;;;;;;kDAIhB,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAsE;;;;;;8CAGnF,6LAAC;oCAAE,WAAU;8CAA8E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvG;KAjTwB", "debugId": null}}, {"offset": {"line": 2691, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/AppearanceSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport { Palette, Info, RotateCcw } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\n\r\ninterface AppearanceSectionProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  currentUserPlan: \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | null;\r\n}\r\n\r\nexport default function AppearanceSection({\r\n  form,\r\n  currentUserPlan,\r\n}: AppearanceSectionProps) {\r\n  // Show this section for all users\r\n  // No longer restricting based on plan\r\n\r\n  return (\r\n    <div className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n          <Palette className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Appearance\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            Customize your card&apos;s visual appearance\r\n          </p>\r\n        </div>\r\n        {/* Removed Growth+ badge as these features are now available to all users */}\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 sm:gap-6\">\r\n        {/* Theme Color - Visible to all but only editable for Pro and Enterprise */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"theme_color\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Palette className=\"h-3.5 w-3.5 text-primary\" />\r\n                Theme Color\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"flex items-center gap-2 sm:gap-3\">\r\n                  <div className=\"relative flex-grow\">\r\n                    <Palette className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400\" />\r\n                    <Input\r\n                      type=\"text\"\r\n                      {...field}\r\n                      className={`pl-10 w-full font-mono uppercase rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200 ${\r\n                        !(\r\n                          currentUserPlan === \"pro\" ||\r\n                          currentUserPlan === \"enterprise\"\r\n                        )\r\n                          ? \"opacity-60 cursor-not-allowed\"\r\n                          : \"\"\r\n                      }`}\r\n                      value={field.value || \"#F5D76E\"}\r\n                      onChange={(e) => {\r\n                        // Only allow changes for Pro and Enterprise users\r\n                        if (\r\n                          currentUserPlan === \"pro\" ||\r\n                          currentUserPlan === \"enterprise\"\r\n                        ) {\r\n                          const value = e.target.value;\r\n                          if (\r\n                            value === \"\" ||\r\n                            /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6})$/.test(value)\r\n                          ) {\r\n                            field.onChange(value);\r\n                          }\r\n                        }\r\n                      }}\r\n                      disabled={\r\n                        !(\r\n                          currentUserPlan === \"pro\" ||\r\n                          currentUserPlan === \"enterprise\"\r\n                        )\r\n                      }\r\n                    />\r\n                  </div>\r\n                  <Input\r\n                    type=\"color\"\r\n                    {...field}\r\n                    className={`h-8 sm:h-10 w-10 sm:w-12 p-1 rounded-md ${\r\n                      currentUserPlan === \"pro\" ||\r\n                      currentUserPlan === \"enterprise\"\r\n                        ? \"cursor-pointer\"\r\n                        : \"cursor-not-allowed opacity-60\"\r\n                    } border border-neutral-200 dark:border-neutral-700 hover:border-primary dark:hover:border-primary transition-all duration-200`}\r\n                    value={field.value || \"#F5D76E\"}\r\n                    disabled={\r\n                      !(\r\n                        currentUserPlan === \"pro\" ||\r\n                        currentUserPlan === \"enterprise\"\r\n                      )\r\n                    }\r\n                  />\r\n                  <div\r\n                    className=\"h-8 sm:h-10 w-8 sm:w-10 rounded-md border border-neutral-200 dark:border-neutral-700 shadow-inner transition-transform hover:scale-105\"\r\n                    style={{ backgroundColor: field.value || \"#F5D76E\" }}\r\n                  />\r\n                  {/* Reset Button */}\r\n                  {(currentUserPlan === \"pro\" || currentUserPlan === \"enterprise\") && (\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => field.onChange(\"\")}\r\n                      className=\"h-8 sm:h-10 px-2 sm:px-3 text-xs hover:bg-red-50 hover:border-red-200 hover:text-red-600 dark:hover:bg-red-950/20 dark:hover:border-red-800 dark:hover:text-red-400 transition-colors\"\r\n                      title=\"Reset to default color\"\r\n                    >\r\n                      <RotateCcw className=\"h-3 w-3 sm:h-3.5 sm:w-3.5\" />\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1\">\r\n                <Info className=\"w-3 h-3\" />\r\n                {currentUserPlan === \"pro\" || currentUserPlan === \"enterprise\"\r\n                  ? \"Select the primary accent color for your card\"\r\n                  : \"Theme customization is only available for Pro and Enterprise plans\"}\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Card Texture section removed as it doesn't exist in the database */}\r\n      </div>\r\n\r\n      {/* Tip Section for Basic and Growth Plan */}\r\n      {(currentUserPlan === \"basic\" ||\r\n        currentUserPlan === \"growth\" ||\r\n        currentUserPlan === \"trial\") && (\r\n        <div className=\"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-blue-100 dark:border-blue-900/30 shadow-sm\">\r\n          <div className=\"flex items-start gap-2 sm:gap-3\">\r\n            <div className=\"p-1.5 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 mt-0.5 shadow-sm\">\r\n              <Info className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n            </div>\r\n            <div>\r\n              <p className=\"text-xs sm:text-sm font-medium text-blue-800 dark:text-blue-300\">\r\n                Pro Plan Unlocks Custom Theme Colors\r\n              </p>\r\n              <p className=\"text-xs text-blue-700 dark:text-blue-400 mt-0.5 sm:mt-1 leading-relaxed\">\r\n                Upgrade to Pro for more customization options, including custom\r\n                theme colors for your card.\r\n              </p>\r\n            </div>\r\n            <button className=\"ml-auto px-3 sm:px-4 py-1.5 sm:py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-xs sm:text-sm font-medium transition-colors\">\r\n              Upgrade\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;AAqBe,SAAS,kBAAkB,EACxC,IAAI,EACJ,eAAe,EACQ;IACvB,kCAAkC;IAClC,sCAAsC;IAEtC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAG1F,6LAAC;gCAAE,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAOzE,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,4HAAA,CAAA,YAAS;oBACR,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,4HAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,2MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA6B;;;;;;;8CAGlD,6LAAC,4HAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6LAAC,6HAAA,CAAA,QAAK;wDACJ,MAAK;wDACJ,GAAG,KAAK;wDACT,WAAW,CAAC,+OAA+O,EACzP,CAAC,CACC,oBAAoB,SACpB,oBAAoB,YACtB,IACI,kCACA,IACJ;wDACF,OAAO,MAAM,KAAK,IAAI;wDACtB,UAAU,CAAC;4DACT,kDAAkD;4DAClD,IACE,oBAAoB,SACpB,oBAAoB,cACpB;gEACA,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gEAC5B,IACE,UAAU,MACV,qCAAqC,IAAI,CAAC,QAC1C;oEACA,MAAM,QAAQ,CAAC;gEACjB;4DACF;wDACF;wDACA,UACE,CAAC,CACC,oBAAoB,SACpB,oBAAoB,YACtB;;;;;;;;;;;;0DAIN,6LAAC,6HAAA,CAAA,QAAK;gDACJ,MAAK;gDACJ,GAAG,KAAK;gDACT,WAAW,CAAC,wCAAwC,EAClD,oBAAoB,SACpB,oBAAoB,eAChB,mBACA,gCACL,6HAA6H,CAAC;gDAC/H,OAAO,MAAM,KAAK,IAAI;gDACtB,UACE,CAAC,CACC,oBAAoB,SACpB,oBAAoB,YACtB;;;;;;0DAGJ,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,MAAM,KAAK,IAAI;gDAAU;;;;;;4CAGpD,CAAC,oBAAoB,SAAS,oBAAoB,YAAY,mBAC7D,6LAAC,8HAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,MAAM,QAAQ,CAAC;gDAC9B,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8CAK7B,6LAAC,4HAAA,CAAA,kBAAe;oCAAC,WAAU;;sDACzB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,oBAAoB,SAAS,oBAAoB,eAC9C,kDACA;;;;;;;8CAEN,6LAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAS9B,CAAC,oBAAoB,WACpB,oBAAoB,YACpB,oBAAoB,OAAO,mBAC3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAkE;;;;;;8CAG/E,6LAAC;oCAAE,WAAU;8CAA0E;;;;;;;;;;;;sCAKzF,6LAAC;4BAAO,WAAU;sCAA2I;;;;;;;;;;;;;;;;;;;;;;;AAQzK;KAvJwB", "debugId": null}}, {"offset": {"line": 2998, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/ThemeSpecificHeaderUpload.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRef } from \"react\";\r\nimport { Upload, X, Loader2, Sun, Moon } from \"lucide-react\";\r\nimport NextImage from \"next/image\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface ThemeSpecificHeaderUploadProps {\r\n  theme: 'light' | 'dark';\r\n  imageUrl?: string;\r\n  isUploading: boolean;\r\n  isDeleting: boolean;\r\n  isDragging: boolean;\r\n  onFileSelect: (_file: File | null, _theme: 'light' | 'dark') => void;\r\n  onDelete: (_theme: 'light' | 'dark') => void;\r\n  onDrop: (_e: React.DragEvent, _theme: 'light' | 'dark') => void;\r\n  onDragOver: (_e: React.DragEvent, _theme: 'light' | 'dark') => void;\r\n  onDragLeave: (_e: React.DragEvent, _theme: 'light' | 'dark') => void;\r\n}\r\n\r\nexport default function ThemeSpecificHeaderUpload({\r\n  theme,\r\n  imageUrl,\r\n  isUploading,\r\n  isDeleting,\r\n  isDragging,\r\n  onFileSelect,\r\n  onDelete,\r\n  onDrop,\r\n  onDragOver,\r\n  onDragLeave,\r\n}: ThemeSpecificHeaderUploadProps) {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  const ThemeIcon = theme === 'light' ? Sun : Moon;\r\n  const themeLabel = theme === 'light' ? 'Light' : 'Dark';\r\n  const themeColor = theme === 'light' ? 'text-yellow-600' : 'text-blue-600';\r\n  const themeBg = theme === 'light' ? 'bg-yellow-50 dark:bg-yellow-950/20' : 'bg-blue-50 dark:bg-blue-950/20';\r\n  const themeBorder = theme === 'light' ? 'border-yellow-200 dark:border-yellow-800' : 'border-blue-200 dark:border-blue-800';\r\n\r\n  return (\r\n    <div className=\"space-y-2\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <ThemeIcon className={`h-4 w-4 ${themeColor}`} />\r\n        <span className=\"text-sm font-medium\">{themeLabel} Theme</span>\r\n      </div>\r\n\r\n      <Card\r\n        className={`border-dashed border-2 transition-colors cursor-pointer ${\r\n          isDragging\r\n            ? \"border-primary bg-primary/5\"\r\n            : \"border-muted-foreground/25 hover:border-primary/50\"\r\n        }`}\r\n        onDrop={(e) => onDrop(e, theme)}\r\n        onDragOver={(e) => onDragOver(e, theme)}\r\n        onDragLeave={(e) => onDragLeave(e, theme)}\r\n        onClick={() => !isUploading && !isDeleting && fileInputRef.current?.click()}\r\n      >\r\n        <CardContent className=\"p-4\">\r\n          {isUploading ? (\r\n            <div className=\"text-center\">\r\n              <div className=\"w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2\">\r\n                <Loader2 className=\"h-5 w-5 text-muted-foreground animate-spin\" />\r\n              </div>\r\n              <p className=\"text-xs font-medium mb-1\">Uploading...</p>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Processing {themeLabel.toLowerCase()} theme image\r\n              </p>\r\n            </div>\r\n          ) : imageUrl ? (\r\n            <div className=\"space-y-3\">\r\n              <div className=\"relative\">\r\n                <div className={`w-full h-16 overflow-hidden rounded-lg border-2 ${themeBorder} ${themeBg}`}>\r\n                  <NextImage\r\n                    src={imageUrl}\r\n                    alt={`${themeLabel} theme header`}\r\n                    width={200}\r\n                    height={64}\r\n                    className=\"w-full h-full object-contain\"\r\n                  />\r\n                </div>\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"destructive\"\r\n                  size=\"sm\"\r\n                  onClick={(e) => {\r\n                    e.stopPropagation();\r\n                    onDelete(theme);\r\n                  }}\r\n                  disabled={isDeleting}\r\n                  className=\"absolute top-1 right-1 h-6 w-6 p-0\"\r\n                >\r\n                  {isDeleting ? (\r\n                    <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                  ) : (\r\n                    <X className=\"h-3 w-3\" />\r\n                  )}\r\n                </Button>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <p className={`text-xs font-medium ${themeColor}`}>\r\n                  {themeLabel} theme image uploaded\r\n                </p>\r\n                <p className=\"text-xs text-muted-foreground\">\r\n                  Click to replace\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center\">\r\n              <div className=\"w-10 h-10 rounded-lg bg-muted flex items-center justify-center mx-auto mb-2\">\r\n                <Upload className=\"h-5 w-5 text-muted-foreground\" />\r\n              </div>\r\n              <p className=\"text-xs font-medium mb-1\">Upload {themeLabel} Image</p>\r\n              <p className=\"text-xs text-muted-foreground mb-1\">\r\n                Drag & drop or click\r\n              </p>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                PNG • Max 5MB\r\n              </p>\r\n            </div>\r\n          )}\r\n          <input\r\n            ref={fileInputRef}\r\n            type=\"file\"\r\n            accept=\"image/*\"\r\n            onChange={(e) => onFileSelect(e.target.files?.[0] || null, theme)}\r\n            className=\"hidden\"\r\n            disabled={isUploading || isDeleting}\r\n          />\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAqBe,SAAS,0BAA0B,EAChD,KAAK,EACL,QAAQ,EACR,WAAW,EACX,UAAU,EACV,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,UAAU,EACV,WAAW,EACoB;;IAC/B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,YAAY,UAAU,UAAU,mMAAA,CAAA,MAAG,GAAG,qMAAA,CAAA,OAAI;IAChD,MAAM,aAAa,UAAU,UAAU,UAAU;IACjD,MAAM,aAAa,UAAU,UAAU,oBAAoB;IAC3D,MAAM,UAAU,UAAU,UAAU,uCAAuC;IAC3E,MAAM,cAAc,UAAU,UAAU,6CAA6C;IAErF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAU,WAAW,CAAC,QAAQ,EAAE,YAAY;;;;;;kCAC7C,6LAAC;wBAAK,WAAU;;4BAAuB;4BAAW;;;;;;;;;;;;;0BAGpD,6LAAC,4HAAA,CAAA,OAAI;gBACH,WAAW,CAAC,wDAAwD,EAClE,aACI,gCACA,sDACJ;gBACF,QAAQ,CAAC,IAAM,OAAO,GAAG;gBACzB,YAAY,CAAC,IAAM,WAAW,GAAG;gBACjC,aAAa,CAAC,IAAM,YAAY,GAAG;gBACnC,SAAS,IAAM,CAAC,eAAe,CAAC,cAAc,aAAa,OAAO,EAAE;0BAEpE,cAAA,6LAAC,4HAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,4BACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;oCAAE,WAAU;8CAA2B;;;;;;8CACxC,6LAAC;oCAAE,WAAU;;wCAAgC;wCAC/B,WAAW,WAAW;wCAAG;;;;;;;;;;;;mCAGvC,yBACF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,gDAAgD,EAAE,YAAY,CAAC,EAAE,SAAS;sDACzF,cAAA,6LAAC,gIAAA,CAAA,UAAS;gDACR,KAAK;gDACL,KAAK,GAAG,WAAW,aAAa,CAAC;gDACjC,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;;;;;;sDAGd,6LAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,CAAC;gDACR,EAAE,eAAe;gDACjB,SAAS;4CACX;4CACA,UAAU;4CACV,WAAU;sDAET,2BACC,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAInB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAW,CAAC,oBAAoB,EAAE,YAAY;;gDAC9C;gDAAW;;;;;;;sDAEd,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;iDAMjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC;oCAAE,WAAU;;wCAA2B;wCAAQ;wCAAW;;;;;;;8CAC3D,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,6LAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAKjD,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI,MAAM;4BAC3D,WAAU;4BACV,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAMrC;GAjHwB;KAAA", "debugId": null}}, {"offset": {"line": 3293, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/CustomBrandingSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { <PERSON><PERSON>, Type, Eye, EyeOff, Image } from \"lucide-react\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport {\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport { toast } from \"sonner\";\r\nimport { compressImageModerateClient } from \"@/lib/utils/client-image-compression\";\r\n\r\nimport { useTheme } from \"next-themes\";\r\nimport ThemeSpecificHeaderUpload from \"./ThemeSpecificHeaderUpload\";\r\n\r\ninterface CustomBrandingSectionProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  currentUserPlan: \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | null;\r\n}\r\n\r\nexport default function CustomBrandingSection({\r\n  form,\r\n  currentUserPlan,\r\n}: CustomBrandingSectionProps) {\r\n  // Check if user has access to custom branding\r\n  const hasCustomBrandingAccess = currentUserPlan === \"pro\" || currentUserPlan === \"enterprise\";\r\n\r\n  // Theme hook\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  // State for theme-specific header uploads\r\n  const [isDraggingLightHeader, setIsDraggingLightHeader] = useState(false);\r\n  const [isDraggingDarkHeader, setIsDraggingDarkHeader] = useState(false);\r\n\r\n  // Handle custom header text change - automatically toggle hide branding\r\n  const handleHeaderTextChange = (value: string) => {\r\n    // If user types something, automatically enable hide branding\r\n    if (value.trim()) {\r\n      form.setValue(\"custom_branding.hide_dukancard_branding\", true);\r\n    }\r\n    // If user clears the text, disable hide branding\r\n    else {\r\n      form.setValue(\"custom_branding.hide_dukancard_branding\", false);\r\n    }\r\n  };\r\n\r\n  // Handle hide branding toggle change\r\n  const handleHideBrandingToggle = (checked: boolean) => {\r\n    // If turning off hide branding, clear any validation errors\r\n    if (!checked) {\r\n      // Clear validation errors for custom_header_text when toggle is off\r\n      form.clearErrors(\"custom_branding.custom_header_text\");\r\n      // Trigger validation to update the form state\r\n      form.trigger(\"custom_branding\");\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Theme-specific header upload handlers - store files for upload on save\r\n  const handleThemeSpecificHeaderFileSelect = async (file: File | null, theme: 'light' | 'dark') => {\r\n    if (!file) return;\r\n\r\n    // Frontend validation for file type\r\n    if (!file.type.startsWith(\"image/\")) {\r\n      toast.error(\"Please upload a valid image file\");\r\n      return;\r\n    }\r\n\r\n    // Frontend validation for file size (5MB limit)\r\n    const maxSizeBytes = 5 * 1024 * 1024; // 5MB\r\n    if (file.size > maxSizeBytes) {\r\n      const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);\r\n      toast.error(`Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB for optimal performance.`);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Compress image on client-side first\r\n      const compressionResult = await compressImageModerateClient(file, {\r\n        maxDimension: 400, // Smaller size for header images\r\n        targetSizeKB: 150  // Target 150KB max for header images\r\n      });\r\n\r\n      // Convert compressed blob back to file\r\n      const compressedFile = new File([compressionResult.blob], file.name, {\r\n        type: compressionResult.blob.type\r\n      });\r\n\r\n      // Create a local preview URL for immediate feedback\r\n      const previewUrl = URL.createObjectURL(compressedFile);\r\n\r\n      // Store the compressed file and preview URL in form state for upload on save\r\n      if (theme === 'light') {\r\n        form.setValue(\"custom_branding.custom_header_image_light_url\", previewUrl, { shouldDirty: true });\r\n        form.setValue(\"custom_branding.pending_light_header_file\", compressedFile, { shouldDirty: true });\r\n      } else {\r\n        form.setValue(\"custom_branding.custom_header_image_dark_url\", previewUrl, { shouldDirty: true });\r\n        form.setValue(\"custom_branding.pending_dark_header_file\", compressedFile, { shouldDirty: true });\r\n      }\r\n\r\n      form.setValue(\"custom_branding.hide_dukancard_branding\", true, { shouldDirty: true });\r\n      toast.success(`${theme === 'light' ? 'Light' : 'Dark'} theme header image compressed and ready. Click \"Save Changes\" to upload.`);\r\n    } catch (error) {\r\n      console.error(\"Image compression failed:\", error);\r\n      toast.error(\"Failed to process image. Please try a different image.\");\r\n    }\r\n  };\r\n\r\n  const handleThemeSpecificHeaderDelete = (theme: 'light' | 'dark') => {\r\n    // Only update form state, actual deletion will happen on save\r\n    if (theme === 'light') {\r\n      form.setValue(\"custom_branding.custom_header_image_light_url\", \"\", { shouldDirty: true });\r\n      form.setValue(\"custom_branding.pending_light_header_file\", null, { shouldDirty: true });\r\n    } else {\r\n      form.setValue(\"custom_branding.custom_header_image_dark_url\", \"\", { shouldDirty: true });\r\n      form.setValue(\"custom_branding.pending_dark_header_file\", null, { shouldDirty: true });\r\n    }\r\n    toast.success(`${theme === 'light' ? 'Light' : 'Dark'} theme header image will be removed when you save changes`);\r\n  };\r\n\r\n  // Theme-specific drag and drop handlers\r\n  const handleThemeSpecificHeaderDrop = (e: React.DragEvent, theme: 'light' | 'dark') => {\r\n    e.preventDefault();\r\n    const setDragging = theme === 'light' ? setIsDraggingLightHeader : setIsDraggingDarkHeader;\r\n    setDragging(false);\r\n    const files = e.dataTransfer.files;\r\n    if (files.length > 0) {\r\n      handleThemeSpecificHeaderFileSelect(files[0], theme);\r\n    }\r\n  };\r\n\r\n  const handleThemeSpecificHeaderDragOver = (e: React.DragEvent, theme: 'light' | 'dark') => {\r\n    e.preventDefault();\r\n    const setDragging = theme === 'light' ? setIsDraggingLightHeader : setIsDraggingDarkHeader;\r\n    setDragging(true);\r\n  };\r\n\r\n  const handleThemeSpecificHeaderDragLeave = (e: React.DragEvent, theme: 'light' | 'dark') => {\r\n    e.preventDefault();\r\n    const setDragging = theme === 'light' ? setIsDraggingLightHeader : setIsDraggingDarkHeader;\r\n    setDragging(false);\r\n  };\r\n\r\n  if (!hasCustomBrandingAccess) {\r\n    return (\r\n      <div className=\"space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Palette className=\"h-5 w-5 text-amber-600 dark:text-amber-400\" />\r\n          <h3 className=\"text-lg font-semibold text-amber-800 dark:text-amber-200\">\r\n            Custom Branding\r\n          </h3>\r\n          <Badge variant=\"secondary\" className=\"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200\">\r\n            Pro Feature\r\n          </Badge>\r\n        </div>\r\n        <p className=\"text-sm text-amber-700 dark:text-amber-300\">\r\n          Upgrade to Pro or Enterprise plan to access custom branding features including custom logos,\r\n          watermarks, colors, and the ability to hide Dukancard branding.\r\n        </p>\r\n        <Button variant=\"outline\" className=\"border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20\">\r\n          Upgrade to Pro\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <Palette className=\"h-5 w-5 text-primary\" />\r\n        <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-200\">\r\n          Custom Branding\r\n        </h3>\r\n        <Badge variant=\"default\" className=\"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\r\n          {currentUserPlan?.toUpperCase()}\r\n        </Badge>\r\n      </div>\r\n\r\n      <div className=\"space-y-6\">\r\n        {/* Hide Dukancard Branding Toggle */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"custom_branding.hide_dukancard_branding\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-4\">\r\n              <div className=\"space-y-0.5\">\r\n                <FormLabel className=\"text-base font-medium flex items-center gap-2\">\r\n                  {field.value ? <EyeOff className=\"h-4 w-4\" /> : <Eye className=\"h-4 w-4\" />}\r\n                  Hide Dukancard Branding\r\n                </FormLabel>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Remove Dukancard branding from your business card\r\n                </p>\r\n              </div>\r\n              <FormControl>\r\n                <Switch\r\n                  checked={field.value}\r\n                  onCheckedChange={(checked) => {\r\n                    field.onChange(checked);\r\n                    handleHideBrandingToggle(checked);\r\n                  }}\r\n                />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        <Separator />\r\n\r\n        {/* Custom Header Text */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"custom_branding.custom_header_text\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-2\">\r\n              <FormLabel className=\"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Type className=\"h-4 w-4 text-primary\" />\r\n                Custom Header Text\r\n              </FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  value={field.value || \"\"}\r\n                  onChange={(e) => {\r\n                    field.onChange(e.target.value);\r\n                    handleHeaderTextChange(e.target.value);\r\n                  }}\r\n                  placeholder=\"e.g., Powered by YourBrand\"\r\n                  maxLength={50}\r\n                />\r\n              </FormControl>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Custom text to display in the header instead of Dukancard branding (max 50 characters). Required when &quot;Hide Dukancard Branding&quot; is enabled.\r\n              </p>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        <Separator />\r\n\r\n        {/* Theme-Specific Custom Header Images */}\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <FormLabel className=\"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n              <Image className=\"h-4 w-4 text-primary\" aria-label=\"Theme specific header\" />\r\n              Theme-Specific Header Images (Alternative to Text)\r\n            </FormLabel>\r\n            <p className=\"text-xs text-muted-foreground\">\r\n              Upload custom images for light and dark themes. Images will automatically switch based on the user&apos;s theme preference. PNG format recommended for best quality.\r\n            </p>\r\n            <div className=\"flex items-center gap-2 text-xs text-muted-foreground\">\r\n              <span className=\"flex items-center gap-1\">\r\n                <span className={`w-2 h-2 rounded-full ${resolvedTheme === 'light' ? 'bg-yellow-500' : 'bg-gray-400'}`}></span>\r\n                Current: {resolvedTheme === 'light' ? 'Light' : resolvedTheme === 'dark' ? 'Dark' : 'System'}\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Light and Dark Theme Upload Cards */}\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n            <ThemeSpecificHeaderUpload\r\n              theme=\"light\"\r\n              imageUrl={form.watch(\"custom_branding.custom_header_image_light_url\")}\r\n              isUploading={false}\r\n              isDeleting={false}\r\n              isDragging={isDraggingLightHeader}\r\n              onFileSelect={handleThemeSpecificHeaderFileSelect}\r\n              onDelete={handleThemeSpecificHeaderDelete}\r\n              onDrop={handleThemeSpecificHeaderDrop}\r\n              onDragOver={handleThemeSpecificHeaderDragOver}\r\n              onDragLeave={handleThemeSpecificHeaderDragLeave}\r\n            />\r\n\r\n            <ThemeSpecificHeaderUpload\r\n              theme=\"dark\"\r\n              imageUrl={form.watch(\"custom_branding.custom_header_image_dark_url\")}\r\n              isUploading={false}\r\n              isDeleting={false}\r\n              isDragging={isDraggingDarkHeader}\r\n              onFileSelect={handleThemeSpecificHeaderFileSelect}\r\n              onDelete={handleThemeSpecificHeaderDelete}\r\n              onDrop={handleThemeSpecificHeaderDrop}\r\n              onDragOver={handleThemeSpecificHeaderDragOver}\r\n              onDragLeave={handleThemeSpecificHeaderDragLeave}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <Separator />\r\n\r\n        {/* Theme Color Note */}\r\n        <div className=\"bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\r\n          <div className=\"flex items-start gap-3\">\r\n            <div className=\"w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center flex-shrink-0 mt-0.5\">\r\n              <Palette className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\r\n            </div>\r\n            <div>\r\n              <h4 className=\"text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1\">\r\n                Theme Color Customization\r\n              </h4>\r\n              <p className=\"text-sm text-blue-700 dark:text-blue-300\">\r\n                Customize your business card&apos;s theme color in the <strong>Appearance</strong> section above.\r\n                Pro and Enterprise users can choose any custom color, while other plans use the default gold theme.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AAtBA;;;;;;;;;;;;;AA6Be,SAAS,sBAAsB,EAC5C,IAAI,EACJ,eAAe,EACY;;IAC3B,8CAA8C;IAC9C,MAAM,0BAA0B,oBAAoB,SAAS,oBAAoB;IAEjF,aAAa;IACb,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEjC,0CAA0C;IAC1C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,wEAAwE;IACxE,MAAM,yBAAyB,CAAC;QAC9B,8DAA8D;QAC9D,IAAI,MAAM,IAAI,IAAI;YAChB,KAAK,QAAQ,CAAC,2CAA2C;QAC3D,OAEK;YACH,KAAK,QAAQ,CAAC,2CAA2C;QAC3D;IACF;IAEA,qCAAqC;IACrC,MAAM,2BAA2B,CAAC;QAChC,4DAA4D;QAC5D,IAAI,CAAC,SAAS;YACZ,oEAAoE;YACpE,KAAK,WAAW,CAAC;YACjB,8CAA8C;YAC9C,KAAK,OAAO,CAAC;QACf;IACF;IAIA,yEAAyE;IACzE,MAAM,sCAAsC,OAAO,MAAmB;QACpE,IAAI,CAAC,MAAM;QAEX,oCAAoC;QACpC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gDAAgD;QAChD,MAAM,eAAe,IAAI,OAAO,MAAM,MAAM;QAC5C,IAAI,KAAK,IAAI,GAAG,cAAc;YAC5B,MAAM,aAAa,CAAC,KAAK,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;YACvD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,WAAW,kFAAkF,CAAC;YACzH;QACF;QAEA,IAAI;YACF,sCAAsC;YACtC,MAAM,oBAAoB,MAAM,CAAA,GAAA,iJAAA,CAAA,8BAA2B,AAAD,EAAE,MAAM;gBAChE,cAAc;gBACd,cAAc,IAAK,qCAAqC;YAC1D;YAEA,uCAAuC;YACvC,MAAM,iBAAiB,IAAI,KAAK;gBAAC,kBAAkB,IAAI;aAAC,EAAE,KAAK,IAAI,EAAE;gBACnE,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACnC;YAEA,oDAAoD;YACpD,MAAM,aAAa,IAAI,eAAe,CAAC;YAEvC,6EAA6E;YAC7E,IAAI,UAAU,SAAS;gBACrB,KAAK,QAAQ,CAAC,iDAAiD,YAAY;oBAAE,aAAa;gBAAK;gBAC/F,KAAK,QAAQ,CAAC,6CAA6C,gBAAgB;oBAAE,aAAa;gBAAK;YACjG,OAAO;gBACL,KAAK,QAAQ,CAAC,gDAAgD,YAAY;oBAAE,aAAa;gBAAK;gBAC9F,KAAK,QAAQ,CAAC,4CAA4C,gBAAgB;oBAAE,aAAa;gBAAK;YAChG;YAEA,KAAK,QAAQ,CAAC,2CAA2C,MAAM;gBAAE,aAAa;YAAK;YACnF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,UAAU,UAAU,UAAU,OAAO,yEAAyE,CAAC;QAClI,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kCAAkC,CAAC;QACvC,8DAA8D;QAC9D,IAAI,UAAU,SAAS;YACrB,KAAK,QAAQ,CAAC,iDAAiD,IAAI;gBAAE,aAAa;YAAK;YACvF,KAAK,QAAQ,CAAC,6CAA6C,MAAM;gBAAE,aAAa;YAAK;QACvF,OAAO;YACL,KAAK,QAAQ,CAAC,gDAAgD,IAAI;gBAAE,aAAa;YAAK;YACtF,KAAK,QAAQ,CAAC,4CAA4C,MAAM;gBAAE,aAAa;YAAK;QACtF;QACA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,UAAU,UAAU,UAAU,OAAO,yDAAyD,CAAC;IAClH;IAEA,wCAAwC;IACxC,MAAM,gCAAgC,CAAC,GAAoB;QACzD,EAAE,cAAc;QAChB,MAAM,cAAc,UAAU,UAAU,2BAA2B;QACnE,YAAY;QACZ,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,oCAAoC,KAAK,CAAC,EAAE,EAAE;QAChD;IACF;IAEA,MAAM,oCAAoC,CAAC,GAAoB;QAC7D,EAAE,cAAc;QAChB,MAAM,cAAc,UAAU,UAAU,2BAA2B;QACnE,YAAY;IACd;IAEA,MAAM,qCAAqC,CAAC,GAAoB;QAC9D,EAAE,cAAc;QAChB,MAAM,cAAc,UAAU,UAAU,2BAA2B;QACnE,YAAY;IACd;IAEA,IAAI,CAAC,yBAAyB;QAC5B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,6LAAC,6HAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAAoE;;;;;;;;;;;;8BAI3G,6LAAC;oBAAE,WAAU;8BAA6C;;;;;;8BAI1D,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,WAAU;8BAA0H;;;;;;;;;;;;IAKpK;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,2MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAG7E,6LAAC,6HAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;kCAChC,iBAAiB;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;oDAClB,MAAM,KAAK,iBAAG,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;+EAAe,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAa;;;;;;;0DAG9E,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,MAAM,KAAK;4CACpB,iBAAiB,CAAC;gDAChB,MAAM,QAAQ,CAAC;gDACf,yBAAyB;4CAC3B;;;;;;;;;;;;;;;;;;;;;;kCAOV,6LAAC,iIAAA,CAAA,YAAS;;;;;kCAGV,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAyB;;;;;;;kDAG3C,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,OAAO,MAAM,KAAK,IAAI;4CACtB,UAAU,CAAC;gDACT,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;gDAC7B,uBAAuB,EAAE,MAAM,CAAC,KAAK;4CACvC;4CACA,aAAY;4CACZ,WAAW;;;;;;;;;;;kDAGf,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kCAKlB,6LAAC,iIAAA,CAAA,YAAS;;;;;kCAGV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;gDAAuB,cAAW;;;;;;4CAA0B;;;;;;;kDAG/E,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAK,WAAW,CAAC,qBAAqB,EAAE,kBAAkB,UAAU,kBAAkB,eAAe;;;;;;gDAAS;gDACrG,kBAAkB,UAAU,UAAU,kBAAkB,SAAS,SAAS;;;;;;;;;;;;;;;;;;0CAM1F,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAyB;wCACxB,OAAM;wCACN,UAAU,KAAK,KAAK,CAAC;wCACrB,aAAa;wCACb,YAAY;wCACZ,YAAY;wCACZ,cAAc;wCACd,UAAU;wCACV,QAAQ;wCACR,YAAY;wCACZ,aAAa;;;;;;kDAGf,6LAAC,oNAAA,CAAA,UAAyB;wCACxB,OAAM;wCACN,UAAU,KAAK,KAAK,CAAC;wCACrB,aAAa;wCACb,YAAY;wCACZ,YAAY;wCACZ,cAAc;wCACd,UAAU;wCACV,QAAQ;wCACR,YAAY;wCACZ,aAAa;;;;;;;;;;;;;;;;;;kCAKnB,6LAAC,iIAAA,CAAA,YAAS;;;;;kCAGV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,6LAAC;4CAAE,WAAU;;gDAA2C;8DACC,6LAAC;8DAAO;;;;;;gDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlG;GApSwB;;QAQI,mJAAA,CAAA,WAAQ;;;KARZ", "debugId": null}}, {"offset": {"line": 3909, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions/customAdUpload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getCustomAdImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface CustomAdUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface CustomAdUpdateResult {\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload custom ad image with compression and auto-save to database\r\n */\r\nexport async function uploadCustomAdImage(\r\n  formData: FormData\r\n): Promise<CustomAdUploadResult> {\r\n  try {\r\n    // Create admin client for storage operations\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Extract the cropped image file from FormData\r\n    const imageFile = formData.get(\"image\") as File;\r\n    if (!imageFile) {\r\n      return {\r\n        success: false,\r\n        error: \"No image file provided\",\r\n      };\r\n    }\r\n\r\n    // Validate file type\r\n    if (!imageFile.type.startsWith(\"image/\")) {\r\n      return {\r\n        success: false,\r\n        error: \"Invalid file type. Please upload an image.\",\r\n      };\r\n    }\r\n\r\n    // Validate file size (max 15MB before compression)\r\n    if (imageFile.size > 15 * 1024 * 1024) {\r\n      return {\r\n        success: false,\r\n        error: \"File too large. Maximum size is 15MB.\",\r\n      };\r\n    }\r\n\r\n    const bucketName = \"business\";\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const imagePath = getCustomAdImagePath(user.id, timestamp);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Custom Ad Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    // Auto-save to database - update custom_ads field\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: true,\r\n          image_url: urlData.publicUrl,\r\n          link_url: \"\", // Will be updated separately\r\n          uploaded_at: new Date().toISOString(),\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\"Database update error:\", updateError);\r\n      // Image uploaded successfully but database update failed\r\n      // We could delete the image here, but let's keep it and return success\r\n      // The user can try again\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad upload error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred during upload.\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update custom ad link URL\r\n */\r\nexport async function updateCustomAdLink(linkUrl: string): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Validate URL if provided\r\n    if (linkUrl && linkUrl.trim()) {\r\n      try {\r\n        new URL(linkUrl);\r\n      } catch {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid URL format\",\r\n        };\r\n      }\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the link_url field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      link_url: linkUrl.trim(),\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to update ad link\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad link update error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Toggle custom ad enabled/disabled state\r\n */\r\nexport async function toggleCustomAd(enabled: boolean): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the enabled field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      enabled,\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to toggle ad state\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad toggle error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete custom ad image and reset data\r\n */\r\nexport async function deleteCustomAd(): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // First, get the current custom ad data to extract the image URL\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    const currentCustomAds = profile?.custom_ads;\r\n    const imageUrl = currentCustomAds?.image_url;\r\n\r\n    // Delete the image from storage if it exists\r\n    if (imageUrl) {\r\n      try {\r\n        // Extract the file path from the URL\r\n        // URL format: https://domain.supabase.co/storage/v1/object/public/business/users/xx/xx/userId/ads/custom_ad_timestamp.webp\r\n        const urlParts = imageUrl.split('/storage/v1/object/public/business/');\r\n        if (urlParts.length === 2) {\r\n          const filePath = urlParts[1];\r\n\r\n          // Use admin client to delete from storage\r\n          const adminSupabase = createAdminClient();\r\n          const { error: deleteError } = await adminSupabase.storage\r\n            .from(\"business\")\r\n            .remove([filePath]);\r\n\r\n          if (deleteError) {\r\n            console.error(\"Storage deletion error:\", deleteError);\r\n            // Continue with database update even if storage deletion fails\r\n          }\r\n        }\r\n      } catch (storageError) {\r\n        console.error(\"Error deleting custom ad from storage:\", storageError);\r\n        // Continue with database update even if storage deletion fails\r\n      }\r\n    }\r\n\r\n    // Reset custom_ads data in database\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: false,\r\n          image_url: \"\",\r\n          link_url: \"\",\r\n          uploaded_at: null,\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to delete custom ad\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad delete error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoBsB,sBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3925, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions/customAdUpload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getCustomAdImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface CustomAdUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface CustomAdUpdateResult {\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload custom ad image with compression and auto-save to database\r\n */\r\nexport async function uploadCustomAdImage(\r\n  formData: FormData\r\n): Promise<CustomAdUploadResult> {\r\n  try {\r\n    // Create admin client for storage operations\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Extract the cropped image file from FormData\r\n    const imageFile = formData.get(\"image\") as File;\r\n    if (!imageFile) {\r\n      return {\r\n        success: false,\r\n        error: \"No image file provided\",\r\n      };\r\n    }\r\n\r\n    // Validate file type\r\n    if (!imageFile.type.startsWith(\"image/\")) {\r\n      return {\r\n        success: false,\r\n        error: \"Invalid file type. Please upload an image.\",\r\n      };\r\n    }\r\n\r\n    // Validate file size (max 15MB before compression)\r\n    if (imageFile.size > 15 * 1024 * 1024) {\r\n      return {\r\n        success: false,\r\n        error: \"File too large. Maximum size is 15MB.\",\r\n      };\r\n    }\r\n\r\n    const bucketName = \"business\";\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const imagePath = getCustomAdImagePath(user.id, timestamp);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Custom Ad Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    // Auto-save to database - update custom_ads field\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: true,\r\n          image_url: urlData.publicUrl,\r\n          link_url: \"\", // Will be updated separately\r\n          uploaded_at: new Date().toISOString(),\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\"Database update error:\", updateError);\r\n      // Image uploaded successfully but database update failed\r\n      // We could delete the image here, but let's keep it and return success\r\n      // The user can try again\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad upload error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred during upload.\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update custom ad link URL\r\n */\r\nexport async function updateCustomAdLink(linkUrl: string): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Validate URL if provided\r\n    if (linkUrl && linkUrl.trim()) {\r\n      try {\r\n        new URL(linkUrl);\r\n      } catch {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid URL format\",\r\n        };\r\n      }\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the link_url field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      link_url: linkUrl.trim(),\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to update ad link\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad link update error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Toggle custom ad enabled/disabled state\r\n */\r\nexport async function toggleCustomAd(enabled: boolean): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the enabled field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      enabled,\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to toggle ad state\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad toggle error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete custom ad image and reset data\r\n */\r\nexport async function deleteCustomAd(): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // First, get the current custom ad data to extract the image URL\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    const currentCustomAds = profile?.custom_ads;\r\n    const imageUrl = currentCustomAds?.image_url;\r\n\r\n    // Delete the image from storage if it exists\r\n    if (imageUrl) {\r\n      try {\r\n        // Extract the file path from the URL\r\n        // URL format: https://domain.supabase.co/storage/v1/object/public/business/users/xx/xx/userId/ads/custom_ad_timestamp.webp\r\n        const urlParts = imageUrl.split('/storage/v1/object/public/business/');\r\n        if (urlParts.length === 2) {\r\n          const filePath = urlParts[1];\r\n\r\n          // Use admin client to delete from storage\r\n          const adminSupabase = createAdminClient();\r\n          const { error: deleteError } = await adminSupabase.storage\r\n            .from(\"business\")\r\n            .remove([filePath]);\r\n\r\n          if (deleteError) {\r\n            console.error(\"Storage deletion error:\", deleteError);\r\n            // Continue with database update even if storage deletion fails\r\n          }\r\n        }\r\n      } catch (storageError) {\r\n        console.error(\"Error deleting custom ad from storage:\", storageError);\r\n        // Continue with database update even if storage deletion fails\r\n      }\r\n    }\r\n\r\n    // Reset custom_ads data in database\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: false,\r\n          image_url: \"\",\r\n          link_url: \"\",\r\n          uploaded_at: null,\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to delete custom ad\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad delete error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAuQsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 3941, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CustomAdCropDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback, useEffect } from \"react\";\r\nimport <PERSON><PERSON><PERSON>, { Point, Area } from \"react-easy-crop\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogHeader,\r\n  <PERSON><PERSON>T<PERSON>le,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { Loader2, Crop } from \"lucide-react\";\r\nimport { Slider } from \"@/components/ui/slider\";\r\n\r\n// Helper function to create an image element\r\nconst createImage = (url: string): Promise<HTMLImageElement> =>\r\n  new Promise((resolve, reject) => {\r\n    const image = new Image();\r\n    image.addEventListener(\"load\", () => resolve(image));\r\n    image.addEventListener(\"error\", (error) => reject(error));\r\n    image.setAttribute(\"crossOrigin\", \"anonymous\");\r\n    image.src = url;\r\n  });\r\n\r\n// Helper function to get the cropped image blob\r\nasync function getCroppedImgBlob(\r\n  imageSrc: string,\r\n  pixelCrop: Area\r\n): Promise<Blob | null> {\r\n  const image = await createImage(imageSrc);\r\n  const canvas = document.createElement(\"canvas\");\r\n  const ctx = canvas.getContext(\"2d\");\r\n\r\n  if (!ctx) {\r\n    return null;\r\n  }\r\n\r\n  const scaleX = image.naturalWidth / image.width;\r\n  const scaleY = image.naturalHeight / image.height;\r\n  const pixelRatio = window.devicePixelRatio || 1;\r\n\r\n  canvas.width = pixelCrop.width * pixelRatio * scaleX;\r\n  canvas.height = pixelCrop.height * pixelRatio * scaleY;\r\n\r\n  ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\r\n  ctx.imageSmoothingQuality = \"high\";\r\n\r\n  ctx.drawImage(\r\n    image,\r\n    pixelCrop.x * scaleX,\r\n    pixelCrop.y * scaleY,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY,\r\n    0,\r\n    0,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY\r\n  );\r\n\r\n  return new Promise((resolve) => {\r\n    canvas.toBlob(\r\n      resolve,\r\n      \"image/png\" // Output as PNG from canvas\r\n    );\r\n  });\r\n}\r\n\r\ninterface CustomAdCropDialogProps {\r\n  imgSrc: string | null;\r\n  onCropComplete: (_blob: Blob | null) => void | Promise<void>;\r\n  onClose: () => void;\r\n  isOpen: boolean;\r\n  isUploading?: boolean; // Add external upload state\r\n}\r\n\r\nexport default function CustomAdCropDialog({\r\n  imgSrc,\r\n  onCropComplete,\r\n  onClose,\r\n  isOpen,\r\n  isUploading = false,\r\n}: CustomAdCropDialogProps) {\r\n  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);\r\n  const [isCropping, setIsCropping] = useState(false);\r\n\r\n  const onCropCompleteCallback = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {\r\n    setCroppedAreaPixels(croppedAreaPixels);\r\n  }, []);\r\n\r\n  const handleCrop = async () => {\r\n    if (!imgSrc || !croppedAreaPixels) {\r\n      console.warn(\"Image source or crop area not available.\");\r\n      onCropComplete(null);\r\n      return;\r\n    }\r\n    setIsCropping(true);\r\n    try {\r\n      const croppedBlob = await getCroppedImgBlob(imgSrc, croppedAreaPixels);\r\n      onCropComplete(croppedBlob);\r\n    } catch (e) {\r\n      console.error(\"Error cropping image:\", e);\r\n      onCropComplete(null);\r\n    } finally {\r\n      setIsCropping(false);\r\n      // Note: Dialog will stay open until parent component sets isUploading to false\r\n    }\r\n  };\r\n\r\n  // Reset zoom when dialog opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      setZoom(1);\r\n      setCrop({ x: 0, y: 0 });\r\n    }\r\n  }, [isOpen]);\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && !isUploading && !isCropping && onClose()}>\r\n      <DialogContent className=\"sm:max-w-[700px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <Crop className=\"h-5 w-5 text-primary\" />\r\n            Crop Your Advertisement\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"space-y-4\">\r\n          {/* Crop area */}\r\n          <div className=\"relative h-[45vh] md:h-[55vh] w-full bg-neutral-100 dark:bg-neutral-800 rounded-lg overflow-hidden border-2 border-primary/20\">\r\n            {imgSrc ? (\r\n              <Cropper\r\n                image={imgSrc}\r\n                crop={crop}\r\n                zoom={zoom}\r\n                aspect={16 / 9} // 16:9 aspect ratio for custom ads\r\n                cropShape=\"rect\"\r\n                showGrid={true}\r\n                onCropChange={setCrop}\r\n                onZoomChange={setZoom}\r\n                onCropComplete={onCropCompleteCallback}\r\n                style={{\r\n                  containerStyle: {\r\n                    borderRadius: '8px',\r\n                    overflow: 'hidden',\r\n                  },\r\n                  cropAreaStyle: {\r\n                    border: '2px solid hsl(var(--primary))',\r\n                    borderRadius: '4px',\r\n                  },\r\n                }}\r\n              />\r\n            ) : (\r\n              <div className=\"flex items-center justify-center h-full\">\r\n                <div className=\"text-center\">\r\n                  <Loader2 className=\"h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground\" />\r\n                  <p className=\"text-sm text-muted-foreground\">Loading image...</p>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Zoom control */}\r\n          <div className=\"space-y-2\">\r\n            <label className=\"text-sm font-medium text-neutral-700 dark:text-neutral-300\">\r\n              Zoom: {Math.round(zoom * 100)}%\r\n            </label>\r\n            <Slider\r\n              value={[zoom]}\r\n              onValueChange={(value) => setZoom(value[0])}\r\n              min={1}\r\n              max={3}\r\n              step={0.1}\r\n              className=\"w-full\"\r\n            />\r\n          </div>\r\n\r\n          {/* Preview info */}\r\n          <div className=\"bg-neutral-50 dark:bg-neutral-900 rounded-lg p-3 border\">\r\n            <div className=\"flex items-center justify-center text-sm\">\r\n              <span className=\"text-muted-foreground\">Cropping to 16:9 aspect ratio for advertisement display</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <DialogFooter className=\"gap-2\">\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={onClose}\r\n            disabled={isCropping || isUploading}\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleCrop}\r\n            disabled={isCropping || isUploading || !croppedAreaPixels}\r\n            className=\"min-w-[120px]\"\r\n          >\r\n            {isCropping || isUploading ? (\r\n              <>\r\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                {isCropping ? \"Processing...\" : \"Uploading...\"}\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Crop className=\"mr-2 h-4 w-4\" />\r\n                Upload Ad\r\n              </>\r\n            )}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AAAA;AACA;;;AAbA;;;;;;;AAeA,6CAA6C;AAC7C,MAAM,cAAc,CAAC,MACnB,IAAI,QAAQ,CAAC,SAAS;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,gBAAgB,CAAC,QAAQ,IAAM,QAAQ;QAC7C,MAAM,gBAAgB,CAAC,SAAS,CAAC,QAAU,OAAO;QAClD,MAAM,YAAY,CAAC,eAAe;QAClC,MAAM,GAAG,GAAG;IACd;AAEF,gDAAgD;AAChD,eAAe,kBACb,QAAgB,EAChB,SAAe;IAEf,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,MAAM,MAAM,OAAO,UAAU,CAAC;IAE9B,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IAEA,MAAM,SAAS,MAAM,YAAY,GAAG,MAAM,KAAK;IAC/C,MAAM,SAAS,MAAM,aAAa,GAAG,MAAM,MAAM;IACjD,MAAM,aAAa,OAAO,gBAAgB,IAAI;IAE9C,OAAO,KAAK,GAAG,UAAU,KAAK,GAAG,aAAa;IAC9C,OAAO,MAAM,GAAG,UAAU,MAAM,GAAG,aAAa;IAEhD,IAAI,YAAY,CAAC,YAAY,GAAG,GAAG,YAAY,GAAG;IAClD,IAAI,qBAAqB,GAAG;IAE5B,IAAI,SAAS,CACX,OACA,UAAU,CAAC,GAAG,QACd,UAAU,CAAC,GAAG,QACd,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG,QACnB,GACA,GACA,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG;IAGrB,OAAO,IAAI,QAAQ,CAAC;QAClB,OAAO,MAAM,CACX,SACA,YAAY,4BAA4B;;IAE5C;AACF;AAUe,SAAS,mBAAmB,EACzC,MAAM,EACN,cAAc,EACd,OAAO,EACP,MAAM,EACN,cAAc,KAAK,EACK;;IACxB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;QAAE,GAAG;QAAG,GAAG;IAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE,CAAC,cAAoB;YAC9D,qBAAqB;QACvB;iEAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB;YACjC,QAAQ,IAAI,CAAC;YACb,eAAe;YACf;QACF;QACA,cAAc;QACd,IAAI;YACF,MAAM,cAAc,MAAM,kBAAkB,QAAQ;YACpD,eAAe;QACjB,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe;QACjB,SAAU;YACR,cAAc;QACd,+EAA+E;QACjF;IACF;IAEA,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,QAAQ;gBACV,QAAQ;gBACR,QAAQ;oBAAE,GAAG;oBAAG,GAAG;gBAAE;YACvB;QACF;uCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc;kBACpF,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAyB;;;;;;;;;;;;8BAK7C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,uBACC,6LAAC,2JAAA,CAAA,UAAO;gCACN,OAAO;gCACP,MAAM;gCACN,MAAM;gCACN,QAAQ,KAAK;gCACb,WAAU;gCACV,UAAU;gCACV,cAAc;gCACd,cAAc;gCACd,gBAAgB;gCAChB,OAAO;oCACL,gBAAgB;wCACd,cAAc;wCACd,UAAU;oCACZ;oCACA,eAAe;wCACb,QAAQ;wCACR,cAAc;oCAChB;gCACF;;;;;qDAGF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;sCAOrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;;wCAA6D;wCACrE,KAAK,KAAK,CAAC,OAAO;wCAAK;;;;;;;8CAEhC,6LAAC,8HAAA,CAAA,SAAM;oCACL,OAAO;wCAAC;qCAAK;oCACb,eAAe,CAAC,QAAU,QAAQ,KAAK,CAAC,EAAE;oCAC1C,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,WAAU;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;8BAK9C,6LAAC,8HAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,cAAc;sCACzB;;;;;;sCAGD,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,cAAc,eAAe,CAAC;4BACxC,WAAU;sCAET,cAAc,4BACb;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,aAAa,kBAAkB;;6DAGlC;;kDACE,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;GA5IwB;KAAA", "debugId": null}}, {"offset": {"line": 4269, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/CustomAdUpload.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useCallback } from \"react\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { Upload, Image, Link, Eye, EyeOff, X, Loader2 } from \"lucide-react\";\r\nimport NextImage from \"next/image\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport {\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Switch } from \"@/components/ui/switch\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { toast } from \"sonner\";\r\nimport { uploadCustomAdImage, deleteCustomAd } from \"../../actions/customAdUpload\";\r\nimport { compressImageUltraAggressiveClient } from \"@/lib/utils/client-image-compression\";\r\nimport CustomAdCropDialog from \"../CustomAdCropDialog\";\r\n\r\ninterface CustomAdUploadProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  currentUserPlan: \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | null;\r\n}\r\n\r\nexport default function CustomAdUpload({\r\n  form,\r\n  currentUserPlan,\r\n}: CustomAdUploadProps) {\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [imageToCrop, setImageToCrop] = useState<string | null>(null);\r\n  const [originalFile, setOriginalFile] = useState<File | null>(null);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Check if user has access to custom ads\r\n  const hasCustomAdAccess = currentUserPlan === \"pro\" || currentUserPlan === \"enterprise\";\r\n\r\n  // Handle file selection\r\n  const handleFileSelect = useCallback((file: File | null) => {\r\n    if (!file) return;\r\n\r\n    // Validate file type\r\n    if (!file.type.startsWith('image/')) {\r\n      toast.error(\"Please select a valid image file\");\r\n      return;\r\n    }\r\n\r\n    // Validate file size (max 5MB)\r\n    if (file.size > 5 * 1024 * 1024) {\r\n      toast.error(\"Image file size must be less than 5MB. Please choose a smaller file.\");\r\n      return;\r\n    }\r\n\r\n    setOriginalFile(file);\r\n    const reader = new FileReader();\r\n    reader.onloadend = () => {\r\n      setImageToCrop(reader.result as string);\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }, []);\r\n\r\n  // Handle drag and drop\r\n  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n\r\n    if (!hasCustomAdAccess || isUploading) return;\r\n\r\n    const droppedFile = e.dataTransfer.files?.[0];\r\n    if (droppedFile) {\r\n      handleFileSelect(droppedFile);\r\n    }\r\n  }, [hasCustomAdAccess, isUploading, handleFileSelect]);\r\n\r\n  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    if (hasCustomAdAccess && !isUploading) {\r\n      setIsDragging(true);\r\n    }\r\n  }, [hasCustomAdAccess, isUploading]);\r\n\r\n  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    setIsDragging(false);\r\n  }, []);\r\n\r\n  // Handle crop completion and upload\r\n  const handleCropComplete = async (croppedImageBlob: Blob | null) => {\r\n    if (!originalFile || !croppedImageBlob) {\r\n      // If cropping failed, close dialog immediately\r\n      setImageToCrop(null);\r\n      setOriginalFile(null);\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    try {\r\n      // Convert blob to file for compression\r\n      const croppedFile = new File([croppedImageBlob], originalFile.name, {\r\n        type: 'image/png', // Canvas outputs PNG\r\n        lastModified: Date.now()\r\n      });\r\n\r\n      // Compress image on client-side first\r\n      const compressionResult = await compressImageUltraAggressiveClient(croppedFile, {\r\n        maxDimension: 1200, // 16:9 aspect ratio - max 1200x675 for ads\r\n        targetSizeKB: 100   // Target 100KB max\r\n      });\r\n\r\n      // Convert compressed blob back to file\r\n      const compressedFile = new File([compressionResult.blob], originalFile.name, {\r\n        type: compressionResult.blob.type\r\n      });\r\n\r\n      // Create FormData with the compressed image\r\n      const formData = new FormData();\r\n      formData.append('image', compressedFile);\r\n\r\n      // Upload the image\r\n      const result = await uploadCustomAdImage(formData);\r\n\r\n      if (result.success && result.url) {\r\n        // Update form data\r\n        form.setValue(\"custom_ads.image_url\", result.url);\r\n        form.setValue(\"custom_ads.enabled\", true);\r\n        form.setValue(\"custom_ads.uploaded_at\", new Date().toISOString());\r\n\r\n        toast.success(\"Custom ad uploaded successfully!\");\r\n      } else {\r\n        toast.error(result.error || \"Failed to upload custom ad\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Upload error:\", error);\r\n      toast.error(\"Failed to upload custom ad\");\r\n    } finally {\r\n      setIsUploading(false);\r\n      // Close dialog after upload is complete (success or failure)\r\n      setImageToCrop(null);\r\n      setOriginalFile(null);\r\n    }\r\n  };\r\n\r\n  // Handle ad toggle - only update form state, save on main form submission\r\n  const handleAdToggle = (enabled: boolean) => {\r\n    form.setValue(\"custom_ads.enabled\", enabled, { shouldDirty: true });\r\n  };\r\n\r\n  // Handle ad deletion\r\n  const handleDeleteAd = async () => {\r\n    setIsDeleting(true);\r\n    try {\r\n      const result = await deleteCustomAd();\r\n      if (result.success) {\r\n        form.setValue(\"custom_ads.enabled\", false);\r\n        form.setValue(\"custom_ads.image_url\", \"\");\r\n        form.setValue(\"custom_ads.link_url\", \"\");\r\n        form.setValue(\"custom_ads.uploaded_at\", \"\");\r\n        toast.success(\"Custom ad deleted successfully\");\r\n      } else {\r\n        toast.error(result.error || \"Failed to delete custom ad\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Delete error:\", error);\r\n      toast.error(\"Failed to delete custom ad\");\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  if (!hasCustomAdAccess) {\r\n    return (\r\n      <div className=\"space-y-4 p-4 border border-amber-200 dark:border-amber-800 rounded-lg bg-amber-50 dark:bg-amber-950/20\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Image className=\"h-5 w-5 text-amber-600 dark:text-amber-400\" aria-label=\"Custom ads feature\" />\r\n          <h3 className=\"text-lg font-semibold text-amber-800 dark:text-amber-200\">\r\n            Custom Ads\r\n          </h3>\r\n          <Badge variant=\"secondary\" className=\"bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200\">\r\n            Pro Feature\r\n          </Badge>\r\n        </div>\r\n        <p className=\"text-sm text-amber-700 dark:text-amber-300\">\r\n          Upgrade to Pro or Enterprise plan to upload custom advertisement images that will be displayed on your public business card page.\r\n        </p>\r\n        <Button variant=\"outline\" className=\"border-amber-300 text-amber-700 hover:bg-amber-100 dark:border-amber-700 dark:text-amber-300 dark:hover:bg-amber-900/20\">\r\n          Upgrade to Pro\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const currentAdImage = form.watch(\"custom_ads.image_url\");\r\n  const isAdEnabled = form.watch(\"custom_ads.enabled\");\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex items-center gap-2\">\r\n        <Image className=\"h-5 w-5 text-primary\" aria-label=\"Custom advertisement\" />\r\n        <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-200\">\r\n          Custom Advertisement\r\n        </h3>\r\n        <Badge variant=\"default\" className=\"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\r\n          {currentUserPlan?.toUpperCase()}\r\n        </Badge>\r\n      </div>\r\n\r\n      <div className=\"space-y-6\">\r\n        {/* Enable/Disable Toggle */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"custom_ads.enabled\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"flex flex-row items-center justify-between rounded-lg border p-4\">\r\n              <div className=\"space-y-0.5\">\r\n                <FormLabel className=\"text-base font-medium flex items-center gap-2\">\r\n                  {field.value ? <Eye className=\"h-4 w-4\" /> : <EyeOff className=\"h-4 w-4\" />}\r\n                  Show Custom Ad\r\n                </FormLabel>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Display your custom advertisement on the public business card page\r\n                </p>\r\n              </div>\r\n              <FormControl>\r\n                <Switch\r\n                  checked={field.value}\r\n                  onCheckedChange={(checked) => {\r\n                    field.onChange(checked);\r\n                    handleAdToggle(checked);\r\n                  }}\r\n                  disabled={!currentAdImage}\r\n                />\r\n              </FormControl>\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Image Upload */}\r\n        <div className=\"space-y-3\">\r\n          <FormLabel className=\"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n            <Upload className=\"h-4 w-4 text-primary\" />\r\n            Advertisement Image\r\n          </FormLabel>\r\n\r\n          <Card\r\n            className={`border-dashed border-2 transition-colors cursor-pointer ${\r\n              isDragging\r\n                ? \"border-primary bg-primary/5\"\r\n                : \"border-muted-foreground/25 hover:border-primary/50\"\r\n            }`}\r\n            onDrop={handleDrop}\r\n            onDragOver={handleDragOver}\r\n            onDragLeave={handleDragLeave}\r\n            onClick={() => !isUploading && !isDeleting && fileInputRef.current?.click()}\r\n          >\r\n            <CardContent className=\"p-6\">\r\n              {isUploading ? (\r\n                <div className=\"text-center\">\r\n                  <div className=\"w-16 h-16 rounded-lg bg-primary/10 flex items-center justify-center mx-auto mb-4 border-2 border-primary/20\">\r\n                    <Loader2 className=\"h-8 w-8 text-primary animate-spin\" />\r\n                  </div>\r\n                  <p className=\"text-sm font-medium mb-2 text-primary\">Uploading Custom Ad...</p>\r\n                  <div className=\"space-y-1\">\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Compressing and optimizing image\r\n                    </p>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      Uploading to secure storage\r\n                    </p>\r\n                  </div>\r\n                  <div className=\"mt-3 w-full bg-muted rounded-full h-1.5\">\r\n                    <div className=\"bg-primary h-1.5 rounded-full animate-pulse\" style={{ width: '70%' }}></div>\r\n                  </div>\r\n                </div>\r\n              ) : currentAdImage ? (\r\n                <div className=\"space-y-4\">\r\n                  <div className=\"relative\">\r\n                    <div className=\"w-full aspect-[16/9] overflow-hidden rounded-lg border-2 border-green-200 dark:border-green-800\">\r\n                      <NextImage\r\n                        src={currentAdImage}\r\n                        alt=\"Custom advertisement\"\r\n                        width={400}\r\n                        height={225}\r\n                        className=\"w-full h-full object-cover\"\r\n                      />\r\n                    </div>\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"destructive\"\r\n                      size=\"sm\"\r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        handleDeleteAd();\r\n                      }}\r\n                      disabled={isDeleting}\r\n                      className=\"absolute top-2 right-2\"\r\n                    >\r\n                      {isDeleting ? (\r\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                      ) : (\r\n                        <X className=\"h-4 w-4\" />\r\n                      )}\r\n                    </Button>\r\n                    {/* Ad status indicator */}\r\n                    <div className=\"absolute bottom-2 left-2 bg-green-600 text-white text-xs px-2 py-1 rounded flex items-center gap-1\">\r\n                      <Eye className=\"h-3 w-3\" />\r\n                      {isAdEnabled ? \"Live\" : \"Draft\"}\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"text-center\">\r\n                    <p className=\"text-sm font-medium text-green-700 dark:text-green-300\">\r\n                      Custom ad uploaded successfully\r\n                    </p>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      {isAdEnabled ? \"Visible on your public card\" : \"Enable to show on public card\"} • Click to replace\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"text-center\">\r\n                  <div className=\"w-12 h-12 rounded-lg bg-muted flex items-center justify-center mx-auto mb-3\">\r\n                    <Upload className=\"h-6 w-6 text-muted-foreground\" />\r\n                  </div>\r\n                  <p className=\"text-sm font-medium mb-1\">Upload Custom Ad</p>\r\n                  <p className=\"text-xs text-muted-foreground mb-2\">\r\n                    Drag & drop or click to select\r\n                  </p>\r\n                  <p className=\"text-xs text-muted-foreground\">\r\n                    <strong>Aspect Ratio:</strong> 16:9 (recommended)\r\n                  </p>\r\n                </div>\r\n              )}\r\n              <input\r\n                ref={fileInputRef}\r\n                type=\"file\"\r\n                accept=\"image/*\"\r\n                onChange={(e) => handleFileSelect(e.target.files?.[0] || null)}\r\n                className=\"hidden\"\r\n                disabled={isUploading || isDeleting}\r\n              />\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {/* Link URL */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"custom_ads.link_url\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-2\">\r\n              <FormLabel className=\"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Link className=\"h-4 w-4 text-primary\" />\r\n                Advertisement Link (Optional)\r\n              </FormLabel>\r\n              <FormControl>\r\n                <Input\r\n                  {...field}\r\n                  value={field.value || \"\"}\r\n                  onChange={(e) => {\r\n                    field.onChange(e.target.value);\r\n                  }}\r\n                  placeholder=\"https://example.com\"\r\n                />\r\n              </FormControl>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Make your ad clickable by adding a website URL. Changes will be saved when you click &quot;Save Changes&quot;.\r\n              </p>\r\n              <FormMessage />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n\r\n      {/* Custom Ad Crop Dialog */}\r\n      <CustomAdCropDialog\r\n        isOpen={!!imageToCrop}\r\n        imgSrc={imageToCrop}\r\n        onCropComplete={handleCropComplete}\r\n        onClose={() => {\r\n          // Only allow closing if not uploading\r\n          if (!isUploading) {\r\n            setImageToCrop(null);\r\n            setOriginalFile(null);\r\n          }\r\n        }}\r\n        isUploading={isUploading}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAtBA;;;;;;;;;;;;;;AA6Be,SAAS,eAAe,EACrC,IAAI,EACJ,eAAe,EACK;;IACpB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,yCAAyC;IACzC,MAAM,oBAAoB,oBAAoB,SAAS,oBAAoB;IAE3E,wBAAwB;IACxB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,IAAI,CAAC,MAAM;YAEX,qBAAqB;YACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,+BAA+B;YAC/B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,gBAAgB;YAChB,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS;gEAAG;oBACjB,eAAe,OAAO,MAAM;gBAC9B;;YACA,OAAO,aAAa,CAAC;QACvB;uDAAG,EAAE;IAEL,uBAAuB;IACvB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YAC9B,EAAE,cAAc;YAChB,cAAc;YAEd,IAAI,CAAC,qBAAqB,aAAa;YAEvC,MAAM,cAAc,EAAE,YAAY,CAAC,KAAK,EAAE,CAAC,EAAE;YAC7C,IAAI,aAAa;gBACf,iBAAiB;YACnB;QACF;iDAAG;QAAC;QAAmB;QAAa;KAAiB;IAErD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,EAAE,cAAc;YAChB,IAAI,qBAAqB,CAAC,aAAa;gBACrC,cAAc;YAChB;QACF;qDAAG;QAAC;QAAmB;KAAY;IAEnC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC;YACnC,EAAE,cAAc;YAChB,cAAc;QAChB;sDAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,gBAAgB,CAAC,kBAAkB;YACtC,+CAA+C;YAC/C,eAAe;YACf,gBAAgB;YAChB;QACF;QAEA,eAAe;QACf,IAAI;YACF,uCAAuC;YACvC,MAAM,cAAc,IAAI,KAAK;gBAAC;aAAiB,EAAE,aAAa,IAAI,EAAE;gBAClE,MAAM;gBACN,cAAc,KAAK,GAAG;YACxB;YAEA,sCAAsC;YACtC,MAAM,oBAAoB,MAAM,CAAA,GAAA,iJAAA,CAAA,qCAAkC,AAAD,EAAE,aAAa;gBAC9E,cAAc;gBACd,cAAc,IAAM,mBAAmB;YACzC;YAEA,uCAAuC;YACvC,MAAM,iBAAiB,IAAI,KAAK;gBAAC,kBAAkB,IAAI;aAAC,EAAE,aAAa,IAAI,EAAE;gBAC3E,MAAM,kBAAkB,IAAI,CAAC,IAAI;YACnC;YAEA,4CAA4C;YAC5C,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,mBAAmB;YACnB,MAAM,SAAS,MAAM,CAAA,GAAA,2MAAA,CAAA,sBAAmB,AAAD,EAAE;YAEzC,IAAI,OAAO,OAAO,IAAI,OAAO,GAAG,EAAE;gBAChC,mBAAmB;gBACnB,KAAK,QAAQ,CAAC,wBAAwB,OAAO,GAAG;gBAChD,KAAK,QAAQ,CAAC,sBAAsB;gBACpC,KAAK,QAAQ,CAAC,0BAA0B,IAAI,OAAO,WAAW;gBAE9D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;YACf,6DAA6D;YAC7D,eAAe;YACf,gBAAgB;QAClB;IACF;IAEA,0EAA0E;IAC1E,MAAM,iBAAiB,CAAC;QACtB,KAAK,QAAQ,CAAC,sBAAsB,SAAS;YAAE,aAAa;QAAK;IACnE;IAEA,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,2MAAA,CAAA,iBAAc,AAAD;YAClC,IAAI,OAAO,OAAO,EAAE;gBAClB,KAAK,QAAQ,CAAC,sBAAsB;gBACpC,KAAK,QAAQ,CAAC,wBAAwB;gBACtC,KAAK,QAAQ,CAAC,uBAAuB;gBACrC,KAAK,QAAQ,CAAC,0BAA0B;gBACxC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,mBAAmB;QACtB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;4BAA6C,cAAW;;;;;;sCACzE,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,6LAAC,6HAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAAoE;;;;;;;;;;;;8BAI3G,6LAAC;oBAAE,WAAU;8BAA6C;;;;;;8BAG1D,6LAAC,8HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,WAAU;8BAA0H;;;;;;;;;;;;IAKpK;IAEA,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,cAAc,KAAK,KAAK,CAAC;IAE/B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;wBAAuB,cAAW;;;;;;kCACnD,6LAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAG7E,6LAAC,6HAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAU,WAAU;kCAChC,iBAAiB;;;;;;;;;;;;0BAItB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;oDAClB,MAAM,KAAK,iBAAG,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;+EAAe,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAa;;;;;;;0DAG9E,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,8HAAA,CAAA,SAAM;4CACL,SAAS,MAAM,KAAK;4CACpB,iBAAiB,CAAC;gDAChB,MAAM,QAAQ,CAAC;gDACf,eAAe;4CACjB;4CACA,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;kCAQrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,4HAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAyB;;;;;;;0CAI7C,6LAAC,4HAAA,CAAA,OAAI;gCACH,WAAW,CAAC,wDAAwD,EAClE,aACI,gCACA,sDACJ;gCACF,QAAQ;gCACR,YAAY;gCACZ,aAAa;gCACb,SAAS,IAAM,CAAC,eAAe,CAAC,cAAc,aAAa,OAAO,EAAE;0CAEpE,cAAA,6LAAC,4HAAA,CAAA,cAAW;oCAAC,WAAU;;wCACpB,4BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6LAAC;oDAAE,WAAU;8DAAwC;;;;;;8DACrD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;sEAG7C,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAI/C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA8C,OAAO;4DAAE,OAAO;wDAAM;;;;;;;;;;;;;;;;mDAGrF,+BACF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,gIAAA,CAAA,UAAS;gEACR,KAAK;gEACL,KAAI;gEACJ,OAAO;gEACP,QAAQ;gEACR,WAAU;;;;;;;;;;;sEAGd,6LAAC,8HAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB;4DACF;4DACA,UAAU;4DACV,WAAU;sEAET,2BACC,6LAAC,oNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;qFAEnB,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;sEAIjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,cAAc,SAAS;;;;;;;;;;;;;8DAG5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyD;;;;;;sEAGtE,6LAAC;4DAAE,WAAU;;gEACV,cAAc,gCAAgC;gEAAgC;;;;;;;;;;;;;;;;;;iEAKrF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,6LAAC;oDAAE,WAAU;8DAAqC;;;;;;8DAGlD,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAsB;;;;;;;;;;;;;sDAIpC,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;4CACzD,WAAU;4CACV,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;kCAOjC,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAyB;;;;;;;kDAG3C,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,6HAAA,CAAA,QAAK;4CACH,GAAG,KAAK;4CACT,OAAO,MAAM,KAAK,IAAI;4CACtB,UAAU,CAAC;gDACT,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,KAAK;4CAC/B;4CACA,aAAY;;;;;;;;;;;kDAGhB,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;kDAG7C,6LAAC,4HAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0BAOpB,6LAAC,6LAAA,CAAA,UAAkB;gBACjB,QAAQ,CAAC,CAAC;gBACV,QAAQ;gBACR,gBAAgB;gBAChB,SAAS;oBACP,sCAAsC;oBACtC,IAAI,CAAC,aAAa;wBAChB,eAAe;wBACf,gBAAgB;oBAClB;gBACF;gBACA,aAAa;;;;;;;;;;;;AAIrB;GA7WwB;KAAA", "debugId": null}}, {"offset": {"line": 5036, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/BusinessHoursEditor.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Clock, Trash } from \"lucide-react\";\r\n\r\n// Convert 24-hour time format to 12-hour AM/PM format for display\r\nconst formatTimeTo12Hour = (time24: string): string => {\r\n  if (!time24 || time24.length < 5) return time24;\r\n\r\n  const [hourStr, minuteStr] = time24.split(\":\");\r\n  const hour = parseInt(hourStr, 10);\r\n\r\n  if (isNaN(hour)) return time24;\r\n\r\n  const period = hour >= 12 ? \"PM\" : \"AM\";\r\n  const hour12 = hour % 12 || 12; // Convert 0 to 12 for 12 AM\r\n\r\n  return `${hour12}:${minuteStr} ${period}`;\r\n};\r\n\r\n// Define the structure for business hours\r\nexport interface DayHours {\r\n  isOpen: boolean;\r\n  openTime: string;\r\n  closeTime: string;\r\n}\r\n\r\nexport interface BusinessHours {\r\n  monday: DayHours;\r\n  tuesday: DayHours;\r\n  wednesday: DayHours;\r\n  thursday: DayHours;\r\n  friday: DayHours;\r\n  saturday: DayHours;\r\n  sunday: DayHours;\r\n}\r\n\r\n// Empty hours template with all days closed and default times\r\nconst EMPTY_HOURS: DayHours = {\r\n  isOpen: false,\r\n  openTime: \"09:00\",\r\n  closeTime: \"18:00\",\r\n};\r\n\r\n// Empty business hours with all days closed\r\nconst EMPTY_BUSINESS_HOURS: BusinessHours = {\r\n  monday: { ...EMPTY_HOURS },\r\n  tuesday: { ...EMPTY_HOURS },\r\n  wednesday: { ...EMPTY_HOURS },\r\n  thursday: { ...EMPTY_HOURS },\r\n  friday: { ...EMPTY_HOURS },\r\n  saturday: { ...EMPTY_HOURS },\r\n  sunday: { ...EMPTY_HOURS },\r\n};\r\n\r\ninterface BusinessHoursEditorProps {\r\n  value: BusinessHours | null | undefined;\r\n  onChange: (_value: BusinessHours | null) => void;\r\n}\r\n\r\n// Define day groups for easier management\r\ntype DayGroup = \"weekdays\" | \"weekend\" | \"all\";\r\n\r\ninterface DayGroupConfig {\r\n  label: string;\r\n  days: (keyof BusinessHours)[];\r\n}\r\n\r\nconst DAY_GROUPS: Record<DayGroup, DayGroupConfig> = {\r\n  weekdays: {\r\n    label: \"Mon-Fri\",\r\n    days: [\"monday\", \"tuesday\", \"wednesday\", \"thursday\", \"friday\"],\r\n  },\r\n  weekend: {\r\n    label: \"Sat-Sun\",\r\n    days: [\"saturday\", \"sunday\"],\r\n  },\r\n  all: {\r\n    label: \"All Days\",\r\n    days: [\r\n      \"monday\",\r\n      \"tuesday\",\r\n      \"wednesday\",\r\n      \"thursday\",\r\n      \"friday\",\r\n      \"saturday\",\r\n      \"sunday\",\r\n    ],\r\n  },\r\n};\r\n\r\nexport function BusinessHoursEditor({\r\n  value,\r\n  onChange,\r\n}: BusinessHoursEditorProps) {\r\n  // Initialize state with either the provided value or empty hours\r\n  const [hours, setHours] = useState<BusinessHours>(() => {\r\n    try {\r\n      // If value is null or undefined, return empty hours (all days closed)\r\n      if (!value) {\r\n        return { ...EMPTY_BUSINESS_HOURS };\r\n      }\r\n\r\n      // Ensure all days are present in the value\r\n      const initialValue = { ...EMPTY_BUSINESS_HOURS };\r\n\r\n      // Only override with existing values if they match our expected structure\r\n      Object.keys(EMPTY_BUSINESS_HOURS).forEach((day) => {\r\n        const typedDay = day as keyof BusinessHours;\r\n        if (value[typedDay] && typeof value[typedDay] === \"object\") {\r\n          const dayData = value[typedDay] as Partial<DayHours>;\r\n          initialValue[typedDay] = {\r\n            isOpen:\r\n              typeof dayData.isOpen === \"boolean\" ? dayData.isOpen : false,\r\n            openTime:\r\n              typeof dayData.openTime === \"string\" ? dayData.openTime : \"09:00\",\r\n            closeTime:\r\n              typeof dayData.closeTime === \"string\"\r\n                ? dayData.closeTime\r\n                : \"18:00\",\r\n          };\r\n        }\r\n      });\r\n\r\n      return initialValue;\r\n    } catch (error) {\r\n      console.error(\"Error parsing business hours:\", error);\r\n      return { ...EMPTY_BUSINESS_HOURS };\r\n    }\r\n  });\r\n\r\n  // No longer tracking active group as we use tabs instead\r\n\r\n  // Track if the business is open on specific days\r\n  const [isOpen, setIsOpen] = useState<Record<DayGroup, boolean>>(() => {\r\n    // Initialize based on current hours\r\n    return {\r\n      weekdays: DAY_GROUPS.weekdays.days.some((day) => hours[day].isOpen),\r\n      weekend: DAY_GROUPS.weekend.days.some((day) => hours[day].isOpen),\r\n      all: Object.values(hours).some((dayHour) => dayHour.isOpen),\r\n    };\r\n  });\r\n\r\n  // Track hours for each group\r\n  const [groupHours, setGroupHours] = useState<\r\n    Record<DayGroup, { openTime: string; closeTime: string }>\r\n  >(() => {\r\n    // Initialize with the first day's hours from each group that is open\r\n    // or default hours if none are open\r\n    const getGroupTime = (\r\n      group: DayGroup\r\n    ): { openTime: string; closeTime: string } => {\r\n      const openDay = DAY_GROUPS[group].days.find((day) => hours[day].isOpen);\r\n      if (openDay) {\r\n        return {\r\n          openTime: hours[openDay].openTime,\r\n          closeTime: hours[openDay].closeTime,\r\n        };\r\n      }\r\n      return { openTime: \"09:00\", closeTime: \"18:00\" };\r\n    };\r\n\r\n    return {\r\n      weekdays: getGroupTime(\"weekdays\"),\r\n      weekend: getGroupTime(\"weekend\"),\r\n      all: getGroupTime(\"all\"),\r\n    };\r\n  });\r\n\r\n  // Update hours for a specific group\r\n  const updateGroupHours = (\r\n    group: DayGroup,\r\n    isOpenValue: boolean,\r\n    openTime?: string,\r\n    closeTime?: string\r\n  ) => {\r\n    // Update the group's open status\r\n    setIsOpen((prev) => ({\r\n      ...prev,\r\n      [group]: isOpenValue,\r\n    }));\r\n\r\n    // Update the group's hours if provided\r\n    if (openTime !== undefined && closeTime !== undefined) {\r\n      setGroupHours((prev) => ({\r\n        ...prev,\r\n        [group]: { openTime, closeTime },\r\n      }));\r\n    }\r\n\r\n    // Apply changes to all days in the group\r\n    const newHours = { ...hours };\r\n    DAY_GROUPS[group].days.forEach((day) => {\r\n      newHours[day] = {\r\n        isOpen: isOpenValue,\r\n        openTime: openTime || groupHours[group].openTime,\r\n        closeTime: closeTime || groupHours[group].closeTime,\r\n      };\r\n    });\r\n\r\n    // Update state and notify parent\r\n    setHours(newHours);\r\n    onChange(newHours);\r\n  };\r\n\r\n  // Reset all hours to empty (all days closed)\r\n  const resetAll = () => {\r\n    setHours({ ...EMPTY_BUSINESS_HOURS });\r\n    setIsOpen({ weekdays: false, weekend: false, all: false });\r\n    setGroupHours({\r\n      weekdays: { openTime: \"09:00\", closeTime: \"18:00\" },\r\n      weekend: { openTime: \"09:00\", closeTime: \"18:00\" },\r\n      all: { openTime: \"09:00\", closeTime: \"18:00\" },\r\n    });\r\n    onChange({ ...EMPTY_BUSINESS_HOURS });\r\n  };\r\n\r\n  return (\r\n    <div className=\"border border-neutral-200 dark:border-neutral-700 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 overflow-hidden\">\r\n      <div>\r\n        <div className=\"flex items-center justify-between px-4 py-3 border-b border-neutral-200 dark:border-neutral-700\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Clock className=\"h-4 w-4 text-neutral-500\" />\r\n            <h3 className=\"text-sm font-medium\">Business Hours</h3>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"p-4 space-y-4\">\r\n          <div className=\"space-y-4\">\r\n            {/* Weekdays section */}\r\n            <div className=\"rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden\">\r\n              <div className=\"flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Checkbox\r\n                    id=\"weekdays-open\"\r\n                    checked={isOpen.weekdays}\r\n                    onCheckedChange={(checked) => {\r\n                      updateGroupHours(\"weekdays\", checked === true);\r\n                    }}\r\n                  />\r\n                  <Label\r\n                    htmlFor=\"weekdays-open\"\r\n                    className=\"font-medium cursor-pointer\"\r\n                  >\r\n                    {DAY_GROUPS.weekdays.label}\r\n                  </Label>\r\n                </div>\r\n              </div>\r\n\r\n              {isOpen.weekdays && (\r\n                <div className=\"p-3 bg-white dark:bg-neutral-900\">\r\n                  <div className=\"flex flex-wrap items-center gap-2\">\r\n                    <Label className=\"text-xs text-neutral-500 w-full sm:w-auto\">\r\n                      Hours:\r\n                    </Label>\r\n                    <div className=\"flex items-center gap-2 flex-1 flex-wrap\">\r\n                      <Input\r\n                        type=\"time\"\r\n                        value={groupHours.weekdays.openTime}\r\n                        onChange={(e) => {\r\n                          updateGroupHours(\r\n                            \"weekdays\",\r\n                            true,\r\n                            e.target.value,\r\n                            groupHours.weekdays.closeTime\r\n                          );\r\n                        }}\r\n                        className=\"w-32 py-1 px-2 text-sm\"\r\n                      />\r\n                      <span className=\"text-neutral-500 text-xs\">to</span>\r\n                      <Input\r\n                        type=\"time\"\r\n                        value={groupHours.weekdays.closeTime}\r\n                        onChange={(e) => {\r\n                          updateGroupHours(\r\n                            \"weekdays\",\r\n                            true,\r\n                            groupHours.weekdays.openTime,\r\n                            e.target.value\r\n                          );\r\n                        }}\r\n                        className=\"w-32 py-1 px-2 text-sm\"\r\n                      />\r\n                      <span className=\"text-neutral-500 text-xs ml-2\">\r\n                        {formatTimeTo12Hour(groupHours.weekdays.openTime)} -{\" \"}\r\n                        {formatTimeTo12Hour(groupHours.weekdays.closeTime)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Weekend section */}\r\n            <div className=\"rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden\">\r\n              <div className=\"flex items-center justify-between p-3 bg-neutral-100 dark:bg-neutral-800\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <Checkbox\r\n                    id=\"weekend-open\"\r\n                    checked={isOpen.weekend}\r\n                    onCheckedChange={(checked) => {\r\n                      updateGroupHours(\"weekend\", checked === true);\r\n                    }}\r\n                  />\r\n                  <Label\r\n                    htmlFor=\"weekend-open\"\r\n                    className=\"font-medium cursor-pointer\"\r\n                  >\r\n                    {DAY_GROUPS.weekend.label}\r\n                  </Label>\r\n                </div>\r\n              </div>\r\n\r\n              {isOpen.weekend && (\r\n                <div className=\"p-3 bg-white dark:bg-neutral-900\">\r\n                  <div className=\"flex flex-wrap items-center gap-2\">\r\n                    <Label className=\"text-xs text-neutral-500 w-full sm:w-auto\">\r\n                      Hours:\r\n                    </Label>\r\n                    <div className=\"flex items-center gap-2 flex-1 flex-wrap\">\r\n                      <Input\r\n                        type=\"time\"\r\n                        value={groupHours.weekend.openTime}\r\n                        onChange={(e) => {\r\n                          updateGroupHours(\r\n                            \"weekend\",\r\n                            true,\r\n                            e.target.value,\r\n                            groupHours.weekend.closeTime\r\n                          );\r\n                        }}\r\n                        className=\"w-32 py-1 px-2 text-sm\"\r\n                      />\r\n                      <span className=\"text-neutral-500 text-xs\">to</span>\r\n                      <Input\r\n                        type=\"time\"\r\n                        value={groupHours.weekend.closeTime}\r\n                        onChange={(e) => {\r\n                          updateGroupHours(\r\n                            \"weekend\",\r\n                            true,\r\n                            groupHours.weekend.openTime,\r\n                            e.target.value\r\n                          );\r\n                        }}\r\n                        className=\"w-32 py-1 px-2 text-sm\"\r\n                      />\r\n                      <span className=\"text-neutral-500 text-xs ml-2\">\r\n                        {formatTimeTo12Hour(groupHours.weekend.openTime)} -{\" \"}\r\n                        {formatTimeTo12Hour(groupHours.weekend.closeTime)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Reset button */}\r\n      <div className=\"flex justify-end p-4 border-t border-neutral-200 dark:border-neutral-700\">\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          onClick={resetAll}\r\n          className=\"text-xs\"\r\n        >\r\n          <Trash className=\"h-3 w-3 mr-1\" />\r\n          Reset All\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;;AASA,kEAAkE;AAClE,MAAM,qBAAqB,CAAC;IAC1B,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG,OAAO;IAEzC,MAAM,CAAC,SAAS,UAAU,GAAG,OAAO,KAAK,CAAC;IAC1C,MAAM,OAAO,SAAS,SAAS;IAE/B,IAAI,MAAM,OAAO,OAAO;IAExB,MAAM,SAAS,QAAQ,KAAK,OAAO;IACnC,MAAM,SAAS,OAAO,MAAM,IAAI,4BAA4B;IAE5D,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAmBA,8DAA8D;AAC9D,MAAM,cAAwB;IAC5B,QAAQ;IACR,UAAU;IACV,WAAW;AACb;AAEA,4CAA4C;AAC5C,MAAM,uBAAsC;IAC1C,QAAQ;QAAE,GAAG,WAAW;IAAC;IACzB,SAAS;QAAE,GAAG,WAAW;IAAC;IAC1B,WAAW;QAAE,GAAG,WAAW;IAAC;IAC5B,UAAU;QAAE,GAAG,WAAW;IAAC;IAC3B,QAAQ;QAAE,GAAG,WAAW;IAAC;IACzB,UAAU;QAAE,GAAG,WAAW;IAAC;IAC3B,QAAQ;QAAE,GAAG,WAAW;IAAC;AAC3B;AAeA,MAAM,aAA+C;IACnD,UAAU;QACR,OAAO;QACP,MAAM;YAAC;YAAU;YAAW;YAAa;YAAY;SAAS;IAChE;IACA,SAAS;QACP,OAAO;QACP,MAAM;YAAC;YAAY;SAAS;IAC9B;IACA,KAAK;QACH,OAAO;QACP,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEO,SAAS,oBAAoB,EAClC,KAAK,EACL,QAAQ,EACiB;;IACzB,iEAAiE;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;wCAAiB;YAChD,IAAI;gBACF,sEAAsE;gBACtE,IAAI,CAAC,OAAO;oBACV,OAAO;wBAAE,GAAG,oBAAoB;oBAAC;gBACnC;gBAEA,2CAA2C;gBAC3C,MAAM,eAAe;oBAAE,GAAG,oBAAoB;gBAAC;gBAE/C,0EAA0E;gBAC1E,OAAO,IAAI,CAAC,sBAAsB,OAAO;oDAAC,CAAC;wBACzC,MAAM,WAAW;wBACjB,IAAI,KAAK,CAAC,SAAS,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,UAAU;4BAC1D,MAAM,UAAU,KAAK,CAAC,SAAS;4BAC/B,YAAY,CAAC,SAAS,GAAG;gCACvB,QACE,OAAO,QAAQ,MAAM,KAAK,YAAY,QAAQ,MAAM,GAAG;gCACzD,UACE,OAAO,QAAQ,QAAQ,KAAK,WAAW,QAAQ,QAAQ,GAAG;gCAC5D,WACE,OAAO,QAAQ,SAAS,KAAK,WACzB,QAAQ,SAAS,GACjB;4BACR;wBACF;oBACF;;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,OAAO;oBAAE,GAAG,oBAAoB;gBAAC;YACnC;QACF;;IAEA,yDAAyD;IAEzD,iDAAiD;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;wCAA6B;YAC9D,oCAAoC;YACpC,OAAO;gBACL,UAAU,WAAW,QAAQ,CAAC,IAAI,CAAC,IAAI;oDAAC,CAAC,MAAQ,KAAK,CAAC,IAAI,CAAC,MAAM;;gBAClE,SAAS,WAAW,OAAO,CAAC,IAAI,CAAC,IAAI;oDAAC,CAAC,MAAQ,KAAK,CAAC,IAAI,CAAC,MAAM;;gBAChE,KAAK,OAAO,MAAM,CAAC,OAAO,IAAI;oDAAC,CAAC,UAAY,QAAQ,MAAM;;YAC5D;QACF;;IAEA,6BAA6B;IAC7B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;wCAEzC;YACA,qEAAqE;YACrE,oCAAoC;YACpC,MAAM;6DAAe,CACnB;oBAEA,MAAM,UAAU,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;6EAAC,CAAC,MAAQ,KAAK,CAAC,IAAI,CAAC,MAAM;;oBACtE,IAAI,SAAS;wBACX,OAAO;4BACL,UAAU,KAAK,CAAC,QAAQ,CAAC,QAAQ;4BACjC,WAAW,KAAK,CAAC,QAAQ,CAAC,SAAS;wBACrC;oBACF;oBACA,OAAO;wBAAE,UAAU;wBAAS,WAAW;oBAAQ;gBACjD;;YAEA,OAAO;gBACL,UAAU,aAAa;gBACvB,SAAS,aAAa;gBACtB,KAAK,aAAa;YACpB;QACF;;IAEA,oCAAoC;IACpC,MAAM,mBAAmB,CACvB,OACA,aACA,UACA;QAEA,iCAAiC;QACjC,UAAU,CAAC,OAAS,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QAED,uCAAuC;QACvC,IAAI,aAAa,aAAa,cAAc,WAAW;YACrD,cAAc,CAAC,OAAS,CAAC;oBACvB,GAAG,IAAI;oBACP,CAAC,MAAM,EAAE;wBAAE;wBAAU;oBAAU;gBACjC,CAAC;QACH;QAEA,yCAAyC;QACzC,MAAM,WAAW;YAAE,GAAG,KAAK;QAAC;QAC5B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9B,QAAQ,CAAC,IAAI,GAAG;gBACd,QAAQ;gBACR,UAAU,YAAY,UAAU,CAAC,MAAM,CAAC,QAAQ;gBAChD,WAAW,aAAa,UAAU,CAAC,MAAM,CAAC,SAAS;YACrD;QACF;QAEA,iCAAiC;QACjC,SAAS;QACT,SAAS;IACX;IAEA,6CAA6C;IAC7C,MAAM,WAAW;QACf,SAAS;YAAE,GAAG,oBAAoB;QAAC;QACnC,UAAU;YAAE,UAAU;YAAO,SAAS;YAAO,KAAK;QAAM;QACxD,cAAc;YACZ,UAAU;gBAAE,UAAU;gBAAS,WAAW;YAAQ;YAClD,SAAS;gBAAE,UAAU;gBAAS,WAAW;YAAQ;YACjD,KAAK;gBAAE,UAAU;gBAAS,WAAW;YAAQ;QAC/C;QACA,SAAS;YAAE,GAAG,oBAAoB;QAAC;IACrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAG,WAAU;8CAAsB;;;;;;;;;;;;;;;;;kCAIxC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,SAAS,OAAO,QAAQ;wDACxB,iBAAiB,CAAC;4DAChB,iBAAiB,YAAY,YAAY;wDAC3C;;;;;;kEAEF,6LAAC,6HAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAU;kEAET,WAAW,QAAQ,CAAC,KAAK;;;;;;;;;;;;;;;;;wCAK/B,OAAO,QAAQ,kBACd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4C;;;;;;kEAG7D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6HAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,WAAW,QAAQ,CAAC,QAAQ;gEACnC,UAAU,CAAC;oEACT,iBACE,YACA,MACA,EAAE,MAAM,CAAC,KAAK,EACd,WAAW,QAAQ,CAAC,SAAS;gEAEjC;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAA2B;;;;;;0EAC3C,6LAAC,6HAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,WAAW,QAAQ,CAAC,SAAS;gEACpC,UAAU,CAAC;oEACT,iBACE,YACA,MACA,WAAW,QAAQ,CAAC,QAAQ,EAC5B,EAAE,MAAM,CAAC,KAAK;gEAElB;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;;oEACb,mBAAmB,WAAW,QAAQ,CAAC,QAAQ;oEAAE;oEAAG;oEACpD,mBAAmB,WAAW,QAAQ,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAS7D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,gIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,SAAS,OAAO,OAAO;wDACvB,iBAAiB,CAAC;4DAChB,iBAAiB,WAAW,YAAY;wDAC1C;;;;;;kEAEF,6LAAC,6HAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAU;kEAET,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;wCAK9B,OAAO,OAAO,kBACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6HAAA,CAAA,QAAK;wDAAC,WAAU;kEAA4C;;;;;;kEAG7D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6HAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,WAAW,OAAO,CAAC,QAAQ;gEAClC,UAAU,CAAC;oEACT,iBACE,WACA,MACA,EAAE,MAAM,CAAC,KAAK,EACd,WAAW,OAAO,CAAC,SAAS;gEAEhC;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAA2B;;;;;;0EAC3C,6LAAC,6HAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO,WAAW,OAAO,CAAC,SAAS;gEACnC,UAAU,CAAC;oEACT,iBACE,WACA,MACA,WAAW,OAAO,CAAC,QAAQ,EAC3B,EAAE,MAAM,CAAC,KAAK;gEAElB;gEACA,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;;oEACb,mBAAmB,WAAW,OAAO,CAAC,QAAQ;oEAAE;oEAAG;oEACnD,mBAAmB,WAAW,OAAO,CAAC,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYlE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS;oBACT,WAAU;;sCAEV,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAM5C;GA5RgB;KAAA", "debugId": null}}, {"offset": {"line": 5625, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/BusinessDetailsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport { Clock, Truck, Info } from \"lucide-react\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { BusinessHoursEditor } from \"./BusinessHoursEditor\";\r\n\r\ninterface BusinessDetailsSectionProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n}\r\n\r\nexport default function BusinessDetailsSection({\r\n  form,\r\n}: BusinessDetailsSectionProps) {\r\n  return (\r\n    <div className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n          <Clock className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Business Details\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            Add your business hours and delivery information\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 sm:gap-6\">\r\n        {/* Business Hours */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"business_hours\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Clock className=\"h-3.5 w-3.5 text-primary\" />\r\n                Business Hours\r\n              </FormLabel>\r\n              <FormControl>\r\n                <BusinessHoursEditor\r\n                  value={field.value}\r\n                  onChange={field.onChange}\r\n                />\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1\">\r\n                <Info className=\"w-3 h-3\" />\r\n                Set your business hours to let customers know when you&apos;re\r\n                open\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Delivery Info */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"delivery_info\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Truck className=\"h-3.5 w-3.5 text-primary\" />\r\n                Delivery Info\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"relative\">\r\n                  <Truck className=\"absolute left-3 top-3 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400\" />\r\n                  <Textarea\r\n                    placeholder=\"e.g., Free delivery within 5km, Delivery charges apply...\"\r\n                    {...field}\r\n                    className=\"min-h-[80px] pl-10 rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm resize-none transition-all duration-200\"\r\n                    maxLength={100}\r\n                  />\r\n                  <div className=\"absolute right-2 bottom-2 px-1.5 py-0.5 bg-white dark:bg-neutral-800 rounded-md text-xs font-medium text-neutral-400 dark:text-neutral-500\">\r\n                    {field.value?.length || 0}/100\r\n                  </div>\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1 ml-1\">\r\n                <Info className=\"w-3 h-3\" />\r\n                Delivery details shown on your card\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AACA;AACA;AAQA;AAdA;;;;;;AAoBe,SAAS,uBAAuB,EAC7C,IAAI,EACwB;IAC5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAG1F,6LAAC;gCAAE,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAMzE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAA6B;;;;;;;kDAGhD,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,8MAAA,CAAA,sBAAmB;4CAClB,OAAO,MAAM,KAAK;4CAClB,UAAU,MAAM,QAAQ;;;;;;;;;;;kDAG5B,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;;0DACzB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAI9B,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAA6B;;;;;;;kDAGhD,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,gIAAA,CAAA,WAAQ;oDACP,aAAY;oDACX,GAAG,KAAK;oDACT,WAAU;oDACV,WAAW;;;;;;8DAEb,6LAAC;oDAAI,WAAU;;wDACZ,MAAM,KAAK,EAAE,UAAU;wDAAE;;;;;;;;;;;;;;;;;;kDAIhC,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;;0DACzB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrC;KAlFwB", "debugId": null}}, {"offset": {"line": 5894, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/LinksSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { Link, MessageSquare, Instagram, Facebook } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\n\r\ninterface LinksSectionProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n}\r\n\r\nexport default function LinksSection({ form }: LinksSectionProps) {\r\n  const primaryPhone = form.watch(\"phone\");\r\n\r\n  // Local state for copy checkboxes\r\n  const [usePrimaryForWhatsapp, setUsePrimaryForWhatsapp] = useState(false);\r\n\r\n  // Effect to update whatsapp_number when checkbox/primary phone changes\r\n  useEffect(() => {\r\n    if (usePrimaryForWhatsapp) {\r\n      form.setValue(\"whatsapp_number\", primaryPhone || \"\", {\r\n        shouldValidate: true,\r\n        shouldDirty: true,\r\n      });\r\n    }\r\n  }, [usePrimaryForWhatsapp, primaryPhone, form]);\r\n\r\n  return (\r\n    <div className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-lg\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n          <Link className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Links\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            Add your social media and communication links\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 sm:gap-6\">\r\n        {/* WhatsApp Number and Instagram URL Fields - 2 columns on tablet and desktop */}\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6\">\r\n          {/* WhatsApp Number Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"whatsapp_number\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <MessageSquare className=\"h-3.5 w-3.5 text-primary\" />\r\n                  WhatsApp Number\r\n                </FormLabel>\r\n                <div className=\"flex flex-col space-y-2\">\r\n                  <FormControl>\r\n                    <div className=\"relative\">\r\n                      <MessageSquare className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400\" />\r\n                      <Input\r\n                        type=\"tel\"\r\n                        pattern=\"[0-9]*\"\r\n                        inputMode=\"numeric\"\r\n                        placeholder=\"9876543210\"\r\n                        {...field}\r\n                        onChange={(e) => {\r\n                          // Remove any +91 prefix if user enters it\r\n                          let value = e.target.value.replace(/^\\+91/, '');\r\n                          // Only allow numeric input and limit to 10 digits\r\n                          value = value.replace(/\\D/g, '');\r\n                          if (value.length <= 10) {\r\n                            field.onChange(value);\r\n                          }\r\n                        }}\r\n                        onKeyDown={(e) => {\r\n                          // Prevent non-numeric input\r\n                          const isNumeric = /^[0-9]$/.test(e.key);\r\n                          const isControl = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key);\r\n                          if (!isNumeric && !isControl) {\r\n                            e.preventDefault();\r\n                          }\r\n                        }}\r\n                        className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                        disabled={usePrimaryForWhatsapp}\r\n                      />\r\n                    </div>\r\n                  </FormControl>\r\n                  <div className=\"flex items-center space-x-1\">\r\n                    <Checkbox\r\n                      id=\"copy_whatsapp\"\r\n                      checked={usePrimaryForWhatsapp}\r\n                      onCheckedChange={(checked) =>\r\n                        setUsePrimaryForWhatsapp(Boolean(checked))\r\n                      }\r\n                      disabled={!primaryPhone}\r\n                    />\r\n                    <Label\r\n                      htmlFor=\"copy_whatsapp\"\r\n                      className=\"text-xs font-normal cursor-pointer text-neutral-700 dark:text-neutral-300\"\r\n                    >\r\n                      Use primary phone\r\n                    </Label>\r\n                  </div>\r\n                </div>\r\n                <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                  Used to generate wa.me link. Enter 10-digit mobile number.\r\n                </FormDescription>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n\r\n          {/* Instagram URL Field */}\r\n          <FormField\r\n            control={form.control}\r\n            name=\"instagram_url\"\r\n            render={({ field }) => (\r\n              <FormItem className=\"space-y-1 sm:space-y-2\">\r\n                <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                  <Instagram className=\"h-3.5 w-3.5 text-primary\" />\r\n                  Instagram Profile URL\r\n                </FormLabel>\r\n                <FormControl>\r\n                  <div className=\"relative\">\r\n                    <Instagram className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400\" />\r\n                    <Input\r\n                      type=\"url\"\r\n                      placeholder=\"https://instagram.com/yourprofile\"\r\n                      {...field}\r\n                      className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                    />\r\n                  </div>\r\n                </FormControl>\r\n                <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                  Your Instagram profile link\r\n                </FormDescription>\r\n                <FormMessage className=\"text-xs text-red-500\" />\r\n              </FormItem>\r\n            )}\r\n          />\r\n        </div>\r\n\r\n        {/* Facebook URL Field */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"facebook_url\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-1 sm:space-y-2\">\r\n              <FormLabel className=\"text-xs font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-1.5\">\r\n                <Facebook className=\"h-3.5 w-3.5 text-primary\" />\r\n                Facebook Page URL\r\n              </FormLabel>\r\n              <FormControl>\r\n                <div className=\"relative\">\r\n                  <Facebook className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-neutral-500 dark:text-neutral-400\" />\r\n                  <Input\r\n                    type=\"url\"\r\n                    placeholder=\"https://facebook.com/yourpage\"\r\n                    {...field}\r\n                    className=\"w-full rounded-lg border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-800/70 py-4 sm:py-5 pl-10 pr-3 text-sm focus:ring-2 focus:ring-primary/20 focus:border-primary shadow-sm transition-all duration-200\"\r\n                  />\r\n                </div>\r\n              </FormControl>\r\n              <FormDescription className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                Your Facebook page link\r\n              </FormDescription>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n      </div>\r\n\r\n      {/* Tip Section */}\r\n      <div className=\"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm\">\r\n        <div className=\"flex items-start gap-2 sm:gap-3\">\r\n          <div className=\"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm\">\r\n            <Link className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300\">\r\n              Links Tip\r\n            </p>\r\n            <p className=\"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed\">\r\n              Including your WhatsApp number and social media links makes it easier for customers to connect with you instantly.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;AAsBe,SAAS,aAAa,EAAE,IAAI,EAAqB;;IAC9D,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,kCAAkC;IAClC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,uEAAuE;IACvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,uBAAuB;gBACzB,KAAK,QAAQ,CAAC,mBAAmB,gBAAgB,IAAI;oBACnD,gBAAgB;oBAChB,aAAa;gBACf;YACF;QACF;iCAAG;QAAC;QAAuB;QAAc;KAAK;IAE9C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAG1F,6LAAC;gCAAE,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAMzE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAA6B;;;;;;;0DAGxD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,4HAAA,CAAA,cAAW;kEACV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;8EACzB,6LAAC,6HAAA,CAAA,QAAK;oEACJ,MAAK;oEACL,SAAQ;oEACR,WAAU;oEACV,aAAY;oEACX,GAAG,KAAK;oEACT,UAAU,CAAC;wEACT,0CAA0C;wEAC1C,IAAI,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS;wEAC5C,kDAAkD;wEAClD,QAAQ,MAAM,OAAO,CAAC,OAAO;wEAC7B,IAAI,MAAM,MAAM,IAAI,IAAI;4EACtB,MAAM,QAAQ,CAAC;wEACjB;oEACF;oEACA,WAAW,CAAC;wEACV,4BAA4B;wEAC5B,MAAM,YAAY,UAAU,IAAI,CAAC,EAAE,GAAG;wEACtC,MAAM,YAAY;4EAAC;4EAAa;4EAAU;4EAAa;4EAAc;yEAAM,CAAC,QAAQ,CAAC,EAAE,GAAG;wEAC1F,IAAI,CAAC,aAAa,CAAC,WAAW;4EAC5B,EAAE,cAAc;wEAClB;oEACF;oEACA,WAAU;oEACV,UAAU;;;;;;;;;;;;;;;;;kEAIhB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,gIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,SAAS;gEACT,iBAAiB,CAAC,UAChB,yBAAyB,QAAQ;gEAEnC,UAAU,CAAC;;;;;;0EAEb,6LAAC,6HAAA,CAAA,QAAK;gEACJ,SAAQ;gEACR,WAAU;0EACX;;;;;;;;;;;;;;;;;;0DAKL,6LAAC,4HAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAsD;;;;;;0DAGjF,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;0CAM7B,6LAAC,4HAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,4HAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAA6B;;;;;;;0DAGpD,6LAAC,4HAAA,CAAA,cAAW;0DACV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC,6HAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,aAAY;4DACX,GAAG,KAAK;4DACT,WAAU;;;;;;;;;;;;;;;;;0DAIhB,6LAAC,4HAAA,CAAA,kBAAe;gDAAC,WAAU;0DAAsD;;;;;;0DAGjF,6LAAC,4HAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAA6B;;;;;;;kDAGnD,6LAAC,4HAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC,6HAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACX,GAAG,KAAK;oDACT,WAAU;;;;;;;;;;;;;;;;;kDAIhB,6LAAC,4HAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAsD;;;;;;kDAGjF,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAO/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAsE;;;;;;8CAGnF,6LAAC;oCAAE,WAAU;8CAA8E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvG;GArLwB;KAAA", "debugId": null}}, {"offset": {"line": 6385, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/slug/slugUtils.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { checkBusinessSlugAvailability } from \"@/lib/utils/slugUtils\";\r\nimport { generateSlug } from \"../utils/slugGenerator\";\r\nimport { validateSlugFormat } from \"../validation/businessCardValidation\";\r\nimport { nanoid, MAX_SLUG_ATTEMPTS } from \"../utils/constants\";\r\n\r\n/**\r\n * Generates a unique slug for a business\r\n * @param businessName - The business name to generate slug from\r\n * @param currentSlug - Current slug if any\r\n * @param userId - User ID for availability checking\r\n * @returns Object with success status and final slug or error\r\n */\r\nexport async function generateUniqueSlug(\r\n  businessName: string,\r\n  currentSlug: string,\r\n  userId: string\r\n): Promise<{ success: boolean; slug?: string; error?: string }> {\r\n  const desiredSlug = currentSlug || generateSlug(businessName);\r\n\r\n  let isUnique = false;\r\n  let checkSlug = desiredSlug;\r\n  let attempts = 0;\r\n\r\n  while (!isUnique && attempts < MAX_SLUG_ATTEMPTS) {\r\n    // Use the shared slug availability check\r\n    const { available, error: slugCheckError } = await checkBusinessSlugAvailability(checkSlug, userId);\r\n\r\n    if (slugCheckError) {\r\n      console.error(\"Slug Check Error:\", slugCheckError);\r\n      return { success: false, error: \"Error checking slug availability.\" };\r\n    }\r\n\r\n    if (available) {\r\n      isUnique = true;\r\n      const finalSlug = checkSlug;\r\n      \r\n      // Validate the final slug format\r\n      const slugValidation = validateSlugFormat(finalSlug);\r\n      if (!slugValidation.success) {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid business slug format generated. Please set one manually.\",\r\n        };\r\n      }\r\n      \r\n      return { success: true, slug: finalSlug };\r\n    } else {\r\n      attempts++;\r\n      checkSlug = `${desiredSlug}-${nanoid()}`;\r\n      if (attempts === MAX_SLUG_ATTEMPTS) {\r\n        return {\r\n          success: false,\r\n          error: `Could not generate a unique slug for '${desiredSlug}'. Please try setting one manually.`,\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  return { success: false, error: \"Failed to generate unique slug.\" };\r\n}\r\n\r\n/**\r\n * Action to check slug availability (wrapper for shared utility)\r\n * @param slug - The slug to check\r\n * @returns Object with availability status\r\n */\r\nexport async function checkSlugAvailability(\r\n  slug: string\r\n): Promise<{ available: boolean; error?: string }> {\r\n  return checkBusinessSlugAvailability(slug);\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAoEsB,wBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 6401, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/StatusSlugSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback, useRef, useMemo } from \"react\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport {\r\n  Globe,\r\n  CheckCircle,\r\n  XCircle,\r\n  Loader2,\r\n  Info,\r\n  Check,\r\n  AlertCircle,\r\n  User,\r\n  Building2,\r\n  Phone,\r\n  MapPin,\r\n  EyeOff,\r\n  Eye,\r\n  Link,\r\n} from \"lucide-react\";\r\nimport { debounce } from \"lodash\";\r\nimport { motion } from \"framer-motion\";\r\nimport { checkSlugAvailability } from \"../../actions\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { RadioGroup, RadioGroupItem } from \"@/components/ui/radio-group\";\r\nimport {\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { BusinessCardData, requiredFieldsForOnline } from \"../../schema\";\r\n\r\ninterface StatusSlugSectionProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  slugStatus: {\r\n    checking: boolean;\r\n    available: boolean | null;\r\n    message: string | null;\r\n  };\r\n  canGoOnline: boolean;\r\n  isSubscriptionHalted?: boolean;\r\n  onSlugCheckingChange?: (_isChecking: boolean) => void;\r\n}\r\n\r\nexport default function StatusSlugSection({\r\n  form,\r\n  canGoOnline,\r\n  isSubscriptionHalted = false,\r\n  onSlugCheckingChange: _onSlugCheckingChange,\r\n}: StatusSlugSectionProps) {\r\n  // Internal state for slug checking\r\n  const [isCheckingSlug, setIsCheckingSlug] = useState(false);\r\n  const [slugAvailable, setSlugAvailable] = useState<boolean | null>(null);\r\n  const [slugToCheck, setSlugToCheck] = useState<string>(\"\");\r\n\r\n  // Helper function to get missing required fields - wrapped in useCallback to prevent recreation on every render\r\n  const getMissingFields = useCallback((): string[] => {\r\n    const formValues = form.getValues();\r\n    return requiredFieldsForOnline.filter(\r\n      (field) => !formValues[field] || String(formValues[field]).trim() === \"\"\r\n    );\r\n  }, [form]);\r\n\r\n  // Debug information - log to console\r\n  useEffect(() => {\r\n    console.log(\"StatusSlugSection - canGoOnline:\", canGoOnline);\r\n    console.log(\"Missing fields:\", getMissingFields());\r\n  }, [canGoOnline, getMissingFields]);\r\n\r\n  // Create a debounced slug check function\r\n  const performSlugCheck = useCallback(\r\n    async (slug: string) => {\r\n      // Don't check if slug is empty or too short\r\n      if (!slug || slug.length < 3 || !/^[a-z0-9-]+$/.test(slug)) {\r\n        setSlugAvailable(null); // Reset if slug is invalid format or too short\r\n        return;\r\n      }\r\n\r\n      // Skip check if it's the same as the current value and we already know it's available\r\n      const currentValue = form.getValues(\"business_slug\");\r\n      if (currentValue === slug && slugAvailable === true) {\r\n        return;\r\n      }\r\n\r\n      setIsCheckingSlug(true);\r\n      _onSlugCheckingChange?.(true);\r\n      try {\r\n        const { available } = await checkSlugAvailability(slug);\r\n        setSlugAvailable(available);\r\n      } catch (error) {\r\n        console.error(\"Error checking slug availability:\", error);\r\n        setSlugAvailable(false);\r\n      } finally {\r\n        setIsCheckingSlug(false);\r\n        _onSlugCheckingChange?.(false);\r\n      }\r\n    },\r\n    [form, slugAvailable, _onSlugCheckingChange]\r\n  );\r\n\r\n  const debouncedSlugCheck = useMemo(\r\n    () => debounce(performSlugCheck, 500),\r\n    [performSlugCheck]\r\n  );\r\n\r\n  // Clean up debounce on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      debouncedSlugCheck.cancel();\r\n    };\r\n  }, [debouncedSlugCheck]);\r\n\r\n  // Trigger slug check when slugToCheck changes\r\n  useEffect(() => {\r\n    if (slugToCheck) {\r\n      debouncedSlugCheck(slugToCheck);\r\n    }\r\n  }, [slugToCheck, debouncedSlugCheck]);\r\n\r\n  // Set form error when slug is not available\r\n  useEffect(() => {\r\n    if (slugAvailable === false) {\r\n      form.setError(\"business_slug\", {\r\n        type: \"manual\",\r\n        message: \"This URL slug is already taken.\",\r\n      });\r\n    } else if (slugAvailable === true) {\r\n      form.clearErrors(\"business_slug\");\r\n    }\r\n  }, [slugAvailable, form]);\r\n\r\n  // Initialize slug check with current value - use a ref to ensure it only runs once\r\n  const initialCheckDone = useRef(false);\r\n\r\n  useEffect(() => {\r\n    if (!initialCheckDone.current) {\r\n      const currentSlug = form.getValues(\"business_slug\");\r\n      if (currentSlug && currentSlug.length >= 3) {\r\n        setSlugToCheck(currentSlug);\r\n      }\r\n      initialCheckDone.current = true;\r\n    }\r\n  }, [form]); // Keep form in dependencies for consistency\r\n\r\n  // Use internal slug status instead of external one\r\n  const slugStatus = {\r\n    checking: isCheckingSlug,\r\n    available: slugAvailable,\r\n    message:\r\n      slugAvailable === true\r\n        ? \"Slug is available!\"\r\n        : slugAvailable === false\r\n        ? \"Slug is already taken.\"\r\n        : null,\r\n  };\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4 }}\r\n      className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-lg p-4 sm:p-5 md:p-6 mb-4 md:mb-6 transition-all duration-300 hover:shadow-xl\"\r\n    >\r\n      {/* Header */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 mb-5 sm:mb-6 pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-3 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 text-primary self-start shadow-sm\">\r\n          <Globe className=\"w-5 sm:w-6 h-5 sm:h-6\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-xl font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Card Status & URL\r\n          </h3>\r\n          <p className=\"text-sm text-neutral-500 dark:text-neutral-400 mt-1\">\r\n            Configure your card&apos;s visibility and unique URL\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex flex-col gap-4 sm:gap-6\">\r\n        {/* Card Status Field */}\r\n        <FormField\r\n          control={form.control}\r\n          name=\"status\"\r\n          render={({ field }) => (\r\n            <FormItem className=\"space-y-3 sm:space-y-4\">\r\n              <FormLabel className=\"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                <Globe className=\"h-4 w-4 text-primary\" />\r\n                Card Status\r\n              </FormLabel>\r\n              <RadioGroup\r\n                onValueChange={field.onChange}\r\n                value={field.value}\r\n                className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-5\"\r\n              >\r\n                {/* Offline Option */}\r\n                <Label htmlFor=\"status-offline\" className=\"cursor-pointer\">\r\n                  <motion.div\r\n                    whileHover={{ scale: 1.02 }}\r\n                    whileTap={{ scale: 0.98 }}\r\n                    transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\r\n                    className={`relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm\r\n                      ${field.value === \"offline\"\r\n                        ? \"border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10\"\r\n                        : \"border-neutral-200 dark:border-neutral-700 hover:border-neutral-300 dark:hover:border-neutral-600\"\r\n                      }\r\n                    `}\r\n                  >\r\n                    {field.value === \"offline\" && (\r\n                      <div className=\"absolute top-0 left-0 w-full h-1 bg-[--theme-color]\"></div>\r\n                    )}\r\n                    <div className=\"p-4 sm:p-5\">\r\n                      <div className=\"flex items-center justify-between mb-3\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                          <div className={`p-2 rounded-full ${field.value === \"offline\" ? \"bg-[--theme-color]/20\" : \"bg-neutral-100 dark:bg-neutral-800\"}`}>\r\n                            <EyeOff className={`h-5 w-5 ${field.value === \"offline\" ? \"text-[--theme-color]\" : \"text-neutral-500 dark:text-neutral-400\"}`} />\r\n                          </div>\r\n                          <h4 className=\"text-base font-semibold text-neutral-800 dark:text-neutral-100\">\r\n                            Offline (Private)\r\n                          </h4>\r\n                        </div>\r\n                        <RadioGroupItem\r\n                          value=\"offline\"\r\n                          id=\"status-offline\"\r\n                          className=\"translate-y-0\"\r\n                        />\r\n                      </div>\r\n                      <p className=\"text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed\">\r\n                        Your card is not publicly visible. Use for expired plans\r\n                        or private cards.\r\n                      </p>\r\n                    </div>\r\n                  </motion.div>\r\n                </Label>\r\n\r\n                {/* Online Option */}\r\n                <Label\r\n                  htmlFor=\"status-online\"\r\n                  className={`${!canGoOnline ? \"cursor-not-allowed\" : \"cursor-pointer\"}`}\r\n                >\r\n                  <motion.div\r\n                    whileHover={canGoOnline ? { scale: 1.02 } : {}}\r\n                    whileTap={canGoOnline ? { scale: 0.98 } : {}}\r\n                    transition={{ type: \"spring\", stiffness: 400, damping: 17 }}\r\n                    className={`relative h-full rounded-xl overflow-hidden border-2 transition-all duration-300 shadow-sm\r\n                      ${field.value === \"online\"\r\n                        ? \"border-[--theme-color] bg-[--theme-color]/5 shadow-[--theme-color]/10\"\r\n                        : \"border-neutral-200 dark:border-neutral-700\"\r\n                      }\r\n                      ${!canGoOnline ? \"opacity-70\" : \"hover:border-neutral-300 dark:hover:border-neutral-600\"}\r\n                    `}\r\n                  >\r\n                    {field.value === \"online\" && (\r\n                      <div className=\"absolute top-0 left-0 w-full h-1 bg-[--theme-color]\"></div>\r\n                    )}\r\n                    <div className=\"p-4 sm:p-5\">\r\n                      <div className=\"flex items-center justify-between mb-3\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                          <div className={`p-2 rounded-full ${field.value === \"online\" ? \"bg-[--theme-color]/20\" : \"bg-neutral-100 dark:bg-neutral-800\"}`}>\r\n                            <Eye className={`h-5 w-5 ${field.value === \"online\" ? \"text-[--theme-color]\" : \"text-neutral-500 dark:text-neutral-400\"}`} />\r\n                          </div>\r\n                          <h4 className=\"text-base font-semibold text-neutral-800 dark:text-neutral-100\">\r\n                            Online (Public)\r\n                          </h4>\r\n                        </div>\r\n                        <RadioGroupItem\r\n                          value=\"online\"\r\n                          id=\"status-online\"\r\n                          disabled={!canGoOnline}\r\n                          className=\"translate-y-0\"\r\n                        />\r\n                      </div>\r\n                      <p className=\"text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed\">\r\n                        Make your card accessible via a unique URL. Requires an\r\n                        active plan and unique slug.\r\n                      </p>\r\n\r\n                      {isSubscriptionHalted && (\r\n                        <div className=\"mt-3 p-3 rounded-lg bg-red-50 dark:bg-red-950/30 border border-red-200 dark:border-red-800/50\">\r\n                          <p className=\"text-xs text-red-700 dark:text-red-400 flex items-center font-medium mb-2\">\r\n                            <AlertCircle className=\"w-3.5 h-3.5 mr-1.5 flex-shrink-0\" />\r\n                            Subscription Paused\r\n                          </p>\r\n                          <p className=\"text-xs text-red-600 dark:text-red-400\">\r\n                            Your subscription is currently paused. You cannot set your card to online status until you resume your subscription.\r\n                            Please visit the Plan page to resume your subscription.\r\n                          </p>\r\n                        </div>\r\n                      )}\r\n\r\n                      {!canGoOnline && !isSubscriptionHalted && (\r\n                        <div className=\"mt-3 p-3 rounded-lg bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800/50\">\r\n                          <p className=\"text-xs text-amber-700 dark:text-amber-400 flex items-center font-medium mb-2\">\r\n                            <AlertCircle className=\"w-3.5 h-3.5 mr-1.5 flex-shrink-0\" />\r\n                            Required fields to go online:\r\n                          </p>\r\n                          <div className=\"grid grid-cols-2 gap-x-3 gap-y-1.5\">\r\n                            {/* Map through required fields and show their status */}\r\n                            {(() => {\r\n                              const missingFields = getMissingFields();\r\n                              const fieldIcons = {\r\n                                member_name: <User className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                title: <User className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                business_name: <Building2 className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                phone: <Phone className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                address_line: <Building2 className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                pincode: <MapPin className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                city: <MapPin className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                state: <MapPin className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                                locality: <MapPin className=\"w-3 h-3 mr-1.5 flex-shrink-0\" />,\r\n                              };\r\n\r\n                              const fieldLabels = {\r\n                                member_name: \"Your Name\",\r\n                                title: \"Your Title\",\r\n                                business_name: \"Business Name\",\r\n                                phone: \"Primary Phone\",\r\n                                address_line: \"Address Line\",\r\n                                pincode: \"Pincode\",\r\n                                city: \"City\",\r\n                                state: \"State\",\r\n                                locality: \"Locality\",\r\n                              };\r\n\r\n                              return requiredFieldsForOnline.map((field) => {\r\n                                const isMissing = missingFields.includes(field);\r\n                                return (\r\n                                  <p\r\n                                    key={field}\r\n                                    className={`text-xs flex items-center ${\r\n                                      isMissing\r\n                                        ? \"text-red-500 dark:text-red-400 font-medium\"\r\n                                        : \"text-green-500 dark:text-green-400\"\r\n                                    }`}\r\n                                  >\r\n                                    {fieldIcons[field as keyof typeof fieldIcons]}\r\n                                    {fieldLabels[field as keyof typeof fieldLabels]}\r\n                                    {!isMissing && <Check className=\"w-3 h-3 ml-1 flex-shrink-0\" />}\r\n                                  </p>\r\n                                );\r\n                              });\r\n                            })()}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </motion.div>\r\n                </Label>\r\n              </RadioGroup>\r\n              <FormMessage className=\"text-xs text-red-500\" />\r\n            </FormItem>\r\n          )}\r\n        />\r\n\r\n        {/* Unique Card URL (Slug) Field */}\r\n        {form.watch(\"status\") === \"online\" && (\r\n          <motion.div\r\n            initial={{ opacity: 0, height: 0 }}\r\n            animate={{ opacity: 1, height: \"auto\" }}\r\n            exit={{ opacity: 0, height: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          >\r\n            <FormField\r\n              control={form.control}\r\n              name=\"business_slug\"\r\n              render={({ field }) => (\r\n                <FormItem className=\"space-y-3 sm:space-y-4 mt-2\">\r\n                  <FormLabel className=\"text-sm font-semibold text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                    <Link className=\"h-4 w-4 text-primary\" />\r\n                    Unique Card URL\r\n                  </FormLabel>\r\n\r\n                  <div className=\"p-4 sm:p-5 rounded-xl border border-neutral-200 dark:border-neutral-700 bg-neutral-50/50 dark:bg-neutral-800/30 shadow-sm\">\r\n                    <div className=\"flex flex-col space-y-3\">\r\n                      {/* Input Field */}\r\n                      <div className=\"flex items-center space-x-0 rounded-lg overflow-hidden border-2 border-neutral-200 dark:border-neutral-700 focus-within:border-primary/70 focus-within:ring-2 focus-within:ring-primary/20 transition-all duration-200 shadow-sm\">\r\n                        <div className=\"bg-neutral-100 dark:bg-neutral-800 px-3 sm:px-4 py-3 border-r border-neutral-200 dark:border-neutral-700 text-neutral-600 dark:text-neutral-300 text-sm font-medium flex items-center\">\r\n                          <Globe className=\"h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400\" />\r\n                          dukancard.in/\r\n                        </div>\r\n                        <div className=\"relative flex-1\">\r\n                          <FormControl>\r\n                            <Input\r\n                              placeholder=\"your-unique-name\"\r\n                              {...field}\r\n                              className=\"w-full border-0 bg-white dark:bg-black py-3 px-4 text-base focus:ring-0 shadow-none\"\r\n                              onChange={(e) => {\r\n                                // Format the slug to ensure it's valid\r\n                                const slug = e.target.value\r\n                                  .toLowerCase()\r\n                                  .replace(/\\s+/g, \"-\")\r\n                                  .replace(/[^a-z0-9-]/g, \"\");\r\n\r\n                                // Update the form field with the formatted slug\r\n                                field.onChange(slug);\r\n\r\n                                // Trigger slug check\r\n                                setSlugToCheck(slug);\r\n                                setSlugAvailable(null);\r\n                              }}\r\n                            />\r\n                          </FormControl>\r\n                          <div className=\"absolute right-3 top-1/2 transform -translate-y-1/2\">\r\n                            {slugStatus.checking && (\r\n                              <div className=\"bg-neutral-100 dark:bg-neutral-800 p-1 rounded-full\">\r\n                                <Loader2 className=\"w-5 h-5 text-primary animate-spin\" />\r\n                              </div>\r\n                            )}\r\n                            {!slugStatus.checking && slugStatus.available === true && (\r\n                              <div className=\"bg-green-50 dark:bg-green-900/30 p-1 rounded-full\">\r\n                                <CheckCircle className=\"w-5 h-5 text-green-500 dark:text-green-400\" />\r\n                              </div>\r\n                            )}\r\n                            {!slugStatus.checking && slugStatus.available === false && (\r\n                              <div className=\"bg-red-50 dark:bg-red-900/30 p-1 rounded-full\">\r\n                                <XCircle className=\"w-5 h-5 text-red-500 dark:text-red-400\" />\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* URL Preview */}\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"px-3 py-2 rounded-lg bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-sm font-mono flex items-center\">\r\n                          <Link className=\"h-4 w-4 mr-2 text-neutral-500 dark:text-neutral-400\" />\r\n                          https://dukancard.in/{field.value || \"your-unique-name\"}\r\n                        </div>\r\n                      </div>\r\n\r\n                      {/* Status Message */}\r\n                      <div className=\"flex items-center h-6 px-1\">\r\n                        {slugStatus.checking && (\r\n                          <p className=\"text-sm text-neutral-500 flex items-center\">\r\n                            <Loader2 className=\"w-3.5 h-3.5 mr-2 animate-spin\" />\r\n                            Checking availability...\r\n                          </p>\r\n                        )}\r\n                        {!slugStatus.checking && slugStatus.available === true && !form.formState.errors.business_slug && (\r\n                          <p className=\"text-sm text-green-600 dark:text-green-400 flex items-center\">\r\n                            <CheckCircle className=\"w-3.5 h-3.5 mr-2\" />\r\n                            URL is available!\r\n                          </p>\r\n                        )}\r\n                        {!slugStatus.checking && slugStatus.available === false && (\r\n                          <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\r\n                            <AlertCircle className=\"w-3.5 h-3.5 mr-2\" />\r\n                            URL is already taken.\r\n                          </p>\r\n                        )}\r\n                        {!slugStatus.checking && slugStatus.available === null && (\r\n                          <p className=\"text-sm text-neutral-600 dark:text-neutral-400 flex items-center\">\r\n                            <Info className=\"w-3.5 h-3.5 mr-2\" />\r\n                            Lowercase letters, numbers, hyphens only. Min 3 chars.\r\n                          </p>\r\n                        )}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <FormMessage className=\"text-sm text-red-500\" />\r\n                </FormItem>\r\n              )}\r\n            />\r\n          </motion.div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Tip Section */}\r\n      <div className=\"mt-4 sm:mt-6 rounded-lg bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/20 p-3 sm:p-4 border border-violet-100 dark:border-violet-900/30 shadow-sm\">\r\n        <div className=\"flex items-start gap-2 sm:gap-3\">\r\n          <div className=\"p-1.5 rounded-full bg-violet-100 dark:bg-violet-900/60 text-violet-600 dark:text-violet-300 mt-0.5 shadow-sm\">\r\n            <Info className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"text-xs sm:text-sm font-medium text-violet-800 dark:text-violet-300\">\r\n              Status & URL Tip\r\n            </p>\r\n            <p className=\"text-xs text-violet-700 dark:text-violet-400 mt-0.5 sm:mt-1 leading-relaxed\">\r\n              Choose a unique, memorable slug for your card&apos;s URL to make\r\n              it easy to share. Set to &apos;Online&apos; to make your card\r\n              publicly accessible.\r\n            </p>\r\n            <p className=\"text-xs text-violet-700 dark:text-violet-400 mt-1.5 leading-relaxed\">\r\n              To set your card status to &apos;Online&apos;, you must fill in all required fields:\r\n              your name, title, business name, primary phone, address line, pincode, city, state, and locality.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAOA;;;AAjCA;;;;;;;;;;;AA+Ce,SAAS,kBAAkB,EACxC,IAAI,EACJ,WAAW,EACX,uBAAuB,KAAK,EAC5B,sBAAsB,qBAAqB,EACpB;;IACvB,mCAAmC;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,gHAAgH;IAChH,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACnC,MAAM,aAAa,KAAK,SAAS;YACjC,OAAO,kKAAA,CAAA,0BAAuB,CAAC,MAAM;mEACnC,CAAC,QAAU,CAAC,UAAU,CAAC,MAAM,IAAI,OAAO,UAAU,CAAC,MAAM,EAAE,IAAI,OAAO;;QAE1E;0DAAG;QAAC;KAAK;IAET,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,QAAQ,GAAG,CAAC,oCAAoC;YAChD,QAAQ,GAAG,CAAC,mBAAmB;QACjC;sCAAG;QAAC;QAAa;KAAiB;IAElC,yCAAyC;IACzC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DACjC,OAAO;YACL,4CAA4C;YAC5C,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,eAAe,IAAI,CAAC,OAAO;gBAC1D,iBAAiB,OAAO,+CAA+C;gBACvE;YACF;YAEA,sFAAsF;YACtF,MAAM,eAAe,KAAK,SAAS,CAAC;YACpC,IAAI,iBAAiB,QAAQ,kBAAkB,MAAM;gBACnD;YACF;YAEA,kBAAkB;YAClB,wBAAwB;YACxB,IAAI;gBACF,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAA,GAAA,wMAAA,CAAA,wBAAqB,AAAD,EAAE;gBAClD,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,iBAAiB;YACnB,SAAU;gBACR,kBAAkB;gBAClB,wBAAwB;YAC1B;QACF;0DACA;QAAC;QAAM;QAAe;KAAsB;IAG9C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAC/B,IAAM,CAAA,GAAA,qIAAA,CAAA,UAAQ,AAAD,EAAE,kBAAkB;wDACjC;QAAC;KAAiB;IAGpB,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;+CAAO;oBACL,mBAAmB,MAAM;gBAC3B;;QACF;sCAAG;QAAC;KAAmB;IAEvB,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,aAAa;gBACf,mBAAmB;YACrB;QACF;sCAAG;QAAC;QAAa;KAAmB;IAEpC,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,kBAAkB,OAAO;gBAC3B,KAAK,QAAQ,CAAC,iBAAiB;oBAC7B,MAAM;oBACN,SAAS;gBACX;YACF,OAAO,IAAI,kBAAkB,MAAM;gBACjC,KAAK,WAAW,CAAC;YACnB;QACF;sCAAG;QAAC;QAAe;KAAK;IAExB,mFAAmF;IACnF,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,iBAAiB,OAAO,EAAE;gBAC7B,MAAM,cAAc,KAAK,SAAS,CAAC;gBACnC,IAAI,eAAe,YAAY,MAAM,IAAI,GAAG;oBAC1C,eAAe;gBACjB;gBACA,iBAAiB,OAAO,GAAG;YAC7B;QACF;sCAAG;QAAC;KAAK,GAAG,4CAA4C;IAExD,mDAAmD;IACnD,MAAM,aAAa;QACjB,UAAU;QACV,WAAW;QACX,SACE,kBAAkB,OACd,uBACA,kBAAkB,QAClB,2BACA;IACR;IACA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4E;;;;;;0CAG1F,6LAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;0BAMvE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,4HAAA,CAAA,YAAS;wBACR,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,6LAAC,4HAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAyB;;;;;;;kDAG5C,6LAAC,sIAAA,CAAA,aAAU;wCACT,eAAe,MAAM,QAAQ;wCAC7B,OAAO,MAAM,KAAK;wCAClB,WAAU;;0DAGV,6LAAC,6HAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAiB,WAAU;0DACxC,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,UAAU;wDAAE,OAAO;oDAAK;oDACxB,YAAY;wDAAE,MAAM;wDAAU,WAAW;wDAAK,SAAS;oDAAG;oDAC1D,WAAW,CAAC;sBACV,EAAE,MAAM,KAAK,KAAK,YACd,0EACA,oGACH;oBACH,CAAC;;wDAEA,MAAM,KAAK,KAAK,2BACf,6LAAC;4DAAI,WAAU;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAW,CAAC,iBAAiB,EAAE,MAAM,KAAK,KAAK,YAAY,0BAA0B,sCAAsC;8FAC9H,cAAA,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,KAAK,KAAK,YAAY,yBAAyB,0CAA0C;;;;;;;;;;;8FAE/H,6LAAC;oFAAG,WAAU;8FAAiE;;;;;;;;;;;;sFAIjF,6LAAC,sIAAA,CAAA,iBAAc;4EACb,OAAM;4EACN,IAAG;4EACH,WAAU;;;;;;;;;;;;8EAGd,6LAAC;oEAAE,WAAU;8EAAiE;;;;;;;;;;;;;;;;;;;;;;;0DASpF,6LAAC,6HAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAW,GAAG,CAAC,cAAc,uBAAuB,kBAAkB;0DAEtE,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY,cAAc;wDAAE,OAAO;oDAAK,IAAI,CAAC;oDAC7C,UAAU,cAAc;wDAAE,OAAO;oDAAK,IAAI,CAAC;oDAC3C,YAAY;wDAAE,MAAM;wDAAU,WAAW;wDAAK,SAAS;oDAAG;oDAC1D,WAAW,CAAC;sBACV,EAAE,MAAM,KAAK,KAAK,WACd,0EACA,6CACH;sBACD,EAAE,CAAC,cAAc,eAAe,yDAAyD;oBAC3F,CAAC;;wDAEA,MAAM,KAAK,KAAK,0BACf,6LAAC;4DAAI,WAAU;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAW,CAAC,iBAAiB,EAAE,MAAM,KAAK,KAAK,WAAW,0BAA0B,sCAAsC;8FAC7H,cAAA,6LAAC,mMAAA,CAAA,MAAG;wFAAC,WAAW,CAAC,QAAQ,EAAE,MAAM,KAAK,KAAK,WAAW,yBAAyB,0CAA0C;;;;;;;;;;;8FAE3H,6LAAC;oFAAG,WAAU;8FAAiE;;;;;;;;;;;;sFAIjF,6LAAC,sIAAA,CAAA,iBAAc;4EACb,OAAM;4EACN,IAAG;4EACH,UAAU,CAAC;4EACX,WAAU;;;;;;;;;;;;8EAGd,6LAAC;oEAAE,WAAU;8EAAiE;;;;;;gEAK7E,sCACC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;;8FACX,6LAAC,uNAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;gFAAqC;;;;;;;sFAG9D,6LAAC;4EAAE,WAAU;sFAAyC;;;;;;;;;;;;gEAOzD,CAAC,eAAe,CAAC,sCAChB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;;8FACX,6LAAC,uNAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;gFAAqC;;;;;;;sFAG9D,6LAAC;4EAAI,WAAU;sFAEZ,CAAC;gFACA,MAAM,gBAAgB;gFACtB,MAAM,aAAa;oFACjB,2BAAa,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFAC7B,qBAAO,6LAAC,qMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;oFACvB,6BAAe,6LAAC,mNAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFACpC,qBAAO,6LAAC,uMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFACxB,4BAAc,6LAAC,mNAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;oFACnC,uBAAS,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFAC3B,oBAAM,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACxB,qBAAO,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACzB,wBAAU,6LAAC,6MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;gFAC9B;gFAEA,MAAM,cAAc;oFAClB,aAAa;oFACb,OAAO;oFACP,eAAe;oFACf,OAAO;oFACP,cAAc;oFACd,SAAS;oFACT,MAAM;oFACN,OAAO;oFACP,UAAU;gFACZ;gFAEA,OAAO,kKAAA,CAAA,0BAAuB,CAAC,GAAG,CAAC,CAAC;oFAClC,MAAM,YAAY,cAAc,QAAQ,CAAC;oFACzC,qBACE,6LAAC;wFAEC,WAAW,CAAC,0BAA0B,EACpC,YACI,+CACA,sCACJ;;4FAED,UAAU,CAAC,MAAiC;4FAC5C,WAAW,CAAC,MAAkC;4FAC9C,CAAC,2BAAa,6LAAC,uMAAA,CAAA,QAAK;gGAAC,WAAU;;;;;;;uFAT3B;;;;;gFAYX;4EACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQf,6LAAC,4HAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;oBAM5B,KAAK,KAAK,CAAC,cAAc,0BACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC,4HAAA,CAAA,YAAS;4BACR,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,6LAAC,4HAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,4HAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAyB;;;;;;;sDAI3C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAwD;;;;;;;0EAG3E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,4HAAA,CAAA,cAAW;kFACV,cAAA,6LAAC,6HAAA,CAAA,QAAK;4EACJ,aAAY;4EACX,GAAG,KAAK;4EACT,WAAU;4EACV,UAAU,CAAC;gFACT,uCAAuC;gFACvC,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CACxB,WAAW,GACX,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,eAAe;gFAE1B,gDAAgD;gFAChD,MAAM,QAAQ,CAAC;gFAEf,qBAAqB;gFACrB,eAAe;gFACf,iBAAiB;4EACnB;;;;;;;;;;;kFAGJ,6LAAC;wEAAI,WAAU;;4EACZ,WAAW,QAAQ,kBAClB,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;;;;;;4EAGtB,CAAC,WAAW,QAAQ,IAAI,WAAW,SAAS,KAAK,sBAChD,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;oFAAC,WAAU;;;;;;;;;;;4EAG1B,CAAC,WAAW,QAAQ,IAAI,WAAW,SAAS,KAAK,uBAChD,6LAAC;gFAAI,WAAU;0FACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kEAQ7B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAwD;gEAClD,MAAM,KAAK,IAAI;;;;;;;;;;;;kEAKzC,6LAAC;wDAAI,WAAU;;4DACZ,WAAW,QAAQ,kBAClB,6LAAC;gEAAE,WAAU;;kFACX,6LAAC,oNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;oEAAkC;;;;;;;4DAIxD,CAAC,WAAW,QAAQ,IAAI,WAAW,SAAS,KAAK,QAAQ,CAAC,KAAK,SAAS,CAAC,MAAM,CAAC,aAAa,kBAC5F,6LAAC;gEAAE,WAAU;;kFACX,6LAAC,8NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAqB;;;;;;;4DAI/C,CAAC,WAAW,QAAQ,IAAI,WAAW,SAAS,KAAK,uBAChD,6LAAC;gEAAE,WAAU;;kFACX,6LAAC,uNAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAqB;;;;;;;4DAI/C,CAAC,WAAW,QAAQ,IAAI,WAAW,SAAS,KAAK,sBAChD,6LAAC;gEAAE,WAAU;;kFACX,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAqB;;;;;;;;;;;;;;;;;;;;;;;;sDAQ/C,6LAAC,4HAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASnC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC;;8CACC,6LAAC;oCAAE,WAAU;8CAAsE;;;;;;8CAGnF,6LAAC;oCAAE,WAAU;8CAA8E;;;;;;8CAK3F,6LAAC;oCAAE,WAAU;8CAAsE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/F;GA9bwB;KAAA", "debugId": null}}, {"offset": {"line": 7435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/CardEditFormContent.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport { UserPlanStatus } from \"../../CardEditorClient\";\r\n\r\n// Import all the form sections\r\nimport BasicInfoSection from \"./BasicInfoSection\";\r\nimport ContactLocationSection from \"./ContactLocationSection\";\r\nimport AppearanceSection from \"./AppearanceSection\";\r\nimport CustomBrandingSection from \"./CustomBrandingSection\";\r\nimport CustomAdUpload from \"./CustomAdUpload\";\r\nimport BusinessDetailsSection from \"./BusinessDetailsSection\";\r\nimport LinksSection from \"./LinksSection\";\r\nimport StatusSlugSection from \"./StatusSlugSection\";\r\n\r\ninterface CardEditFormContentProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  canGoOnline: boolean;\r\n  currentUserPlan: UserPlanStatus | null;\r\n  onFileSelect: (_file: File | null) => void;\r\n  isPincodeLoading: boolean;\r\n  availableLocalities: string[];\r\n  onPincodeChange: (_pincode: string) => void;\r\n  isLogoUploading: boolean;\r\n  onLogoDelete: () => void;\r\n  isSubscriptionHalted: boolean;\r\n  onSlugCheckingChange?: (_isChecking: boolean) => void;\r\n}\r\n\r\nexport default function CardEditFormContent({\r\n  form,\r\n  canGoOnline,\r\n  currentUserPlan,\r\n  onFileSelect,\r\n  isPincodeLoading,\r\n  availableLocalities,\r\n  onPincodeChange,\r\n  isLogoUploading,\r\n  onLogoDelete,\r\n  isSubscriptionHalted,\r\n  onSlugCheckingChange: _onSlugCheckingChange,\r\n}: CardEditFormContentProps) {\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Basic Info Section */}\r\n      <BasicInfoSection\r\n        form={form}\r\n        onFileSelect={onFileSelect}\r\n        isLogoUploading={isLogoUploading}\r\n        onLogoDelete={onLogoDelete}\r\n      />\r\n\r\n      {/* Contact & Location Section */}\r\n      <ContactLocationSection\r\n        form={form}\r\n        isPincodeLoading={isPincodeLoading}\r\n        availableLocalities={availableLocalities}\r\n        onPincodeChange={onPincodeChange}\r\n      />\r\n\r\n      {/* Appearance Section */}\r\n      <AppearanceSection form={form} currentUserPlan={currentUserPlan} />\r\n\r\n      {/* Custom Branding Section - Only for Pro/Enterprise */}\r\n      <CustomBrandingSection form={form} currentUserPlan={currentUserPlan} />\r\n\r\n      {/* Custom Ad Upload Section - Only for Pro/Enterprise */}\r\n      <CustomAdUpload form={form} currentUserPlan={currentUserPlan} />\r\n\r\n      {/* Business Details Section */}\r\n      <BusinessDetailsSection form={form} />\r\n\r\n      {/* Links Section */}\r\n      <LinksSection form={form} />\r\n\r\n      {/* Status & Slug Section */}\r\n      <StatusSlugSection\r\n        form={form}\r\n        slugStatus={{ checking: false, available: null, message: null }}\r\n        canGoOnline={canGoOnline}\r\n        isSubscriptionHalted={isSubscriptionHalted}\r\n        onSlugCheckingChange={_onSlugCheckingChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAMA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;AA8Be,SAAS,oBAAoB,EAC1C,IAAI,EACJ,WAAW,EACX,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,eAAe,EACf,YAAY,EACZ,oBAAoB,EACpB,sBAAsB,qBAAqB,EAClB;IACzB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,2MAAA,CAAA,UAAgB;gBACf,MAAM;gBACN,cAAc;gBACd,iBAAiB;gBACjB,cAAc;;;;;;0BAIhB,6LAAC,iNAAA,CAAA,UAAsB;gBACrB,MAAM;gBACN,kBAAkB;gBAClB,qBAAqB;gBACrB,iBAAiB;;;;;;0BAInB,6LAAC,4MAAA,CAAA,UAAiB;gBAAC,MAAM;gBAAM,iBAAiB;;;;;;0BAGhD,6LAAC,gNAAA,CAAA,UAAqB;gBAAC,MAAM;gBAAM,iBAAiB;;;;;;0BAGpD,6LAAC,yMAAA,CAAA,UAAc;gBAAC,MAAM;gBAAM,iBAAiB;;;;;;0BAG7C,6LAAC,iNAAA,CAAA,UAAsB;gBAAC,MAAM;;;;;;0BAG9B,6LAAC,uMAAA,CAAA,UAAY;gBAAC,MAAM;;;;;;0BAGpB,6LAAC,4MAAA,CAAA,UAAiB;gBAChB,MAAM;gBACN,YAAY;oBAAE,UAAU;oBAAO,WAAW;oBAAM,SAAS;gBAAK;gBAC9D,aAAa;gBACb,sBAAsB;gBACtB,sBAAsB;;;;;;;;;;;;AAI9B;KAxDwB", "debugId": null}}, {"offset": {"line": 7554, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/utils/cardUtils.ts"], "sourcesContent": ["export const formatWhatsAppUrl = (number?: string): string | undefined => {\r\n  if (!number) return undefined;\r\n  const digits = number.replace(/\\D/g, \"\");\r\n  if (digits.length < 10) return undefined;\r\n  const formattedNumber = digits.startsWith(\"91\") ? digits : `91${digits}`;\r\n  return `https://wa.me/${formattedNumber}`;\r\n};\r\n\r\nexport const formatTelUrl = (number?: string): string | undefined => {\r\n  if (!number) return undefined;\r\n  const digits = number.replace(/\\D/g, \"\");\r\n  if (digits.length < 10) return undefined;\r\n  return `tel:+91${digits}`;\r\n};\r\n\r\nexport const formatPrice = (price: number | null | undefined): string => {\r\n  if (price === null || price === undefined) return \"N/A\";\r\n  return `₹${price.toLocaleString(\"en-IN\")}`;\r\n};\r\n\r\nexport const formatTimeTo12Hour = (time24: string): string => {\r\n  if (!time24 || time24.length < 5) return time24;\r\n\r\n  const [hourStr, minuteStr] = time24.split(\":\");\r\n  const hour = parseInt(hourStr, 10);\r\n\r\n  if (isNaN(hour)) return time24;\r\n\r\n  const period = hour >= 12 ? \"PM\" : \"AM\";\r\n  const hour12 = hour % 12 || 12;\r\n  return `${hour12}:${minuteStr} ${period}`;\r\n};\r\n\r\nexport const formatDayGroup = (days: string[]): string => {\r\n  const dayAbbreviations: Record<string, string> = {\r\n    monday: \"Mon\",\r\n    tuesday: \"Tue\",\r\n    wednesday: \"Wed\",\r\n    thursday: \"Thu\",\r\n    friday: \"Fri\",\r\n    saturday: \"Sat\",\r\n    sunday: \"Sun\",\r\n  };\r\n\r\n  const dayOrder = [\r\n    \"monday\",\r\n    \"tuesday\",\r\n    \"wednesday\",\r\n    \"thursday\",\r\n    \"friday\",\r\n    \"saturday\",\r\n    \"sunday\",\r\n  ];\r\n  const sortedDays = [...days].sort(\r\n    (a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b)\r\n  );\r\n\r\n  const shortDays = sortedDays.map((day) => dayAbbreviations[day] || day);\r\n\r\n  const weekdays = [\"monday\", \"tuesday\", \"wednesday\", \"thursday\", \"friday\"];\r\n  if (\r\n    sortedDays.length === 5 &&\r\n    weekdays.every((day) => sortedDays.includes(day))\r\n  ) {\r\n    return \"Mon-Fri\";\r\n  }\r\n\r\n  if (\r\n    sortedDays.length === 2 &&\r\n    sortedDays.includes(\"saturday\") &&\r\n    sortedDays.includes(\"sunday\")\r\n  ) {\r\n    return \"Sat-Sun\";\r\n  }\r\n\r\n  if (sortedDays.length === 7) {\r\n    return \"All days\";\r\n  }\r\n\r\n  if (isConsecutive(sortedDays, dayOrder)) {\r\n    return `${shortDays[0]}-${shortDays[shortDays.length - 1]}`;\r\n  }\r\n\r\n  return shortDays.join(\", \");\r\n};\r\n\r\nexport const isConsecutive = (days: string[], dayOrder: string[]): boolean => {\r\n  if (days.length <= 1) return true;\r\n\r\n  const indices = days\r\n    .map((day) => dayOrder.indexOf(day))\r\n    .sort((a, b) => a - b);\r\n\r\n  for (let i = 1; i < indices.length; i++) {\r\n    if (indices[i] !== indices[i - 1] + 1) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\n// Generate card style with theme colors\r\nexport const generateCardStyle = (\r\n  finalThemeColor: string\r\n): React.CSSProperties => {\r\n  return {\r\n    \"--theme-color\": finalThemeColor,\r\n    \"--theme-color-80\": `${finalThemeColor}CC`,\r\n    \"--theme-color-50\": `${finalThemeColor}80`,\r\n    \"--theme-color-30\": `${finalThemeColor}4D`,\r\n    \"--theme-color-20\": `${finalThemeColor}33`,\r\n    \"--theme-color-10\": `${finalThemeColor}1A`,\r\n    \"--theme-color-5\": `${finalThemeColor}0D`,\r\n    \"--theme-accent-end\": \"#E5C76E\", // Less yellow accent\r\n  } as React.CSSProperties;\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,SAAS,OAAO,OAAO,CAAC,OAAO;IACrC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO;IAC/B,MAAM,kBAAkB,OAAO,UAAU,CAAC,QAAQ,SAAS,CAAC,EAAE,EAAE,QAAQ;IACxE,OAAO,CAAC,cAAc,EAAE,iBAAiB;AAC3C;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,SAAS,OAAO,OAAO,CAAC,OAAO;IACrC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO;IAC/B,OAAO,CAAC,OAAO,EAAE,QAAQ;AAC3B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,OAAO,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,UAAU;AAC5C;AAEO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG,OAAO;IAEzC,MAAM,CAAC,SAAS,UAAU,GAAG,OAAO,KAAK,CAAC;IAC1C,MAAM,OAAO,SAAS,SAAS;IAE/B,IAAI,MAAM,OAAO,OAAO;IAExB,MAAM,SAAS,QAAQ,KAAK,OAAO;IACnC,MAAM,SAAS,OAAO,MAAM;IAC5B,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,mBAA2C;QAC/C,QAAQ;QACR,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAC/B,CAAC,GAAG,IAAM,SAAS,OAAO,CAAC,KAAK,SAAS,OAAO,CAAC;IAGnD,MAAM,YAAY,WAAW,GAAG,CAAC,CAAC,MAAQ,gBAAgB,CAAC,IAAI,IAAI;IAEnE,MAAM,WAAW;QAAC;QAAU;QAAW;QAAa;QAAY;KAAS;IACzE,IACE,WAAW,MAAM,KAAK,KACtB,SAAS,KAAK,CAAC,CAAC,MAAQ,WAAW,QAAQ,CAAC,OAC5C;QACA,OAAO;IACT;IAEA,IACE,WAAW,MAAM,KAAK,KACtB,WAAW,QAAQ,CAAC,eACpB,WAAW,QAAQ,CAAC,WACpB;QACA,OAAO;IACT;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IAEA,IAAI,cAAc,YAAY,WAAW;QACvC,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,EAAE;IAC7D;IAEA,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,MAAM,gBAAgB,CAAC,MAAgB;IAC5C,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;IAE7B,MAAM,UAAU,KACb,GAAG,CAAC,CAAC,MAAQ,SAAS,OAAO,CAAC,MAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAC/B;IAEA,OAAO;QACL,iBAAiB;QACjB,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,mBAAmB,GAAG,gBAAgB,EAAE,CAAC;QACzC,sBAAsB;IACxB;AACF", "debugId": null}}, {"offset": {"line": 7664, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardTextures.ts"], "sourcesContent": ["// Define available card textures\r\nexport type CardTexture = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  path: string;\r\n  category: \"paper\" | \"premium\" | \"modern\";\r\n  darkModeOpacity: number;\r\n  lightModeOpacity: number;\r\n  cssClass?: string; // Optional CSS class for static textures\r\n};\r\n\r\n// Array of available textures\r\nexport const cardTextures: CardTexture[] = [\r\n  {\r\n    id: \"linen-paper\",\r\n    name: \"Linen Paper\",\r\n    description: \"Classic linen texture with subtle cross-hatching\",\r\n    path: \"/textures/linen-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"cotton-paper\",\r\n    name: \"Cotton Paper\",\r\n    description: \"Soft, fibrous cotton paper texture\",\r\n    path: \"/textures/cotton-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"recycled-paper\",\r\n    name: \"Recycled Paper\",\r\n    description: \"Eco-friendly recycled paper with small flecks\",\r\n    path: \"/textures/recycled-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"laid-paper\",\r\n    name: \"Laid Paper\",\r\n    description: \"Traditional laid paper with horizontal lines\",\r\n    path: \"/textures/laid-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"marble\",\r\n    name: \"Marble\",\r\n    description: \"Elegant marble texture with subtle veining\",\r\n    path: \"/textures/marble.svg\",\r\n    category: \"premium\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"brushed-metal\",\r\n    name: \"Brushed Metal\",\r\n    description: \"Sophisticated brushed metal finish\",\r\n    path: \"/textures/brushed-metal.svg\",\r\n    category: \"premium\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"subtle-dots\",\r\n    name: \"Subtle Dots\",\r\n    description: \"Modern pattern with subtle dot grid\",\r\n    path: \"/textures/subtle-dots.svg\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"geometric\",\r\n    name: \"Geometric\",\r\n    description: \"Contemporary geometric pattern\",\r\n    path: \"/textures/geometric.svg\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"texture-png\",\r\n    name: \"Classic Texture\",\r\n    description: \"Original texture from the application\",\r\n    path: \"/texture.png\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"none\",\r\n    name: \"No Texture\",\r\n    description: \"Clean look without any texture\",\r\n    path: \"\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0,\r\n    lightModeOpacity: 0,\r\n  },\r\n];\r\n\r\n// Function to get a texture by ID\r\nexport const getTextureById = (id: string): CardTexture => {\r\n  return cardTextures.find((texture) => texture.id === id) || cardTextures[0];\r\n};\r\n\r\n// Default texture ID\r\nexport const DEFAULT_TEXTURE_ID = \"linen-paper\";\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;AAa1B,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;CACD;AAGM,MAAM,iBAAiB,CAAC;IAC7B,OAAO,aAAa,IAAI,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK,OAAO,YAAY,CAAC,EAAE;AAC7E;AAGO,MAAM,qBAAqB", "debugId": null}}, {"offset": {"line": 7775, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardBackgroundEffects.tsx"], "sourcesContent": ["import { getTextureById, DEFAULT_TEXTURE_ID } from \"./CardTextures\";\r\nimport { useMemo } from \"react\";\r\n\r\ninterface CardBackgroundEffectsProps {\r\n  finalThemeColor: string;\r\n}\r\n\r\nexport default function CardBackgroundEffects({ finalThemeColor: _finalThemeColor }: CardBackgroundEffectsProps) {\r\n  // Get texture details based on default texture ID\r\n  const textureDetails = useMemo(() => getTextureById(DEFAULT_TEXTURE_ID), []);\r\n\r\n  return (\r\n    <>\r\n      {/* Card background with subtle pattern */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none z-5\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/card-texture.svg\")`,\r\n          backgroundSize: \"cover\",\r\n          backgroundPosition: \"center\",\r\n          backgroundRepeat: \"no-repeat\",\r\n          opacity: 0.6,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Custom texture overlay with subtle theme compatibility */}\r\n      {textureDetails.path && (\r\n        <div\r\n          className=\"absolute inset-0 mix-blend-overlay pointer-events-none texture-background z-10 dark:opacity-[var(--dark-opacity)] opacity-[var(--light-opacity)]\"\r\n          style={\r\n            {\r\n              backgroundImage: textureDetails.path\r\n                ? `url(${textureDetails.path})`\r\n                : \"none\",\r\n              \"--dark-opacity\": `${textureDetails.darkModeOpacity * 0.7}`,\r\n              \"--light-opacity\": `${textureDetails.lightModeOpacity * 0.7}`,\r\n              backgroundSize: \"cover\",\r\n              backgroundPosition: \"center\",\r\n              backgroundRepeat: \"repeat\",\r\n            } as React.CSSProperties\r\n          }\r\n        ></div>\r\n      )}\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none z-10\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/subtle-pattern.svg\")`,\r\n          backgroundRepeat: \"repeat\",\r\n          backgroundSize: \"20px 20px\",\r\n          opacity: 0.15,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Subtle embossed effect overlay */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/20 mix-blend-overlay pointer-events-none z-15 opacity-20 dark:opacity-15\"></div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAMe,SAAS,sBAAsB,EAAE,iBAAiB,gBAAgB,EAA8B;;IAC7G,kDAAkD;IAClD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yDAAE,IAAM,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD,EAAE,sLAAA,CAAA,qBAAkB;wDAAG,EAAE;IAE3E,qBACE;;0BAEE,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,mCAAmC,CAAC;oBACtD,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;oBAClB,SAAS;gBACX;;;;;;YAID,eAAe,IAAI,kBAClB,6LAAC;gBACC,WAAU;gBACV,OACE;oBACE,iBAAiB,eAAe,IAAI,GAChC,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC,CAAC,CAAC,GAC7B;oBACJ,kBAAkB,GAAG,eAAe,eAAe,GAAG,KAAK;oBAC3D,mBAAmB,GAAG,eAAe,gBAAgB,GAAG,KAAK;oBAC7D,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;;;;;;0BAMN,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,qCAAqC,CAAC;oBACxD,kBAAkB;oBAClB,gBAAgB;oBAChB,SAAS;gBACX;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;GApDwB;KAAA", "debugId": null}}, {"offset": {"line": 7858, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardCornerDecorations.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\nexport default function CardCornerDecorations() {\r\n  return (\r\n    <>\r\n      {/* Subtle corner decorations */}\r\n      <div className=\"absolute top-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(0deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute top-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(90deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(180deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(270deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAe;8BACtC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAgB;8BACvC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAiB;8BACxC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAiB;8BACxC,cAAA,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;;;AAMtB;KAlDwB", "debugId": null}}, {"offset": {"line": 7991, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardGlowEffects.tsx"], "sourcesContent": ["export default function CardGlowEffects() {\r\n  return (\r\n    <>\r\n      {/* Subtle glow effects */}\r\n      <div className=\"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15\"></div>\r\n      <div className=\"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15\"></div>\r\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-[var(--theme-color-5)] blur-3xl opacity-15 dark:opacity-10\"></div>\r\n\r\n      {/* Enhanced inner glow effect with theme color */}\r\n      <div className=\"absolute inset-0 rounded-xl shadow-[inset_0_0_20px_rgba(0,0,0,0.1),inset_0_0_5px_var(--theme-color-20)] dark:shadow-[inset_0_0_20px_rgba(255,255,255,0.03),inset_0_0_5px_var(--theme-color-30)] pointer-events-none z-25\"></div>\r\n\r\n      {/* Vibrant edge glow effect */}\r\n      <div\r\n        className=\"absolute inset-x-0 bottom-0 h-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to top, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-y-0 right-0 w-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to left, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-x-0 top-0 h-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to bottom, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-y-0 left-0 w-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to right, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Enhanced edge effects to simulate premium card thickness */}\r\n      <div className=\"absolute inset-x-0 bottom-0 h-4 bg-gradient-to-t from-black/40 to-transparent pointer-events-none z-25\"></div>\r\n      <div className=\"absolute inset-y-0 right-0 w-4 bg-gradient-to-l from-black/40 to-transparent pointer-events-none z-25\"></div>\r\n\r\n      {/* Enhanced highlight on top edge for premium look */}\r\n      <div className=\"absolute inset-x-0 top-0 h-[4px] bg-gradient-to-b from-white/60 to-transparent pointer-events-none z-25\"></div>\r\n      <div className=\"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-white/60 to-transparent pointer-events-none z-25\"></div>\r\n\r\n      {/* Colorful edge highlight */}\r\n      <div className=\"absolute inset-x-0 bottom-0 h-1 bg-gradient-to-t from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70\"></div>\r\n      <div className=\"absolute inset-y-0 right-0 w-1 bg-gradient-to-l from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70\"></div>\r\n\r\n      {/* Enhanced shadow for depth with theme color influence */}\r\n      <div className=\"absolute inset-0 shadow-[0_15px_60px_rgba(0,0,0,0.3),0_5px_20px_var(--theme-color-20)] dark:shadow-[0_15px_60px_rgba(0,0,0,0.5),0_5px_20px_var(--theme-color-30)] rounded-xl pointer-events-none z-25\"></div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,wDAAwD,CAAC;oBACtE,SAAS;gBACX;;;;;;0BAEF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,yDAAyD,CAAC;oBACvE,SAAS;gBACX;;;;;;0BAEF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,2DAA2D,CAAC;oBACzE,SAAS;gBACX;;;;;;0BAEF,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,0DAA0D,CAAC;oBACxE,SAAS;gBACX;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;;;;;;;;AAGrB;KAzDwB", "debugId": null}}, {"offset": {"line": 8135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardHeader.tsx"], "sourcesContent": ["import { Calendar } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport NextImage from \"next/image\";\r\nimport { getThemeSpecificHeaderImage, getBrandingText, hasCustomBrandingAccess, shouldShowDukancardBranding } from \"@/lib/utils/customBranding\";\r\n\r\ninterface CardHeaderProps {\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n  establishedYear?: number | null;\r\n  customBranding?: {\r\n    hide_dukancard_branding?: boolean;\r\n    custom_header_text?: string;\r\n    custom_header_image_url?: string; // Legacy field\r\n    custom_header_image_light_url?: string; // Light theme\r\n    custom_header_image_dark_url?: string; // Dark theme\r\n  };\r\n}\r\n\r\nexport default function CardHeader({ userPlan, establishedYear, customBranding }: CardHeaderProps) {\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  // Get theme-specific header image with fallback logic\r\n  const themeSpecificHeaderImage = getThemeSpecificHeaderImage(\r\n    userPlan,\r\n    customBranding,\r\n    resolvedTheme\r\n  );\r\n\r\n  // Get custom branding text\r\n  const customBrandingText = getBrandingText(userPlan, customBranding);\r\n\r\n  // Check if user has Pro/Enterprise access\r\n  const hasProEnterpriseAccess = hasCustomBrandingAccess(userPlan);\r\n\r\n  // Determine what to show in the header based on plan and toggle state\r\n  const shouldShowCustomHeaderImage = hasProEnterpriseAccess &&\r\n    customBranding?.hide_dukancard_branding &&\r\n    themeSpecificHeaderImage;\r\n\r\n  const shouldShowCustomHeaderText = hasProEnterpriseAccess &&\r\n    customBranding?.hide_dukancard_branding &&\r\n    customBrandingText &&\r\n    !shouldShowCustomHeaderImage; // Only show text if no image (image has priority)\r\n\r\n  // Use the centralized function for consistent branding logic\r\n  const shouldShowDukancardBrandingResult = shouldShowDukancardBranding(userPlan, customBranding);\r\n\r\n  return (\r\n    <div className=\"flex justify-between items-start\">\r\n      <div className=\"flex items-center\">\r\n        {/* Priority: Theme-specific header image > Custom header text > Dukancard branding */}\r\n        {shouldShowCustomHeaderImage ? (\r\n          <div className=\"max-w-[120px] max-h-[32px] overflow-hidden\">\r\n            <NextImage\r\n              src={themeSpecificHeaderImage}\r\n              alt=\"Custom header\"\r\n              width={120}\r\n              height={32}\r\n              className=\"h-8 w-auto object-contain\"\r\n              style={{ maxWidth: '120px', maxHeight: '32px' }}\r\n              onError={(e) => {\r\n                // Gracefully handle broken images by hiding the element\r\n                e.currentTarget.style.display = 'none';\r\n              }}\r\n            />\r\n          </div>\r\n        ) : shouldShowCustomHeaderText ? (\r\n          <span className=\"font-medium text-sm text-[var(--theme-color)]\">\r\n            {customBrandingText}\r\n          </span>\r\n        ) : (\r\n          /* Show Dukancard branding if toggle is OFF or no custom content */\r\n          shouldShowDukancardBrandingResult && (\r\n            <span className=\"font-bold text-md text-[var(--theme-color)]\">\r\n              Dukan\r\n              <span className=\"text-neutral-900 dark:text-white\">card</span>\r\n            </span>\r\n          )\r\n        )}\r\n      </div>\r\n\r\n      {/* Established Year badge */}\r\n      {establishedYear && (\r\n        <div className=\"flex items-center justify-center text-xs py-0.5 px-2 rounded-full bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)]\">\r\n          <Calendar className=\"w-3 h-3 mr-1 text-[var(--theme-color)]\" />\r\n          <span className=\"font-semibold\">Est. {establishedYear}</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAce,SAAS,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAmB;;IAC/F,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEjC,sDAAsD;IACtD,MAAM,2BAA2B,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EACzD,UACA,gBACA;IAGF,2BAA2B;IAC3B,MAAM,qBAAqB,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;IAErD,0CAA0C;IAC1C,MAAM,yBAAyB,CAAA,GAAA,iIAAA,CAAA,0BAAuB,AAAD,EAAE;IAEvD,sEAAsE;IACtE,MAAM,8BAA8B,0BAClC,gBAAgB,2BAChB;IAEF,MAAM,6BAA6B,0BACjC,gBAAgB,2BAChB,sBACA,CAAC,6BAA6B,kDAAkD;IAElF,6DAA6D;IAC7D,MAAM,oCAAoC,CAAA,GAAA,iIAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU;IAEhF,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAEZ,4CACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,gIAAA,CAAA,UAAS;wBACR,KAAK;wBACL,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,OAAO;4BAAE,UAAU;4BAAS,WAAW;wBAAO;wBAC9C,SAAS,CAAC;4BACR,wDAAwD;4BACxD,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wBAClC;;;;;;;;;;2BAGF,2CACF,6LAAC;oBAAK,WAAU;8BACb;;;;;2BAGH,iEAAiE,GACjE,mDACE,6LAAC;oBAAK,WAAU;;wBAA8C;sCAE5D,6LAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;;;;;;YAO1D,iCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAK,WAAU;;4BAAgB;4BAAM;;;;;;;;;;;;;;;;;;;AAKhD;GAxEwB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 8274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardProfile.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  Too<PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>Provider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { User, Building, Info, Loader2 } from \"lucide-react\";\r\n\r\ntype LogoUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface CardProfileProps {\r\n  logo_url?: string | null;\r\n  localPreviewUrl?: string | null;\r\n  logoUploadStatus: LogoUploadStatus;\r\n  member_name?: string;\r\n  business_name?: string;\r\n  title?: string;\r\n  about_bio?: string;\r\n  finalThemeColor: string;\r\n}\r\n\r\nexport default function CardProfile({\r\n  logo_url,\r\n  localPreviewUrl,\r\n  logoUploadStatus,\r\n  member_name: _member_name,\r\n  business_name,\r\n  title: _title,\r\n  about_bio,\r\n  finalThemeColor,\r\n}: CardProfileProps) {\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <div className=\"relative w-20 h-20 sm:w-24 sm:h-24 rounded-full border-3 border-[var(--theme-color)] overflow-hidden flex items-center justify-center shadow-lg mb-2 sm:mb-3 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 transform hover:scale-105 transition-transform duration-300\">\r\n        {/* Only render Image if we have a valid URL */}\r\n        {(localPreviewUrl || (logo_url && typeof logo_url === 'string' && logo_url.trim() !== \"\")) && (\r\n          <Image\r\n            src={localPreviewUrl || (logo_url || \"\")}\r\n            alt={`${business_name} logo`}\r\n            width={96}\r\n            height={96}\r\n            className=\"object-cover w-full h-full\"\r\n            onError={(e) => (e.currentTarget.style.display = \"none\")}\r\n          />\r\n        )}\r\n        {!localPreviewUrl &&\r\n          (!logo_url || (typeof logo_url === 'string' && logo_url.trim() === \"\")) &&\r\n          logoUploadStatus !== \"uploading\" && (\r\n            <User\r\n              className=\"w-12 h-12 opacity-50\"\r\n              color={finalThemeColor}\r\n            />\r\n          )}\r\n        {logoUploadStatus === \"uploading\" && (\r\n          <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n            <Loader2 className=\"w-8 h-8 text-white animate-spin\" />\r\n          </div>\r\n        )}\r\n\r\n        {/* Subtle glow effect for the profile image */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-tr from-[var(--theme-color-10)] via-transparent to-[var(--theme-color-10)] opacity-40\"></div>\r\n        <div className=\"absolute -bottom-1 -right-1 w-full h-full rounded-full bg-black/5 blur-sm -z-10\"></div>\r\n      </div>\r\n\r\n      {/* Business name only */}\r\n      <h3 className=\"text-base sm:text-lg font-bold text-[--theme-color] mb-2 tracking-wide px-2 text-center\">\r\n        {business_name ? (\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <span className=\"line-clamp-1 relative cursor-default\">\r\n                  {/* Simple text without shadow effect */}\r\n                  <span className=\"relative\">\r\n                    {business_name}\r\n                  </span>\r\n                </span>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p>{business_name}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        ) : (\r\n          <Building\r\n            className=\"inline-block h-5 w-5 opacity-50\"\r\n            color={finalThemeColor}\r\n          />\r\n        )}\r\n      </h3>\r\n\r\n      {about_bio && (\r\n        <div className=\"flex items-start text-xs text-neutral-600 dark:text-neutral-300 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 rounded-lg max-w-xs mx-auto mb-2 sm:mb-3\">\r\n          <Info className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <p className=\"text-start line-clamp-2 cursor-default\">\r\n                  {about_bio}\r\n                </p>\r\n              </TooltipTrigger>\r\n              <TooltipContent className=\"max-w-xs\">\r\n                <p>{about_bio}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAMA;AAAA;AAAA;AAAA;;;;;AAee,SAAS,YAAY,EAClC,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,aAAa,YAAY,EACzB,aAAa,EACb,OAAO,MAAM,EACb,SAAS,EACT,eAAe,EACE;IACjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;oBAEZ,CAAC,mBAAoB,YAAY,OAAO,aAAa,YAAY,SAAS,IAAI,OAAO,EAAG,mBACvF,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,mBAAoB,YAAY;wBACrC,KAAK,GAAG,cAAc,KAAK,CAAC;wBAC5B,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,SAAS,CAAC,IAAO,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;oBAGpD,CAAC,mBACA,CAAC,CAAC,YAAa,OAAO,aAAa,YAAY,SAAS,IAAI,OAAO,EAAG,KACtE,qBAAqB,6BACnB,6LAAC,qMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,OAAO;;;;;;oBAGZ,qBAAqB,6BACpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAKvB,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAG,WAAU;0BACX,8BACC,6LAAC,+HAAA,CAAA,kBAAe;8BACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0CACN,6LAAC,+HAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,6LAAC;oCAAK,WAAU;8CAEd,cAAA,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;0CAIP,6LAAC,+HAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;yCAKV,6LAAC,6MAAA,CAAA,WAAQ;oBACP,WAAU;oBACV,OAAO;;;;;;;;;;;YAKZ,2BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,6LAAC,+HAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;8CACN,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;8CAGL,6LAAC,+HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB;KAxFwB", "debugId": null}}, {"offset": {"line": 8490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardDivider.tsx"], "sourcesContent": ["export default function CardDivider() {\r\n  return (\r\n    <div className=\"h-6 w-full mx-auto max-w-xs mb-3 relative flex items-center justify-center\">\r\n      <div\r\n        className=\"h-px w-full absolute\"\r\n        style={{\r\n          background: `linear-gradient(to right, transparent, var(--theme-color-30), transparent)`,\r\n        }}\r\n      ></div>\r\n      <div className=\"relative z-10 flex items-center justify-center space-x-2\">\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]\"></div>\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-30)]\"></div>\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]\"></div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,0EAA0E,CAAC;gBAC1F;;;;;;0BAEF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;KAhBwB", "debugId": null}}, {"offset": {"line": 8558, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardBusinessInfo.tsx"], "sourcesContent": ["import {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  Toolt<PERSON><PERSON>rovider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { Phone, MapPin, Clock, Truck, Mail } from \"lucide-react\";\r\nimport { formatTimeTo12Hour, formatDayGroup } from \"./utils/cardUtils\";\r\n\r\ninterface CardBusinessInfoProps {\r\n  fullAddress?: string;\r\n  displayAddressLine?: string;\r\n  locality?: string;\r\n  displayCityStatePin?: string;\r\n  phone?: string;\r\n  displayPhone?: string;\r\n  isAuthenticated: boolean;\r\n  telUrl?: string;\r\n  displayEmail?: string;\r\n  mailtoUrl?: string;\r\n  business_hours?: Record<string, { isOpen?: boolean; openTime?: string; closeTime?: string }> | null;\r\n  delivery_info?: string;\r\n}\r\n\r\nexport default function CardBusinessInfo({\r\n  fullAddress,\r\n  displayAddressLine,\r\n  locality,\r\n  displayCityStatePin,\r\n  phone,\r\n  displayPhone,\r\n  isAuthenticated,\r\n  telUrl,\r\n  displayEmail,\r\n  mailtoUrl,\r\n  business_hours,\r\n  delivery_info,\r\n}: CardBusinessInfoProps) {\r\n  return (\r\n    <div className=\"flex flex-col gap-2 sm:gap-3 max-w-xs mx-auto overflow-hidden\">\r\n      {/* Address section (on top) */}\r\n      {fullAddress && (\r\n        <div className=\"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full\">\r\n          <div className=\"flex items-start mb-2.5\">\r\n            <MapPin className=\"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"flex flex-col overflow-hidden\">\r\n              {displayAddressLine && locality ? (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <span className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                        {displayAddressLine}, {locality}\r\n                      </span>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>\r\n                        {displayAddressLine}, {locality}\r\n                      </p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              ) : (\r\n                <>\r\n                  {displayAddressLine && (\r\n                    <TooltipProvider>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <span className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                            {displayAddressLine}\r\n                          </span>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>\r\n                          <p>{displayAddressLine}</p>\r\n                        </TooltipContent>\r\n                      </Tooltip>\r\n                    </TooltipProvider>\r\n                  )}\r\n                  {locality && (\r\n                    <TooltipProvider>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <span className=\"text-xs text-neutral-600 dark:text-neutral-300 line-clamp-1 cursor-default\">\r\n                            {locality}\r\n                          </span>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>\r\n                          <p>{locality}</p>\r\n                        </TooltipContent>\r\n                      </Tooltip>\r\n                    </TooltipProvider>\r\n                  )}\r\n                </>\r\n              )}\r\n              {displayCityStatePin && (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <span className=\"text-xs text-neutral-500 dark:text-neutral-400 line-clamp-1 cursor-default\">\r\n                        {displayCityStatePin}\r\n                      </span>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>{displayCityStatePin}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n\r\n        </div>\r\n      )}\r\n      {/* Contact info section (below address) */}\r\n      <div className=\"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full\">\r\n        {/* Phone */}\r\n        {phone && (\r\n          <div className=\"flex items-center mb-2.5\">\r\n            <Phone className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"overflow-hidden\">\r\n              {displayPhone && (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <a\r\n                        href={isAuthenticated && telUrl ? telUrl : \"#\"}\r\n                        className={\r\n                          isAuthenticated && telUrl\r\n                            ? \"hover:underline font-medium text-xs truncate block\"\r\n                            : \"cursor-default font-medium text-xs truncate block\"\r\n                        }\r\n                      >\r\n                        {displayPhone}\r\n                      </a>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>{displayPhone}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Email */}\r\n        {displayEmail && (\r\n          <div className=\"flex items-center mb-2.5\">\r\n            <Mail className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"overflow-hidden\">\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <a\r\n                      href={\r\n                        isAuthenticated && mailtoUrl ? mailtoUrl : \"#\"\r\n                      }\r\n                      className={\r\n                        isAuthenticated\r\n                          ? \"hover:underline font-medium text-xs truncate block\"\r\n                          : \"cursor-default font-medium text-xs truncate block\"\r\n                      }\r\n                    >\r\n                      {displayEmail}\r\n                    </a>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>\r\n                    <p>{displayEmail}</p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Business hours - only show if at least one day is open */}\r\n        {(() => {\r\n          // Check if business_hours exists and is an object\r\n          if (!business_hours || typeof business_hours !== \"object\" || Object.keys(business_hours).length === 0) {\r\n            return null;\r\n          }\r\n\r\n          try {\r\n            // Check if at least one day has isOpen: true\r\n            const hasOpenDay = Object.values(business_hours).some(\r\n              (hours) => hours && typeof hours === \"object\" && (hours as { isOpen?: boolean }).isOpen\r\n            );\r\n\r\n            // If no days are open, don't show the business hours section\r\n            if (!hasOpenDay) {\r\n              return null;\r\n            }\r\n\r\n            // Get open days and their hours\r\n            const openDays = Object.entries(\r\n              business_hours as Record<string, unknown>\r\n            )\r\n              .filter(([, hours]) => {\r\n                return (\r\n                  hours &&\r\n                  typeof hours === \"object\" &&\r\n                  (hours as { isOpen?: boolean }).isOpen\r\n                );\r\n              })\r\n              .map(([day, hours]) => {\r\n                const hourData = hours as {\r\n                  isOpen: boolean;\r\n                  openTime?: string;\r\n                  closeTime?: string;\r\n                };\r\n                return {\r\n                  day,\r\n                  hours:\r\n                    hourData.openTime && hourData.closeTime\r\n                      ? `${formatTimeTo12Hour(\r\n                          hourData.openTime\r\n                        )} - ${formatTimeTo12Hour(\r\n                          hourData.closeTime\r\n                        )}`\r\n                      : \"\",\r\n                };\r\n              })\r\n              .filter((item) => item.hours);\r\n\r\n            // If no valid open days with hours, return null\r\n            if (openDays.length === 0) {\r\n              return null;\r\n            }\r\n\r\n            // Group days with the same hours\r\n            const hourGroups: Record<string, string[]> = {};\r\n            openDays.forEach(({ day, hours }) => {\r\n              if (!hourGroups[hours]) {\r\n                hourGroups[hours] = [];\r\n              }\r\n              hourGroups[hours].push(day);\r\n            });\r\n\r\n            // Return the business hours section with formatted day groups\r\n            return (\r\n              <div className=\"flex items-start mb-2.5\">\r\n                <Clock className=\"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]\" />\r\n                <div className=\"text-xs font-medium\">\r\n                  {Object.entries(hourGroups).map(\r\n                    ([hours, days], index) => {\r\n                      // Format days (e.g., \"Mon, Tue, Wed\" or \"Mon-Wed\")\r\n                      const formattedDays = formatDayGroup(days);\r\n\r\n                      return (\r\n                        <div\r\n                          key={index}\r\n                          className=\"flex justify-between\"\r\n                        >\r\n                          <span className=\"capitalize\">\r\n                            {formattedDays}:\r\n                          </span>\r\n                          <span className=\"ml-2\">{hours}</span>\r\n                        </div>\r\n                      );\r\n                    }\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          } catch (error) {\r\n            // If there's an error parsing the business hours, return null\r\n            console.error(\"Error parsing business hours:\", error);\r\n            return null;\r\n          }\r\n        })()}\r\n        {/* Delivery info - now available for all users */}\r\n        {delivery_info && (\r\n          <div className=\"flex items-center\">\r\n            <Truck className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <TooltipProvider>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <p className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                    {delivery_info}\r\n                  </p>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>{delivery_info}</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAiBe,SAAS,iBAAiB,EACvC,WAAW,EACX,kBAAkB,EAClB,QAAQ,EACR,mBAAmB,EACnB,KAAK,EACL,YAAY,EACZ,eAAe,EACf,MAAM,EACN,YAAY,EACZ,SAAS,EACT,cAAc,EACd,aAAa,EACS;IACtB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAI,WAAU;;gCACZ,sBAAsB,yBACrB,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDAAK,WAAU;;wDACb;wDAAmB;wDAAG;;;;;;;;;;;;0DAG3B,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;;wDACE;wDAAmB;wDAAG;;;;;;;;;;;;;;;;;;;;;;yDAM/B;;wCACG,oCACC,6LAAC,+HAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;kEACN,6LAAC,+HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC;4DAAK,WAAU;sEACb;;;;;;;;;;;kEAGL,6LAAC,+HAAA,CAAA,iBAAc;kEACb,cAAA,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;wCAKX,0BACC,6LAAC,+HAAA,CAAA,kBAAe;sDACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;kEACN,6LAAC,+HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,6LAAC;4DAAK,WAAU;sEACb;;;;;;;;;;;kEAGL,6LAAC,+HAAA,CAAA,iBAAc;kEACb,cAAA,6LAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;gCAOf,qCACC,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;0DAGL,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,6LAAC;gBAAI,WAAU;;oBAEZ,uBACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAI,WAAU;0CACZ,8BACC,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDACC,MAAM,mBAAmB,SAAS,SAAS;oDAC3C,WACE,mBAAmB,SACf,uDACA;8DAGL;;;;;;;;;;;0DAGL,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASjB,8BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,+HAAA,CAAA,kBAAe;8CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,6LAAC;oDACC,MACE,mBAAmB,YAAY,YAAY;oDAE7C,WACE,kBACI,uDACA;8DAGL;;;;;;;;;;;0DAGL,6LAAC,+HAAA,CAAA,iBAAc;0DACb,cAAA,6LAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQf,CAAC;wBACA,kDAAkD;wBAClD,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,YAAY,OAAO,IAAI,CAAC,gBAAgB,MAAM,KAAK,GAAG;4BACrG,OAAO;wBACT;wBAEA,IAAI;4BACF,6CAA6C;4BAC7C,MAAM,aAAa,OAAO,MAAM,CAAC,gBAAgB,IAAI,CACnD,CAAC,QAAU,SAAS,OAAO,UAAU,YAAY,AAAC,MAA+B,MAAM;4BAGzF,6DAA6D;4BAC7D,IAAI,CAAC,YAAY;gCACf,OAAO;4BACT;4BAEA,gCAAgC;4BAChC,MAAM,WAAW,OAAO,OAAO,CAC7B,gBAEC,MAAM,CAAC,CAAC,GAAG,MAAM;gCAChB,OACE,SACA,OAAO,UAAU,YACjB,AAAC,MAA+B,MAAM;4BAE1C,GACC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;gCAChB,MAAM,WAAW;gCAKjB,OAAO;oCACL;oCACA,OACE,SAAS,QAAQ,IAAI,SAAS,SAAS,GACnC,GAAG,CAAA,GAAA,4LAAA,CAAA,qBAAkB,AAAD,EAClB,SAAS,QAAQ,EACjB,GAAG,EAAE,CAAA,GAAA,4LAAA,CAAA,qBAAkB,AAAD,EACtB,SAAS,SAAS,GACjB,GACH;gCACR;4BACF,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,KAAK;4BAE9B,gDAAgD;4BAChD,IAAI,SAAS,MAAM,KAAK,GAAG;gCACzB,OAAO;4BACT;4BAEA,iCAAiC;4BACjC,MAAM,aAAuC,CAAC;4BAC9C,SAAS,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;gCAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;oCACtB,UAAU,CAAC,MAAM,GAAG,EAAE;gCACxB;gCACA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;4BACzB;4BAEA,8DAA8D;4BAC9D,qBACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,YAAY,GAAG,CAC7B,CAAC,CAAC,OAAO,KAAK,EAAE;4CACd,mDAAmD;4CACnD,MAAM,gBAAgB,CAAA,GAAA,4LAAA,CAAA,iBAAc,AAAD,EAAE;4CAErC,qBACE,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;;4DACb;4DAAc;;;;;;;kEAEjB,6LAAC;wDAAK,WAAU;kEAAQ;;;;;;;+CANnB;;;;;wCASX;;;;;;;;;;;;wBAKV,EAAE,OAAO,OAAO;4BACd,8DAA8D;4BAC9D,QAAQ,KAAK,CAAC,iCAAiC;4BAC/C,OAAO;wBACT;oBACF,CAAC;oBAEA,+BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC,+HAAA,CAAA,kBAAe;0CACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;sDACN,6LAAC,+HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,6LAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;sDAGL,6LAAC,+HAAA,CAAA,iBAAc;sDACb,cAAA,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;KAzQwB", "debugId": null}}, {"offset": {"line": 9102, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/BusinessCardPreview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useMemo } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { BusinessCardData, defaultBusinessCardData } from \"../schema\";\r\nimport QRCode from \"react-qr-code\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Phone,\r\n  ShoppingBag,\r\n  Star,\r\n  Heart,\r\n  UserPlus,\r\n  QrCode, // Added for fake QR code in demo mode\r\n} from \"lucide-react\";\r\nimport WhatsAppIcon from \"@/app/components/icons/WhatsAppIcon\";\r\nimport InstagramIcon from \"@/app/components/icons/InstagramIcon\";\r\nimport FacebookIcon from \"@/app/components/icons/FacebookIcon\";\r\nimport { ProductServiceData } from \"../../products/actions\";\r\nimport {\r\n  maskEmail,\r\n  maskPhoneNumber,\r\n  formatIndianNumberShort,\r\n} from \"@/lib/utils\";\r\nimport {\r\n  formatWhatsAppUrl,\r\n  formatTelUrl,\r\n  formatPrice,\r\n} from \"./utils/cardUtils\";\r\nimport {\r\n  generateCustomBrandingStyles,\r\n  getPrimaryThemeColor,\r\n} from \"@/lib/utils/customBranding\";\r\nimport CardBackgroundEffects from \"./CardBackgroundEffects\";\r\nimport CardCornerDecorations from \"./CardCornerDecorations\";\r\nimport CardGlowEffects from \"./CardGlowEffects\";\r\nimport CardHeader from \"./CardHeader\";\r\nimport CardProfile from \"./CardProfile\";\r\nimport CardDivider from \"./CardDivider\";\r\nimport CardBusinessInfo from \"./CardBusinessInfo\";\r\n\r\n\r\ntype LogoUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface BusinessCardPreviewProps {\r\n  data: BusinessCardData & {\r\n    products_services?: ProductServiceData[];\r\n  };\r\n  totalLikes?: number;\r\n  totalSubscriptions?: number;\r\n  averageRating?: number;\r\n  isSubscribed?: boolean;\r\n  hasLiked?: boolean;\r\n  isLoadingInteraction?: boolean;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n  logoUploadStatus?: LogoUploadStatus;\r\n  localPreviewUrl?: string | null;\r\n  isAuthenticated?: boolean;\r\n  isCurrentUserBusiness?: boolean;\r\n  isDemo?: boolean; // New prop to indicate if this is a demo card for homepage\r\n}\r\n\r\nexport default function BusinessCardPreview({\r\n  data,\r\n  userPlan,\r\n  logoUploadStatus = \"idle\",\r\n  localPreviewUrl = null,\r\n  isAuthenticated = false,\r\n  totalLikes = 0,\r\n  totalSubscriptions = 0,\r\n  averageRating = 0,\r\n  isDemo = false, // New prop with default value\r\n}: BusinessCardPreviewProps) {\r\n  const {\r\n    logo_url = data.logo_url ?? defaultBusinessCardData.logo_url,\r\n    member_name = defaultBusinessCardData.member_name || \"Your Name\",\r\n    business_name = defaultBusinessCardData.business_name ||\r\n      \"Your Business Name\",\r\n    about_bio = defaultBusinessCardData.about_bio,\r\n    address_line = defaultBusinessCardData.address_line,\r\n    locality = data.locality ?? defaultBusinessCardData.locality,\r\n    city = defaultBusinessCardData.city,\r\n    state = defaultBusinessCardData.state,\r\n    established_year = data.established_year ?? defaultBusinessCardData.established_year,\r\n    pincode = defaultBusinessCardData.pincode,\r\n    phone = defaultBusinessCardData.phone,\r\n    instagram_url = defaultBusinessCardData.instagram_url,\r\n    facebook_url = defaultBusinessCardData.facebook_url,\r\n    whatsapp_number = defaultBusinessCardData.whatsapp_number,\r\n    theme_color = data.theme_color,\r\n    business_hours = data.business_hours,\r\n    delivery_info = data.delivery_info,\r\n    business_slug = defaultBusinessCardData.business_slug || \"\",\r\n    products_services = [],\r\n    contact_email = defaultBusinessCardData.contact_email,\r\n    title = data.title || \"\",\r\n  } = data;\r\n\r\n  const whatsappUrl = formatWhatsAppUrl(whatsapp_number);\r\n  const telUrl = formatTelUrl(phone);\r\n  const mailtoUrl = contact_email ? `mailto:${contact_email}` : undefined;\r\n\r\n  const fullAddress = [address_line, locality, city, state, pincode]\r\n    .filter(Boolean)\r\n    .join(\", \");\r\n\r\n  const displayPhone = isAuthenticated ? phone : maskPhoneNumber(phone);\r\n  const displayEmail = isAuthenticated\r\n    ? contact_email\r\n    : maskEmail(contact_email);\r\n  // Always show address_line regardless of authentication status\r\n  const displayAddressLine = address_line?.trim() || \"\";\r\n  const displayCityStatePin = `${city || \"\"}, ${state || \"\"} ${\r\n    pincode ? `- ${pincode}` : \"\"\r\n  }`.trim();\r\n\r\n  const qrValue = business_slug\r\n    ? `https://dukancard.in/${business_slug}`\r\n    : null;\r\n  const qrDisplayUrl = business_slug\r\n    ? `dukancard.in/${business_slug}`\r\n    : \"Set Slug to activate\";\r\n\r\n  // Updated theme color logic - use custom branding colors if available and user is Pro/Enterprise\r\n  const finalThemeColor = useMemo(() => {\r\n    return getPrimaryThemeColor(userPlan, data.custom_branding, theme_color);\r\n  }, [theme_color, userPlan, data.custom_branding]);\r\n\r\n  // Generate card styles with custom branding support\r\n  const cardStyle = useMemo(() => {\r\n    return generateCustomBrandingStyles(userPlan, data.custom_branding, theme_color);\r\n  }, [userPlan, data.custom_branding, theme_color]);\r\n\r\n  return (\r\n    <motion.div\r\n      data-card-element\r\n      className={cn(`\r\n        relative w-full max-w-sm\r\n        rounded-xl overflow-hidden\r\n        transition-all duration-500\r\n\r\n        bg-gradient-to-br from-neutral-100 to-white dark:from-neutral-900 dark:to-neutral-950\r\n        shadow-xl\r\n        transform-gpu\r\n        border-0\r\n      `)}\r\n      style={cardStyle}\r\n    >\r\n      <CardBackgroundEffects finalThemeColor={finalThemeColor} />\r\n\r\n      <CardCornerDecorations />\r\n\r\n      {/* Content container */}\r\n      <div className=\"relative p-3 xs:p-4 sm:p-5 flex flex-col justify-between text-neutral-800 dark:text-white z-10\">\r\n        <CardHeader\r\n          userPlan={userPlan}\r\n          establishedYear={established_year}\r\n          customBranding={data.custom_branding}\r\n        />\r\n\r\n        {/* Floating interaction buttons are rendered at the bottom */}\r\n\r\n        {/* Main content - Profile section */}\r\n        <div className=\"mt-1\">\r\n          <CardProfile\r\n            logo_url={logo_url}\r\n            localPreviewUrl={localPreviewUrl}\r\n            logoUploadStatus={logoUploadStatus}\r\n            member_name={member_name}\r\n            business_name={business_name}\r\n            title={title}\r\n            about_bio={about_bio}\r\n            finalThemeColor={finalThemeColor}\r\n          />\r\n\r\n          <CardDivider />\r\n\r\n          <CardBusinessInfo\r\n            fullAddress={fullAddress}\r\n            displayAddressLine={displayAddressLine}\r\n            locality={locality}\r\n            displayCityStatePin={displayCityStatePin}\r\n            phone={phone}\r\n            displayPhone={displayPhone}\r\n            isAuthenticated={isAuthenticated}\r\n            telUrl={telUrl}\r\n            displayEmail={displayEmail}\r\n            mailtoUrl={mailtoUrl}\r\n            business_hours={business_hours}\r\n            delivery_info={delivery_info}\r\n          />\r\n\r\n          {/* Products & Services section - Full width */}\r\n          {products_services && products_services.length > 0 && (\r\n            <div className=\"mt-3 max-w-xs mx-auto\">\r\n              <div className=\"flex items-center text-xs uppercase font-bold tracking-wider text-[--theme-color] mb-2 justify-center\">\r\n                <ShoppingBag\r\n                  className=\"w-3 h-3 mr-1.5\"\r\n                  color={finalThemeColor}\r\n                />\r\n                Products & Services\r\n              </div>\r\n\r\n              <div className=\"space-y-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 p-2.5 rounded-lg\">\r\n                {products_services.slice(0, 3).map((item) => (\r\n                  <div\r\n                    key={item.id}\r\n                    className=\"text-neutral-700 dark:text-neutral-200\"\r\n                  >\r\n                    <div className=\"flex justify-between items-baseline gap-2\">\r\n                      <TooltipProvider>\r\n                        <Tooltip>\r\n                          <TooltipTrigger asChild>\r\n                            <span className=\"font-medium text-xs truncate cursor-default\">\r\n                              {item.name}\r\n                            </span>\r\n                          </TooltipTrigger>\r\n                          <TooltipContent>\r\n                            <p>{item.name}</p>\r\n                          </TooltipContent>\r\n                        </Tooltip>\r\n                      </TooltipProvider>\r\n                      <span className=\"text-xs font-bold bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)] py-0.5 px-2 rounded-full flex-shrink-0\">\r\n                        {formatPrice(item.base_price)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* QR Code section */}\r\n          <div className=\"mt-3 max-w-xs mx-auto\">\r\n            {isDemo ? (\r\n              // Demo mode - show fake QR code\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <p className=\"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default\">\r\n                    dukancard.in/demo-business\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0\">\r\n                  <QrCode\r\n                    className=\"w-14 h-14 text-neutral-800\"\r\n                    strokeWidth={1.5}\r\n                  />\r\n                </div>\r\n              </div>\r\n            ) : qrValue ? (\r\n              // Real QR code for actual business cards\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <TooltipProvider>\r\n                    <Tooltip>\r\n                      <TooltipTrigger asChild>\r\n                        <p className=\"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default\">\r\n                          {qrDisplayUrl}\r\n                        </p>\r\n                      </TooltipTrigger>\r\n                      <TooltipContent>\r\n                        <p>{qrDisplayUrl}</p>\r\n                      </TooltipContent>\r\n                    </Tooltip>\r\n                  </TooltipProvider>\r\n                </div>\r\n                <div\r\n                  id=\"business-card-qrcode\"\r\n                  className=\"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0\"\r\n                >\r\n                  <QRCode\r\n                    value={qrValue}\r\n                    size={60}\r\n                    level=\"M\"\r\n                    bgColor=\"#FFFFFF\"\r\n                    fgColor=\"#000000\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Placeholder for when no slug is set\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <p className=\"text-xs font-mono text-neutral-500 dark:text-neutral-500 line-clamp-1\">\r\n                    {qrDisplayUrl}\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white p-1.5 rounded-lg shadow-md opacity-50 flex-shrink-0\">\r\n                  <svg\r\n                    className=\"w-14 h-14 text-[--theme-color]\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Interaction metrics */}\r\n          <div className=\"flex justify-center items-center gap-2 sm:gap-4 text-xs text-neutral-500 dark:text-neutral-400 mt-3 mb-2 flex-wrap\">\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <Heart className=\"w-4 h-4 text-red-500\" />\r\n              <span className=\"font-medium\">\r\n                {formatIndianNumberShort(totalLikes)}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <UserPlus className=\"w-4 h-4 text-blue-500\" />\r\n              <span className=\"font-medium\">\r\n                {formatIndianNumberShort(totalSubscriptions)}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <Star className=\"w-4 h-4 text-amber-500 fill-current\" />\r\n              <span className=\"font-medium\">{averageRating.toFixed(1)}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Interaction buttons have been moved outside the card */}\r\n        </div>\r\n\r\n        {/* Social Links */}\r\n        <div className=\"pt-3 pb-2\">\r\n          <TooltipProvider>\r\n            <div className=\"flex justify-center items-center space-x-2\">\r\n              {instagram_url && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <InstagramIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={instagram_url}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <InstagramIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Instagram Button\" : \"Instagram\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {facebook_url && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <FacebookIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={facebook_url}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <FacebookIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Facebook Button\" : \"Facebook\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {whatsappUrl && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <WhatsAppIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={whatsappUrl}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <WhatsAppIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo WhatsApp Button\" : \"Chat on WhatsApp\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {displayPhone && telUrl && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <Phone className=\"w-4 h-4\" color={finalThemeColor} />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable or non-clickable based on authentication\r\n                      <a\r\n                        href={isAuthenticated ? telUrl : \"#\"}\r\n                        className={`w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all ${\r\n                          isAuthenticated\r\n                            ? \"hover:scale-110 hover:shadow-md\"\r\n                            : \"cursor-default opacity-70\"\r\n                        }`}\r\n                      >\r\n                        <Phone className=\"w-4 h-4\" color={finalThemeColor} />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Call Button\" : \"Call directly\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n            </div>\r\n          </TooltipProvider>\r\n        </div>\r\n\r\n        {/* Bottom accent bar */}\r\n        <div\r\n          className=\"absolute bottom-0 left-0 right-0 h-1.5 mt-2\"\r\n          style={{\r\n            background: `linear-gradient(to right, var(--theme-color), var(--theme-accent-end), var(--theme-color))`,\r\n          }}\r\n        ></div>\r\n      </div>\r\n\r\n      <CardGlowEffects />\r\n    </motion.div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAOA;AAKA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA7CA;;;;;;;;;;;;;;;;;;;;;AAoEe,SAAS,oBAAoB,EAC1C,IAAI,EACJ,QAAQ,EACR,mBAAmB,MAAM,EACzB,kBAAkB,IAAI,EACtB,kBAAkB,KAAK,EACvB,aAAa,CAAC,EACd,qBAAqB,CAAC,EACtB,gBAAgB,CAAC,EACjB,SAAS,KAAK,EACW;;IACzB,MAAM,EACJ,WAAW,KAAK,QAAQ,IAAI,kKAAA,CAAA,0BAAuB,CAAC,QAAQ,EAC5D,cAAc,kKAAA,CAAA,0BAAuB,CAAC,WAAW,IAAI,WAAW,EAChE,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,IACnD,oBAAoB,EACtB,YAAY,kKAAA,CAAA,0BAAuB,CAAC,SAAS,EAC7C,eAAe,kKAAA,CAAA,0BAAuB,CAAC,YAAY,EACnD,WAAW,KAAK,QAAQ,IAAI,kKAAA,CAAA,0BAAuB,CAAC,QAAQ,EAC5D,OAAO,kKAAA,CAAA,0BAAuB,CAAC,IAAI,EACnC,QAAQ,kKAAA,CAAA,0BAAuB,CAAC,KAAK,EACrC,mBAAmB,KAAK,gBAAgB,IAAI,kKAAA,CAAA,0BAAuB,CAAC,gBAAgB,EACpF,UAAU,kKAAA,CAAA,0BAAuB,CAAC,OAAO,EACzC,QAAQ,kKAAA,CAAA,0BAAuB,CAAC,KAAK,EACrC,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,EACrD,eAAe,kKAAA,CAAA,0BAAuB,CAAC,YAAY,EACnD,kBAAkB,kKAAA,CAAA,0BAAuB,CAAC,eAAe,EACzD,cAAc,KAAK,WAAW,EAC9B,iBAAiB,KAAK,cAAc,EACpC,gBAAgB,KAAK,aAAa,EAClC,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,IAAI,EAAE,EAC3D,oBAAoB,EAAE,EACtB,gBAAgB,kKAAA,CAAA,0BAAuB,CAAC,aAAa,EACrD,QAAQ,KAAK,KAAK,IAAI,EAAE,EACzB,GAAG;IAEJ,MAAM,cAAc,CAAA,GAAA,4LAAA,CAAA,oBAAiB,AAAD,EAAE;IACtC,MAAM,SAAS,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD,EAAE;IAC5B,MAAM,YAAY,gBAAgB,CAAC,OAAO,EAAE,eAAe,GAAG;IAE9D,MAAM,cAAc;QAAC;QAAc;QAAU;QAAM;QAAO;KAAQ,CAC/D,MAAM,CAAC,SACP,IAAI,CAAC;IAER,MAAM,eAAe,kBAAkB,QAAQ,CAAA,GAAA,+GAAA,CAAA,kBAAe,AAAD,EAAE;IAC/D,MAAM,eAAe,kBACjB,gBACA,CAAA,GAAA,+GAAA,CAAA,YAAS,AAAD,EAAE;IACd,+DAA+D;IAC/D,MAAM,qBAAqB,cAAc,UAAU;IACnD,MAAM,sBAAsB,GAAG,QAAQ,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,EACzD,UAAU,CAAC,EAAE,EAAE,SAAS,GAAG,IAC3B,CAAC,IAAI;IAEP,MAAM,UAAU,gBACZ,CAAC,qBAAqB,EAAE,eAAe,GACvC;IACJ,MAAM,eAAe,gBACjB,CAAC,aAAa,EAAE,eAAe,GAC/B;IAEJ,iGAAiG;IACjG,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;wDAAE;YAC9B,OAAO,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,KAAK,eAAe,EAAE;QAC9D;uDAAG;QAAC;QAAa;QAAU,KAAK,eAAe;KAAC;IAEhD,oDAAoD;IACpD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAAE;YACxB,OAAO,CAAA,GAAA,iIAAA,CAAA,+BAA4B,AAAD,EAAE,UAAU,KAAK,eAAe,EAAE;QACtE;iDAAG;QAAC;QAAU,KAAK,eAAe;QAAE;KAAY;IAEhD,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,mBAAiB;QACjB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,CAAC;;;;;;;;;MASf,CAAC;QACD,OAAO;;0BAEP,6LAAC,gMAAA,CAAA,UAAqB;gBAAC,iBAAiB;;;;;;0BAExC,6LAAC,gMAAA,CAAA,UAAqB;;;;;0BAGtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qLAAA,CAAA,UAAU;wBACT,UAAU;wBACV,iBAAiB;wBACjB,gBAAgB,KAAK,eAAe;;;;;;kCAMtC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sLAAA,CAAA,UAAW;gCACV,UAAU;gCACV,iBAAiB;gCACjB,kBAAkB;gCAClB,aAAa;gCACb,eAAe;gCACf,OAAO;gCACP,WAAW;gCACX,iBAAiB;;;;;;0CAGnB,6LAAC,sLAAA,CAAA,UAAW;;;;;0CAEZ,6LAAC,2LAAA,CAAA,UAAgB;gCACf,aAAa;gCACb,oBAAoB;gCACpB,UAAU;gCACV,qBAAqB;gCACrB,OAAO;gCACP,cAAc;gCACd,iBAAiB;gCACjB,QAAQ;gCACR,cAAc;gCACd,WAAW;gCACX,gBAAgB;gCAChB,eAAe;;;;;;4BAIhB,qBAAqB,kBAAkB,MAAM,GAAG,mBAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uNAAA,CAAA,cAAW;gDACV,WAAU;gDACV,OAAO;;;;;;4CACP;;;;;;;kDAIJ,6LAAC;wCAAI,WAAU;kDACZ,kBAAkB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAClC,6LAAC;gDAEC,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+HAAA,CAAA,kBAAe;sEACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;kFACN,6LAAC,+HAAA,CAAA,iBAAc;wEAAC,OAAO;kFACrB,cAAA,6LAAC;4EAAK,WAAU;sFACb,KAAK,IAAI;;;;;;;;;;;kFAGd,6LAAC,+HAAA,CAAA,iBAAc;kFACb,cAAA,6LAAC;sFAAG,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sEAInB,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,4LAAA,CAAA,cAAW,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;+CAjB3B,KAAK,EAAE;;;;;;;;;;;;;;;;0CA2BtB,6LAAC;gCAAI,WAAU;0CACZ,SACC,gCAAgC;8CAChC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,6LAAC;oDAAE,WAAU;8DAAmF;;;;;;;;;;;;sDAIlG,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDACL,WAAU;gDACV,aAAa;;;;;;;;;;;;;;;;2CAIjB,UACF,yCAAyC;8CACzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,6LAAC,+HAAA,CAAA,kBAAe;8DACd,cAAA,6LAAC,+HAAA,CAAA,UAAO;;0EACN,6LAAC,+HAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,6LAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;0EAGL,6LAAC,+HAAA,CAAA,iBAAc;0EACb,cAAA,6LAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKZ,6LAAC;4CACC,IAAG;4CACH,WAAU;sDAEV,cAAA,6LAAC,sJAAA,CAAA,UAAM;gDACL,OAAO;gDACP,MAAM;gDACN,OAAM;gDACN,SAAQ;gDACR,SAAQ;;;;;;;;;;;;;;;;2CAKd,sCAAsC;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,6LAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;sDAGL,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,SAAQ;gDACR,MAAK;gDACL,QAAO;0DAEP,cAAA,6LAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASd,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;;;;;;;;;;;;kDAG7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;;;;;;;;;;;;kDAG7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;gDAAK,WAAU;0DAAe,cAAc,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+HAAA,CAAA,kBAAe;sCACd,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,+BACC,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,+IAAA,CAAA,UAAa;wDAAC,WAAU;;;;;;;;;;2DAG3B,6BAA6B;8DAC7B,6LAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,+IAAA,CAAA,UAAa;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI/B,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,0BAA0B;;;;;;;;;;;;oCAKzC,8BACC,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;2DAG1B,6BAA6B;8DAC7B,6LAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI9B,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,yBAAyB;;;;;;;;;;;;oCAKxC,6BACC,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;2DAG1B,6BAA6B;8DAC7B,6LAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,8IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI9B,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,yBAAyB;;;;;;;;;;;;oCAKxC,gBAAgB,wBACf,6LAAC,+HAAA,CAAA,UAAO;;0DACN,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAU,OAAO;;;;;;;;;;2DAGpC,iEAAiE;8DACjE,6LAAC;oDACC,MAAM,kBAAkB,SAAS;oDACjC,WAAW,CAAC,yLAAyL,EACnM,kBACI,oCACA,6BACJ;8DAEF,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAU,OAAO;;;;;;;;;;;;;;;;0DAIxC,6LAAC,+HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3C,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,0FAA0F,CAAC;wBAC1G;;;;;;;;;;;;0BAIJ,6LAAC,0LAAA,CAAA,UAAe;;;;;;;;;;;AAGtB;GAjZwB;KAAA", "debugId": null}}, {"offset": {"line": 9946, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardPreviewSection/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { RefObject } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { BusinessCardData } from \"../../schema\";\r\nimport type { LogoUploadStatus } from \"../hooks/useLogoUpload\";\r\nimport BusinessCardPreview from \"../BusinessCardPreview\";\r\nimport EnhancedCardActions from \"@/app/components/shared/EnhancedCardActions\";\r\nimport { formatAddress } from \"@/lib/utils\";\r\n\r\ninterface CardPreviewSectionProps {\r\n  cardData: BusinessCardData;\r\n  logoUploadStatus: LogoUploadStatus;\r\n  localPreviewUrl: string | null;\r\n  userPlan: \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | undefined;\r\n  cardPreviewRef: RefObject<HTMLDivElement | null>; // Changed type here\r\n}\r\n\r\nconst previewVariants = {\r\n  hidden: { opacity: 0, scale: 0.95 },\r\n  visible: {\r\n    opacity: 1,\r\n    scale: 1,\r\n    transition: { duration: 0.5, delay: 0.1, ease: \"easeOut\" },\r\n  },\r\n};\r\n\r\nexport default function CardPreviewSection({\r\n  cardData,\r\n  logoUploadStatus,\r\n  localPreviewUrl,\r\n  userPlan,\r\n  cardPreviewRef,\r\n}: CardPreviewSectionProps) {\r\n  return (\r\n    <motion.div\r\n      variants={previewVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      style={{ width: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center' } as React.CSSProperties}\r\n      ref={cardPreviewRef} // Pass the ref here\r\n    >\r\n      <BusinessCardPreview\r\n        data={cardData}\r\n        logoUploadStatus={logoUploadStatus}\r\n        localPreviewUrl={localPreviewUrl}\r\n        userPlan={userPlan}\r\n        isAuthenticated={true}\r\n        totalLikes={cardData.total_likes ?? 0}\r\n        totalSubscriptions={cardData.total_subscriptions ?? 0}\r\n        averageRating={cardData.average_rating ?? 0}\r\n      />\r\n\r\n      {/* Share/Download Buttons */}\r\n      <EnhancedCardActions\r\n        businessSlug={cardData.business_slug || \"\"}\r\n        businessName={cardData.business_name || \"\"}\r\n        ownerName={cardData.member_name || \"\"}\r\n        businessAddress={formatAddress(cardData)}\r\n        themeColor={cardData.theme_color || \"#F59E0B\"}\r\n        className=\"mt-6\"\r\n      />\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAGA;AACA;AACA;AARA;;;;;;AAkBA,MAAM,kBAAkB;IACtB,QAAQ;QAAE,SAAS;QAAG,OAAO;IAAK;IAClC,SAAS;QACP,SAAS;QACT,OAAO;QACP,YAAY;YAAE,UAAU;YAAK,OAAO;YAAK,MAAM;QAAU;IAC3D;AACF;AAEe,SAAS,mBAAmB,EACzC,QAAQ,EACR,gBAAgB,EAChB,eAAe,EACf,QAAQ,EACR,cAAc,EACU;IACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,OAAO;YAAE,OAAO;YAAQ,SAAS;YAAQ,eAAe;YAAU,YAAY;QAAS;QACvF,KAAK;;0BAEL,6LAAC,8LAAA,CAAA,UAAmB;gBAClB,MAAM;gBACN,kBAAkB;gBAClB,iBAAiB;gBACjB,UAAU;gBACV,iBAAiB;gBACjB,YAAY,SAAS,WAAW,IAAI;gBACpC,oBAAoB,SAAS,mBAAmB,IAAI;gBACpD,eAAe,SAAS,cAAc,IAAI;;;;;;0BAI5C,6LAAC,sJAAA,CAAA,UAAmB;gBAClB,cAAc,SAAS,aAAa,IAAI;gBACxC,cAAc,SAAS,aAAa,IAAI;gBACxC,WAAW,SAAS,WAAW,IAAI;gBACnC,iBAAiB,CAAA,GAAA,+GAAA,CAAA,gBAAa,AAAD,EAAE;gBAC/B,YAAY,SAAS,WAAW,IAAI;gBACpC,WAAU;;;;;;;;;;;;AAIlB;KArCwB", "debugId": null}}, {"offset": {"line": 10033, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/ImageCropDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useCallback, useEffect } from \"react\"; // Added React and useEffect import\r\nimport <PERSON><PERSON><PERSON>, { Point, Area } from \"react-easy-crop\"; // Import types directly\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>alog<PERSON>ontent,\r\n  <PERSON><PERSON>Header,\r\n  <PERSON><PERSON><PERSON><PERSON>le,\r\n  DialogFooter,\r\n} from \"@/components/ui/dialog\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { Slider } from \"@/components/ui/slider\"; // Import Slider\r\n\r\n// Helper function to create an image element\r\nconst createImage = (url: string): Promise<HTMLImageElement> =>\r\n  new Promise((resolve, reject) => {\r\n    const image = new Image();\r\n    image.addEventListener(\"load\", () => resolve(image));\r\n    image.addEventListener(\"error\", (error) => reject(error));\r\n    image.setAttribute(\"crossOrigin\", \"anonymous\"); // needed to avoid cross-origin issues\r\n    image.src = url;\r\n  });\r\n\r\n// Helper function to get the cropped image blob\r\nasync function getCroppedImgBlob(\r\n  imageSrc: string,\r\n  pixelCrop: Area\r\n): Promise<Blob | null> {\r\n  const image = await createImage(imageSrc);\r\n  const canvas = document.createElement(\"canvas\");\r\n  const ctx = canvas.getContext(\"2d\");\r\n\r\n  if (!ctx) {\r\n    return null;\r\n  }\r\n\r\n  const scaleX = image.naturalWidth / image.width;\r\n  const scaleY = image.naturalHeight / image.height;\r\n  const pixelRatio = window.devicePixelRatio || 1;\r\n\r\n  canvas.width = pixelCrop.width * pixelRatio * scaleX;\r\n  canvas.height = pixelCrop.height * pixelRatio * scaleY;\r\n\r\n  ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\r\n  ctx.imageSmoothingQuality = \"high\";\r\n\r\n  ctx.drawImage(\r\n    image,\r\n    pixelCrop.x * scaleX,\r\n    pixelCrop.y * scaleY,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY,\r\n    0,\r\n    0,\r\n    pixelCrop.width * scaleX,\r\n    pixelCrop.height * scaleY\r\n  );\r\n\r\n  return new Promise((resolve) => {\r\n    canvas.toBlob(\r\n      resolve, // Pass resolve directly as the callback\r\n       \"image/png\" // Output as PNG from canvas\r\n       // Quality parameter is not applicable for PNG\r\n     );\r\n  });\r\n}\r\n\r\ninterface ImageCropDialogProps {\r\n  imgSrc: string | null;\r\n  onCropComplete: (_blob: Blob | null) => void; // Disabled warning for unused type param\r\n  onClose: () => void;\r\n  isOpen: boolean;\r\n}\r\n\r\nexport default function ImageCropDialog({\r\n  imgSrc,\r\n  onCropComplete,\r\n  onClose,\r\n  isOpen,\r\n}: ImageCropDialogProps) {\r\n  const [crop, setCrop] = useState<Point>({ x: 0, y: 0 });\r\n  const [zoom, setZoom] = useState(1);\r\n  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null);\r\n  const [isCropping, setIsCropping] = useState(false);\r\n\r\n  const onCropCompleteCallback = useCallback((_croppedArea: Area, croppedAreaPixels: Area) => {\r\n    setCroppedAreaPixels(croppedAreaPixels);\r\n  }, []);\r\n\r\n  const handleCrop = async () => {\r\n    if (!imgSrc || !croppedAreaPixels) {\r\n      console.warn(\"Image source or crop area not available.\");\r\n      onCropComplete(null);\r\n      return;\r\n    }\r\n    setIsCropping(true);\r\n    try {\r\n      const croppedBlob = await getCroppedImgBlob(imgSrc, croppedAreaPixels);\r\n      onCropComplete(croppedBlob);\r\n    } catch (e) {\r\n      console.error(\"Error cropping image:\", e);\r\n      onCropComplete(null); // Indicate error\r\n    } finally {\r\n      setIsCropping(false);\r\n    }\r\n  };\r\n\r\n  // Reset zoom when dialog opens using useEffect\r\n  useEffect(() => { // Use useEffect directly after importing React\r\n    if (isOpen) {\r\n      setZoom(1);\r\n    }\r\n  }, [isOpen]); // Add isOpen as a dependency\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"sm:max-w-[600px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>Crop Your Logo</DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"relative h-[40vh] md:h-[50vh] w-full my-4 bg-neutral-200 dark:bg-neutral-800\">\r\n          {imgSrc ? (\r\n            <Cropper\r\n              image={imgSrc}\r\n              crop={crop}\r\n              zoom={zoom}\r\n              aspect={1} // 1:1 aspect ratio\r\n              cropShape=\"round\" // Make the crop area round\r\n              showGrid={false}\r\n              onCropChange={setCrop}\r\n              onZoomChange={setZoom}\r\n              onCropComplete={onCropCompleteCallback}\r\n            />\r\n          ) : (\r\n            <div className=\"flex items-center justify-center h-full\">\r\n              <p>Loading image...</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n         {/* Zoom Slider */}\r\n         <div className=\"px-4 pb-4\">\r\n           <Slider\r\n             min={1}\r\n              max={3}\r\n              step={0.1}\r\n              value={[zoom]}\r\n              onValueChange={(value: number[]) => setZoom(value[0])} // Added type for value\r\n              className=\"w-full\"\r\n              aria-label=\"Zoom slider\"\r\n           />\r\n         </div>\r\n        <DialogFooter>\r\n          <Button variant=\"outline\" onClick={onClose} disabled={isCropping}>\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            onClick={handleCrop}\r\n            disabled={isCropping}\r\n            className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)]\"\r\n          >\r\n            {isCropping ? (\r\n              <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n            ) : null}\r\n            Crop Image\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  ); // Ensure the function closing brace and semicolon are correct\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,mRAAiE,mCAAmC;AACpG,+QAAwD,wBAAwB;AAChF;AACA;AAOA;AACA,8NAAiD,gBAAgB;;;AAbjE;;;;;;;AAeA,6CAA6C;AAC7C,MAAM,cAAc,CAAC,MACnB,IAAI,QAAQ,CAAC,SAAS;QACpB,MAAM,QAAQ,IAAI;QAClB,MAAM,gBAAgB,CAAC,QAAQ,IAAM,QAAQ;QAC7C,MAAM,gBAAgB,CAAC,SAAS,CAAC,QAAU,OAAO;QAClD,MAAM,YAAY,CAAC,eAAe,cAAc,sCAAsC;QACtF,MAAM,GAAG,GAAG;IACd;AAEF,gDAAgD;AAChD,eAAe,kBACb,QAAgB,EAChB,SAAe;IAEf,MAAM,QAAQ,MAAM,YAAY;IAChC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,MAAM,MAAM,OAAO,UAAU,CAAC;IAE9B,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IAEA,MAAM,SAAS,MAAM,YAAY,GAAG,MAAM,KAAK;IAC/C,MAAM,SAAS,MAAM,aAAa,GAAG,MAAM,MAAM;IACjD,MAAM,aAAa,OAAO,gBAAgB,IAAI;IAE9C,OAAO,KAAK,GAAG,UAAU,KAAK,GAAG,aAAa;IAC9C,OAAO,MAAM,GAAG,UAAU,MAAM,GAAG,aAAa;IAEhD,IAAI,YAAY,CAAC,YAAY,GAAG,GAAG,YAAY,GAAG;IAClD,IAAI,qBAAqB,GAAG;IAE5B,IAAI,SAAS,CACX,OACA,UAAU,CAAC,GAAG,QACd,UAAU,CAAC,GAAG,QACd,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG,QACnB,GACA,GACA,UAAU,KAAK,GAAG,QAClB,UAAU,MAAM,GAAG;IAGrB,OAAO,IAAI,QAAQ,CAAC;QAClB,OAAO,MAAM,CACX,SACC,YAAY,4BAA4B;;IAG7C;AACF;AASe,SAAS,gBAAgB,EACtC,MAAM,EACN,cAAc,EACd,OAAO,EACP,MAAM,EACe;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;QAAE,GAAG;QAAG,GAAG;IAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+DAAE,CAAC,cAAoB;YAC9D,qBAAqB;QACvB;8DAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI,CAAC,UAAU,CAAC,mBAAmB;YACjC,QAAQ,IAAI,CAAC;YACb,eAAe;YACf;QACF;QACA,cAAc;QACd,IAAI;YACF,MAAM,cAAc,MAAM,kBAAkB,QAAQ;YACpD,eAAe;QACjB,EAAE,OAAO,GAAG;YACV,QAAQ,KAAK,CAAC,yBAAyB;YACvC,eAAe,OAAO,iBAAiB;QACzC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,QAAQ;gBACV,QAAQ;YACV;QACF;oCAAG;QAAC;KAAO,GAAG,6BAA6B;IAE3C,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAEf,6LAAC;oBAAI,WAAU;8BACZ,uBACC,6LAAC,2JAAA,CAAA,UAAO;wBACN,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,WAAU,QAAQ,2BAA2B;;wBAC7C,UAAU;wBACV,cAAc;wBACd,cAAc;wBACd,gBAAgB;;;;;6CAGlB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;8BAKR,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,8HAAA,CAAA,SAAM;wBACL,KAAK;wBACJ,KAAK;wBACL,MAAM;wBACN,OAAO;4BAAC;yBAAK;wBACb,eAAe,CAAC,QAAoB,QAAQ,KAAK,CAAC,EAAE;wBACpD,WAAU;wBACV,cAAW;;;;;;;;;;;8BAGjB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;4BAAS,UAAU;sCAAY;;;;;;sCAGlE,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU;4BACV,WAAU;;gCAET,2BACC,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;2CACjB;gCAAK;;;;;;;;;;;;;;;;;;;;;;;cAMhB,8DAA8D;AACnE;GA/FwB;KAAA", "debugId": null}}, {"offset": {"line": 10261, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/LogoDeleteDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// useState is not used in this component\r\nimport React from \"react\";\r\nimport { Loader2, Trash2 } from \"lucide-react\";\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface LogoDeleteDialogProps {\r\n  isOpen: boolean;\r\n  isDeleting: boolean;\r\n  onClose: () => void;\r\n  onConfirm: () => void;\r\n}\r\n\r\nexport default function LogoDeleteDialog({\r\n  isOpen,\r\n  isDeleting,\r\n  onClose,\r\n  onConfirm,\r\n}: LogoDeleteDialogProps) {\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && !isDeleting && onClose()}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2 text-red-600 dark:text-red-500\">\r\n            <Trash2 className=\"h-5 w-5\" />\r\n            Delete Logo\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Are you sure you want to delete your logo? This action cannot be undone.\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        <div className=\"py-4\">\r\n          <p className=\"text-sm text-neutral-600 dark:text-neutral-400\">\r\n            Your logo will be permanently removed from your business card and from our storage.\r\n          </p>\r\n        </div>\r\n        <DialogFooter>\r\n          <Button\r\n            variant=\"outline\"\r\n            onClick={onClose}\r\n            disabled={isDeleting}\r\n            className=\"border-neutral-200 dark:border-neutral-700\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n          <Button\r\n            variant=\"destructive\"\r\n            onClick={onConfirm}\r\n            disabled={isDeleting}\r\n            className=\"bg-red-600 hover:bg-red-700 text-white\"\r\n          >\r\n            {isDeleting ? (\r\n              <>\r\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                Deleting...\r\n              </>\r\n            ) : (\r\n              \"Delete Logo\"\r\n            )}\r\n          </Button>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AACA;AAQA;AAbA;;;;;AAsBe,SAAS,iBAAiB,EACvC,MAAM,EACN,UAAU,EACV,OAAO,EACP,SAAS,EACa;IACtB,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ,CAAC,cAAc;kBACpE,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGhC,6LAAC,8HAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAIrB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAiD;;;;;;;;;;;8BAIhE,6LAAC,8HAAA,CAAA,eAAY;;sCACX,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,2BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA8B;;+CAInD;;;;;;;;;;;;;;;;;;;;;;;AAOd;KAnDwB", "debugId": null}}, {"offset": {"line": 10393, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardEditForm/FormSubmitButton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../../schema\"; // Added import\r\n\r\ninterface FormSubmitButtonProps {\r\n  form: UseFormReturn<BusinessCardData>; // Changed 'any' to 'BusinessCardData'\r\n  isPending: boolean;\r\n  isLogoUploading: boolean;\r\n  isCheckingSlug?: boolean;\r\n  isPincodeLoading?: boolean;\r\n  onSave: () => void;\r\n}\r\n\r\nexport default function FormSubmitButton({\r\n  form,\r\n  isPending,\r\n  isLogoUploading,\r\n  isCheckingSlug = false,\r\n  isPincodeLoading = false,\r\n  onSave,\r\n}: FormSubmitButtonProps) {\r\n  return (\r\n    <div className=\"mt-8 flex flex-col space-y-4\">\r\n      <div className=\"h-px w-full bg-neutral-200 dark:bg-neutral-800\"></div>\r\n      <div className=\"flex justify-between items-center\">\r\n        <div className=\"text-sm text-neutral-500 dark:text-neutral-400\">\r\n          {form.formState.isDirty ? (\r\n            <span className=\"flex items-center text-amber-600 dark:text-amber-400\">\r\n              <span className=\"relative flex h-2 w-2 mr-2\">\r\n                <span className=\"animate-ping absolute inline-flex h-full w-full rounded-full bg-amber-400 opacity-75\"></span>\r\n                <span className=\"relative inline-flex rounded-full h-2 w-2 bg-amber-500\"></span>\r\n              </span>\r\n              You have unsaved changes\r\n            </span>\r\n          ) : (\r\n            <span className=\"flex items-center\">\r\n              <span className=\"relative flex h-2 w-2 mr-2\">\r\n                <span className=\"relative inline-flex rounded-full h-2 w-2 bg-green-500\"></span>\r\n              </span>\r\n              All changes saved\r\n            </span>\r\n          )}\r\n        </div>\r\n        <Button\r\n          type=\"button\"\r\n          disabled={\r\n            isPending ||\r\n            isLogoUploading ||\r\n            isCheckingSlug ||\r\n            isPincodeLoading ||\r\n            Object.keys(form.formState.errors).length > 0\r\n          }\r\n          onClick={onSave}\r\n          className=\"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] disabled:opacity-50 transition-all duration-200 ease-in-out shadow-sm hover:shadow-md\"\r\n        >\r\n          {(isPending || isLogoUploading || isCheckingSlug || isPincodeLoading) ? (\r\n            <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n          ) : null}\r\n          {isPending ? \"Saving...\" :\r\n           isLogoUploading ? \"Uploading...\" :\r\n           isCheckingSlug ? \"Checking URL...\" :\r\n           isPincodeLoading ? \"Loading Location...\" :\r\n           \"Save Changes\"}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBe,SAAS,iBAAiB,EACvC,IAAI,EACJ,SAAS,EACT,eAAe,EACf,iBAAiB,KAAK,EACtB,mBAAmB,KAAK,EACxB,MAAM,EACgB;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,CAAC,OAAO,iBACrB,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAK,WAAU;;sDACd,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;;;;;;;;;;;;gCACX;;;;;;iDAIT,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAK,WAAU;8CACd,cAAA,6LAAC;wCAAK,WAAU;;;;;;;;;;;gCACX;;;;;;;;;;;;kCAKb,6LAAC,8HAAA,CAAA,SAAM;wBACL,MAAK;wBACL,UACE,aACA,mBACA,kBACA,oBACA,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG;wBAE9C,SAAS;wBACT,WAAU;;4BAER,aAAa,mBAAmB,kBAAkB,iCAClD,6LAAC,oNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;uCACjB;4BACH,YAAY,cACZ,kBAAkB,iBAClB,iBAAiB,oBACjB,mBAAmB,wBACnB;;;;;;;;;;;;;;;;;;;AAKX;KAtDwB", "debugId": null}}, {"offset": {"line": 10525, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/UnsavedChangesReminder.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { AlertTriangle, Save, X, Loader2 } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { UseFormReturn } from \"react-hook-form\";\r\nimport { BusinessCardData } from \"../schema\";\r\n\r\ninterface UnsavedChangesReminderProps {\r\n  form: UseFormReturn<BusinessCardData>;\r\n  isPending: boolean;\r\n  isLogoUploading: boolean;\r\n  isCheckingSlug?: boolean;\r\n  isPincodeLoading?: boolean;\r\n  onSave: () => void;\r\n  onDiscard: () => void;\r\n}\r\n\r\nexport default function UnsavedChangesReminder({\r\n  form,\r\n  isPending,\r\n  isLogoUploading,\r\n  isCheckingSlug = false,\r\n  isPincodeLoading = false,\r\n  onSave,\r\n  onDiscard,\r\n}: UnsavedChangesReminderProps) {\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [hasUserInteracted, setHasUserInteracted] = useState(false);\r\n\r\n  // Track when form is properly initialized\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setIsInitialized(true);\r\n    }, 5000); // 5 second delay to allow form initialization to complete\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  // Track form state changes to detect user interaction\r\n  useEffect(() => {\r\n    if (!isInitialized) return;\r\n\r\n    // Only mark as user interacted if form becomes dirty after initialization\r\n    if (form.formState.isDirty && !hasUserInteracted) {\r\n      // Add a small delay to ensure this isn't from initial form setup\r\n      const timer = setTimeout(() => {\r\n        if (form.formState.isDirty) {\r\n          setHasUserInteracted(true);\r\n        }\r\n      }, 500);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [form.formState.isDirty, isInitialized, hasUserInteracted]);\r\n\r\n  // Reset user interaction flag when form is submitted successfully\r\n  useEffect(() => {\r\n    if (!form.formState.isDirty) {\r\n      setHasUserInteracted(false);\r\n    }\r\n  }, [form.formState.isDirty]);\r\n\r\n  const hasUnsavedChanges = isInitialized && hasUserInteracted && form.formState.isDirty;\r\n\r\n  return (\r\n    <AnimatePresence>\r\n      {hasUnsavedChanges && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: -100 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -100 }}\r\n          transition={{ duration: 0.3, ease: \"easeOut\" }}\r\n          className=\"fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[calc(100%-2rem)] max-w-md\"\r\n        >\r\n          <div className=\"bg-white dark:bg-neutral-900 border border-amber-200 dark:border-amber-800 rounded-xl shadow-xl backdrop-blur-sm p-4 ring-1 ring-black/5 dark:ring-white/10\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                <div className=\"flex-shrink-0\">\r\n                  <AlertTriangle className=\"h-5 w-5 text-amber-500\" />\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <p className=\"text-sm font-medium text-neutral-900 dark:text-neutral-100\">\r\n                    You have unsaved changes\r\n                  </p>\r\n                  <p className=\"text-xs text-neutral-600 dark:text-neutral-400\">\r\n                    Don&apos;t forget to save your changes before leaving\r\n                  </p>\r\n                </div>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Button\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  onClick={onDiscard}\r\n                  disabled={isPending || isLogoUploading || isCheckingSlug || isPincodeLoading}\r\n                  className=\"text-xs px-2 py-1 h-7\"\r\n                >\r\n                  <X className=\"h-3 w-3 mr-1\" />\r\n                  Discard\r\n                </Button>\r\n                <Button\r\n                  size=\"sm\"\r\n                  onClick={onSave}\r\n                  disabled={\r\n                    isPending ||\r\n                    isLogoUploading ||\r\n                    isCheckingSlug ||\r\n                    isPincodeLoading ||\r\n                    Object.keys(form.formState.errors).length > 0\r\n                  }\r\n                  className=\"text-xs px-3 py-1.5 h-8 bg-gradient-to-r from-[var(--brand-gold)] to-[var(--brand-gold-light)] hover:from-[var(--brand-gold-light)] hover:to-[var(--brand-gold)] text-[var(--brand-gold-foreground)] font-medium shadow-sm hover:shadow-md transition-all duration-200 border-0\"\r\n                >\r\n                  {(isPending || isLogoUploading || isCheckingSlug || isPincodeLoading) ? (\r\n                    <>\r\n                      <Loader2 className=\"h-3 w-3 mr-1.5 animate-spin\" />\r\n                      {isPending ? \"Saving...\" :\r\n                       isLogoUploading ? \"Uploading...\" :\r\n                       isCheckingSlug ? \"Checking...\" :\r\n                       isPincodeLoading ? \"Loading...\" :\r\n                       \"Saving...\"}\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Save className=\"h-3 w-3 mr-1.5\" />\r\n                      Save\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAmBe,SAAS,uBAAuB,EAC7C,IAAI,EACJ,SAAS,EACT,eAAe,EACf,iBAAiB,KAAK,EACtB,mBAAmB,KAAK,EACxB,MAAM,EACN,SAAS,EACmB;;IAC5B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,QAAQ;0DAAW;oBACvB,iBAAiB;gBACnB;yDAAG,OAAO,0DAA0D;YAEpE;oDAAO,IAAM,aAAa;;QAC5B;2CAAG,EAAE;IAEL,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,eAAe;YAEpB,0EAA0E;YAC1E,IAAI,KAAK,SAAS,CAAC,OAAO,IAAI,CAAC,mBAAmB;gBAChD,iEAAiE;gBACjE,MAAM,QAAQ;8DAAW;wBACvB,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE;4BAC1B,qBAAqB;wBACvB;oBACF;6DAAG;gBAEH;wDAAO,IAAM,aAAa;;YAC5B;QACF;2CAAG;QAAC,KAAK,SAAS,CAAC,OAAO;QAAE;QAAe;KAAkB;IAE7D,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,IAAI,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE;gBAC3B,qBAAqB;YACvB;QACF;2CAAG;QAAC,KAAK,SAAS,CAAC,OAAO;KAAC;IAE3B,MAAM,oBAAoB,iBAAiB,qBAAqB,KAAK,SAAS,CAAC,OAAO;IAEtF,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,mCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAI;YAC/B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAI;YAC5B,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;YAC7C,WAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;8CAE3B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA6D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAAiD;;;;;;;;;;;;;;;;;;sCAKlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU,aAAa,mBAAmB,kBAAkB;oCAC5D,WAAU;;sDAEV,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGhC,6LAAC,8HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UACE,aACA,mBACA,kBACA,oBACA,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG;oCAE9C,WAAU;8CAET,AAAC,aAAa,mBAAmB,kBAAkB,iCAClD;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,YAAY,cACZ,kBAAkB,iBAClB,iBAAiB,gBACjB,mBAAmB,eACnB;;qEAGH;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzD;GAtHwB;KAAA", "debugId": null}}, {"offset": {"line": 10766, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/CardEditorClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useTransition, useRef, useMemo, useCallback } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { toast } from \"sonner\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Link as LinkIcon } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { scrollToFirstError } from \"./utils/scrollToError\";\r\n\r\nimport {\r\n  BusinessCardData,\r\n  businessCardSchema,\r\n  defaultBusinessCardData,\r\n  requiredFieldsForOnline,\r\n  requiredFieldsForSaving,\r\n} from \"./schema\";\r\nimport { updateBusinessCard } from \"./actions\";\r\n\r\n// Import custom hooks\r\nimport { useLogoUpload } from \"./components/hooks/useLogoUpload\";\r\nimport { usePincodeDetails } from \"./components/hooks/usePincodeDetails\";\r\n\r\n// Import components\r\nimport CardEditFormContent from \"./components/CardEditForm/CardEditFormContent\";\r\nimport CardPreviewSection from \"./components/CardPreviewSection\";\r\nimport ImageCropDialog from \"./components/ImageCropDialog\";\r\nimport LogoDeleteDialog from \"./components/LogoDeleteDialog\";\r\nimport FormSubmitButton from \"./components/CardEditForm/FormSubmitButton\";\r\nimport UnsavedChangesReminder from \"./components/UnsavedChangesReminder\";\r\nimport { Form } from \"@/components/ui/form\";\r\n\r\nexport type UserPlanStatus =\r\n  | \"basic\"\r\n  | \"growth\"\r\n  | \"pro\"\r\n  | \"enterprise\"\r\n  | \"trial\"\r\n  | null;\r\n\r\ninterface CardEditorClientProps {\r\n  initialData: Partial<BusinessCardData>;\r\n  currentUserPlan: UserPlanStatus;\r\n  subscriptionStatus: string | null;\r\n}\r\n\r\nexport default function CardEditorClient({\r\n  initialData,\r\n  currentUserPlan,\r\n  subscriptionStatus,\r\n}: CardEditorClientProps) {\r\n  // Client-side check to prevent SSR issues\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Main state for the current card data (for preview)\r\n  const [currentCardData, setCurrentCardData] = useState<BusinessCardData>(\r\n    () => {\r\n      const mergedData = {\r\n        ...defaultBusinessCardData,\r\n        ...initialData,\r\n      };\r\n      return {\r\n        ...mergedData,\r\n        member_name: mergedData.member_name || \"\",\r\n        title: mergedData.title || \"\",\r\n        business_name: mergedData.business_name || \"\",\r\n        status: mergedData.status || \"offline\",\r\n      } as BusinessCardData;\r\n    }\r\n  );\r\n\r\n  // Store the original saved data for discard functionality\r\n  const [savedCardData, setSavedCardData] = useState<BusinessCardData>(\r\n    () => {\r\n      const mergedData = {\r\n        ...defaultBusinessCardData,\r\n        ...initialData,\r\n      };\r\n      return {\r\n        ...mergedData,\r\n        member_name: mergedData.member_name || \"\",\r\n        title: mergedData.title || \"\",\r\n        business_name: mergedData.business_name || \"\",\r\n        status: mergedData.status || \"offline\",\r\n      } as BusinessCardData;\r\n    }\r\n  );\r\n\r\n  // Form submission state\r\n  const [isPending, startTransition] = useTransition();\r\n  const [isResetting, setIsResetting] = useState(false);\r\n  const [isCheckingSlug, setIsCheckingSlug] = useState(false);\r\n\r\n  // Reference to the card preview element for QR code download\r\n  const cardPreviewRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  // Properly merge initial data with defaults to prevent dirty state on load\r\n  const formDefaultValues = useMemo(() => ({\r\n    ...defaultBusinessCardData,\r\n    ...initialData,\r\n    locality: initialData?.locality ?? \"\",\r\n    // Ensure custom_branding and custom_ads have proper structure\r\n    custom_branding: {\r\n      ...defaultBusinessCardData.custom_branding,\r\n      ...(initialData?.custom_branding || {}),\r\n    },\r\n    custom_ads: {\r\n      ...defaultBusinessCardData.custom_ads,\r\n      ...(initialData?.custom_ads || {}),\r\n    },\r\n  }), [initialData]);\r\n\r\n  // Initialize the form with zod resolver\r\n  const form = useForm<BusinessCardData>({\r\n    resolver: zodResolver(businessCardSchema),\r\n    defaultValues: formDefaultValues,\r\n    mode: \"onChange\",\r\n    // This ensures the form properly tracks changes\r\n    resetOptions: {\r\n      keepDirtyValues: false, // When form is reset, all fields are marked as pristine\r\n      keepErrors: false,      // Clear all errors when form is reset\r\n    }\r\n  });\r\n\r\n  // Get watched form values\r\n  const watchedFields = form.watch();\r\n\r\n  // Check if subscription is halted\r\n  const isSubscriptionHalted = subscriptionStatus === \"halted\";\r\n\r\n  // Determine if user can go online based on required fields and subscription status\r\n  // If subscription is halted, user cannot go online regardless of required fields\r\n  const canGoOnline = !isSubscriptionHalted && requiredFieldsForOnline.every(\r\n    (field) => watchedFields[field] && String(watchedFields[field]).trim() !== \"\"\r\n  );\r\n\r\n  // Use custom hooks\r\n  const { isPincodeLoading, availableLocalities, handlePincodeChange } =\r\n    usePincodeDetails({\r\n      form,\r\n      initialPincode: initialData?.pincode,\r\n      initialLocality: initialData?.locality,\r\n    });\r\n\r\n  const {\r\n    logoUploadStatus,\r\n    localPreviewUrl,\r\n    isLogoUploading,\r\n    imageToCrop,\r\n    onFileSelect,\r\n    handleCropComplete,\r\n    handleCropDialogClose,\r\n    handleLogoDelete,\r\n    logoErrorDisplay,\r\n    isDeleteDialogOpen,\r\n    isDeleting,\r\n    // openDeleteDialog is not used\r\n    closeDeleteDialog,\r\n    confirmLogoDelete,\r\n  } = useLogoUpload({\r\n    form,\r\n    initialLogoUrl: initialData?.logo_url || \"\",\r\n    onUpdateCardData: (data) =>\r\n      setCurrentCardData((prev) => ({ ...prev, ...data })),\r\n  });\r\n\r\n  // Effect to set client-side flag\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Effect to properly reset form after initial data is loaded to prevent dirty state\r\n  useEffect(() => {\r\n    if (!isClient) return;\r\n\r\n    // Reset the form with the properly merged data to ensure it's not marked as dirty\r\n    // Only run this once after component mounts\r\n    const timer = setTimeout(() => {\r\n      setIsResetting(true);\r\n      form.reset(formDefaultValues, {\r\n        keepDirtyValues: false,\r\n        keepErrors: false,\r\n        keepDirty: false,\r\n        keepIsSubmitted: false,\r\n      });\r\n      // Clear resetting flag after a short delay\r\n      setTimeout(() => setIsResetting(false), 100);\r\n    }, 100); // Small delay to ensure form is fully initialized\r\n\r\n    return () => clearTimeout(timer);\r\n  }, [isClient, form, formDefaultValues]); // Include form and formDefaultValues dependencies\r\n\r\n  // Effect to watch form changes and update preview state\r\n  useEffect(() => {\r\n    const subscription = form.watch((value) => {\r\n      // Don't update preview during form resets to prevent loops\r\n      if (!isResetting) {\r\n        setCurrentCardData((prev) => ({\r\n          ...prev,\r\n          ...value,\r\n        }));\r\n      }\r\n    });\r\n\r\n    return () => {\r\n      subscription.unsubscribe();\r\n      if (localPreviewUrl) URL.revokeObjectURL(localPreviewUrl);\r\n    };\r\n  }, [form, localPreviewUrl, isResetting]);\r\n\r\n  // Helper function to get missing required fields\r\n  const getMissingFields = (forOnlineOnly: boolean = true): Array<keyof BusinessCardData> => {\r\n    const formValues = form.getValues();\r\n    const fieldsToCheck = forOnlineOnly ? requiredFieldsForOnline : requiredFieldsForSaving;\r\n    return fieldsToCheck.filter(\r\n      (field) => !formValues[field] || String(formValues[field]).trim() === \"\"\r\n    );\r\n  };\r\n\r\n  // Helper function to get human-readable field names\r\n  const getFieldLabel = (field: keyof BusinessCardData): string => {\r\n    const fieldLabels: Record<string, string> = {\r\n      member_name: \"Your Name\",\r\n      title: \"Your Title\",\r\n      business_name: \"Business Name\",\r\n      phone: \"Primary Phone\",\r\n      address_line: \"Address Line\",\r\n      pincode: \"Pincode\",\r\n      city: \"City\",\r\n      state: \"State\",\r\n      locality: \"Locality\",\r\n      contact_email: \"Contact Email\",\r\n      business_category: \"Business Category\",\r\n    };\r\n    return fieldLabels[field] || field;\r\n  };\r\n\r\n  // Handler to discard unsaved changes\r\n  const handleDiscardChanges = () => {\r\n    // Set resetting flag to prevent watch subscription from triggering\r\n    setIsResetting(true);\r\n\r\n    // Reset to the last saved state (savedCardData)\r\n    form.reset(savedCardData, {\r\n      keepDirtyValues: false,\r\n      keepErrors: false,\r\n      keepDirty: false,\r\n      keepIsSubmitted: false,\r\n    });\r\n\r\n    // Also update the current card data to match the saved state\r\n    setCurrentCardData(savedCardData);\r\n\r\n    // Clear resetting flag after a short delay\r\n    setTimeout(() => setIsResetting(false), 100);\r\n\r\n    toast.info(\"Changes discarded\");\r\n  };\r\n\r\n  // Single form submission handler (used by both floating save and form save button)\r\n  const onSubmit = (data: BusinessCardData) => {\r\n    // Check if there are any validation errors from zod\r\n    if (Object.keys(form.formState.errors).length > 0) {\r\n      // Scroll to the first error\r\n      scrollToFirstError('business-card-form');\r\n\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot save business card</p>\r\n          <p className=\"text-sm mb-1\">Please fix the validation errors</p>\r\n        </div>\r\n      );\r\n      return;\r\n    }\r\n\r\n    // First check if required fields for saving are missing\r\n    const missingSavingFields = getMissingFields(false);\r\n    if (missingSavingFields.length > 0) {\r\n      const missingFieldLabels = missingSavingFields.map(getFieldLabel);\r\n\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot save business card</p>\r\n          <p className=\"text-sm mb-1\">Please fill in the following required fields:</p>\r\n          <ul className=\"text-sm list-disc pl-4\">\r\n            {missingFieldLabels.map((field, index) => (\r\n              <li key={index}>{field}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      );\r\n\r\n      // Focus on the first missing field\r\n      const firstMissingField = missingSavingFields[0];\r\n      form.setFocus(firstMissingField);\r\n      return;\r\n    }\r\n\r\n    // Check if subscription is halted and trying to go online\r\n    if (data.status === \"online\" && isSubscriptionHalted) {\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot set card to online status</p>\r\n          <p className=\"text-sm mb-1\">Your subscription is currently paused. Please resume your subscription to set your card online.</p>\r\n        </div>\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Then check if trying to go online but missing required fields\r\n    if (data.status === \"online\" && !canGoOnline && !isSubscriptionHalted) {\r\n      const missingOnlineFields = getMissingFields(true);\r\n      const missingFieldLabels = missingOnlineFields.map(getFieldLabel);\r\n\r\n      toast.error(\r\n        <div>\r\n          <p className=\"font-medium mb-1\">Cannot set card to online status</p>\r\n          <p className=\"text-sm mb-1\">Please fill in the following required fields:</p>\r\n          <ul className=\"text-sm list-disc pl-4\">\r\n            {missingFieldLabels.map((field, index) => (\r\n              <li key={index}>{field}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      );\r\n\r\n      // Focus on the first missing field\r\n      const firstMissingField = missingOnlineFields[0];\r\n      form.setFocus(firstMissingField);\r\n      return;\r\n    }\r\n\r\n    startTransition(async () => {\r\n      const result = await updateBusinessCard(data);\r\n\r\n      if (result.success && result.data) {\r\n        toast.success(\"Business card updated successfully!\");\r\n\r\n        // Update both current and saved card data states\r\n        setCurrentCardData(result.data);\r\n        setSavedCardData(result.data);\r\n\r\n        // Set resetting flag to prevent watch subscription from triggering\r\n        setIsResetting(true);\r\n\r\n        // Reset the form with the updated data immediately\r\n        // Use the proper reset options to ensure form state is properly updated\r\n        form.reset(result.data, {\r\n          keepDirtyValues: false, // Mark all fields as pristine\r\n          keepErrors: false,      // Clear all errors\r\n          keepDirty: false,       // Reset dirty state\r\n          keepIsSubmitted: false, // Reset submitted state\r\n        });\r\n\r\n        // Clear resetting flag after a short delay\r\n        setTimeout(() => setIsResetting(false), 100);\r\n      } else {\r\n        toast.error(result.error || \"Failed to update business card.\");\r\n      }\r\n    });\r\n  };\r\n\r\n  // Callback for slug checking state changes\r\n  const handleSlugCheckingChange = useCallback((checking: boolean) => {\r\n    setIsCheckingSlug(checking);\r\n  }, []);\r\n\r\n  // Single save handler for both floating save and form save button\r\n  const handleSave = async () => {\r\n    // Prevent multiple simultaneous submissions or if async operations are in progress\r\n    if (isPending || isCheckingSlug || isPincodeLoading) {\r\n      return;\r\n    }\r\n\r\n    // Check if there are any form validation errors (including slug availability)\r\n    if (Object.keys(form.formState.errors).length > 0) {\r\n      toast.error(\"Please fix the form errors before saving\");\r\n      return;\r\n    }\r\n\r\n    // Get current form values\r\n    const formValues = form.getValues();\r\n\r\n    // Manually validate the form\r\n    const validation = businessCardSchema.safeParse(formValues);\r\n\r\n    if (!validation.success) {\r\n      console.error(\"Validation failed:\", validation.error);\r\n      toast.error(\"Please fix the form errors before saving\");\r\n      return;\r\n    }\r\n\r\n    // If validation passes, call onSubmit directly\r\n    onSubmit(validation.data);\r\n  };\r\n\r\n  // Show loading state until client is ready\r\n  if (!isClient) {\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-[400px]\">\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-[var(--brand-gold)] mx-auto mb-4\"></div>\r\n          <p className=\"text-sm text-muted-foreground\">Loading editor...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Floating Unsaved Changes Reminder */}\r\n      <UnsavedChangesReminder\r\n        form={form}\r\n        isPending={isPending}\r\n        isLogoUploading={isLogoUploading}\r\n        isCheckingSlug={isCheckingSlug}\r\n        isPincodeLoading={isPincodeLoading}\r\n        onSave={handleSave}\r\n        onDiscard={handleDiscardChanges}\r\n      />\r\n\r\n      {/* Single Form for both Mobile and Desktop */}\r\n      <Form {...form}>\r\n        <form onSubmit={(e) => e.preventDefault()} className=\"space-y-8\">\r\n          {/* Mobile/Tablet Layout - Card on top, form below (visible on screens below lg breakpoint) */}\r\n          <div className=\"flex flex-col gap-8 lg:hidden\">\r\n            {/* Preview Section */}\r\n            <CardPreviewSection\r\n              cardData={currentCardData}\r\n              logoUploadStatus={logoUploadStatus}\r\n              localPreviewUrl={localPreviewUrl}\r\n              userPlan={\r\n                currentUserPlan === \"trial\" ? \"basic\" : currentUserPlan ?? undefined\r\n              }\r\n              cardPreviewRef={cardPreviewRef}\r\n            />\r\n\r\n            {/* Edit Form Section */}\r\n            <motion.div initial=\"hidden\" animate=\"visible\" className=\"space-y-6\">\r\n              {/* Header Section */}\r\n              <div className=\"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6\">\r\n                <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n                  <LinkIcon className=\"w-6 h-6 text-foreground\" />\r\n                </div>\r\n                <div className=\"flex-1\">\r\n                  <h1 className=\"text-2xl font-bold text-foreground\">\r\n                    Edit Business Card\r\n                  </h1>\r\n                  <p className=\"text-muted-foreground mt-1\">\r\n                    Customize your digital business card below. Changes reflect in real-time.\r\n                  </p>\r\n                </div>\r\n                <Button\r\n                  type=\"button\"\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    const slug = form.getValues(\"business_slug\");\r\n                    if (slug) window.open(`/${slug}`, \"_blank\");\r\n                    else toast.error(\"Please set a business slug first.\");\r\n                  }}\r\n                  disabled={!form.getValues(\"business_slug\")}\r\n                  className=\"flex items-center gap-2\"\r\n                >\r\n                  <LinkIcon className=\"h-4 w-4\" />\r\n                  View Public Card\r\n                </Button>\r\n              </div>\r\n\r\n              {/* Form Content */}\r\n              <CardEditFormContent\r\n                form={form}\r\n                canGoOnline={canGoOnline}\r\n                currentUserPlan={currentUserPlan}\r\n                onFileSelect={onFileSelect}\r\n                isPincodeLoading={isPincodeLoading}\r\n                availableLocalities={availableLocalities}\r\n                onPincodeChange={handlePincodeChange}\r\n                isLogoUploading={isLogoUploading}\r\n                onLogoDelete={handleLogoDelete}\r\n                isSubscriptionHalted={isSubscriptionHalted}\r\n                onSlugCheckingChange={handleSlugCheckingChange}\r\n              />\r\n\r\n              <div className=\"flex flex-col gap-2 sm:gap-3 mt-6\">\r\n                {logoErrorDisplay && (\r\n                  <p className=\"text-xs text-red-500 dark:text-red-400 text-right\">\r\n                    {logoErrorDisplay}\r\n                  </p>\r\n                )}\r\n                <FormSubmitButton\r\n                  form={form}\r\n                  isPending={isPending}\r\n                  isLogoUploading={isLogoUploading}\r\n                  isCheckingSlug={isCheckingSlug}\r\n                  isPincodeLoading={isPincodeLoading}\r\n                  onSave={handleSave}\r\n                />\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Desktop Layout - Only visible at lg breakpoint and above */}\r\n          <div className=\"hidden lg:block space-y-8\">\r\n            {/* Header Section */}\r\n            <div className=\"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6\">\r\n              <div className=\"p-3 rounded-xl bg-muted\">\r\n                <LinkIcon className=\"w-6 h-6 text-foreground\" />\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <h1 className=\"text-2xl font-bold text-foreground\">\r\n                  Edit Business Card\r\n                </h1>\r\n                <p className=\"text-muted-foreground mt-1\">\r\n                  Customize your digital business card below. Changes reflect in real-time.\r\n                </p>\r\n              </div>\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={() => {\r\n                  const slug = form.getValues(\"business_slug\");\r\n                  if (slug) window.open(`/${slug}`, \"_blank\");\r\n                  else toast.error(\"Please set a business slug first.\");\r\n                }}\r\n                disabled={!form.getValues(\"business_slug\")}\r\n                className=\"flex items-center gap-2\"\r\n              >\r\n                <LinkIcon className=\"h-4 w-4\" />\r\n                View Public Card\r\n              </Button>\r\n            </div>\r\n\r\n            {/* Two Column Layout */}\r\n            <div className=\"flex flex-row gap-8 pb-12\">\r\n              {/* Preview Column */}\r\n              <div className=\"flex-[1] w-1/2 sticky top-24 self-start\">\r\n                <CardPreviewSection\r\n                  cardData={currentCardData}\r\n                  logoUploadStatus={logoUploadStatus}\r\n                  localPreviewUrl={localPreviewUrl}\r\n                  userPlan={\r\n                    currentUserPlan === \"trial\"\r\n                      ? \"basic\"\r\n                      : currentUserPlan ?? undefined\r\n                  }\r\n                  cardPreviewRef={cardPreviewRef}\r\n                />\r\n              </div>\r\n\r\n              {/* Edit Form Column */}\r\n              <motion.div\r\n                initial=\"hidden\"\r\n                animate=\"visible\"\r\n                className=\"flex-[2] space-y-6\"\r\n                style={{position: 'sticky', top: '6rem', alignSelf: 'flex-start'}}\r\n              >\r\n                {/* Form Content */}\r\n                <CardEditFormContent\r\n                  form={form}\r\n                  canGoOnline={canGoOnline}\r\n                  currentUserPlan={currentUserPlan}\r\n                  onFileSelect={onFileSelect}\r\n                  isPincodeLoading={isPincodeLoading}\r\n                  availableLocalities={availableLocalities}\r\n                  onPincodeChange={handlePincodeChange}\r\n                  isLogoUploading={isLogoUploading}\r\n                  onLogoDelete={handleLogoDelete}\r\n                  isSubscriptionHalted={isSubscriptionHalted}\r\n                  onSlugCheckingChange={handleSlugCheckingChange}\r\n                />\r\n\r\n                <div className=\"flex flex-col gap-2 sm:gap-3 mt-6\">\r\n                  {logoErrorDisplay && (\r\n                    <p className=\"text-xs text-red-500 dark:text-red-400 text-right\">\r\n                      {logoErrorDisplay}\r\n                    </p>\r\n                  )}\r\n                  <FormSubmitButton\r\n                    form={form}\r\n                    isPending={isPending}\r\n                    isLogoUploading={isLogoUploading}\r\n                    isCheckingSlug={isCheckingSlug}\r\n                    isPincodeLoading={isPincodeLoading}\r\n                    onSave={handleSave}\r\n                  />\r\n                </div>\r\n              </motion.div>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </Form>\r\n\r\n      {/* Image Crop Dialog */}\r\n      <ImageCropDialog\r\n        isOpen={!!imageToCrop}\r\n        imgSrc={imageToCrop}\r\n        onClose={handleCropDialogClose}\r\n        onCropComplete={handleCropComplete}\r\n      />\r\n\r\n      {/* Logo Delete Confirmation Dialog */}\r\n      <LogoDeleteDialog\r\n        isOpen={isDeleteDialogOpen}\r\n        isDeleting={isDeleting}\r\n        onClose={closeDeleteDialog}\r\n        onConfirm={confirmLogoDelete}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAOA;AAAA;AAEA,sBAAsB;AACtB;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;;;AA/BA;;;;;;;;;;;;;;;;;;;;AA+Ce,SAAS,iBAAiB,EACvC,WAAW,EACX,eAAe,EACf,kBAAkB,EACI;;IACtB,0CAA0C;IAC1C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qDAAqD;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;qCACnD;YACE,MAAM,aAAa;gBACjB,GAAG,kKAAA,CAAA,0BAAuB;gBAC1B,GAAG,WAAW;YAChB;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa,WAAW,WAAW,IAAI;gBACvC,OAAO,WAAW,KAAK,IAAI;gBAC3B,eAAe,WAAW,aAAa,IAAI;gBAC3C,QAAQ,WAAW,MAAM,IAAI;YAC/B;QACF;;IAGF,0DAA0D;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;qCAC/C;YACE,MAAM,aAAa;gBACjB,GAAG,kKAAA,CAAA,0BAAuB;gBAC1B,GAAG,WAAW;YAChB;YACA,OAAO;gBACL,GAAG,UAAU;gBACb,aAAa,WAAW,WAAW,IAAI;gBACvC,OAAO,WAAW,KAAK,IAAI;gBAC3B,eAAe,WAAW,aAAa,IAAI;gBAC3C,QAAQ,WAAW,MAAM,IAAI;YAC/B;QACF;;IAGF,wBAAwB;IACxB,MAAM,CAAC,WAAW,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,6DAA6D;IAC7D,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAErD,2EAA2E;IAC3E,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uDAAE,IAAM,CAAC;gBACvC,GAAG,kKAAA,CAAA,0BAAuB;gBAC1B,GAAG,WAAW;gBACd,UAAU,aAAa,YAAY;gBACnC,8DAA8D;gBAC9D,iBAAiB;oBACf,GAAG,kKAAA,CAAA,0BAAuB,CAAC,eAAe;oBAC1C,GAAI,aAAa,mBAAmB,CAAC,CAAC;gBACxC;gBACA,YAAY;oBACV,GAAG,kKAAA,CAAA,0BAAuB,CAAC,UAAU;oBACrC,GAAI,aAAa,cAAc,CAAC,CAAC;gBACnC;YACF,CAAC;sDAAG;QAAC;KAAY;IAEjB,wCAAwC;IACxC,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QACrC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,kKAAA,CAAA,qBAAkB;QACxC,eAAe;QACf,MAAM;QACN,gDAAgD;QAChD,cAAc;YACZ,iBAAiB;YACjB,YAAY;QACd;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,KAAK,KAAK;IAEhC,kCAAkC;IAClC,MAAM,uBAAuB,uBAAuB;IAEpD,mFAAmF;IACnF,iFAAiF;IACjF,MAAM,cAAc,CAAC,wBAAwB,kKAAA,CAAA,0BAAuB,CAAC,KAAK,CACxE,CAAC,QAAU,aAAa,CAAC,MAAM,IAAI,OAAO,aAAa,CAAC,MAAM,EAAE,IAAI,OAAO;IAG7E,mBAAmB;IACnB,MAAM,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,GAClE,CAAA,GAAA,oMAAA,CAAA,oBAAiB,AAAD,EAAE;QAChB;QACA,gBAAgB,aAAa;QAC7B,iBAAiB,aAAa;IAChC;IAEF,MAAM,EACJ,gBAAgB,EAChB,eAAe,EACf,eAAe,EACf,WAAW,EACX,YAAY,EACZ,kBAAkB,EAClB,qBAAqB,EACrB,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,UAAU,EACV,+BAA+B;IAC/B,iBAAiB,EACjB,iBAAiB,EAClB,GAAG,CAAA,GAAA,gMAAA,CAAA,gBAAa,AAAD,EAAE;QAChB;QACA,gBAAgB,aAAa,YAAY;QACzC,gBAAgB;8CAAE,CAAC,OACjB;sDAAmB,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,GAAG,IAAI;wBAAC,CAAC;;;IACtD;IAEA,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,YAAY;QACd;qCAAG,EAAE;IAEL,oFAAoF;IACpF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,UAAU;YAEf,kFAAkF;YAClF,4CAA4C;YAC5C,MAAM,QAAQ;oDAAW;oBACvB,eAAe;oBACf,KAAK,KAAK,CAAC,mBAAmB;wBAC5B,iBAAiB;wBACjB,YAAY;wBACZ,WAAW;wBACX,iBAAiB;oBACnB;oBACA,2CAA2C;oBAC3C;4DAAW,IAAM,eAAe;2DAAQ;gBAC1C;mDAAG,MAAM,kDAAkD;YAE3D;8CAAO,IAAM,aAAa;;QAC5B;qCAAG;QAAC;QAAU;QAAM;KAAkB,GAAG,kDAAkD;IAE3F,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,eAAe,KAAK,KAAK;2DAAC,CAAC;oBAC/B,2DAA2D;oBAC3D,IAAI,CAAC,aAAa;wBAChB;uEAAmB,CAAC,OAAS,CAAC;oCAC5B,GAAG,IAAI;oCACP,GAAG,KAAK;gCACV,CAAC;;oBACH;gBACF;;YAEA;8CAAO;oBACL,aAAa,WAAW;oBACxB,IAAI,iBAAiB,IAAI,eAAe,CAAC;gBAC3C;;QACF;qCAAG;QAAC;QAAM;QAAiB;KAAY;IAEvC,iDAAiD;IACjD,MAAM,mBAAmB,CAAC,gBAAyB,IAAI;QACrD,MAAM,aAAa,KAAK,SAAS;QACjC,MAAM,gBAAgB,gBAAgB,kKAAA,CAAA,0BAAuB,GAAG,kKAAA,CAAA,0BAAuB;QACvF,OAAO,cAAc,MAAM,CACzB,CAAC,QAAU,CAAC,UAAU,CAAC,MAAM,IAAI,OAAO,UAAU,CAAC,MAAM,EAAE,IAAI,OAAO;IAE1E;IAEA,oDAAoD;IACpD,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAsC;YAC1C,aAAa;YACb,OAAO;YACP,eAAe;YACf,OAAO;YACP,cAAc;YACd,SAAS;YACT,MAAM;YACN,OAAO;YACP,UAAU;YACV,eAAe;YACf,mBAAmB;QACrB;QACA,OAAO,WAAW,CAAC,MAAM,IAAI;IAC/B;IAEA,qCAAqC;IACrC,MAAM,uBAAuB;QAC3B,mEAAmE;QACnE,eAAe;QAEf,gDAAgD;QAChD,KAAK,KAAK,CAAC,eAAe;YACxB,iBAAiB;YACjB,YAAY;YACZ,WAAW;YACX,iBAAiB;QACnB;QAEA,6DAA6D;QAC7D,mBAAmB;QAEnB,2CAA2C;QAC3C,WAAW,IAAM,eAAe,QAAQ;QAExC,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,mFAAmF;IACnF,MAAM,WAAW,CAAC;QAChB,oDAAoD;QACpD,IAAI,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG;YACjD,4BAA4B;YAC5B,CAAA,GAAA,kLAAA,CAAA,qBAAkB,AAAD,EAAE;YAEnB,2IAAA,CAAA,QAAK,CAAC,KAAK,eACT,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;YAGhC;QACF;QAEA,wDAAwD;QACxD,MAAM,sBAAsB,iBAAiB;QAC7C,IAAI,oBAAoB,MAAM,GAAG,GAAG;YAClC,MAAM,qBAAqB,oBAAoB,GAAG,CAAC;YAEnD,2IAAA,CAAA,QAAK,CAAC,KAAK,eACT,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,6LAAC;wBAAG,WAAU;kCACX,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;0CAAgB;+BAAR;;;;;;;;;;;;;;;;YAMjB,mCAAmC;YACnC,MAAM,oBAAoB,mBAAmB,CAAC,EAAE;YAChD,KAAK,QAAQ,CAAC;YACd;QACF;QAEA,0DAA0D;QAC1D,IAAI,KAAK,MAAM,KAAK,YAAY,sBAAsB;YACpD,2IAAA,CAAA,QAAK,CAAC,KAAK,eACT,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;;;;;;;YAGhC;QACF;QAEA,gEAAgE;QAChE,IAAI,KAAK,MAAM,KAAK,YAAY,CAAC,eAAe,CAAC,sBAAsB;YACrE,MAAM,sBAAsB,iBAAiB;YAC7C,MAAM,qBAAqB,oBAAoB,GAAG,CAAC;YAEnD,2IAAA,CAAA,QAAK,CAAC,KAAK,eACT,6LAAC;;kCACC,6LAAC;wBAAE,WAAU;kCAAmB;;;;;;kCAChC,6LAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,6LAAC;wBAAG,WAAU;kCACX,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;0CAAgB;+BAAR;;;;;;;;;;;;;;;;YAMjB,mCAAmC;YACnC,MAAM,oBAAoB,mBAAmB,CAAC,EAAE;YAChD,KAAK,QAAQ,CAAC;YACd;QACF;QAEA,gBAAgB;YACd,MAAM,SAAS,MAAM,CAAA,GAAA,oNAAA,CAAA,qBAAkB,AAAD,EAAE;YAExC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,iDAAiD;gBACjD,mBAAmB,OAAO,IAAI;gBAC9B,iBAAiB,OAAO,IAAI;gBAE5B,mEAAmE;gBACnE,eAAe;gBAEf,mDAAmD;gBACnD,wEAAwE;gBACxE,KAAK,KAAK,CAAC,OAAO,IAAI,EAAE;oBACtB,iBAAiB;oBACjB,YAAY;oBACZ,WAAW;oBACX,iBAAiB;gBACnB;gBAEA,2CAA2C;gBAC3C,WAAW,IAAM,eAAe,QAAQ;YAC1C,OAAO;gBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF;IACF;IAEA,2CAA2C;IAC3C,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kEAAE,CAAC;YAC5C,kBAAkB;QACpB;iEAAG,EAAE;IAEL,kEAAkE;IAClE,MAAM,aAAa;QACjB,mFAAmF;QACnF,IAAI,aAAa,kBAAkB,kBAAkB;YACnD;QACF;QAEA,8EAA8E;QAC9E,IAAI,OAAO,IAAI,CAAC,KAAK,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG;YACjD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,0BAA0B;QAC1B,MAAM,aAAa,KAAK,SAAS;QAEjC,6BAA6B;QAC7B,MAAM,aAAa,kKAAA,CAAA,qBAAkB,CAAC,SAAS,CAAC;QAEhD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,QAAQ,KAAK,CAAC,sBAAsB,WAAW,KAAK;YACpD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,+CAA+C;QAC/C,SAAS,WAAW,IAAI;IAC1B;IAEA,2CAA2C;IAC3C,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIrD;IAEA,qBACE;;0BAEE,6LAAC,iMAAA,CAAA,UAAsB;gBACrB,MAAM;gBACN,WAAW;gBACX,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,QAAQ;gBACR,WAAW;;;;;;0BAIb,6LAAC,4HAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBAAK,UAAU,CAAC,IAAM,EAAE,cAAc;oBAAI,WAAU;;sCAEnD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,sMAAA,CAAA,UAAkB;oCACjB,UAAU;oCACV,kBAAkB;oCAClB,iBAAiB;oCACjB,UACE,oBAAoB,UAAU,UAAU,mBAAmB;oCAE7D,gBAAgB;;;;;;8CAIlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,SAAQ;oCAAS,SAAQ;oCAAU,WAAU;;sDAEvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEAGnD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;8DAI5C,6LAAC,8HAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;wDACP,MAAM,OAAO,KAAK,SAAS,CAAC;wDAC5B,IAAI,MAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;6DAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oDACnB;oDACA,UAAU,CAAC,KAAK,SAAS,CAAC;oDAC1B,WAAU;;sEAEV,6LAAC,qMAAA,CAAA,OAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;sDAMpC,6LAAC,8MAAA,CAAA,UAAmB;4CAClB,MAAM;4CACN,aAAa;4CACb,iBAAiB;4CACjB,cAAc;4CACd,kBAAkB;4CAClB,qBAAqB;4CACrB,iBAAiB;4CACjB,iBAAiB;4CACjB,cAAc;4CACd,sBAAsB;4CACtB,sBAAsB;;;;;;sDAGxB,6LAAC;4CAAI,WAAU;;gDACZ,kCACC,6LAAC;oDAAE,WAAU;8DACV;;;;;;8DAGL,6LAAC,2MAAA,CAAA,UAAgB;oDACf,MAAM;oDACN,WAAW;oDACX,iBAAiB;oDACjB,gBAAgB;oDAChB,kBAAkB;oDAClB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DAGnD,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;sDAI5C,6LAAC,8HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,MAAM,OAAO,KAAK,SAAS,CAAC;gDAC5B,IAAI,MAAM,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;qDAC7B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;4CACnB;4CACA,UAAU,CAAC,KAAK,SAAS,CAAC;4CAC1B,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAQ;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAMpC,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,sMAAA,CAAA,UAAkB;gDACjB,UAAU;gDACV,kBAAkB;gDAClB,iBAAiB;gDACjB,UACE,oBAAoB,UAChB,UACA,mBAAmB;gDAEzB,gBAAgB;;;;;;;;;;;sDAKpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAQ;4CACR,SAAQ;4CACR,WAAU;4CACV,OAAO;gDAAC,UAAU;gDAAU,KAAK;gDAAQ,WAAW;4CAAY;;8DAGhE,6LAAC,8MAAA,CAAA,UAAmB;oDAClB,MAAM;oDACN,aAAa;oDACb,iBAAiB;oDACjB,cAAc;oDACd,kBAAkB;oDAClB,qBAAqB;oDACrB,iBAAiB;oDACjB,iBAAiB;oDACjB,cAAc;oDACd,sBAAsB;oDACtB,sBAAsB;;;;;;8DAGxB,6LAAC;oDAAI,WAAU;;wDACZ,kCACC,6LAAC;4DAAE,WAAU;sEACV;;;;;;sEAGL,6LAAC,2MAAA,CAAA,UAAgB;4DACf,MAAM;4DACN,WAAW;4DACX,iBAAiB;4DACjB,gBAAgB;4DAChB,kBAAkB;4DAClB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtB,6LAAC,0LAAA,CAAA,UAAe;gBACd,QAAQ,CAAC,CAAC;gBACV,QAAQ;gBACR,SAAS;gBACT,gBAAgB;;;;;;0BAIlB,6LAAC,2LAAA,CAAA,UAAgB;gBACf,QAAQ;gBACR,YAAY;gBACZ,SAAS;gBACT,WAAW;;;;;;;;AAInB;GArjBwB;;QA2Ce,6JAAA,CAAA,gBAAa;QAwBrC,iKAAA,CAAA,UAAO;QAyBlB,oMAAA,CAAA,oBAAiB;QAqBf,gMAAA,CAAA,gBAAa;;;KAjHK", "debugId": null}}]}