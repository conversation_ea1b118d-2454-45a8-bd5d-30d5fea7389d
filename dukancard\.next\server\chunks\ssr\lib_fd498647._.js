module.exports = {

"[project]/lib/razorpay/services/subscription/index.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/lib_razorpay_8dbfe432._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/razorpay/services/subscription/index.ts [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/lib/config/plans.ts [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/lib_config_plans_ts_794a5ac4._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/lib/config/plans.ts [app-rsc] (ecmascript)");
    });
});
}}),

};