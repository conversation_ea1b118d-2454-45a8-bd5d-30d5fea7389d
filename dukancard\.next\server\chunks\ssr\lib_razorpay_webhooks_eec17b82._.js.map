{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-constants.ts"], "sourcesContent": ["// Subscription status constants - IMMUT<PERSON>LE SINGLE SOURCE OF TRUTH\r\nexport const SUBSCRIPTION_STATUS = {\r\n  ACTIVE: 'active',\r\n  AUTHENTICATED: 'authenticated',\r\n  TRIAL: 'trial',\r\n  PENDING: 'pending',\r\n  HALTED: 'halted',\r\n  CANCELLED: 'cancelled',\r\n  EXPIRED: 'expired',\r\n  COMPLETED: 'completed',\r\n  PAYMENT_FAILED: 'payment_failed',\r\n  CANCELLATION_SCHEDULED: 'cancellation_scheduled'\r\n} as const;\r\n\r\nexport type SubscriptionStatus = typeof SUBSCRIPTION_STATUS[keyof typeof SUBSCRIPTION_STATUS];\r\n\r\n// Plan ID constants for consistency\r\nexport const PLAN_IDS = {\r\n  FREE: 'free',\r\n  BASIC: 'basic',\r\n  GROWTH: 'growth',\r\n  PRO: 'pro',\r\n  ENTERPRISE: 'enterprise'\r\n} as const;\r\n\r\nexport type PlanId = typeof PLAN_IDS[keyof typeof PLAN_IDS];"], "names": [], "mappings": "AAAA,mEAAmE;;;;;AAC5D,MAAM,sBAAsB;IACjC,QAAQ;IACR,eAAe;IACf,OAAO;IACP,SAAS;IACT,QAAQ;IACR,WAAW;IACX,SAAS;IACT,WAAW;IACX,gBAAgB;IAChB,wBAAwB;AAC1B;AAKO,MAAM,WAAW;IACtB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,KAAK;IACL,YAAY;AACd", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-state-manager.ts"], "sourcesContent": ["import { PLAN_IDS, SUBSCRIPTION_STATUS } from \"./subscription-constants\";\r\n\r\n/**\r\n * CENTRALIZED SUBSCRIPTION STATE MANAGER\r\n *\r\n * This class provides the single source of truth for ALL subscription logic.\r\n * Use this instead of scattered functions throughout the codebase.\r\n *\r\n * CRITICAL: This is the ONLY place where subscription access logic should be defined.\r\n */\r\nexport class SubscriptionStateManager {\r\n  /**\r\n   * MASTER FUNCTION: Determines if a user should have active subscription access\r\n   * This is the ONLY function that should be used for subscription access control\r\n   *\r\n   * @param status The subscription status\r\n   * @param planId The plan ID (required for accurate determination)\r\n   * @returns true if the user should have active subscription features\r\n   */\r\n  static shouldHaveActiveSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {\r\n    // Free plan users NEVER have \"active subscription\" - they're on free tier\r\n    if (planId === PLAN_IDS.FREE) {\r\n      return false;\r\n    }\r\n\r\n    // Trial users NEVER have \"active subscription\" - they're testing, not paying\r\n    if (status === SUBSCRIPTION_STATUS.TRIAL) {\r\n      return false;\r\n    }\r\n\r\n    // Only ACTIVE status on PAID plans counts as \"active subscription\"\r\n    // Authenticated users have selected a plan but haven't paid yet\r\n    const activeStatuses: string[] = [\r\n      SUBSCRIPTION_STATUS.ACTIVE\r\n    ];\r\n\r\n    return activeStatuses.includes(status);\r\n  }\r\n\r\n  /**\r\n   * Determines if a subscription status is considered terminal (final state)\r\n   * Terminal states cannot transition to active states without creating new subscription\r\n   */\r\n  static isTerminalStatus(status: string): boolean {\r\n    const terminalStatuses: string[] = [\r\n      SUBSCRIPTION_STATUS.CANCELLED,\r\n      SUBSCRIPTION_STATUS.EXPIRED,\r\n      SUBSCRIPTION_STATUS.COMPLETED\r\n    ];\r\n    return terminalStatuses.includes(status);\r\n  }\r\n\r\n  /**\r\n   * Determines if a subscription status indicates trial period\r\n   */\r\n  static isTrialStatus(status: string): boolean {\r\n    return status === SUBSCRIPTION_STATUS.TRIAL;\r\n  }\r\n\r\n  /**\r\n   * Determines if a plan is a free plan\r\n   */\r\n  static isFreeStatus(status: string, planId?: string): boolean {\r\n    return planId === PLAN_IDS.FREE || status === 'free';\r\n  }\r\n\r\n  /**\r\n   * Get user's access level based on subscription state\r\n   */\r\n  static getAccessLevel(status: string, planId: string = PLAN_IDS.FREE): 'free' | 'trial' | 'paid' {\r\n    if (planId === PLAN_IDS.FREE) return 'free';\r\n    if (status === SUBSCRIPTION_STATUS.TRIAL) return 'trial';\r\n    if (this.shouldHaveActiveSubscription(status, planId)) return 'paid';\r\n    return 'free';\r\n  }\r\n\r\n  /**\r\n   * Determines if a subscription status indicates an active paid subscription\r\n   */\r\n  static isActivePaidSubscription(status: string, planId: string = PLAN_IDS.FREE): boolean {\r\n    return this.shouldHaveActiveSubscription(status, planId);\r\n  }\r\n\r\n  /**\r\n   * Validates if a status transition is allowed\r\n   */\r\n  static isValidStatusTransition(fromStatus: string, toStatus: string): boolean {\r\n    // Terminal states cannot transition to non-terminal states\r\n    if (this.isTerminalStatus(fromStatus) && !this.isTerminalStatus(toStatus)) {\r\n      return false;\r\n    }\r\n\r\n    // All other transitions are allowed\r\n    return true;\r\n  }\r\n}\r\n\r\n// Legacy function wrappers for backward compatibility - DEPRECATED\r\n// Use SubscriptionStateManager instead for new code\r\nexport function shouldHaveActiveSubscription(status: string): boolean {\r\n  console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');\r\n  return SubscriptionStateManager.shouldHaveActiveSubscription(status, PLAN_IDS.FREE);\r\n}\r\n\r\nexport function isTerminalStatus(status: string): boolean {\r\n  return SubscriptionStateManager.isTerminalStatus(status);\r\n}\r\n\r\nexport function isActivePaidSubscription(status: string): boolean {\r\n  console.warn('[DEPRECATED] Use SubscriptionStateManager.isActivePaidSubscription(status, planId) instead');\r\n  return SubscriptionStateManager.isActivePaidSubscription(status, PLAN_IDS.FREE);\r\n}\r\n\r\nexport function isTrialStatus(status: string): boolean {\r\n  return SubscriptionStateManager.isTrialStatus(status);\r\n}\r\n\r\nexport function isFreeStatus(status: string, planId?: string): boolean {\r\n  return SubscriptionStateManager.isFreeStatus(status, planId);\r\n}\r\n\r\n/**\r\n * LEGACY FUNCTION - Use SubscriptionStateManager.shouldHaveActiveSubscription instead\r\n *\r\n * @deprecated Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead\r\n */\r\nexport function shouldHaveActiveSubscriptionByPlan(status: string, planId: string): boolean {\r\n  console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');\r\n  return SubscriptionStateManager.shouldHaveActiveSubscription(status, planId);\r\n}"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAUO,MAAM;IACX;;;;;;;GAOC,GACD,OAAO,6BAA6B,MAAc,EAAE,SAAiB,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAW;QAC3F,0EAA0E;QAC1E,IAAI,WAAW,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAE;YAC5B,OAAO;QACT;QAEA,6EAA6E;QAC7E,IAAI,WAAW,oKAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;YACxC,OAAO;QACT;QAEA,mEAAmE;QACnE,gEAAgE;QAChE,MAAM,iBAA2B;YAC/B,oKAAA,CAAA,sBAAmB,CAAC,MAAM;SAC3B;QAED,OAAO,eAAe,QAAQ,CAAC;IACjC;IAEA;;;GAGC,GACD,OAAO,iBAAiB,MAAc,EAAW;QAC/C,MAAM,mBAA6B;YACjC,oKAAA,CAAA,sBAAmB,CAAC,SAAS;YAC7B,oKAAA,CAAA,sBAAmB,CAAC,OAAO;YAC3B,oKAAA,CAAA,sBAAmB,CAAC,SAAS;SAC9B;QACD,OAAO,iBAAiB,QAAQ,CAAC;IACnC;IAEA;;GAEC,GACD,OAAO,cAAc,MAAc,EAAW;QAC5C,OAAO,WAAW,oKAAA,CAAA,sBAAmB,CAAC,KAAK;IAC7C;IAEA;;GAEC,GACD,OAAO,aAAa,MAAc,EAAE,MAAe,EAAW;QAC5D,OAAO,WAAW,oKAAA,CAAA,WAAQ,CAAC,IAAI,IAAI,WAAW;IAChD;IAEA;;GAEC,GACD,OAAO,eAAe,MAAc,EAAE,SAAiB,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAA6B;QAC/F,IAAI,WAAW,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAE,OAAO;QACrC,IAAI,WAAW,oKAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE,OAAO;QACjD,IAAI,IAAI,CAAC,4BAA4B,CAAC,QAAQ,SAAS,OAAO;QAC9D,OAAO;IACT;IAEA;;GAEC,GACD,OAAO,yBAAyB,MAAc,EAAE,SAAiB,oKAAA,CAAA,WAAQ,CAAC,IAAI,EAAW;QACvF,OAAO,IAAI,CAAC,4BAA4B,CAAC,QAAQ;IACnD;IAEA;;GAEC,GACD,OAAO,wBAAwB,UAAkB,EAAE,QAAgB,EAAW;QAC5E,2DAA2D;QAC3D,IAAI,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW;YACzE,OAAO;QACT;QAEA,oCAAoC;QACpC,OAAO;IACT;AACF;AAIO,SAAS,6BAA6B,MAAc;IACzD,QAAQ,IAAI,CAAC;IACb,OAAO,yBAAyB,4BAA4B,CAAC,QAAQ,oKAAA,CAAA,WAAQ,CAAC,IAAI;AACpF;AAEO,SAAS,iBAAiB,MAAc;IAC7C,OAAO,yBAAyB,gBAAgB,CAAC;AACnD;AAEO,SAAS,yBAAyB,MAAc;IACrD,QAAQ,IAAI,CAAC;IACb,OAAO,yBAAyB,wBAAwB,CAAC,QAAQ,oKAAA,CAAA,WAAQ,CAAC,IAAI;AAChF;AAEO,SAAS,cAAc,MAAc;IAC1C,OAAO,yBAAyB,aAAa,CAAC;AAChD;AAEO,SAAS,aAAa,MAAc,EAAE,MAAe;IAC1D,OAAO,yBAAyB,YAAY,CAAC,QAAQ;AACvD;AAOO,SAAS,mCAAmC,MAAc,EAAE,MAAc;IAC/E,QAAQ,IAAI,CAAC;IACb,OAAO,yBAAyB,4BAA4B,CAAC,QAAQ;AACvE", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-state-validator.ts"], "sourcesContent": ["import { SUBSCRIPTION_STATUS } from \"./subscription-constants\";\r\nimport { SubscriptionStateManager } from \"./subscription-state-manager\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\n\r\n/**\r\n * CENTRALIZED SUBSCRIPTION STATE VALIDATION\r\n *\r\n * Validates the entire subscription state for consistency.\r\n * This function should be used whenever we need to verify subscription integrity.\r\n */\r\nexport interface SubscriptionStateValidation {\r\n  isValid: boolean;\r\n  hasActiveSubscription: boolean;\r\n  accessLevel: 'free' | 'trial' | 'paid';\r\n  warnings: string[];\r\n  errors: string[];\r\n}\r\n\r\n/**\r\n * Validates subscription state for consistency across business_profiles and payment_subscriptions\r\n * Uses the centralized SubscriptionStateManager for all logic\r\n */\r\nexport function validateSubscriptionState(\r\n  businessProfile: {\r\n    has_active_subscription: boolean;\r\n    trial_end_date: string | null;\r\n  },\r\n  paymentSubscription: {\r\n    subscription_status: string;\r\n    plan_id: string;\r\n  } | null\r\n): SubscriptionStateValidation {\r\n  const warnings: string[] = [];\r\n  const errors: string[] = [];\r\n\r\n  // Determine expected state based on payment subscription using centralized manager\r\n  let expectedHasActiveSubscription = false;\r\n  let accessLevel: 'free' | 'trial' | 'paid' = 'free';\r\n\r\n  if (paymentSubscription) {\r\n    expectedHasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(\r\n      paymentSubscription.subscription_status,\r\n      paymentSubscription.plan_id\r\n    );\r\n\r\n    accessLevel = SubscriptionStateManager.getAccessLevel(\r\n      paymentSubscription.subscription_status,\r\n      paymentSubscription.plan_id\r\n    );\r\n  }\r\n\r\n  // Check for inconsistencies\r\n  if (businessProfile.has_active_subscription !== expectedHasActiveSubscription) {\r\n    errors.push(\r\n      `has_active_subscription mismatch: business_profiles=${businessProfile.has_active_subscription}, expected=${expectedHasActiveSubscription}`\r\n    );\r\n  }\r\n\r\n  // Check trial state consistency\r\n  if (paymentSubscription?.subscription_status === SUBSCRIPTION_STATUS.TRIAL) {\r\n    if (!businessProfile.trial_end_date) {\r\n      warnings.push('Trial status but no trial_end_date set');\r\n    } else {\r\n      const trialEnd = new Date(businessProfile.trial_end_date);\r\n      const now = new Date();\r\n      if (trialEnd <= now) {\r\n        warnings.push('Trial status but trial period has expired');\r\n      }\r\n    }\r\n  }\r\n\r\n  return {\r\n    isValid: errors.length === 0,\r\n    hasActiveSubscription: expectedHasActiveSubscription,\r\n    accessLevel,\r\n    warnings,\r\n    errors\r\n  };\r\n}\r\n\r\n/**\r\n * ENHANCED SUBSCRIPTION STATE VALIDATOR\r\n *\r\n * Validates and fixes subscription state inconsistencies across tables\r\n */\r\nexport async function validateAndFixSubscriptionState(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; message: string; fixed: boolean }> {\r\n  try {\r\n    const adminClient = createAdminClient();\r\n\r\n    // Get current state from both tables\r\n    const [profileResult, subscriptionResult] = await Promise.all([\r\n      adminClient\r\n        .from('business_profiles')\r\n        .select('has_active_subscription, trial_end_date')\r\n        .eq('id', businessProfileId)\r\n        .maybeSingle(),\r\n      adminClient\r\n        .from('payment_subscriptions')\r\n        .select('subscription_status, plan_id')\r\n        .eq('business_profile_id', businessProfileId)\r\n        .order('created_at', { ascending: false })\r\n        .limit(1)\r\n        .maybeSingle()\r\n    ]);\r\n\r\n    if (profileResult.error) {\r\n      return { success: false, message: `Profile fetch error: ${profileResult.error.message}`, fixed: false };\r\n    }\r\n\r\n    if (!profileResult.data) {\r\n      return { success: false, message: 'Business profile not found', fixed: false };\r\n    }\r\n\r\n    // Use centralized validation\r\n    const validation = validateSubscriptionState(\r\n      profileResult.data,\r\n      subscriptionResult.data\r\n    );\r\n\r\n    if (validation.isValid) {\r\n      return { success: true, message: 'Subscription state is consistent', fixed: false };\r\n    }\r\n\r\n    // Fix inconsistencies\r\n    const expectedHasActiveSubscription = validation.hasActiveSubscription;\r\n\r\n    if (profileResult.data.has_active_subscription !== expectedHasActiveSubscription) {\r\n      const { error: fixError } = await adminClient\r\n        .from('business_profiles')\r\n        .update({\r\n          has_active_subscription: expectedHasActiveSubscription,\r\n          updated_at: new Date().toISOString()\r\n        })\r\n        .eq('id', businessProfileId);\r\n\r\n      if (fixError) {\r\n        return {\r\n          success: false,\r\n          message: `Failed to fix inconsistency: ${fixError.message}`,\r\n          fixed: false\r\n        };\r\n      }\r\n\r\n      console.log(`[STATE_VALIDATOR] Fixed has_active_subscription for ${businessProfileId}: ${profileResult.data.has_active_subscription} -> ${expectedHasActiveSubscription}`);\r\n      return {\r\n        success: true,\r\n        message: `Fixed subscription state inconsistency`,\r\n        fixed: true\r\n      };\r\n    }\r\n\r\n    return { success: true, message: 'No fixes needed', fixed: false };\r\n  } catch (error) {\r\n    console.error(`[STATE_VALIDATOR] Exception:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Validation exception: ${error instanceof Error ? error.message : String(error)}`,\r\n      fixed: false\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAoBO,SAAS,0BACd,eAGC,EACD,mBAGQ;IAER,MAAM,WAAqB,EAAE;IAC7B,MAAM,SAAmB,EAAE;IAE3B,mFAAmF;IACnF,IAAI,gCAAgC;IACpC,IAAI,cAAyC;IAE7C,IAAI,qBAAqB;QACvB,gCAAgC,2KAAA,CAAA,2BAAwB,CAAC,4BAA4B,CACnF,oBAAoB,mBAAmB,EACvC,oBAAoB,OAAO;QAG7B,cAAc,2KAAA,CAAA,2BAAwB,CAAC,cAAc,CACnD,oBAAoB,mBAAmB,EACvC,oBAAoB,OAAO;IAE/B;IAEA,4BAA4B;IAC5B,IAAI,gBAAgB,uBAAuB,KAAK,+BAA+B;QAC7E,OAAO,IAAI,CACT,CAAC,oDAAoD,EAAE,gBAAgB,uBAAuB,CAAC,WAAW,EAAE,+BAA+B;IAE/I;IAEA,gCAAgC;IAChC,IAAI,qBAAqB,wBAAwB,oKAAA,CAAA,sBAAmB,CAAC,KAAK,EAAE;QAC1E,IAAI,CAAC,gBAAgB,cAAc,EAAE;YACnC,SAAS,IAAI,CAAC;QAChB,OAAO;YACL,MAAM,WAAW,IAAI,KAAK,gBAAgB,cAAc;YACxD,MAAM,MAAM,IAAI;YAChB,IAAI,YAAY,KAAK;gBACnB,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B,uBAAuB;QACvB;QACA;QACA;IACF;AACF;AAOO,eAAe,gCACpB,iBAAyB;IAEzB,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEpC,qCAAqC;QACrC,MAAM,CAAC,eAAe,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC5D,YACG,IAAI,CAAC,qBACL,MAAM,CAAC,2CACP,EAAE,CAAC,MAAM,mBACT,WAAW;YACd,YACG,IAAI,CAAC,yBACL,MAAM,CAAC,gCACP,EAAE,CAAC,uBAAuB,mBAC1B,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC,GACN,WAAW;SACf;QAED,IAAI,cAAc,KAAK,EAAE;YACvB,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,qBAAqB,EAAE,cAAc,KAAK,CAAC,OAAO,EAAE;gBAAE,OAAO;YAAM;QACxG;QAEA,IAAI,CAAC,cAAc,IAAI,EAAE;YACvB,OAAO;gBAAE,SAAS;gBAAO,SAAS;gBAA8B,OAAO;YAAM;QAC/E;QAEA,6BAA6B;QAC7B,MAAM,aAAa,0BACjB,cAAc,IAAI,EAClB,mBAAmB,IAAI;QAGzB,IAAI,WAAW,OAAO,EAAE;YACtB,OAAO;gBAAE,SAAS;gBAAM,SAAS;gBAAoC,OAAO;YAAM;QACpF;QAEA,sBAAsB;QACtB,MAAM,gCAAgC,WAAW,qBAAqB;QAEtE,IAAI,cAAc,IAAI,CAAC,uBAAuB,KAAK,+BAA+B;YAChF,MAAM,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,YAC/B,IAAI,CAAC,qBACL,MAAM,CAAC;gBACN,yBAAyB;gBACzB,YAAY,IAAI,OAAO,WAAW;YACpC,GACC,EAAE,CAAC,MAAM;YAEZ,IAAI,UAAU;gBACZ,OAAO;oBACL,SAAS;oBACT,SAAS,CAAC,6BAA6B,EAAE,SAAS,OAAO,EAAE;oBAC3D,OAAO;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,kBAAkB,EAAE,EAAE,cAAc,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,+BAA+B;YACzK,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,sCAAsC,CAAC;gBACjD,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,SAAS;YAAmB,OAAO;QAAM;IACnE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAC9C,OAAO;YACL,SAAS;YACT,SAAS,CAAC,sBAAsB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;YAC1F,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/types.ts"], "sourcesContent": ["/**\r\n * Razorpay Subscription Webhook Event Types\r\n *\r\n * This file contains the types for Razorpay subscription webhook events.\r\n */\r\n\r\n// Subscription webhook event types\r\nexport enum RazorpaySubscriptionEventType {\r\n  // Subscription events\r\n  _SUBSCRIPTION_AUTHENTICATED = \"subscription.authenticated\",\r\n  _SUBSCRIPTION_ACTIVATED = \"subscription.activated\",\r\n  _SUBSCRIPTION_CHARGED = \"subscription.charged\",\r\n  _SUBSCRIPTION_PENDING = \"subscription.pending\",\r\n  _SUBSCRIPTION_HALTED = \"subscription.halted\", // This is the event for paused subscriptions\r\n  _SUBSCRIPTION_CANCELLED = \"subscription.cancelled\",\r\n  _SUBSCRIPTION_COMPLETED = \"subscription.completed\",\r\n  _SUBSCRIPTION_EXPIRED = \"subscription.expired\",\r\n  _SUBSCRIPTION_UPDATED = \"subscription.updated\",\r\n\r\n  // Payment events\r\n  _PAYMENT_AUTHORIZED = \"payment.authorized\",\r\n  _PAYMENT_CAPTURED = \"payment.captured\",\r\n  _PAYMENT_FAILED = \"payment.failed\",\r\n\r\n  // Invoice events\r\n  _INVOICE_PAID = \"invoice.paid\",\r\n\r\n  // Refund events\r\n  _REFUND_CREATED = \"refund.created\",\r\n  _REFUND_PROCESSED = \"refund.processed\",\r\n  _REFUND_FAILED = \"refund.failed\"\r\n}\r\n\r\n// Subscription status values\r\nexport enum RazorpaySubscriptionStatus {\r\n  _CREATED = \"created\",\r\n  _AUTHENTICATED = \"authenticated\",\r\n  _ACTIVE = \"active\",\r\n  _PENDING = \"pending\",\r\n  _HALTED = \"halted\", // This is the status for paused subscriptions\r\n  _CANCELLED = \"cancelled\",\r\n  _COMPLETED = \"completed\",\r\n  _EXPIRED = \"expired\"\r\n}\r\n\r\n// Payment status values\r\nexport enum RazorpayPaymentStatus {\r\n  _CREATED = \"created\",\r\n  _AUTHORIZED = \"authorized\",\r\n  _CAPTURED = \"captured\",\r\n  _REFUNDED = \"refunded\",\r\n  _FAILED = \"failed\"\r\n}\r\n\r\n// Refund status values\r\nexport enum RazorpayRefundStatus {\r\n  _CREATED = \"created\",\r\n  _PROCESSED = \"processed\",\r\n  _FAILED = \"failed\"\r\n}\r\n\r\n// Supabase subscription status types (reused from existing implementation)\r\nexport enum SupabaseSubscriptionStatus {\r\n  _ACTIVE = \"active\",\r\n  _PENDING = \"pending\",\r\n  _HALTED = \"halted\", // This is the status for paused subscriptions\r\n  _CANCELLED = \"cancelled\",\r\n  _COMPLETED = \"completed\",\r\n  _EXPIRED = \"expired\",\r\n  _PAYMENT_FAILED = \"payment_failed\",\r\n  _AUTHENTICATED = \"authenticated\"\r\n}\r\n\r\n/**\r\n * Maps Razorpay subscription status to Supabase subscription status\r\n * @param razorpayStatus The Razorpay subscription status\r\n * @returns The corresponding Supabase subscription status\r\n */\r\nexport function mapRazorpayStatusToSupabase(razorpayStatus: string): SupabaseSubscriptionStatus {\r\n  switch (razorpayStatus) {\r\n    case RazorpaySubscriptionStatus._ACTIVE:\r\n      return SupabaseSubscriptionStatus._ACTIVE;\r\n    case RazorpaySubscriptionStatus._PENDING:\r\n      return SupabaseSubscriptionStatus._PENDING;\r\n    case RazorpaySubscriptionStatus._HALTED:\r\n      return SupabaseSubscriptionStatus._HALTED;\r\n    case RazorpaySubscriptionStatus._CANCELLED:\r\n      return SupabaseSubscriptionStatus._CANCELLED;\r\n    case RazorpaySubscriptionStatus._COMPLETED:\r\n      return SupabaseSubscriptionStatus._COMPLETED;\r\n    case RazorpaySubscriptionStatus._AUTHENTICATED:\r\n      return SupabaseSubscriptionStatus._AUTHENTICATED;\r\n    case RazorpaySubscriptionStatus._EXPIRED:\r\n      return SupabaseSubscriptionStatus._EXPIRED;\r\n    default:\r\n      // Default to pending for unknown statuses\r\n      console.warn(`Unknown Razorpay status: ${razorpayStatus}, defaulting to pending`);\r\n      return SupabaseSubscriptionStatus._PENDING;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,mCAAmC;;;;;;;;;AAC5B,IAAA,AAAK,uDAAA;IACV,sBAAsB;;;;;;;;;;IAWtB,iBAAiB;;;;IAKjB,iBAAiB;;IAGjB,gBAAgB;;;;WApBN;;AA2BL,IAAA,AAAK,oDAAA;;;;;;;;;WAAA;;AAYL,IAAA,AAAK,+CAAA;;;;;;WAAA;;AASL,IAAA,AAAK,8CAAA;;;;WAAA;;AAOL,IAAA,AAAK,oDAAA;;;;;;;;;WAAA;;AAgBL,SAAS,4BAA4B,cAAsB;IAChE,OAAQ;QACN;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE;QACF;YACE,0CAA0C;YAC1C,QAAQ,IAAI,CAAC,CAAC,yBAAyB,EAAE,eAAe,uBAAuB,CAAC;YAChF;IACJ;AACF", "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/subscription-db-updater.ts"], "sourcesContent": ["import { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { SupabaseSubscriptionStatus } from \"../types\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { SubscriptionStateManager } from \"./subscription-state-manager\";\r\n\r\n/**\r\n * CENTRALIZED SUBSCRIPTION UPDATE FUNCTION\r\n * \r\n * This function implements the single source of truth for subscription status handling.\r\n * All subscription updates must go through this function to ensure consistency.\r\n * \r\n * @param supabase The Supabase client (admin client will be used internally)\r\n * @param subscriptionId The Razorpay subscription ID\r\n * @param status The new subscription status\r\n * @param additionalData Additional data to update\r\n * @returns The result of the update operation\r\n */\r\nexport async function updateSubscription(\r\n  _supabase: SupabaseClient, // Original client not used, using admin client instead\r\n  subscriptionId: string,\r\n  status: SupabaseSubscriptionStatus,\r\n  additionalData: Record<string, unknown> = {}\r\n): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    // Get admin client to bypass RLS\r\n    const adminClient = createAdminClient();\r\n\r\n    // Get subscription details from Razorpay to find the business_profile_id and other details\r\n    const { getSubscription } = await import('@/lib/razorpay/services/subscription');\r\n    const subscriptionDetails = await getSubscription(subscriptionId);\r\n\r\n    if (!subscriptionDetails.success || !subscriptionDetails.data) {\r\n      console.error(`[RAZORPAY_WEBHOOK] Failed to get subscription details from Razorpay for ${subscriptionId}`);\r\n\r\n      // ENHANCED: Try to get business_profile_id from our database instead\r\n      const { data: localSubscription, error: localError } = await adminClient\r\n        .from('payment_subscriptions')\r\n        .select('business_profile_id, plan_id, plan_cycle')\r\n        .eq('razorpay_subscription_id', subscriptionId)\r\n        .maybeSingle();\r\n\r\n      if (localError || !localSubscription) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Also failed to get subscription from local database for ${subscriptionId}:`, localError);\r\n        return { success: false, message: `Failed to get subscription details from both Razorpay and local database` };\r\n      }\r\n\r\n      console.log(`[RAZORPAY_WEBHOOK] Using local subscription data for ${subscriptionId} since Razorpay API failed`);\r\n\r\n      // Create a mock subscription details object using local data\r\n      const mockSubscriptionDetails = {\r\n        success: true,\r\n        data: {\r\n          id: subscriptionId,\r\n          plan_id: `${localSubscription.plan_id}_${localSubscription.plan_cycle}`,\r\n          customer_id: null,\r\n          current_start: null,\r\n          current_end: null,\r\n          charge_at: null,\r\n          start_at: null,\r\n          notes: {\r\n            business_profile_id: localSubscription.business_profile_id,\r\n            plan_type: localSubscription.plan_id,\r\n            plan_cycle: localSubscription.plan_cycle\r\n          }\r\n        }\r\n      };\r\n\r\n      // Use the mock data for the rest of the function\r\n      const subscriptionDetailsToUse = mockSubscriptionDetails;\r\n      return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetailsToUse.data);\r\n    }\r\n\r\n    // Continue with normal processing using Razorpay data\r\n    return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetails.data);\r\n  } catch (error) {\r\n    console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Process subscription update with given subscription data\r\n * This function contains the main logic extracted from updateSubscription\r\n */\r\nasync function processSubscriptionUpdate(\r\n  adminClient: SupabaseClient,\r\n  subscriptionId: string,\r\n  status: SupabaseSubscriptionStatus,\r\n  additionalData: Record<string, unknown>,\r\n  subscriptionData: {\r\n    id: string;\r\n    plan_id: string;\r\n    customer_id: string | null;\r\n    current_start: number | null;\r\n    current_end: number | null;\r\n    charge_at: number | null;\r\n    start_at: number | null;\r\n    notes: {\r\n      business_profile_id?: string;\r\n      user_id?: string;\r\n      plan_type?: string;\r\n      plan_cycle?: string;\r\n    };\r\n  }\r\n): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    // Extract business_profile_id from notes\r\n    const businessProfileId = subscriptionData.notes?.business_profile_id ||\r\n                             subscriptionData.notes?.user_id;\r\n\r\n    if (!businessProfileId) {\r\n      console.error(`[RAZORPAY_WEBHOOK] No business_profile_id found in subscription notes for ${subscriptionId}`);\r\n      return { success: false, message: `No business_profile_id found in subscription notes` };\r\n    }\r\n\r\n    // Extract plan details from notes or plan_id\r\n    let planType = subscriptionData.notes?.plan_type;\r\n    let planCycle = subscriptionData.notes?.plan_cycle;\r\n\r\n    // If plan type and cycle are not in notes, try to determine from plan_id\r\n    if (!planType || !planCycle) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Plan type or cycle not found in notes, determining from plan_id: ${subscriptionData.plan_id}`);\r\n\r\n      // Use centralized plan configuration to map Razorpay plan ID to plan details\r\n      const { getPlanByRazorpayPlanId } = await import('@/lib/config/plans');\r\n      const planDetails = getPlanByRazorpayPlanId(subscriptionData.plan_id);\r\n\r\n      if (planDetails) {\r\n        planType = planDetails.id;\r\n        // Determine cycle by checking which Razorpay plan ID matches\r\n        planCycle = planDetails.razorpayPlanIds.monthly === subscriptionData.plan_id ? \"monthly\" : \"yearly\";\r\n        console.log(`[RAZORPAY_WEBHOOK] Determined plan type: ${planType}, cycle: ${planCycle} from plan_id using centralized config`);\r\n      } else {\r\n        // Default to basic monthly if we can't determine\r\n        planType = \"basic\";\r\n        planCycle = \"monthly\";\r\n        console.log(`[RAZORPAY_WEBHOOK] Could not determine plan type and cycle from plan_id: ${subscriptionData.plan_id}, defaulting to basic monthly`);\r\n      }\r\n    }\r\n\r\n    // CENTRALIZED LOGIC: Use SubscriptionStateManager to determine has_active_subscription\r\n    const hasActiveSubscription = SubscriptionStateManager.shouldHaveActiveSubscription(status, planType || 'free');\r\n\r\n    // Create a copy of additionalData to avoid modifying the original\r\n    const additionalDataCopy = { ...additionalData };\r\n\r\n    // Remove has_active_subscription from additionalData if it exists\r\n    // This ensures we always set it based on the status, not what's passed in\r\n    if ('has_active_subscription' in additionalDataCopy) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Removing has_active_subscription from additionalData for subscription with status ${status}`);\r\n      delete additionalDataCopy.has_active_subscription;\r\n    }\r\n\r\n    // Extract subscription dates from Razorpay subscription details\r\n    let subscriptionStartDate = subscriptionData.current_start\r\n      ? new Date(subscriptionData.current_start * 1000).toISOString()\r\n      : null;\r\n\r\n    const subscriptionExpiryTime = subscriptionData.current_end\r\n      ? new Date(subscriptionData.current_end * 1000).toISOString()\r\n      : null;\r\n\r\n    const subscriptionChargeTime = subscriptionData.charge_at\r\n      ? new Date(subscriptionData.charge_at * 1000).toISOString()\r\n      : null;\r\n\r\n    // Check if the status is authenticated or active\r\n    const isValidStatus = status === SupabaseSubscriptionStatus._AUTHENTICATED ||\r\n                         status === SupabaseSubscriptionStatus._ACTIVE;\r\n\r\n    if (!isValidStatus) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Skipping creation/update of subscription record for ${subscriptionId} with status ${status} - only handling authenticated or active statuses`);\r\n      return { success: true, message: `Skipped creation/update of subscription record with status ${status}` };\r\n    }\r\n\r\n    // For authenticated subscriptions, ensure we're setting the correct dates\r\n    // This is especially important for trial users where start_at is in the future\r\n    if (status === SupabaseSubscriptionStatus._AUTHENTICATED) {\r\n      console.log(`[RAZORPAY_WEBHOOK] Authenticated subscription detected, ensuring dates are set correctly`);\r\n\r\n      // For authenticated subscriptions, we need to check if start_at is set\r\n      // If it is, we should use that for subscription_start_date instead of current_start\r\n      if (subscriptionData.start_at) {\r\n        const startAt = new Date(subscriptionData.start_at * 1000).toISOString();\r\n        console.log(`[RAZORPAY_WEBHOOK] Using start_at (${startAt}) for subscription_start_date`);\r\n\r\n        // Override the subscription_start_date with start_at\r\n        subscriptionStartDate = startAt;\r\n      }\r\n    }\r\n\r\n    // Find existing subscription record\r\n    const { data: subscription, error: findError } = await adminClient\r\n      .from('payment_subscriptions')\r\n      .select('id, business_profile_id, razorpay_subscription_id')\r\n      .eq('razorpay_subscription_id', subscriptionId)\r\n      .maybeSingle();\r\n\r\n    if (findError) {\r\n      console.error(`[RAZORPAY_WEBHOOK] Error finding subscription ${subscriptionId}:`, findError);\r\n      return { success: false, message: `Error finding subscription: ${findError.message}` };\r\n    }\r\n\r\n    // If no subscription record exists for this Razorpay subscription ID, check if there's an existing record for the business\r\n    if (!subscription) {\r\n      console.log(`[RAZORPAY_WEBHOOK] No subscription found with ID ${subscriptionId}, checking for existing subscription for business ${businessProfileId}`);\r\n\r\n      // Check if there's an existing subscription for this business\r\n      const { data: existingSubscriptions, error: existingError } = await adminClient\r\n        .from('payment_subscriptions')\r\n        .select('id, razorpay_subscription_id, subscription_status')\r\n        .eq('business_profile_id', businessProfileId);\r\n\r\n      if (existingError) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Error checking for existing subscriptions for business ${businessProfileId}:`, existingError);\r\n        return { success: false, message: `Error checking for existing subscriptions: ${existingError.message}` };\r\n      }\r\n\r\n      // If there's an existing subscription for this business, update it instead of creating a new one\r\n      if (existingSubscriptions && existingSubscriptions.length > 0) {\r\n        console.log(`[RAZORPAY_WEBHOOK] Found existing subscription for business ${businessProfileId}, updating instead of creating new one`);\r\n\r\n        const existingSubscription = existingSubscriptions[0];\r\n\r\n        // Create the update data object\r\n        const updateData = {\r\n          razorpay_subscription_id: subscriptionId,\r\n          razorpay_customer_id: subscriptionData.customer_id || null,\r\n          subscription_status: status,\r\n          plan_id: planType,\r\n          plan_cycle: planCycle,\r\n          subscription_start_date: subscriptionStartDate,\r\n          subscription_expiry_time: subscriptionExpiryTime,\r\n          subscription_charge_time: subscriptionChargeTime,\r\n          updated_at: new Date().toISOString(),\r\n          ...additionalDataCopy\r\n        };\r\n\r\n        // Update the existing subscription record\r\n        const { error: updateError } = await adminClient\r\n          .from('payment_subscriptions')\r\n          .update(updateData)\r\n          .eq('id', existingSubscription.id);\r\n\r\n        if (updateError) {\r\n          console.error(`[RAZORPAY_WEBHOOK] Error updating existing subscription ${existingSubscription.id}:`, updateError);\r\n          return { success: false, message: `Error updating existing subscription: ${updateError.message}` };\r\n        }\r\n\r\n        console.log(`[RAZORPAY_WEBHOOK] Updated existing subscription ${existingSubscription.id} with new Razorpay ID ${subscriptionId} and status ${status}`);\r\n\r\n        // Use transaction utility to ensure consistency\r\n        const transactionResult = await updateSubscriptionWithBusinessProfile({\r\n          subscription_id: subscriptionId,\r\n          business_profile_id: businessProfileId,\r\n          subscription_status: status,\r\n          has_active_subscription: hasActiveSubscription,\r\n          additional_data: updateData\r\n        });\r\n\r\n        if (!transactionResult.success) {\r\n          console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);\r\n          return { success: false, message: `Transaction failed: ${transactionResult.message}` };\r\n        }\r\n\r\n        return { success: true, message: `Updated existing subscription with new Razorpay ID and status ${status}` };\r\n      }\r\n\r\n      // If no existing subscription for this business, create a new one\r\n      console.log(`[RAZORPAY_WEBHOOK] No existing subscription found for business ${businessProfileId}, creating new one`);\r\n\r\n      const insertData = {\r\n        business_profile_id: businessProfileId,\r\n        razorpay_subscription_id: subscriptionId,\r\n        razorpay_customer_id: subscriptionData.customer_id || null,\r\n        subscription_status: status,\r\n        plan_id: planType,\r\n        plan_cycle: planCycle,\r\n        subscription_start_date: subscriptionStartDate,\r\n        subscription_expiry_time: subscriptionExpiryTime,\r\n        subscription_charge_time: subscriptionChargeTime,\r\n        created_at: new Date().toISOString(),\r\n        updated_at: new Date().toISOString(),\r\n        ...additionalDataCopy\r\n      };\r\n\r\n      const { data: _newSubscription, error: insertError } = await adminClient\r\n        .from('payment_subscriptions')\r\n        .insert(insertData)\r\n        .select('id')\r\n        .single();\r\n\r\n      if (insertError) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Error creating subscription record for ${subscriptionId}:`, insertError);\r\n        return { success: false, message: `Error creating subscription record: ${insertError.message}` };\r\n      }\r\n\r\n      console.log(`[RAZORPAY_WEBHOOK] Created new subscription record for ${subscriptionId}`);\r\n\r\n      // Use transaction utility to ensure consistency\r\n      const transactionResult = await updateSubscriptionWithBusinessProfile({\r\n        subscription_id: subscriptionId,\r\n        business_profile_id: businessProfileId,\r\n        subscription_status: status,\r\n        has_active_subscription: hasActiveSubscription,\r\n        additional_data: insertData\r\n      });\r\n\r\n      if (!transactionResult.success) {\r\n        console.error(`[RAZORPAY_WEBHOOK] Transaction failed for new subscription ${subscriptionId}:`, transactionResult.message);\r\n        return { success: false, message: `Transaction failed: ${transactionResult.message}` };\r\n      }\r\n\r\n      console.log(`[RAZORPAY_WEBHOOK] Created subscription for ${subscriptionId} with status ${status}`);\r\n      return { success: true, message: `Created subscription with status ${status}` };\r\n    }\r\n\r\n    // If subscription exists, update it\r\n    const updateData = {\r\n      subscription_status: status,\r\n      subscription_start_date: subscriptionStartDate,\r\n      subscription_expiry_time: subscriptionExpiryTime,\r\n      subscription_charge_time: subscriptionChargeTime,\r\n      updated_at: new Date().toISOString(),\r\n      ...additionalDataCopy\r\n    };\r\n\r\n    // ENHANCED: Use atomic RPC function for transaction safety\r\n    const transactionResult = await updateSubscriptionWithBusinessProfile({\r\n      subscription_id: subscriptionId,\r\n      business_profile_id: businessProfileId,\r\n      subscription_status: status,\r\n      has_active_subscription: hasActiveSubscription,\r\n      additional_data: updateData\r\n    });\r\n\r\n    if (!transactionResult.success) {\r\n      console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);\r\n      return { success: false, message: `Transaction failed: ${transactionResult.message}` };\r\n    }\r\n\r\n    console.log(`[RAZORPAY_WEBHOOK] Updated subscription ${subscription.id} with status ${status}`);\r\n    return { success: true, message: `Updated subscription with status ${status}` };\r\n  } catch (error) {\r\n    console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * ATOMIC TRANSACTION UTILITY\r\n *\r\n * Ensures atomic updates between payment_subscriptions and business_profiles tables.\r\n * This prevents data inconsistencies that could cause users to lose access.\r\n */\r\ninterface TransactionParams {\r\n  subscription_id: string;\r\n  business_profile_id: string;\r\n  subscription_status: string;\r\n  has_active_subscription: boolean;\r\n  additional_data?: Record<string, unknown>;\r\n}\r\n\r\nexport async function updateSubscriptionWithBusinessProfile(\r\n  params: TransactionParams\r\n): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    const adminClient = createAdminClient();\r\n\r\n    console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${params.subscription_id}`);\r\n\r\n    // ENHANCED: Use atomic RPC function for true transaction safety\r\n    const { data: result, error } = await adminClient.rpc('update_subscription_atomic', {\r\n      p_subscription_id: params.subscription_id,\r\n      p_new_status: params.subscription_status,\r\n      p_business_profile_id: params.business_profile_id,\r\n      p_has_active_subscription: params.has_active_subscription,\r\n      p_additional_data: params.additional_data || {},\r\n      p_webhook_timestamp: params.additional_data?.last_webhook_timestamp || null\r\n    });\r\n\r\n    if (error) {\r\n      console.error(`[ATOMIC_TRANSACTION] RPC error for ${params.subscription_id}:`, error);\r\n      return {\r\n        success: false,\r\n        message: `RPC error: ${error.message}`\r\n      };\r\n    }\r\n\r\n    if (!result?.success) {\r\n      console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${params.subscription_id}:`, result);\r\n      return {\r\n        success: false,\r\n        message: result?.error || 'Unknown RPC error'\r\n      };\r\n    }\r\n\r\n    console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${params.subscription_id} atomically`);\r\n\r\n    return { success: true, message: 'Atomic transaction completed successfully via RPC' };\r\n  } catch (error) {\r\n    console.error(`[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:`, error);\r\n    return {\r\n      success: false,\r\n      message: `Atomic transaction exception: ${error instanceof Error ? error.message : String(error)}`\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;AAcO,eAAe,mBACpB,SAAyB,EACzB,cAAsB,EACtB,MAAkC,EAClC,iBAA0C,CAAC,CAAC;IAE5C,IAAI;QACF,iCAAiC;QACjC,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEpC,2FAA2F;QAC3F,MAAM,EAAE,eAAe,EAAE,GAAG;QAC5B,MAAM,sBAAsB,MAAM,gBAAgB;QAElD,IAAI,CAAC,oBAAoB,OAAO,IAAI,CAAC,oBAAoB,IAAI,EAAE;YAC7D,QAAQ,KAAK,CAAC,CAAC,wEAAwE,EAAE,gBAAgB;YAEzG,qEAAqE;YACrE,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,YAC1D,IAAI,CAAC,yBACL,MAAM,CAAC,4CACP,EAAE,CAAC,4BAA4B,gBAC/B,WAAW;YAEd,IAAI,cAAc,CAAC,mBAAmB;gBACpC,QAAQ,KAAK,CAAC,CAAC,2EAA2E,EAAE,eAAe,CAAC,CAAC,EAAE;gBAC/G,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,wEAAwE,CAAC;gBAAC;YAC/G;YAEA,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,eAAe,0BAA0B,CAAC;YAE9G,6DAA6D;YAC7D,MAAM,0BAA0B;gBAC9B,SAAS;gBACT,MAAM;oBACJ,IAAI;oBACJ,SAAS,GAAG,kBAAkB,OAAO,CAAC,CAAC,EAAE,kBAAkB,UAAU,EAAE;oBACvE,aAAa;oBACb,eAAe;oBACf,aAAa;oBACb,WAAW;oBACX,UAAU;oBACV,OAAO;wBACL,qBAAqB,kBAAkB,mBAAmB;wBAC1D,WAAW,kBAAkB,OAAO;wBACpC,YAAY,kBAAkB,UAAU;oBAC1C;gBACF;YACF;YAEA,iDAAiD;YACjD,MAAM,2BAA2B;YACjC,OAAO,MAAM,0BAA0B,aAAa,gBAAgB,QAAQ,gBAAgB,yBAAyB,IAAI;QAC3H;QAEA,sDAAsD;QACtD,OAAO,MAAM,0BAA0B,aAAa,gBAAgB,QAAQ,gBAAgB,oBAAoB,IAAI;IACtH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mDAAmD,EAAE,eAAe,CAAC,CAAC,EAAE;QACvF,OAAO;YACL,SAAS;YACT,SAAS,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;QACvG;IACF;AACF;AAEA;;;CAGC,GACD,eAAe,0BACb,WAA2B,EAC3B,cAAsB,EACtB,MAAkC,EAClC,cAAuC,EACvC,gBAcC;IAED,IAAI;QACF,yCAAyC;QACzC,MAAM,oBAAoB,iBAAiB,KAAK,EAAE,uBACzB,iBAAiB,KAAK,EAAE;QAEjD,IAAI,CAAC,mBAAmB;YACtB,QAAQ,KAAK,CAAC,CAAC,0EAA0E,EAAE,gBAAgB;YAC3G,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,kDAAkD,CAAC;YAAC;QACzF;QAEA,6CAA6C;QAC7C,IAAI,WAAW,iBAAiB,KAAK,EAAE;QACvC,IAAI,YAAY,iBAAiB,KAAK,EAAE;QAExC,yEAAyE;QACzE,IAAI,CAAC,YAAY,CAAC,WAAW;YAC3B,QAAQ,GAAG,CAAC,CAAC,oFAAoF,EAAE,iBAAiB,OAAO,EAAE;YAE7H,6EAA6E;YAC7E,MAAM,EAAE,uBAAuB,EAAE,GAAG;YACpC,MAAM,cAAc,wBAAwB,iBAAiB,OAAO;YAEpE,IAAI,aAAa;gBACf,WAAW,YAAY,EAAE;gBACzB,6DAA6D;gBAC7D,YAAY,YAAY,eAAe,CAAC,OAAO,KAAK,iBAAiB,OAAO,GAAG,YAAY;gBAC3F,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,SAAS,SAAS,EAAE,UAAU,sCAAsC,CAAC;YAC/H,OAAO;gBACL,iDAAiD;gBACjD,WAAW;gBACX,YAAY;gBACZ,QAAQ,GAAG,CAAC,CAAC,yEAAyE,EAAE,iBAAiB,OAAO,CAAC,6BAA6B,CAAC;YACjJ;QACF;QAEA,uFAAuF;QACvF,MAAM,wBAAwB,2KAAA,CAAA,2BAAwB,CAAC,4BAA4B,CAAC,QAAQ,YAAY;QAExG,kEAAkE;QAClE,MAAM,qBAAqB;YAAE,GAAG,cAAc;QAAC;QAE/C,kEAAkE;QAClE,0EAA0E;QAC1E,IAAI,6BAA6B,oBAAoB;YACnD,QAAQ,GAAG,CAAC,CAAC,qGAAqG,EAAE,QAAQ;YAC5H,OAAO,mBAAmB,uBAAuB;QACnD;QAEA,gEAAgE;QAChE,IAAI,wBAAwB,iBAAiB,aAAa,GACtD,IAAI,KAAK,iBAAiB,aAAa,GAAG,MAAM,WAAW,KAC3D;QAEJ,MAAM,yBAAyB,iBAAiB,WAAW,GACvD,IAAI,KAAK,iBAAiB,WAAW,GAAG,MAAM,WAAW,KACzD;QAEJ,MAAM,yBAAyB,iBAAiB,SAAS,GACrD,IAAI,KAAK,iBAAiB,SAAS,GAAG,MAAM,WAAW,KACvD;QAEJ,iDAAiD;QACjD,MAAM,gBAAgB,WAAW,oIAAA,CAAA,6BAA0B,CAAC,cAAc,IACrD,WAAW,oIAAA,CAAA,6BAA0B,CAAC,OAAO;QAElE,IAAI,CAAC,eAAe;YAClB,QAAQ,GAAG,CAAC,CAAC,uEAAuE,EAAE,eAAe,aAAa,EAAE,OAAO,iDAAiD,CAAC;YAC7K,OAAO;gBAAE,SAAS;gBAAM,SAAS,CAAC,2DAA2D,EAAE,QAAQ;YAAC;QAC1G;QAEA,0EAA0E;QAC1E,+EAA+E;QAC/E,IAAI,WAAW,oIAAA,CAAA,6BAA0B,CAAC,cAAc,EAAE;YACxD,QAAQ,GAAG,CAAC,CAAC,wFAAwF,CAAC;YAEtG,uEAAuE;YACvE,oFAAoF;YACpF,IAAI,iBAAiB,QAAQ,EAAE;gBAC7B,MAAM,UAAU,IAAI,KAAK,iBAAiB,QAAQ,GAAG,MAAM,WAAW;gBACtE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,QAAQ,6BAA6B,CAAC;gBAExF,qDAAqD;gBACrD,wBAAwB;YAC1B;QACF;QAEA,oCAAoC;QACpC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,YACpD,IAAI,CAAC,yBACL,MAAM,CAAC,qDACP,EAAE,CAAC,4BAA4B,gBAC/B,WAAW;QAEd,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,CAAC,8CAA8C,EAAE,eAAe,CAAC,CAAC,EAAE;YAClF,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,4BAA4B,EAAE,UAAU,OAAO,EAAE;YAAC;QACvF;QAEA,2HAA2H;QAC3H,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,eAAe,kDAAkD,EAAE,mBAAmB;YAEtJ,8DAA8D;YAC9D,MAAM,EAAE,MAAM,qBAAqB,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,YACjE,IAAI,CAAC,yBACL,MAAM,CAAC,qDACP,EAAE,CAAC,uBAAuB;YAE7B,IAAI,eAAe;gBACjB,QAAQ,KAAK,CAAC,CAAC,0EAA0E,EAAE,kBAAkB,CAAC,CAAC,EAAE;gBACjH,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,2CAA2C,EAAE,cAAc,OAAO,EAAE;gBAAC;YAC1G;YAEA,iGAAiG;YACjG,IAAI,yBAAyB,sBAAsB,MAAM,GAAG,GAAG;gBAC7D,QAAQ,GAAG,CAAC,CAAC,4DAA4D,EAAE,kBAAkB,sCAAsC,CAAC;gBAEpI,MAAM,uBAAuB,qBAAqB,CAAC,EAAE;gBAErD,gCAAgC;gBAChC,MAAM,aAAa;oBACjB,0BAA0B;oBAC1B,sBAAsB,iBAAiB,WAAW,IAAI;oBACtD,qBAAqB;oBACrB,SAAS;oBACT,YAAY;oBACZ,yBAAyB;oBACzB,0BAA0B;oBAC1B,0BAA0B;oBAC1B,YAAY,IAAI,OAAO,WAAW;oBAClC,GAAG,kBAAkB;gBACvB;gBAEA,0CAA0C;gBAC1C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,YAClC,IAAI,CAAC,yBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,qBAAqB,EAAE;gBAEnC,IAAI,aAAa;oBACf,QAAQ,KAAK,CAAC,CAAC,wDAAwD,EAAE,qBAAqB,EAAE,CAAC,CAAC,CAAC,EAAE;oBACrG,OAAO;wBAAE,SAAS;wBAAO,SAAS,CAAC,sCAAsC,EAAE,YAAY,OAAO,EAAE;oBAAC;gBACnG;gBAEA,QAAQ,GAAG,CAAC,CAAC,iDAAiD,EAAE,qBAAqB,EAAE,CAAC,sBAAsB,EAAE,eAAe,YAAY,EAAE,QAAQ;gBAErJ,gDAAgD;gBAChD,MAAM,oBAAoB,MAAM,sCAAsC;oBACpE,iBAAiB;oBACjB,qBAAqB;oBACrB,qBAAqB;oBACrB,yBAAyB;oBACzB,iBAAiB;gBACnB;gBAEA,IAAI,CAAC,kBAAkB,OAAO,EAAE;oBAC9B,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,eAAe,CAAC,CAAC,EAAE,kBAAkB,OAAO;oBACpH,OAAO;wBAAE,SAAS;wBAAO,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,OAAO,EAAE;oBAAC;gBACvF;gBAEA,OAAO;oBAAE,SAAS;oBAAM,SAAS,CAAC,8DAA8D,EAAE,QAAQ;gBAAC;YAC7G;YAEA,kEAAkE;YAClE,QAAQ,GAAG,CAAC,CAAC,+DAA+D,EAAE,kBAAkB,kBAAkB,CAAC;YAEnH,MAAM,aAAa;gBACjB,qBAAqB;gBACrB,0BAA0B;gBAC1B,sBAAsB,iBAAiB,WAAW,IAAI;gBACtD,qBAAqB;gBACrB,SAAS;gBACT,YAAY;gBACZ,yBAAyB;gBACzB,0BAA0B;gBAC1B,0BAA0B;gBAC1B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;gBAClC,GAAG,kBAAkB;YACvB;YAEA,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,YAC1D,IAAI,CAAC,yBACL,MAAM,CAAC,YACP,MAAM,CAAC,MACP,MAAM;YAET,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,eAAe,CAAC,CAAC,EAAE;gBAC9F,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,oCAAoC,EAAE,YAAY,OAAO,EAAE;gBAAC;YACjG;YAEA,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,gBAAgB;YAEtF,gDAAgD;YAChD,MAAM,oBAAoB,MAAM,sCAAsC;gBACpE,iBAAiB;gBACjB,qBAAqB;gBACrB,qBAAqB;gBACrB,yBAAyB;gBACzB,iBAAiB;YACnB;YAEA,IAAI,CAAC,kBAAkB,OAAO,EAAE;gBAC9B,QAAQ,KAAK,CAAC,CAAC,2DAA2D,EAAE,eAAe,CAAC,CAAC,EAAE,kBAAkB,OAAO;gBACxH,OAAO;oBAAE,SAAS;oBAAO,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,OAAO,EAAE;gBAAC;YACvF;YAEA,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,eAAe,aAAa,EAAE,QAAQ;YACjG,OAAO;gBAAE,SAAS;gBAAM,SAAS,CAAC,iCAAiC,EAAE,QAAQ;YAAC;QAChF;QAEA,oCAAoC;QACpC,MAAM,aAAa;YACjB,qBAAqB;YACrB,yBAAyB;YACzB,0BAA0B;YAC1B,0BAA0B;YAC1B,YAAY,IAAI,OAAO,WAAW;YAClC,GAAG,kBAAkB;QACvB;QAEA,2DAA2D;QAC3D,MAAM,oBAAoB,MAAM,sCAAsC;YACpE,iBAAiB;YACjB,qBAAqB;YACrB,qBAAqB;YACrB,yBAAyB;YACzB,iBAAiB;QACnB;QAEA,IAAI,CAAC,kBAAkB,OAAO,EAAE;YAC9B,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,eAAe,CAAC,CAAC,EAAE,kBAAkB,OAAO;YACpH,OAAO;gBAAE,SAAS;gBAAO,SAAS,CAAC,oBAAoB,EAAE,kBAAkB,OAAO,EAAE;YAAC;QACvF;QAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,aAAa,EAAE,CAAC,aAAa,EAAE,QAAQ;QAC9F,OAAO;YAAE,SAAS;YAAM,SAAS,CAAC,iCAAiC,EAAE,QAAQ;QAAC;IAChF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mDAAmD,EAAE,eAAe,CAAC,CAAC,EAAE;QACvF,OAAO;YACL,SAAS;YACT,SAAS,CAAC,iCAAiC,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;QACvG;IACF;AACF;AAgBO,eAAe,sCACpB,MAAyB;IAEzB,IAAI;QACF,MAAM,cAAc,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEpC,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,OAAO,eAAe,EAAE;QAE9F,gEAAgE;QAChE,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,YAAY,GAAG,CAAC,8BAA8B;YAClF,mBAAmB,OAAO,eAAe;YACzC,cAAc,OAAO,mBAAmB;YACxC,uBAAuB,OAAO,mBAAmB;YACjD,2BAA2B,OAAO,uBAAuB;YACzD,mBAAmB,OAAO,eAAe,IAAI,CAAC;YAC9C,qBAAqB,OAAO,eAAe,EAAE,0BAA0B;QACzE;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,eAAe,CAAC,CAAC,CAAC,EAAE;YAC/E,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,WAAW,EAAE,MAAM,OAAO,EAAE;YACxC;QACF;QAEA,IAAI,CAAC,QAAQ,SAAS;YACpB,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,OAAO,eAAe,CAAC,CAAC,CAAC,EAAE;YACjG,OAAO;gBACL,SAAS;gBACT,SAAS,QAAQ,SAAS;YAC5B;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,uDAAuD,EAAE,OAAO,eAAe,CAAC,WAAW,CAAC;QAEzG,OAAO;YAAE,SAAS;YAAM,SAAS;QAAoD;IACvF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,wEAAwE,CAAC,EAAE;QAC1F,OAAO;YACL,SAAS;YACT,SAAS,CAAC,8BAA8B,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAAQ;QACpG;IACF;AACF", "debugId": null}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/webhook-utils.ts"], "sourcesContent": ["import { SubscriptionStatus, SUBSCRIPTION_STATUS } from \"./subscription-constants\";\r\n\r\n/**\r\n * ENHANCED WEBHOOK TIMESTAMP EXTRACTION\r\n *\r\n * Extracts webhook timestamp from Razorpay payload for sequence validation.\r\n * This function is critical for preventing out-of-order webhook processing.\r\n *\r\n * @param payload The webhook payload from Razorpay\r\n * @returns Unix timestamp in seconds, or current time if not available\r\n */\r\nexport function extractWebhookTimestamp(payload: any): number { // eslint-disable-line @typescript-eslint/no-explicit-any\r\n  try {\r\n    // Priority order for timestamp extraction (most reliable first)\r\n    const timestampSources = [\r\n      // 1. Main event timestamp (most reliable)\r\n      () => payload.created_at,\r\n\r\n      // 2. Subscription entity timestamps\r\n      () => payload.payload?.subscription?.entity?.created_at,\r\n      () => payload.payload?.subscription?.entity?.updated_at,\r\n\r\n      // 3. Payment entity timestamps\r\n      () => payload.payload?.payment?.entity?.created_at,\r\n      () => payload.payload?.payment?.entity?.updated_at,\r\n\r\n      // 4. Invoice entity timestamps\r\n      () => payload.payload?.invoice?.entity?.created_at,\r\n      () => payload.payload?.invoice?.entity?.updated_at,\r\n\r\n      // 5. Generic entity timestamps\r\n      () => payload.payload?.entity?.created_at,\r\n      () => payload.payload?.entity?.updated_at,\r\n\r\n      // 6. Event-specific timestamps\r\n      () => payload.event_timestamp,\r\n      () => payload.timestamp\r\n    ];\r\n\r\n    // Try each timestamp source in order\r\n    for (const getTimestamp of timestampSources) {\r\n      try {\r\n        const timestamp = getTimestamp();\r\n        if (timestamp && typeof timestamp === 'number' && timestamp > 0) {\r\n          // Validate timestamp is reasonable (not too far in past/future)\r\n          const now = Math.floor(Date.now() / 1000);\r\n          const maxAge = 24 * 60 * 60; // 24 hours\r\n          const maxFuture = 5 * 60; // 5 minutes\r\n\r\n          if (timestamp >= (now - maxAge) && timestamp <= (now + maxFuture)) {\r\n            return timestamp;\r\n          } else {\r\n            console.warn(`[WEBHOOK_TIMESTAMP] Timestamp ${timestamp} outside reasonable range, trying next source`);\r\n          }\r\n        }\r\n      } catch (_sourceError) {\r\n        // Continue to next source if this one fails\r\n        continue;\r\n      }\r\n    }\r\n\r\n    // Fallback to current time with warning\r\n    console.warn('[WEBHOOK_TIMESTAMP] Could not extract valid timestamp from payload, using current time');\r\n    console.warn('[WEBHOOK_TIMESTAMP] Payload structure:', JSON.stringify(payload, null, 2));\r\n    return Math.floor(Date.now() / 1000);\r\n\r\n  } catch (error) {\r\n    console.error('[WEBHOOK_TIMESTAMP] Error extracting timestamp from payload:', error);\r\n    return Math.floor(Date.now() / 1000);\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the appropriate subscription status for a user based on their current state.\r\n * This is used to determine the correct status when creating or updating subscriptions.\r\n *\r\n * @param hasActiveSubscription Current has_active_subscription flag (should be false for trial/free)\r\n * @param trialEndDate Trial end date if any\r\n * @param planId Current plan ID\r\n * @returns The appropriate subscription status\r\n */\r\nexport function getAppropriateSubscriptionStatus(\r\n  hasActiveSubscription: boolean,\r\n  trialEndDate: string | null,\r\n  planId: string\r\n): SubscriptionStatus {\r\n  // If on free plan, always active status but has_active_subscription should be false\r\n  if (planId === 'free') {\r\n    return SUBSCRIPTION_STATUS.ACTIVE;\r\n  }\r\n\r\n  // Check if user is in trial period\r\n  if (trialEndDate) {\r\n    const trialEnd = new Date(trialEndDate);\r\n    const now = new Date();\r\n\r\n    if (trialEnd > now) {\r\n      // User is in trial period - status is trial, has_active_subscription should be false\r\n      return SUBSCRIPTION_STATUS.TRIAL;\r\n    }\r\n  }\r\n\r\n  // If has active subscription flag, it means they have a paid subscription\r\n  if (hasActiveSubscription) {\r\n    return SUBSCRIPTION_STATUS.ACTIVE;\r\n  }\r\n\r\n  // Default to pending if no active subscription and not in trial\r\n  return SUBSCRIPTION_STATUS.PENDING;\r\n}"], "names": [], "mappings": ";;;;AAAA;;AAWO,SAAS,wBAAwB,OAAY;IAClD,IAAI;QACF,gEAAgE;QAChE,MAAM,mBAAmB;YACvB,0CAA0C;YAC1C,IAAM,QAAQ,UAAU;YAExB,oCAAoC;YACpC,IAAM,QAAQ,OAAO,EAAE,cAAc,QAAQ;YAC7C,IAAM,QAAQ,OAAO,EAAE,cAAc,QAAQ;YAE7C,+BAA+B;YAC/B,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YACxC,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YAExC,+BAA+B;YAC/B,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YACxC,IAAM,QAAQ,OAAO,EAAE,SAAS,QAAQ;YAExC,+BAA+B;YAC/B,IAAM,QAAQ,OAAO,EAAE,QAAQ;YAC/B,IAAM,QAAQ,OAAO,EAAE,QAAQ;YAE/B,+BAA+B;YAC/B,IAAM,QAAQ,eAAe;YAC7B,IAAM,QAAQ,SAAS;SACxB;QAED,qCAAqC;QACrC,KAAK,MAAM,gBAAgB,iBAAkB;YAC3C,IAAI;gBACF,MAAM,YAAY;gBAClB,IAAI,aAAa,OAAO,cAAc,YAAY,YAAY,GAAG;oBAC/D,gEAAgE;oBAChE,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;oBACpC,MAAM,SAAS,KAAK,KAAK,IAAI,WAAW;oBACxC,MAAM,YAAY,IAAI,IAAI,YAAY;oBAEtC,IAAI,aAAc,MAAM,UAAW,aAAc,MAAM,WAAY;wBACjE,OAAO;oBACT,OAAO;wBACL,QAAQ,IAAI,CAAC,CAAC,8BAA8B,EAAE,UAAU,6CAA6C,CAAC;oBACxG;gBACF;YACF,EAAE,OAAO,cAAc;gBAErB;YACF;QACF;QAEA,wCAAwC;QACxC,QAAQ,IAAI,CAAC;QACb,QAAQ,IAAI,CAAC,0CAA0C,KAAK,SAAS,CAAC,SAAS,MAAM;QACrF,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IAEjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gEAAgE;QAC9E,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACjC;AACF;AAWO,SAAS,iCACd,qBAA8B,EAC9B,YAA2B,EAC3B,MAAc;IAEd,oFAAoF;IACpF,IAAI,WAAW,QAAQ;QACrB,OAAO,oKAAA,CAAA,sBAAmB,CAAC,MAAM;IACnC;IAEA,mCAAmC;IACnC,IAAI,cAAc;QAChB,MAAM,WAAW,IAAI,KAAK;QAC1B,MAAM,MAAM,IAAI;QAEhB,IAAI,WAAW,KAAK;YAClB,qFAAqF;YACrF,OAAO,oKAAA,CAAA,sBAAmB,CAAC,KAAK;QAClC;IACF;IAEA,0EAA0E;IAC1E,IAAI,uBAAuB;QACzB,OAAO,oKAAA,CAAA,sBAAmB,CAAC,MAAM;IACnC;IAEA,gEAAgE;IAChE,OAAO,oKAAA,CAAA,sBAAmB,CAAC,OAAO;AACpC", "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/razorpay/webhooks/handlers/utils.ts"], "sourcesContent": ["export * from './subscription-constants';\r\nexport * from './subscription-state-manager';\r\nexport * from './subscription-state-validator';\r\nexport * from './subscription-db-updater';\r\nexport * from './webhook-utils';"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}]}