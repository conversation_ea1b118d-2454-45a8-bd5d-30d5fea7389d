"use client";

import { motion } from "framer-motion";
import { LucideIcon } from "lucide-react";

interface AnimatedMetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description: string;
  color: "rose" | "blue" | "amber" | "red" | "yellow";
  isUpdated?: boolean;
}

export default function AnimatedMetricCard({
  title,
  value,
  icon: Icon,
  description,
  color,
  isUpdated = false,
}: AnimatedMetricCardProps) {
  // Simple, clean color variants without glow effects
  const iconColors = {
    rose: "text-rose-600 dark:text-rose-400",
    blue: "text-blue-600 dark:text-blue-400",
    amber: "text-amber-600 dark:text-amber-400",
    red: "text-red-600 dark:text-red-400",
    yellow: "text-yellow-600 dark:text-yellow-400",
  };

  const iconColor = iconColors[color];

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 24,
      },
    },
  };

  const counterVariants = {
    initial: { scale: 1 },
    update: {
      scale: 1.05,
      transition: { duration: 0.3 },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      className="group relative overflow-hidden rounded-xl p-6 bg-white dark:bg-black border border-border hover:border-border/80 shadow-sm hover:shadow-md transition-all duration-300"
    >
      {/* Content */}
      <div className="flex flex-col items-center text-center space-y-4">
        {/* Icon */}
        <div className="p-3 rounded-xl bg-muted">
          <Icon className={`w-6 h-6 ${iconColor}`} />
        </div>

        {/* Value */}
        <div className="space-y-1">
          <motion.div
            className="text-2xl font-bold text-foreground"
            variants={counterVariants}
            initial="initial"
            animate={isUpdated ? "update" : "initial"}
          >
            {value}
          </motion.div>
          <div className="text-sm font-medium text-muted-foreground">
            {title}
          </div>
        </div>

        {/* Description */}
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      </div>
    </motion.div>
  );
}
