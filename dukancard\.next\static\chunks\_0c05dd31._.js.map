{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/EnhancedMetricCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { LucideIcon } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface EnhancedMetricCardProps {\r\n  title: string;\r\n  value: string | number;\r\n  icon: LucideIcon;\r\n  description: string;\r\n  color: \"rose\" | \"blue\" | \"amber\" | \"green\" | \"purple\" | \"indigo\";\r\n  isUpdated?: boolean;\r\n  suffix?: string;\r\n  trend?: {\r\n    value: number;\r\n    isPositive: boolean;\r\n  };\r\n}\r\n\r\nexport default function EnhancedMetricCard({\r\n  title,\r\n  value,\r\n  icon: Icon,\r\n  description,\r\n  color,\r\n  isUpdated = false,\r\n  suffix,\r\n  trend,\r\n}: EnhancedMetricCardProps) {\r\n  // Define color variants\r\n  const colorVariants = {\r\n    rose: {\r\n      bgLight: \"bg-rose-100\",\r\n      bgDark: \"dark:bg-rose-900/30\",\r\n      textLight: \"text-rose-600\",\r\n      textDark: \"dark:text-rose-400\",\r\n      borderHover: \"group-hover:border-rose-300 dark:group-hover:border-rose-700\",\r\n      glowColor: \"rgba(244, 63, 94, 0.4)\",\r\n    },\r\n    blue: {\r\n      bgLight: \"bg-blue-100\",\r\n      bgDark: \"dark:bg-blue-900/30\",\r\n      textLight: \"text-blue-600\",\r\n      textDark: \"dark:text-blue-400\",\r\n      borderHover: \"group-hover:border-blue-300 dark:group-hover:border-blue-700\",\r\n      glowColor: \"rgba(59, 130, 246, 0.4)\",\r\n    },\r\n    amber: {\r\n      bgLight: \"bg-amber-100\",\r\n      bgDark: \"dark:bg-amber-900/30\",\r\n      textLight: \"text-amber-600\",\r\n      textDark: \"dark:text-amber-400\",\r\n      borderHover: \"group-hover:border-amber-300 dark:group-hover:border-amber-700\",\r\n      glowColor: \"rgba(245, 158, 11, 0.4)\",\r\n    },\r\n    green: {\r\n      bgLight: \"bg-green-100\",\r\n      bgDark: \"dark:bg-green-900/30\",\r\n      textLight: \"text-green-600\",\r\n      textDark: \"dark:text-green-400\",\r\n      borderHover: \"group-hover:border-green-300 dark:group-hover:border-green-700\",\r\n      glowColor: \"rgba(34, 197, 94, 0.4)\",\r\n    },\r\n    purple: {\r\n      bgLight: \"bg-purple-100\",\r\n      bgDark: \"dark:bg-purple-900/30\",\r\n      textLight: \"text-purple-600\",\r\n      textDark: \"dark:text-purple-400\",\r\n      borderHover: \"group-hover:border-purple-300 dark:group-hover:border-purple-700\",\r\n      glowColor: \"rgba(168, 85, 247, 0.4)\",\r\n    },\r\n    indigo: {\r\n      bgLight: \"bg-indigo-100\",\r\n      bgDark: \"dark:bg-indigo-900/30\",\r\n      textLight: \"text-indigo-600\",\r\n      textDark: \"dark:text-indigo-400\",\r\n      borderHover: \"group-hover:border-indigo-300 dark:group-hover:border-indigo-700\",\r\n      glowColor: \"rgba(99, 102, 241, 0.4)\",\r\n    },\r\n  };\r\n\r\n  const selectedColor = colorVariants[color];\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 300,\r\n        damping: 24,\r\n      },\r\n    },\r\n  };\r\n\r\n  const counterVariants = {\r\n    initial: { scale: 1 },\r\n    update: {\r\n      scale: [1, 1.1, 1],\r\n      transition: { duration: 0.3 },\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n    >\r\n      <div\r\n        className={cn(\r\n          \"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black shadow-md p-3 sm:p-4 transition-all duration-300 hover:shadow-lg overflow-hidden group\",\r\n          selectedColor.borderHover\r\n        )}\r\n      >\r\n      <div className=\"flex items-center gap-2 mb-2\">\r\n        <div className={cn(\"p-1.5 sm:p-2 rounded-lg self-start\", selectedColor.bgLight, selectedColor.bgDark, selectedColor.textLight, selectedColor.textDark)}>\r\n          <Icon className=\"w-3.5 h-3.5 sm:w-4 sm:h-4\" />\r\n        </div>\r\n        <h3 className=\"text-sm sm:text-base font-semibold text-neutral-800 dark:text-neutral-100 truncate\">\r\n          {title}\r\n        </h3>\r\n      </div>\r\n      <div className=\"px-1 pb-2\">\r\n        <div className=\"flex items-end gap-1\">\r\n          <motion.div\r\n            variants={counterVariants}\r\n            initial=\"initial\"\r\n            animate={isUpdated ? \"update\" : \"initial\"}\r\n          >\r\n            <div className=\"text-xl sm:text-2xl font-bold\">\r\n              {/* Add glow effect to the value */}\r\n              <span className=\"relative\">\r\n                <span className=\"relative z-10\">{value}</span>\r\n                {suffix && <span className=\"text-sm text-muted-foreground ml-1\">{suffix}</span>}\r\n                <div\r\n                  className=\"absolute inset-0 blur-sm opacity-50 z-0\"\r\n                  style={{ backgroundColor: selectedColor.glowColor }}\r\n                />\r\n              </span>\r\n            </div>\r\n          </motion.div>\r\n\r\n          {/* Trend indicator */}\r\n          {trend && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 5 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: 0.2 }}\r\n            >\r\n              <div\r\n                className={cn(\r\n                  \"text-xs font-medium mb-1 flex items-center\",\r\n                  trend.isPositive ? \"text-green-500 dark:text-green-400\" : \"text-rose-500 dark:text-rose-400\"\r\n                )}\r\n              >\r\n                {trend.isPositive ? \"↑\" : \"↓\"} {Math.abs(trend.value)}%\r\n              </div>\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n        <p className=\"text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 mt-1\">\r\n          {description}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Animated border effect */}\r\n      <div\r\n        className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r\"\r\n        style={{\r\n          backgroundImage: `linear-gradient(to right, transparent, ${selectedColor.glowColor}, transparent)`,\r\n          width: \"100%\",\r\n        }}\r\n      />\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAoBe,SAAS,mBAAmB,EACzC,KAAK,EACL,KAAK,EACL,MAAM,IAAI,EACV,WAAW,EACX,KAAK,EACL,YAAY,KAAK,EACjB,MAAM,EACN,KAAK,EACmB;IACxB,wBAAwB;IACxB,MAAM,gBAAgB;QACpB,MAAM;YACJ,SAAS;YACT,QAAQ;YACR,WAAW;YACX,UAAU;YACV,aAAa;YACb,WAAW;QACb;QACA,MAAM;YACJ,SAAS;YACT,QAAQ;YACR,WAAW;YACX,UAAU;YACV,aAAa;YACb,WAAW;QACb;QACA,OAAO;YACL,SAAS;YACT,QAAQ;YACR,WAAW;YACX,UAAU;YACV,aAAa;YACb,WAAW;QACb;QACA,OAAO;YACL,SAAS;YACT,QAAQ;YACR,WAAW;YACX,UAAU;YACV,aAAa;YACb,WAAW;QACb;QACA,QAAQ;YACN,SAAS;YACT,QAAQ;YACR,WAAW;YACX,UAAU;YACV,aAAa;YACb,WAAW;QACb;QACA,QAAQ;YACN,SAAS;YACT,QAAQ;YACR,WAAW;YACX,UAAU;YACV,aAAa;YACb,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,aAAa,CAAC,MAAM;IAE1C,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,SAAS;YAAE,OAAO;QAAE;QACpB,QAAQ;YACN,OAAO;gBAAC;gBAAG;gBAAK;aAAE;YAClB,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;kBAEV,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8KACA,cAAc,WAAW;;8BAG7B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC,cAAc,OAAO,EAAE,cAAc,MAAM,EAAE,cAAc,SAAS,EAAE,cAAc,QAAQ;sCACnJ,cAAA,6LAAC;gCAAK,WAAU;;;;;;;;;;;sCAElB,6LAAC;4BAAG,WAAU;sCACX;;;;;;;;;;;;8BAGL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAU;oCACV,SAAQ;oCACR,SAAS,YAAY,WAAW;8CAEhC,cAAA,6LAAC;wCAAI,WAAU;kDAEb,cAAA,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;gDAChC,wBAAU,6LAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACjE,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB,cAAc,SAAS;oDAAC;;;;;;;;;;;;;;;;;;;;;;gCAOzD,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAEzB,cAAA,6LAAC;wCACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8CACA,MAAM,UAAU,GAAG,uCAAuC;;4CAG3D,MAAM,UAAU,GAAG,MAAM;4CAAI;4CAAE,KAAK,GAAG,CAAC,MAAM,KAAK;4CAAE;;;;;;;;;;;;;;;;;;sCAK9D,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKL,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC,uCAAuC,EAAE,cAAc,SAAS,CAAC,cAAc,CAAC;wBAClG,OAAO;oBACT;;;;;;;;;;;;;;;;;AAKR;KA9JwB", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/EnhancedEngagementMetricsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Heart, Star, UserPlus } from \"lucide-react\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport EnhancedMetricCard from \"./EnhancedMetricCard\";\r\n\r\ninterface BusinessProfile {\r\n  total_likes: number;\r\n  total_subscriptions: number;\r\n  average_rating: number;\r\n}\r\n\r\ninterface EngagementMetricsSectionProps {\r\n  profile: BusinessProfile;\r\n  initialProfile: BusinessProfile;\r\n}\r\n\r\nexport default function EnhancedEngagementMetricsSection({\r\n  profile,\r\n  initialProfile,\r\n}: EngagementMetricsSectionProps) {\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const headerVariants = {\r\n    hidden: { opacity: 0, y: -20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 300,\r\n        damping: 24,\r\n      }\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n    >\r\n      <div className=\"space-y-4\">\r\n      {/* Section Header */}\r\n      <motion.div\r\n        variants={headerVariants}\r\n      >\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-rose-100 dark:bg-rose-900/30 text-rose-600 dark:text-rose-400 self-start\">\r\n          <Heart className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Engagement Metrics\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            How users are interacting with your business card\r\n          </p>\r\n        </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Metrics Cards */}\r\n      <div className=\"grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3\">\r\n        {/* Total Likes */}\r\n        <EnhancedMetricCard\r\n          title=\"Total Likes\"\r\n          value={formatIndianNumberShort(profile.total_likes)}\r\n          icon={Heart}\r\n          description=\"People who liked your card\"\r\n          color=\"rose\"\r\n          isUpdated={profile.total_likes !== initialProfile.total_likes}\r\n        />\r\n\r\n        {/* Total Subscribers */}\r\n        <EnhancedMetricCard\r\n          title=\"Total Subscribers\"\r\n          value={formatIndianNumberShort(profile.total_subscriptions)}\r\n          icon={UserPlus}\r\n          description=\"People subscribed to updates\"\r\n          color=\"blue\"\r\n          isUpdated={profile.total_subscriptions !== initialProfile.total_subscriptions}\r\n        />\r\n\r\n        {/* Average Rating */}\r\n        <EnhancedMetricCard\r\n          title=\"Average Rating\"\r\n          value={profile.average_rating?.toFixed(1) || \"0.0\"}\r\n          suffix=\"/5.0\"\r\n          icon={Star}\r\n          description=\"Average customer rating\"\r\n          color=\"amber\"\r\n          isUpdated={profile.average_rating !== initialProfile.average_rating}\r\n        />\r\n      </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAkBe,SAAS,iCAAiC,EACvD,OAAO,EACP,cAAc,EACgB;IAC9B,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;kBAER,cAAA,6LAAC;YAAI,WAAU;;8BAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAG1F,6LAAC;wCAAE,WAAU;kDAAwD;;;;;;;;;;;;;;;;;;;;;;;8BAQzE,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,kMAAA,CAAA,UAAkB;4BACjB,OAAM;4BACN,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,WAAW;4BAClD,MAAM,uMAAA,CAAA,QAAK;4BACX,aAAY;4BACZ,OAAM;4BACN,WAAW,QAAQ,WAAW,KAAK,eAAe,WAAW;;;;;;sCAI/D,6LAAC,kMAAA,CAAA,UAAkB;4BACjB,OAAM;4BACN,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,mBAAmB;4BAC1D,MAAM,iNAAA,CAAA,WAAQ;4BACd,aAAY;4BACZ,OAAM;4BACN,WAAW,QAAQ,mBAAmB,KAAK,eAAe,mBAAmB;;;;;;sCAI/E,6LAAC,kMAAA,CAAA,UAAkB;4BACjB,OAAM;4BACN,OAAO,QAAQ,cAAc,EAAE,QAAQ,MAAM;4BAC7C,QAAO;4BACP,MAAM,qMAAA,CAAA,OAAI;4BACV,aAAY;4BACZ,OAAM;4BACN,WAAW,QAAQ,cAAc,KAAK,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAM7E;KA1FwB", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/EnhancedVisitMetricsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Users } from \"lucide-react\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport EnhancedMetricCard from \"./EnhancedMetricCard\";\r\n\r\ninterface VisitMetricsSectionProps {\r\n  totalUniqueVisits: number;\r\n  todayUniqueVisits: number;\r\n  yesterdayUniqueVisits: number;\r\n  currentMonthUniqueVisits: number;\r\n  isVisitUpdated: boolean;\r\n}\r\n\r\nexport default function EnhancedVisitMetricsSection({\r\n  totalUniqueVisits,\r\n  todayUniqueVisits,\r\n  yesterdayUniqueVisits,\r\n  isVisitUpdated,\r\n  currentMonthUniqueVisits,\r\n}: VisitMetricsSectionProps) {\r\n  // Calculate trend percentage for today vs yesterday\r\n  const calculateTrend = () => {\r\n    if (yesterdayUniqueVisits === 0) return { value: 0, isPositive: true };\r\n\r\n    const difference = todayUniqueVisits - yesterdayUniqueVisits;\r\n    const percentage = Math.round((difference / yesterdayUniqueVisits) * 100);\r\n\r\n    return {\r\n      value: percentage,\r\n      isPositive: percentage >= 0,\r\n    };\r\n  };\r\n\r\n  const todayTrend = calculateTrend();\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const headerVariants = {\r\n    hidden: { opacity: 0, y: -20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 300,\r\n        damping: 24,\r\n      }\r\n    },\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n    >\r\n      <div className=\"space-y-4\">\r\n      {/* Section Header */}\r\n      <motion.div\r\n        variants={headerVariants}\r\n      >\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 mb-4 sm:mb-6 pb-3 sm:pb-4 border-b border-neutral-100 dark:border-neutral-800\">\r\n        <div className=\"p-2 rounded-lg bg-primary/10 text-primary self-start\">\r\n          <Users className=\"w-4 sm:w-5 h-4 sm:h-5\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-100\">\r\n            Visitor Metrics\r\n          </h3>\r\n          <p className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-0.5\">\r\n            Key statistics about your card visitors\r\n          </p>\r\n        </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Total Unique Visits - Standalone */}\r\n      <div className=\"mb-4\">\r\n        <EnhancedMetricCard\r\n          title=\"Total Unique Visits\"\r\n          value={formatIndianNumberShort(totalUniqueVisits)}\r\n          icon={Users}\r\n          description=\"All-time unique visitors to your card\"\r\n          color=\"blue\"\r\n          isUpdated={isVisitUpdated}\r\n        />\r\n      </div>\r\n\r\n      {/* Daily and Monthly Metrics Cards */}\r\n      <div className=\"grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3\">\r\n        {/* Today's Unique Visits */}\r\n        <EnhancedMetricCard\r\n          title=\"Today's Unique Visits\"\r\n          value={formatIndianNumberShort(todayUniqueVisits)}\r\n          icon={Users}\r\n          description=\"Unique visitors today\"\r\n          color=\"green\"\r\n          isUpdated={isVisitUpdated}\r\n          trend={todayTrend}\r\n        />\r\n\r\n        {/* Yesterday's Unique Visits */}\r\n        <EnhancedMetricCard\r\n          title=\"Yesterday's Unique Visits\"\r\n          value={formatIndianNumberShort(yesterdayUniqueVisits)}\r\n          icon={Users}\r\n          description=\"Unique visitors yesterday\"\r\n          color=\"purple\"\r\n          isUpdated={isVisitUpdated}\r\n        />\r\n\r\n        {/* Current Month's Unique Visits */}\r\n        <EnhancedMetricCard\r\n          title=\"This Month's Unique Visits\"\r\n          value={formatIndianNumberShort(currentMonthUniqueVisits)}\r\n          icon={Users}\r\n          description=\"Unique visitors this month\"\r\n          color=\"indigo\"\r\n          isUpdated={isVisitUpdated}\r\n        />\r\n      </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAee,SAAS,4BAA4B,EAClD,iBAAiB,EACjB,iBAAiB,EACjB,qBAAqB,EACrB,cAAc,EACd,wBAAwB,EACC;IACzB,oDAAoD;IACpD,MAAM,iBAAiB;QACrB,IAAI,0BAA0B,GAAG,OAAO;YAAE,OAAO;YAAG,YAAY;QAAK;QAErE,MAAM,aAAa,oBAAoB;QACvC,MAAM,aAAa,KAAK,KAAK,CAAC,AAAC,aAAa,wBAAyB;QAErE,OAAO;YACL,OAAO;YACP,YAAY,cAAc;QAC5B;IACF;IAEA,MAAM,aAAa;IAEnB,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;kBAER,cAAA,6LAAC;YAAI,WAAU;;8BAEf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAG1F,6LAAC;wCAAE,WAAU;kDAAwD;;;;;;;;;;;;;;;;;;;;;;;8BAQzE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,kMAAA,CAAA,UAAkB;wBACjB,OAAM;wBACN,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;wBAC/B,MAAM,uMAAA,CAAA,QAAK;wBACX,aAAY;wBACZ,OAAM;wBACN,WAAW;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,kMAAA,CAAA,UAAkB;4BACjB,OAAM;4BACN,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;4BAC/B,MAAM,uMAAA,CAAA,QAAK;4BACX,aAAY;4BACZ,OAAM;4BACN,WAAW;4BACX,OAAO;;;;;;sCAIT,6LAAC,kMAAA,CAAA,UAAkB;4BACjB,OAAM;4BACN,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;4BAC/B,MAAM,uMAAA,CAAA,QAAK;4BACX,aAAY;4BACZ,OAAM;4BACN,WAAW;;;;;;sCAIb,6LAAC,kMAAA,CAAA,UAAkB;4BACjB,OAAM;4BACN,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE;4BAC/B,MAAM,uMAAA,CAAA,QAAK;4BACX,aAAY;4BACZ,OAAM;4BACN,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAMrB;KAxHwB", "debugId": null}}, {"offset": {"line": 662, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/PremiumFeatureLock.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Spark<PERSON>, Lock, ArrowRight } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Link from \"next/link\";\r\n\r\ninterface PremiumFeatureLockProps {\r\n  title: string;\r\n  description?: string;\r\n  height?: string;\r\n}\r\n\r\nexport default function PremiumFeatureLock({\r\n  title,\r\n  description = \"Upgrade to Basic plan or higher to unlock this premium analytics feature.\",\r\n  height = \"h-[350px]\",\r\n}: PremiumFeatureLockProps) {\r\n  return (\r\n    <div\r\n      className={`w-full ${height} flex items-center justify-center rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 overflow-hidden relative`}\r\n    >\r\n      {/* Background gradient effect */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-transparent to-amber-50 dark:to-amber-950/20 opacity-50\" />\r\n\r\n      {/* Lock icon with glow effect */}\r\n      <div className=\"absolute opacity-10 text-amber-500 dark:text-amber-400\">\r\n        <Lock className=\"w-32 h-32\" />\r\n      </div>\r\n\r\n      <div className=\"z-10 text-center max-w-md px-6\">\r\n        <div className=\"flex items-center justify-center gap-2 mb-3\">\r\n          <Sparkles className=\"h-5 w-5 text-amber-500 dark:text-amber-400\" />\r\n          <h3 className=\"text-lg font-medium text-amber-700 dark:text-amber-300\">\r\n            Premium Feature: {title}\r\n          </h3>\r\n        </div>\r\n\r\n        <p className=\"text-neutral-600 dark:text-neutral-400 mb-6\">\r\n          {description}\r\n        </p>\r\n\r\n        <div>\r\n          <Button asChild className=\"bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white\">\r\n            <Link href=\"/dashboard/business/plan\" className=\"flex items-center gap-2\">\r\n              Upgrade Now\r\n              <ArrowRight className=\"h-4 w-4\" />\r\n            </Link>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAYe,SAAS,mBAAmB,EACzC,KAAK,EACL,cAAc,2EAA2E,EACzF,SAAS,WAAW,EACI;IACxB,qBACE,6LAAC;QACC,WAAW,CAAC,OAAO,EAAE,OAAO,oJAAoJ,CAAC;;0BAGjL,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;0BAGlB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAG,WAAU;;oCAAyD;oCACnD;;;;;;;;;;;;;kCAItB,6LAAC;wBAAE,WAAU;kCACV;;;;;;kCAGH,6LAAC;kCACC,cAAA,6LAAC,8HAAA,CAAA,SAAM;4BAAC,OAAO;4BAAC,WAAU;sCACxB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAA2B,WAAU;;oCAA0B;kDAExE,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;KAxCwB", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/chart.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as RechartsPrimitive from \"recharts\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\n// Format: { THEME_NAME: CSS_SELECTOR }\r\nconst THEMES = { light: \"\", dark: \".dark\" } as const\r\n\r\nexport type ChartConfig = {\r\n  [_key in string]: {\r\n    label?: React.ReactNode\r\n    icon?: React.ComponentType\r\n  } & (\r\n    | { color?: string; theme?: never }\r\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\r\n  )\r\n}\r\n\r\ntype ChartContextProps = {\r\n  config: ChartConfig\r\n}\r\n\r\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\r\n\r\nfunction useChart() {\r\n  const context = React.useContext(ChartContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction ChartContainer({\r\n  id,\r\n  className,\r\n  children,\r\n  config,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  config: ChartConfig\r\n  children: React.ComponentProps<\r\n    typeof RechartsPrimitive.ResponsiveContainer\r\n  >[\"children\"]\r\n}) {\r\n  const uniqueId = React.useId()\r\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\r\n\r\n  return (\r\n    <ChartContext.Provider value={{ config }}>\r\n      <div\r\n        data-slot=\"chart\"\r\n        data-chart={chartId}\r\n        className={cn(\r\n          \"[&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border flex aspect-video justify-center text-xs [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-hidden [&_.recharts-sector]:outline-hidden [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-surface]:outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <ChartStyle id={chartId} config={config} />\r\n        <RechartsPrimitive.ResponsiveContainer>\r\n          {children}\r\n        </RechartsPrimitive.ResponsiveContainer>\r\n      </div>\r\n    </ChartContext.Provider>\r\n  )\r\n}\r\n\r\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\r\n  const colorConfig = Object.entries(config).filter(\r\n    ([, config]) => config.theme || config.color\r\n  )\r\n\r\n  if (!colorConfig.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <style\r\n      dangerouslySetInnerHTML={{\r\n        __html: Object.entries(THEMES)\r\n          .map(\r\n            ([theme, prefix]) => `\r\n${prefix} [data-chart=${id}] {\r\n${colorConfig\r\n  .map(([key, itemConfig]) => {\r\n    const color =\r\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\r\n      itemConfig.color\r\n    return color ? `  --color-${key}: ${color};` : null\r\n  })\r\n  .join(\"\\n\")}\r\n}\r\n`\r\n          )\r\n          .join(\"\\n\"),\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst ChartTooltip = RechartsPrimitive.Tooltip\r\n\r\nfunction ChartTooltipContent({\r\n  active,\r\n  payload,\r\n  className,\r\n  indicator = \"dot\",\r\n  hideLabel = false,\r\n  hideIndicator = false,\r\n  label,\r\n  labelFormatter,\r\n  labelClassName,\r\n  formatter,\r\n  color,\r\n  nameKey,\r\n  labelKey,\r\n}: React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\r\n  React.ComponentProps<\"div\"> & {\r\n    hideLabel?: boolean\r\n    hideIndicator?: boolean\r\n    indicator?: \"line\" | \"dot\" | \"dashed\"\r\n    nameKey?: string\r\n    labelKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  const tooltipLabel = React.useMemo(() => {\r\n    if (hideLabel || !payload?.length) {\r\n      return null\r\n    }\r\n\r\n    const [item] = payload\r\n    const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\r\n    const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n    const value =\r\n      !labelKey && typeof label === \"string\"\r\n        ? config[label as keyof typeof config]?.label || label\r\n        : itemConfig?.label\r\n\r\n    if (labelFormatter) {\r\n      return (\r\n        <div className={cn(\"font-medium\", labelClassName)}>\r\n          {labelFormatter(value, payload)}\r\n        </div>\r\n      )\r\n    }\r\n\r\n    if (!value) {\r\n      return null\r\n    }\r\n\r\n    return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\r\n  }, [\r\n    label,\r\n    labelFormatter,\r\n    payload,\r\n    hideLabel,\r\n    labelClassName,\r\n    config,\r\n    labelKey,\r\n  ])\r\n\r\n  if (!active || !payload?.length) {\r\n    return null\r\n  }\r\n\r\n  const nestLabel = payload.length === 1 && indicator !== \"dot\"\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"border-border/50 bg-background grid min-w-[8rem] items-start gap-1.5 rounded-lg border px-2.5 py-1.5 text-xs shadow-xl\",\r\n        className\r\n      )}\r\n    >\r\n      {!nestLabel ? tooltipLabel : null}\r\n      <div className=\"grid gap-1.5\">\r\n        {payload.map((item, index) => {\r\n          const key = `${nameKey || item.name || item.dataKey || \"value\"}`\r\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n          const indicatorColor = color || item.payload.fill || item.color\r\n\r\n          return (\r\n            <div\r\n              key={item.dataKey}\r\n              className={cn(\r\n                \"[&>svg]:text-muted-foreground flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5\",\r\n                indicator === \"dot\" && \"items-center\"\r\n              )}\r\n            >\r\n              {formatter && item?.value !== undefined && item.name ? (\r\n                formatter(item.value, item.name, item, index, item.payload)\r\n              ) : (\r\n                <>\r\n                  {itemConfig?.icon ? (\r\n                    <itemConfig.icon />\r\n                  ) : (\r\n                    !hideIndicator && (\r\n                      <div\r\n                        className={cn(\r\n                          \"shrink-0 rounded-[2px] border-(--color-border) bg-(--color-bg)\",\r\n                          {\r\n                            \"h-2.5 w-2.5\": indicator === \"dot\",\r\n                            \"w-1\": indicator === \"line\",\r\n                            \"w-0 border-[1.5px] border-dashed bg-transparent\":\r\n                              indicator === \"dashed\",\r\n                            \"my-0.5\": nestLabel && indicator === \"dashed\",\r\n                          }\r\n                        )}\r\n                        style={\r\n                          {\r\n                            \"--color-bg\": indicatorColor,\r\n                            \"--color-border\": indicatorColor,\r\n                          } as React.CSSProperties\r\n                        }\r\n                      />\r\n                    )\r\n                  )}\r\n                  <div\r\n                    className={cn(\r\n                      \"flex flex-1 justify-between leading-none\",\r\n                      nestLabel ? \"items-end\" : \"items-center\"\r\n                    )}\r\n                  >\r\n                    <div className=\"grid gap-1.5\">\r\n                      {nestLabel ? tooltipLabel : null}\r\n                      <span className=\"text-muted-foreground\">\r\n                        {itemConfig?.label || item.name}\r\n                      </span>\r\n                    </div>\r\n                    {item.value && (\r\n                      <span className=\"text-foreground font-mono font-medium tabular-nums\">\r\n                        {item.value.toLocaleString()}\r\n                      </span>\r\n                    )}\r\n                  </div>\r\n                </>\r\n              )}\r\n            </div>\r\n          )\r\n        })}\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nconst ChartLegend = RechartsPrimitive.Legend\r\n\r\nfunction ChartLegendContent({\r\n  className,\r\n  hideIcon = false,\r\n  payload,\r\n  verticalAlign = \"bottom\",\r\n  nameKey,\r\n}: React.ComponentProps<\"div\"> &\r\n  Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\r\n    hideIcon?: boolean\r\n    nameKey?: string\r\n  }) {\r\n  const { config } = useChart()\r\n\r\n  if (!payload?.length) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex items-center justify-center gap-4\",\r\n        verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\r\n        className\r\n      )}\r\n    >\r\n      {payload.map((item) => {\r\n        const key = `${nameKey || item.dataKey || \"value\"}`\r\n        const itemConfig = getPayloadConfigFromPayload(config, item, key)\r\n\r\n        return (\r\n          <div\r\n            key={item.value}\r\n            className={cn(\r\n              \"[&>svg]:text-muted-foreground flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3\"\r\n            )}\r\n          >\r\n            {itemConfig?.icon && !hideIcon ? (\r\n              <itemConfig.icon />\r\n            ) : (\r\n              <div\r\n                className=\"h-2 w-2 shrink-0 rounded-[2px]\"\r\n                style={{\r\n                  backgroundColor: item.color,\r\n                }}\r\n              />\r\n            )}\r\n            {itemConfig?.label}\r\n          </div>\r\n        )\r\n      })}\r\n    </div>\r\n  )\r\n}\r\n\r\n// Helper to extract item config from a payload.\r\nfunction getPayloadConfigFromPayload(\r\n  config: ChartConfig,\r\n  payload: unknown,\r\n  key: string\r\n) {\r\n  if (typeof payload !== \"object\" || payload === null) {\r\n    return undefined\r\n  }\r\n\r\n  const payloadPayload =\r\n    \"payload\" in payload &&\r\n    typeof payload.payload === \"object\" &&\r\n    payload.payload !== null\r\n      ? payload.payload\r\n      : undefined\r\n\r\n  let configLabelKey: string = key\r\n\r\n  if (\r\n    key in payload &&\r\n    typeof payload[key as keyof typeof payload] === \"string\"\r\n  ) {\r\n    configLabelKey = payload[key as keyof typeof payload] as string\r\n  } else if (\r\n    payloadPayload &&\r\n    key in payloadPayload &&\r\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\r\n  ) {\r\n    configLabelKey = payloadPayload[\r\n      key as keyof typeof payloadPayload\r\n    ] as string\r\n  }\r\n\r\n  return configLabelKey in config\r\n    ? config[configLabelKey]\r\n    : config[key as keyof typeof config]\r\n}\r\n\r\nexport {\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n  ChartLegend,\r\n  ChartLegendContent,\r\n  ChartStyle,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAAA;AAAA;AAEA;;;AALA;;;;AAOA,uCAAuC;AACvC,MAAM,SAAS;IAAE,OAAO;IAAI,MAAM;AAAQ;AAgB1C,MAAM,6BAAe,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA4B;AAEnE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,SAAS,eAAe,EACtB,EAAE,EACF,SAAS,EACT,QAAQ,EACR,MAAM,EACN,GAAG,OAMJ;;IACC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,QAAW,AAAD;IAC3B,MAAM,UAAU,CAAC,MAAM,EAAE,MAAM,SAAS,OAAO,CAAC,MAAM,KAAK;IAE3D,qBACE,6LAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;QAAO;kBACrC,cAAA,6LAAC;YACC,aAAU;YACV,cAAY;YACZ,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+pBACA;YAED,GAAG,KAAK;;8BAET,6LAAC;oBAAW,IAAI;oBAAS,QAAQ;;;;;;8BACjC,6LAAC,sKAAA,CAAA,sBAAqC;8BACnC;;;;;;;;;;;;;;;;;AAKX;IAjCS;KAAA;AAmCT,MAAM,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAuC;IACrE,MAAM,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,CAC/C,CAAC,GAAG,OAAO,GAAK,OAAO,KAAK,IAAI,OAAO,KAAK;IAG9C,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,yBAAyB;YACvB,QAAQ,OAAO,OAAO,CAAC,QACpB,GAAG,CACF,CAAC,CAAC,OAAO,OAAO,GAAK,CAAC;AAClC,EAAE,OAAO,aAAa,EAAE,GAAG;AAC3B,EAAE,YACC,GAAG,CAAC,CAAC,CAAC,KAAK,WAAW;oBACrB,MAAM,QACJ,WAAW,KAAK,EAAE,CAAC,MAAuC,IAC1D,WAAW,KAAK;oBAClB,OAAO,QAAQ,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,GAAG;gBACjD,GACC,IAAI,CAAC,MAAM;;AAEd,CAAC,EAEU,IAAI,CAAC;QACV;;;;;;AAGN;MA/BM;AAiCN,MAAM,eAAe,0JAAA,CAAA,UAAyB;AAE9C,SAAS,oBAAoB,EAC3B,MAAM,EACN,OAAO,EACP,SAAS,EACT,YAAY,KAAK,EACjB,YAAY,KAAK,EACjB,gBAAgB,KAAK,EACrB,KAAK,EACL,cAAc,EACd,cAAc,EACd,SAAS,EACT,KAAK,EACL,OAAO,EACP,QAAQ,EAQP;;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACjC,IAAI,aAAa,CAAC,SAAS,QAAQ;gBACjC,OAAO;YACT;YAEA,MAAM,CAAC,KAAK,GAAG;YACf,MAAM,MAAM,GAAG,YAAY,MAAM,WAAW,MAAM,QAAQ,SAAS;YACnE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAC7D,MAAM,QACJ,CAAC,YAAY,OAAO,UAAU,WAC1B,MAAM,CAAC,MAA6B,EAAE,SAAS,QAC/C,YAAY;YAElB,IAAI,gBAAgB;gBAClB,qBACE,6LAAC;oBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;8BAC/B,eAAe,OAAO;;;;;;YAG7B;YAEA,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YAEA,qBAAO,6LAAC;gBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;0BAAkB;;;;;;QAC7D;oDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ;QAC/B,OAAO;IACT;IAEA,MAAM,YAAY,QAAQ,MAAM,KAAK,KAAK,cAAc;IAExD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0HACA;;YAGD,CAAC,YAAY,eAAe;0BAC7B,6LAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,MAAM;oBAClB,MAAM,MAAM,GAAG,WAAW,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS;oBAChE,MAAM,aAAa,4BAA4B,QAAQ,MAAM;oBAC7D,MAAM,iBAAiB,SAAS,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,KAAK;oBAE/D,qBACE,6LAAC;wBAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA,cAAc,SAAS;kCAGxB,aAAa,MAAM,UAAU,aAAa,KAAK,IAAI,GAClD,UAAU,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,OAAO,KAAK,OAAO,kBAE1D;;gCACG,YAAY,qBACX,6LAAC,WAAW,IAAI;;;;2CAEhB,CAAC,+BACC,6LAAC;oCACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;wCACE,eAAe,cAAc;wCAC7B,OAAO,cAAc;wCACrB,mDACE,cAAc;wCAChB,UAAU,aAAa,cAAc;oCACvC;oCAEF,OACE;wCACE,cAAc;wCACd,kBAAkB;oCACpB;;;;;;8CAKR,6LAAC;oCACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,4CACA,YAAY,cAAc;;sDAG5B,6LAAC;4CAAI,WAAU;;gDACZ,YAAY,eAAe;8DAC5B,6LAAC;oDAAK,WAAU;8DACb,YAAY,SAAS,KAAK,IAAI;;;;;;;;;;;;wCAGlC,KAAK,KAAK,kBACT,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;uBAhD/B,KAAK,OAAO;;;;;gBAwDvB;;;;;;;;;;;;AAIR;IA9IS;;QAsBY;;;MAtBZ;AAgJT,MAAM,cAAc,yJAAA,CAAA,SAAwB;AAE5C,SAAS,mBAAmB,EAC1B,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,EACP,gBAAgB,QAAQ,EACxB,OAAO,EAKN;;IACD,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,IAAI,CAAC,SAAS,QAAQ;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0CACA,kBAAkB,QAAQ,SAAS,QACnC;kBAGD,QAAQ,GAAG,CAAC,CAAC;YACZ,MAAM,MAAM,GAAG,WAAW,KAAK,OAAO,IAAI,SAAS;YACnD,MAAM,aAAa,4BAA4B,QAAQ,MAAM;YAE7D,qBACE,6LAAC;gBAEC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV;;oBAGD,YAAY,QAAQ,CAAC,yBACpB,6LAAC,WAAW,IAAI;;;;6CAEhB,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,KAAK,KAAK;wBAC7B;;;;;;oBAGH,YAAY;;eAfR,KAAK,KAAK;;;;;QAkBrB;;;;;;AAGN;IApDS;;QAWY;;;MAXZ;AAsDT,gDAAgD;AAChD,SAAS,4BACP,MAAmB,EACnB,OAAgB,EAChB,GAAW;IAEX,IAAI,OAAO,YAAY,YAAY,YAAY,MAAM;QACnD,OAAO;IACT;IAEA,MAAM,iBACJ,aAAa,WACb,OAAO,QAAQ,OAAO,KAAK,YAC3B,QAAQ,OAAO,KAAK,OAChB,QAAQ,OAAO,GACf;IAEN,IAAI,iBAAyB;IAE7B,IACE,OAAO,WACP,OAAO,OAAO,CAAC,IAA4B,KAAK,UAChD;QACA,iBAAiB,OAAO,CAAC,IAA4B;IACvD,OAAO,IACL,kBACA,OAAO,kBACP,OAAO,cAAc,CAAC,IAAmC,KAAK,UAC9D;QACA,iBAAiB,cAAc,CAC7B,IACD;IACH;IAEA,OAAO,kBAAkB,SACrB,MAAM,CAAC,eAAe,GACtB,MAAM,CAAC,IAA2B;AACxC", "debugId": null}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/DailyVisitTrendChart.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { BarChart3 } from \"lucide-react\";\r\nimport { Area, AreaChart, CartesianGrid, XAxis, YAxis } from \"recharts\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport PremiumFeatureLock from \"./PremiumFeatureLock\";\r\nimport {\r\n  ChartConfig,\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n} from \"@/components/ui/chart\";\r\n\r\ninterface DailyVisitTrendChartProps {\r\n  trend7Days: { date: string; visits: number }[];\r\n  trend30Days: { date: string; visits: number }[];\r\n  userPlan?: string | null;\r\n}\r\n\r\nconst chartConfig = {\r\n  visits: {\r\n    label: \"Visits\",\r\n    color: \"var(--chart-1)\",\r\n  },\r\n} satisfies ChartConfig;\r\n\r\nexport default function DailyVisitTrendChart({\r\n  trend7Days,\r\n  trend30Days,\r\n  userPlan,\r\n}: DailyVisitTrendChartProps) {\r\n  const isMobile = useIsMobile();\r\n  const [selectedRange, setSelectedRange] = useState<\"7days\" | \"30days\">(\"7days\");\r\n\r\n  // Format date string (YYYY-MM-DD) to a more readable format (DD MMM)\r\n  const formatDate = (dateStr: string) => {\r\n    const date = new Date(dateStr);\r\n    return date.toLocaleDateString(\"en-IN\", {\r\n      day: \"2-digit\",\r\n      month: \"short\",\r\n    });\r\n  };\r\n\r\n  // Format date for X-axis ticks - shorter on mobile\r\n  const formatXAxisTick = (dateStr: string) => {\r\n    if (isMobile) {\r\n      const date = new Date(dateStr);\r\n      return date.getDate().toString(); // Just the day number on mobile\r\n    }\r\n    return dateStr; // Full formatted date on desktop\r\n  };\r\n\r\n  // Generate all dates for the selected range\r\n  const generateAllDates = (days: number) => {\r\n    const dates = [];\r\n    const today = new Date();\r\n\r\n    for (let i = days - 1; i >= 0; i--) {\r\n      const date = new Date();\r\n      date.setDate(today.getDate() - i);\r\n      const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD format\r\n      dates.push(dateStr);\r\n    }\r\n\r\n    return dates;\r\n  };\r\n\r\n  // Get all dates for the selected range\r\n  const allDates = generateAllDates(selectedRange === \"7days\" ? 7 : 30);\r\n\r\n  // Create a map of existing data\r\n  const dataMap = new Map();\r\n\r\n  if (selectedRange === \"7days\") {\r\n    trend7Days.forEach(item => {\r\n      dataMap.set(item.date, item.visits);\r\n    });\r\n  } else {\r\n    trend30Days.forEach(item => {\r\n      dataMap.set(item.date, item.visits);\r\n    });\r\n  }\r\n\r\n  // Create chart data with all dates, using 0 for missing data\r\n  const chartData = allDates.map(date => ({\r\n    date,\r\n    visits: dataMap.has(date) ? dataMap.get(date) : 0,\r\n    formattedDate: formatDate(date),\r\n  }));\r\n\r\n  // Calculate the maximum value for better Y-axis scaling\r\n  const maxVisits = Math.max(...chartData.map(item => item.visits));\r\n  // Calculate a nice rounded upper bound for the Y-axis\r\n  const calculateYAxisMax = (maxValue: number) => {\r\n    if (maxValue <= 0) return 5; // Default if no data\r\n    if (maxValue <= 5) return Math.ceil(maxValue * 1.2); // Add 20% padding for small values\r\n    if (maxValue <= 10) return Math.ceil(maxValue * 1.1); // Add 10% padding for medium values\r\n    return Math.ceil(maxValue * 1.05); // Add 5% padding for large values\r\n  };\r\n  const yAxisMax = calculateYAxisMax(maxVisits);\r\n\r\n\r\n\r\n\r\n\r\n  // Custom formatter for Y-axis ticks using Indian number format\r\n  const formatYAxisTick = (value: number) => {\r\n    return formatIndianNumberShort(Math.floor(value));\r\n  };\r\n\r\n  // Handle range change\r\n  const handleRangeChange = (value: string) => {\r\n    setSelectedRange(value as \"7days\" | \"30days\");\r\n  };\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        duration: 0.5,\r\n      },\r\n    },\r\n  };\r\n\r\n  // Check if user has access to this premium feature\r\n  const isPremiumUser = userPlan === \"growth\" || userPlan === \"pro\" || userPlan === \"enterprise\";\r\n\r\n  // If user doesn't have a premium plan, show the premium feature lock component\r\n  if (!isPremiumUser) {\r\n    return (\r\n      <PremiumFeatureLock\r\n        title=\"Daily Visit Trend\"\r\n        description=\"Upgrade to Growth plan or higher to see detailed daily visit trends for your business.\"\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className=\"rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black p-3 sm:p-4 md:p-6 shadow-sm\"\r\n    >\r\n      <div className=\"mb-4\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-2\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-900 dark:text-neutral-100\">\r\n            Daily Visit Trend\r\n          </h3>\r\n          <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-neutral-500 dark:text-neutral-400\">\r\n            <BarChart3 className=\"h-3 w-3 sm:h-4 sm:w-4\" />\r\n            <span>Unique visitors by day</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Range Selection Controls */}\r\n      <div className=\"mb-4 flex items-center justify-center sm:justify-between\">\r\n        <div className=\"hidden sm:block sm:flex-1\"></div>\r\n        <Select value={selectedRange} onValueChange={handleRangeChange}>\r\n          <SelectTrigger className={isMobile ? \"w-full\" : \"w-[180px]\"}>\r\n            <SelectValue placeholder=\"Select Range\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"7days\">Last 7 Days</SelectItem>\r\n            <SelectItem value=\"30days\">Last 30 Days</SelectItem>\r\n          </SelectContent>\r\n        </Select>\r\n        <div className=\"hidden sm:block sm:flex-1\"></div>\r\n      </div>\r\n\r\n      {/* Chart */}\r\n      <div className=\"h-[250px] sm:h-[300px] w-full px-1 pb-2\">\r\n        <ChartContainer config={chartConfig} className=\"h-full w-full\">\r\n          <AreaChart\r\n            data={chartData}\r\n            margin={isMobile\r\n              ? { top: 5, right: 5, left: 0, bottom: 5 }\r\n              : { top: 10, right: 10, left: 0, bottom: 5 }\r\n            }\r\n          >\r\n            <defs>\r\n              <linearGradient id=\"fillVisits\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\r\n                <stop\r\n                  offset=\"5%\"\r\n                  stopColor=\"var(--color-visits)\"\r\n                  stopOpacity={0.8}\r\n                />\r\n                <stop\r\n                  offset=\"95%\"\r\n                  stopColor=\"var(--color-visits)\"\r\n                  stopOpacity={0.1}\r\n                />\r\n              </linearGradient>\r\n            </defs>\r\n            <CartesianGrid strokeDasharray=\"3 3\" vertical={false} />\r\n            <XAxis\r\n              dataKey=\"formattedDate\"\r\n              axisLine={false}\r\n              tickLine={false}\r\n              tickMargin={8}\r\n              minTickGap={32}\r\n              tickFormatter={formatXAxisTick}\r\n            />\r\n            <YAxis\r\n              axisLine={false}\r\n              tickLine={false}\r\n              domain={[0, yAxisMax]}\r\n              allowDecimals={false}\r\n              tickFormatter={formatYAxisTick}\r\n            />\r\n            <ChartTooltip\r\n              content={\r\n                <ChartTooltipContent\r\n                  labelFormatter={(label) => `Date: ${label}`}\r\n                  formatter={(value) => [\r\n                    formatIndianNumberShort(Number(value)),\r\n                    \" Visits\"\r\n                  ]}\r\n                />\r\n              }\r\n            />\r\n            <Area\r\n              dataKey=\"visits\"\r\n              type=\"natural\"\r\n              fill=\"url(#fillVisits)\"\r\n              stroke=\"var(--color-visits)\"\r\n              strokeWidth={isMobile ? 1.5 : 2}\r\n            />\r\n          </AreaChart>\r\n        </ChartContainer>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAuBA,MAAM,cAAc;IAClB,QAAQ;QACN,OAAO;QACP,OAAO;IACT;AACF;AAEe,SAAS,qBAAqB,EAC3C,UAAU,EACV,WAAW,EACX,QAAQ,EACkB;;IAC1B,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEvE,qEAAqE;IACrE,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,KAAK;YACL,OAAO;QACT;IACF;IAEA,mDAAmD;IACnD,MAAM,kBAAkB,CAAC;QACvB,IAAI,UAAU;YACZ,MAAM,OAAO,IAAI,KAAK;YACtB,OAAO,KAAK,OAAO,GAAG,QAAQ,IAAI,gCAAgC;QACpE;QACA,OAAO,SAAS,iCAAiC;IACnD;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE;QAChB,MAAM,QAAQ,IAAI;QAElB,IAAK,IAAI,IAAI,OAAO,GAAG,KAAK,GAAG,IAAK;YAClC,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,MAAM,OAAO,KAAK;YAC/B,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,oBAAoB;YACtE,MAAM,IAAI,CAAC;QACb;QAEA,OAAO;IACT;IAEA,uCAAuC;IACvC,MAAM,WAAW,iBAAiB,kBAAkB,UAAU,IAAI;IAElE,gCAAgC;IAChC,MAAM,UAAU,IAAI;IAEpB,IAAI,kBAAkB,SAAS;QAC7B,WAAW,OAAO,CAAC,CAAA;YACjB,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,MAAM;QACpC;IACF,OAAO;QACL,YAAY,OAAO,CAAC,CAAA;YAClB,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,MAAM;QACpC;IACF;IAEA,6DAA6D;IAC7D,MAAM,YAAY,SAAS,GAAG,CAAC,CAAA,OAAQ,CAAC;YACtC;YACA,QAAQ,QAAQ,GAAG,CAAC,QAAQ,QAAQ,GAAG,CAAC,QAAQ;YAChD,eAAe,WAAW;QAC5B,CAAC;IAED,wDAAwD;IACxD,MAAM,YAAY,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IAC/D,sDAAsD;IACtD,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,GAAG,OAAO,GAAG,qBAAqB;QAClD,IAAI,YAAY,GAAG,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,mCAAmC;QACxF,IAAI,YAAY,IAAI,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,oCAAoC;QAC1F,OAAO,KAAK,IAAI,CAAC,WAAW,OAAO,kCAAkC;IACvE;IACA,MAAM,WAAW,kBAAkB;IAMnC,+DAA+D;IAC/D,MAAM,kBAAkB,CAAC;QACvB,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,KAAK,CAAC;IAC5C;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;IACnB;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,mDAAmD;IACnD,MAAM,gBAAgB,aAAa,YAAY,aAAa,SAAS,aAAa;IAElF,+EAA+E;IAC/E,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC,kMAAA,CAAA,UAAkB;YACjB,OAAM;YACN,aAAY;;;;;;IAGlB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4E;;;;;;sCAG1F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC,8HAAA,CAAA,SAAM;wBAAC,OAAO;wBAAe,eAAe;;0CAC3C,6LAAC,8HAAA,CAAA,gBAAa;gCAAC,WAAW,WAAW,WAAW;0CAC9C,cAAA,6LAAC,8HAAA,CAAA,cAAW;oCAAC,aAAY;;;;;;;;;;;0CAE3B,6LAAC,8HAAA,CAAA,gBAAa;;kDACZ,6LAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAQ;;;;;;kDAC1B,6LAAC,8HAAA,CAAA,aAAU;wCAAC,OAAM;kDAAS;;;;;;;;;;;;;;;;;;kCAG/B,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAa,WAAU;8BAC7C,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBACR,MAAM;wBACN,QAAQ,WACJ;4BAAE,KAAK;4BAAG,OAAO;4BAAG,MAAM;4BAAG,QAAQ;wBAAE,IACvC;4BAAE,KAAK;4BAAI,OAAO;4BAAI,MAAM;4BAAG,QAAQ;wBAAE;;0CAG7C,6LAAC;0CACC,cAAA,6LAAC;oCAAe,IAAG;oCAAa,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAI,IAAG;;sDACtD,6LAAC;4CACC,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;sDAEf,6LAAC;4CACC,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;;;;;;;;;;;;0CAInB,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,UAAU;;;;;;0CAC/C,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,YAAY;gCACZ,eAAe;;;;;;0CAEjB,6LAAC,wJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,QAAQ;oCAAC;oCAAG;iCAAS;gCACrB,eAAe;gCACf,eAAe;;;;;;0CAEjB,6LAAC,6HAAA,CAAA,eAAY;gCACX,uBACE,6LAAC,6HAAA,CAAA,sBAAmB;oCAClB,gBAAgB,CAAC,QAAU,CAAC,MAAM,EAAE,OAAO;oCAC3C,WAAW,CAAC,QAAU;4CACpB,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO;4CAC/B;yCACD;;;;;;;;;;;0CAIP,6LAAC,uJAAA,CAAA,OAAI;gCACH,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,aAAa,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GAlNwB;;QAKL,yHAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/HourlyVisitTrendChart.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Clock } from \"lucide-react\";\r\nimport { Area, AreaChart, CartesianGrid, XAxis, YAxis } from \"recharts\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport PremiumFeatureLock from \"./PremiumFeatureLock\";\r\nimport {\r\n  ChartConfig,\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n} from \"@/components/ui/chart\";\r\n\r\ninterface HourlyVisitTrendChartProps {\r\n  data: { hour: number; visits: number }[];\r\n  userPlan?: string | null;\r\n}\r\n\r\nconst chartConfig = {\r\n  visits: {\r\n    label: \"Visits\",\r\n    color: \"var(--chart-2)\",\r\n  },\r\n} satisfies ChartConfig;\r\n\r\nexport default function HourlyVisitTrendChart({\r\n  data,\r\n  userPlan,\r\n}: HourlyVisitTrendChartProps) {\r\n  const isMobile = useIsMobile();\r\n\r\n  // Format hour number to a readable time format (e.g., 14 -> \"2 PM\")\r\n  const formatHour = (hour: number) => {\r\n    if (hour === 0) return \"12 AM\";\r\n    if (hour === 12) return \"12 PM\";\r\n    return hour < 12 ? `${hour} AM` : `${hour - 12} PM`;\r\n  };\r\n\r\n  // Format hour for X-axis ticks - shorter on mobile\r\n  const formatXAxisTick = (hourStr: string) => {\r\n    if (isMobile) {\r\n      // Extract just the hour number for mobile\r\n      const match = hourStr.match(/(\\d+)/);\r\n      if (match) {\r\n        return match[1];\r\n      }\r\n      return hourStr;\r\n    }\r\n    return hourStr; // Full formatted hour on desktop\r\n  };\r\n\r\n  // Generate all 24 hours\r\n  const generateAllHours = () => {\r\n    const hours = [];\r\n    for (let i = 0; i < 24; i++) {\r\n      hours.push(i);\r\n    }\r\n    return hours;\r\n  };\r\n\r\n  // Get all hours\r\n  const allHours = generateAllHours();\r\n\r\n  // Create a map of existing data\r\n  const dataMap = new Map();\r\n  data.forEach(item => {\r\n    dataMap.set(item.hour, item.visits);\r\n  });\r\n\r\n  // Format data for the chart with all hours, using 0 for missing data\r\n  const chartData = allHours.map(hour => ({\r\n    hour,\r\n    visits: dataMap.has(hour) ? dataMap.get(hour) : 0,\r\n    formattedHour: formatHour(hour),\r\n  }));\r\n\r\n  // Calculate the maximum value for better Y-axis scaling\r\n  const maxVisits = Math.max(...chartData.map(item => item.visits));\r\n  // Calculate a nice rounded upper bound for the Y-axis\r\n  const calculateYAxisMax = (maxValue: number) => {\r\n    if (maxValue <= 0) return 5; // Default if no data\r\n    if (maxValue <= 5) return Math.ceil(maxValue * 1.2); // Add 20% padding for small values\r\n    if (maxValue <= 10) return Math.ceil(maxValue * 1.1); // Add 10% padding for medium values\r\n    return Math.ceil(maxValue * 1.05); // Add 5% padding for large values\r\n  };\r\n  const yAxisMax = calculateYAxisMax(maxVisits);\r\n\r\n\r\n\r\n\r\n\r\n  // Custom formatter for Y-axis ticks using Indian number format\r\n  const formatYAxisTick = (value: number) => {\r\n    return formatIndianNumberShort(Math.floor(value));\r\n  };\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        duration: 0.5,\r\n      },\r\n    },\r\n  };\r\n\r\n  // Check if user has access to this premium feature\r\n  const isPremiumUser = userPlan === \"growth\" || userPlan === \"pro\" || userPlan === \"enterprise\";\r\n\r\n  // If user doesn't have a premium plan, show the premium feature lock component\r\n  if (!isPremiumUser) {\r\n    return (\r\n      <PremiumFeatureLock\r\n        title=\"Hourly Visit Trend\"\r\n        description=\"Upgrade to Growth plan or higher to see detailed hourly visit trends for your business.\"\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className=\"rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black p-3 sm:p-4 md:p-6 shadow-sm\"\r\n    >\r\n      <div className=\"mb-4\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-2\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-900 dark:text-neutral-100\">\r\n            Hourly Visit Trend\r\n          </h3>\r\n          <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-neutral-500 dark:text-neutral-400\">\r\n            <Clock className=\"h-3 w-3 sm:h-4 sm:w-4\" />\r\n            <span>Today&apos;s visitors by hour</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Chart */}\r\n      <div className=\"h-[250px] sm:h-[300px] w-full px-1 pb-2\">\r\n        <ChartContainer config={chartConfig} className=\"h-full w-full\">\r\n          <AreaChart\r\n            data={chartData}\r\n            margin={isMobile\r\n              ? { top: 5, right: 5, left: 0, bottom: 5 }\r\n              : { top: 10, right: 10, left: 0, bottom: 5 }\r\n            }\r\n          >\r\n            <defs>\r\n              <linearGradient id=\"fillVisits\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\r\n                <stop\r\n                  offset=\"5%\"\r\n                  stopColor=\"var(--color-visits)\"\r\n                  stopOpacity={0.8}\r\n                />\r\n                <stop\r\n                  offset=\"95%\"\r\n                  stopColor=\"var(--color-visits)\"\r\n                  stopOpacity={0.1}\r\n                />\r\n              </linearGradient>\r\n            </defs>\r\n            <CartesianGrid strokeDasharray=\"3 3\" vertical={false} />\r\n            <XAxis\r\n              dataKey=\"formattedHour\"\r\n              axisLine={false}\r\n              tickLine={false}\r\n              tickMargin={8}\r\n              minTickGap={32}\r\n              tickFormatter={formatXAxisTick}\r\n            />\r\n            <YAxis\r\n              axisLine={false}\r\n              tickLine={false}\r\n              domain={[0, yAxisMax]}\r\n              allowDecimals={false}\r\n              tickFormatter={formatYAxisTick}\r\n            />\r\n            <ChartTooltip\r\n              content={\r\n                <ChartTooltipContent\r\n                  labelFormatter={(label) => `Time: ${label}`}\r\n                  formatter={(value) => [\r\n                    formatIndianNumberShort(Number(value)),\r\n                    \" Visits\"\r\n                  ]}\r\n                />\r\n              }\r\n            />\r\n            <Area\r\n              dataKey=\"visits\"\r\n              type=\"natural\"\r\n              fill=\"url(#fillVisits)\"\r\n              stroke=\"var(--color-visits)\"\r\n              strokeWidth={isMobile ? 1.5 : 2}\r\n            />\r\n          </AreaChart>\r\n        </ChartContainer>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBA,MAAM,cAAc;IAClB,QAAQ;QACN,OAAO;QACP,OAAO;IACT;AACF;AAEe,SAAS,sBAAsB,EAC5C,IAAI,EACJ,QAAQ,EACmB;;IAC3B,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAE3B,oEAAoE;IACpE,MAAM,aAAa,CAAC;QAClB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO,OAAO,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,GAAG,OAAO,GAAG,GAAG,CAAC;IACrD;IAEA,mDAAmD;IACnD,MAAM,kBAAkB,CAAC;QACvB,IAAI,UAAU;YACZ,0CAA0C;YAC1C,MAAM,QAAQ,QAAQ,KAAK,CAAC;YAC5B,IAAI,OAAO;gBACT,OAAO,KAAK,CAAC,EAAE;YACjB;YACA,OAAO;QACT;QACA,OAAO,SAAS,iCAAiC;IACnD;IAEA,wBAAwB;IACxB,MAAM,mBAAmB;QACvB,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,IAAK;YAC3B,MAAM,IAAI,CAAC;QACb;QACA,OAAO;IACT;IAEA,gBAAgB;IAChB,MAAM,WAAW;IAEjB,gCAAgC;IAChC,MAAM,UAAU,IAAI;IACpB,KAAK,OAAO,CAAC,CAAA;QACX,QAAQ,GAAG,CAAC,KAAK,IAAI,EAAE,KAAK,MAAM;IACpC;IAEA,qEAAqE;IACrE,MAAM,YAAY,SAAS,GAAG,CAAC,CAAA,OAAQ,CAAC;YACtC;YACA,QAAQ,QAAQ,GAAG,CAAC,QAAQ,QAAQ,GAAG,CAAC,QAAQ;YAChD,eAAe,WAAW;QAC5B,CAAC;IAED,wDAAwD;IACxD,MAAM,YAAY,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IAC/D,sDAAsD;IACtD,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,GAAG,OAAO,GAAG,qBAAqB;QAClD,IAAI,YAAY,GAAG,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,mCAAmC;QACxF,IAAI,YAAY,IAAI,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,oCAAoC;QAC1F,OAAO,KAAK,IAAI,CAAC,WAAW,OAAO,kCAAkC;IACvE;IACA,MAAM,WAAW,kBAAkB;IAMnC,+DAA+D;IAC/D,MAAM,kBAAkB,CAAC;QACvB,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,KAAK,CAAC;IAC5C;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,mDAAmD;IACnD,MAAM,gBAAgB,aAAa,YAAY,aAAa,SAAS,aAAa;IAElF,+EAA+E;IAC/E,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC,kMAAA,CAAA,UAAkB;YACjB,OAAM;YACN,aAAY;;;;;;IAGlB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4E;;;;;;sCAG1F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAa,WAAU;8BAC7C,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBACR,MAAM;wBACN,QAAQ,WACJ;4BAAE,KAAK;4BAAG,OAAO;4BAAG,MAAM;4BAAG,QAAQ;wBAAE,IACvC;4BAAE,KAAK;4BAAI,OAAO;4BAAI,MAAM;4BAAG,QAAQ;wBAAE;;0CAG7C,6LAAC;0CACC,cAAA,6LAAC;oCAAe,IAAG;oCAAa,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAI,IAAG;;sDACtD,6LAAC;4CACC,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;sDAEf,6LAAC;4CACC,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;;;;;;;;;;;;0CAInB,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,UAAU;;;;;;0CAC/C,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,YAAY;gCACZ,eAAe;;;;;;0CAEjB,6LAAC,wJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,QAAQ;oCAAC;oCAAG;iCAAS;gCACrB,eAAe;gCACf,eAAe;;;;;;0CAEjB,6LAAC,6HAAA,CAAA,eAAY;gCACX,uBACE,6LAAC,6HAAA,CAAA,sBAAmB;oCAClB,gBAAgB,CAAC,QAAU,CAAC,MAAM,EAAE,OAAO;oCAC3C,WAAW,CAAC,QAAU;4CACpB,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO;4CAC/B;yCACD;;;;;;;;;;;0CAIP,6LAAC,uJAAA,CAAA,OAAI;gCACH,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,aAAa,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GAjLwB;;QAIL,yHAAA,CAAA,cAAW;;;KAJN", "debugId": null}}, {"offset": {"line": 2101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/MonthlyVisitTrendChart.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { BarChart3, ChevronLeft, ChevronRight } from \"lucide-react\";\r\nimport { Area, AreaChart, CartesianGrid, XAxis, YAxis } from \"recharts\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"@/components/ui/select\";\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport PremiumFeatureLock from \"./PremiumFeatureLock\";\r\nimport {\r\n  ChartConfig,\r\n  ChartContainer,\r\n  ChartTooltip,\r\n  ChartTooltipContent,\r\n} from \"@/components/ui/chart\";\r\n\r\ninterface MonthlyVisitTrendChartProps {\r\n  monthlyTrend: { year: number; month: number; visits: number }[];\r\n  availableYears: number[];\r\n  userPlan?: string | null;\r\n}\r\n\r\nconst chartConfig = {\r\n  visits: {\r\n    label: \"Visits\",\r\n    color: \"var(--chart-3)\",\r\n  },\r\n} satisfies ChartConfig;\r\n\r\nexport default function MonthlyVisitTrendChart({\r\n  monthlyTrend,\r\n  availableYears,\r\n  userPlan,\r\n}: MonthlyVisitTrendChartProps) {\r\n  const isMobile = useIsMobile();\r\n  const [selectedYear, setSelectedYear] = useState<number>(\r\n    // Default to current year or the latest available year\r\n    new Date().getFullYear()\r\n  );\r\n\r\n  // Filter data for the selected year\r\n  const filteredData = monthlyTrend.filter(\r\n    (item) => item.year === selectedYear\r\n  );\r\n\r\n  // Format month number to month name\r\n  const formatMonth = (monthNum: number) => {\r\n    const monthNames = [\r\n      \"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\",\r\n      \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"\r\n    ];\r\n    return monthNames[monthNum - 1];\r\n  };\r\n\r\n  // Generate all 12 months\r\n  const generateAllMonths = () => {\r\n    const months = [];\r\n    for (let i = 1; i <= 12; i++) {\r\n      months.push(i);\r\n    }\r\n    return months;\r\n  };\r\n\r\n  // Get all months\r\n  const allMonths = generateAllMonths();\r\n\r\n  // Create a map of existing data\r\n  const dataMap = new Map();\r\n  filteredData.forEach(item => {\r\n    dataMap.set(item.month, item.visits);\r\n  });\r\n\r\n  // Format data for the chart with all months, using 0 for missing data\r\n  const chartData = allMonths.map(month => ({\r\n    month: formatMonth(month),\r\n    visits: dataMap.has(month) ? dataMap.get(month) : 0,\r\n    monthNum: month, // Keep the month number for sorting\r\n  }));\r\n\r\n  // Calculate the maximum value for better Y-axis scaling\r\n  const maxVisits = Math.max(...chartData.map(item => item.visits));\r\n  // Calculate a nice rounded upper bound for the Y-axis\r\n  const calculateYAxisMax = (maxValue: number) => {\r\n    if (maxValue <= 0) return 5; // Default if no data\r\n    if (maxValue <= 5) return Math.ceil(maxValue * 1.2); // Add 20% padding for small values\r\n    if (maxValue <= 10) return Math.ceil(maxValue * 1.1); // Add 10% padding for medium values\r\n    return Math.ceil(maxValue * 1.05); // Add 5% padding for large values\r\n  };\r\n  const yAxisMax = calculateYAxisMax(maxVisits);\r\n\r\n\r\n\r\n\r\n\r\n  // Custom formatter for Y-axis ticks using Indian number format\r\n  const formatYAxisTick = (value: number) => {\r\n    return formatIndianNumberShort(Math.floor(value));\r\n  };\r\n\r\n  // Handle year change\r\n  const handleYearChange = (value: string) => {\r\n    setSelectedYear(parseInt(value));\r\n  };\r\n\r\n  // Navigate to previous year if available\r\n  const goToPreviousYear = () => {\r\n    const availableYearsSorted = [...availableYears].sort((a, b) => a - b);\r\n    const currentIndex = availableYearsSorted.indexOf(selectedYear);\r\n    if (currentIndex > 0) {\r\n      setSelectedYear(availableYearsSorted[currentIndex - 1]);\r\n    }\r\n  };\r\n\r\n  // Navigate to next year if available\r\n  const goToNextYear = () => {\r\n    const availableYearsSorted = [...availableYears].sort((a, b) => a - b);\r\n    const currentIndex = availableYearsSorted.indexOf(selectedYear);\r\n    if (currentIndex < availableYearsSorted.length - 1) {\r\n      setSelectedYear(availableYearsSorted[currentIndex + 1]);\r\n    }\r\n  };\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        duration: 0.5,\r\n      },\r\n    },\r\n  };\r\n\r\n  // Check if user has access to this premium feature\r\n  const isPremiumUser = userPlan === \"growth\" || userPlan === \"pro\" || userPlan === \"enterprise\";\r\n\r\n  // If user doesn't have a premium plan, show the premium feature lock component\r\n  if (!isPremiumUser) {\r\n    return (\r\n      <PremiumFeatureLock\r\n        title=\"Monthly Visit Trend\"\r\n        description=\"Upgrade to Growth plan or higher to see detailed monthly visit trends for your business.\"\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      variants={containerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      className=\"rounded-lg border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black p-3 sm:p-4 md:p-6 shadow-sm\"\r\n    >\r\n      <div className=\"mb-4\">\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center justify-between gap-2\">\r\n          <h3 className=\"text-base sm:text-lg font-semibold text-neutral-900 dark:text-neutral-100\">\r\n            Monthly Visit Trend\r\n          </h3>\r\n          <div className=\"flex items-center space-x-2 text-xs sm:text-sm text-neutral-500 dark:text-neutral-400\">\r\n            <BarChart3 className=\"h-3 w-3 sm:h-4 sm:w-4\" />\r\n            <span>Unique visitors by month</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Year Selection Controls */}\r\n      <div className=\"mb-4 flex flex-col sm:flex-row items-center gap-3 sm:gap-2\">\r\n        <div className=\"flex w-full sm:w-auto justify-between gap-2 mb-3 sm:mb-0\">\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={goToPreviousYear}\r\n            disabled={availableYears.indexOf(selectedYear) === 0}\r\n            className=\"px-2 sm:px-3\"\r\n          >\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n            <span className={isMobile ? \"sr-only\" : \"ml-1\"}>Previous Year</span>\r\n          </Button>\r\n\r\n          <Select value={selectedYear.toString()} onValueChange={handleYearChange}>\r\n            <SelectTrigger className={isMobile ? \"w-[120px]\" : \"w-[180px]\"}>\r\n              <SelectValue placeholder=\"Select Year\" />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {availableYears.map((year) => (\r\n                <SelectItem key={year} value={year.toString()}>\r\n                  {year}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            onClick={goToNextYear}\r\n            disabled={availableYears.indexOf(selectedYear) === availableYears.length - 1}\r\n            className=\"px-2 sm:px-3\"\r\n          >\r\n            <span className={isMobile ? \"sr-only\" : \"mr-1\"}>Next Year</span>\r\n            <ChevronRight className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Chart */}\r\n      <div className=\"h-[250px] sm:h-[300px] w-full px-1 pb-2\">\r\n        <ChartContainer config={chartConfig} className=\"h-full w-full\">\r\n          <AreaChart\r\n            data={chartData}\r\n            margin={isMobile\r\n              ? { top: 5, right: 5, left: 0, bottom: 5 }\r\n              : { top: 10, right: 10, left: 0, bottom: 5 }\r\n            }\r\n          >\r\n            <defs>\r\n              <linearGradient id=\"fillVisits\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\r\n                <stop\r\n                  offset=\"5%\"\r\n                  stopColor=\"var(--color-visits)\"\r\n                  stopOpacity={0.8}\r\n                />\r\n                <stop\r\n                  offset=\"95%\"\r\n                  stopColor=\"var(--color-visits)\"\r\n                  stopOpacity={0.1}\r\n                />\r\n              </linearGradient>\r\n            </defs>\r\n            <CartesianGrid strokeDasharray=\"3 3\" vertical={false} />\r\n            <XAxis\r\n              dataKey=\"month\"\r\n              axisLine={false}\r\n              tickLine={false}\r\n              tickMargin={8}\r\n              minTickGap={32}\r\n            />\r\n            <YAxis\r\n              axisLine={false}\r\n              tickLine={false}\r\n              domain={[0, yAxisMax]}\r\n              allowDecimals={false}\r\n              tickFormatter={formatYAxisTick}\r\n            />\r\n            <ChartTooltip\r\n              content={\r\n                <ChartTooltipContent\r\n                  labelFormatter={(label) => `Month: ${label}`}\r\n                  formatter={(value) => [\r\n                    formatIndianNumberShort(Number(value)),\r\n                    \" Visits\"\r\n                  ]}\r\n                />\r\n              }\r\n            />\r\n            <Area\r\n              dataKey=\"visits\"\r\n              type=\"natural\"\r\n              fill=\"url(#fillVisits)\"\r\n              stroke=\"var(--color-visits)\"\r\n              strokeWidth={isMobile ? 1.5 : 2}\r\n            />\r\n          </AreaChart>\r\n        </ChartContainer>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAwBA,MAAM,cAAc;IAClB,QAAQ;QACN,OAAO;QACP,OAAO;IACT;AACF;AAEe,SAAS,uBAAuB,EAC7C,YAAY,EACZ,cAAc,EACd,QAAQ,EACoB;;IAC5B,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7C,uDAAuD;IACvD,IAAI,OAAO,WAAW;IAGxB,oCAAoC;IACpC,MAAM,eAAe,aAAa,MAAM,CACtC,CAAC,OAAS,KAAK,IAAI,KAAK;IAG1B,oCAAoC;IACpC,MAAM,cAAc,CAAC;QACnB,MAAM,aAAa;YACjB;YAAO;YAAO;YAAO;YAAO;YAAO;YACnC;YAAO;YAAO;YAAO;YAAO;YAAO;SACpC;QACD,OAAO,UAAU,CAAC,WAAW,EAAE;IACjC;IAEA,yBAAyB;IACzB,MAAM,oBAAoB;QACxB,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAK;YAC5B,OAAO,IAAI,CAAC;QACd;QACA,OAAO;IACT;IAEA,iBAAiB;IACjB,MAAM,YAAY;IAElB,gCAAgC;IAChC,MAAM,UAAU,IAAI;IACpB,aAAa,OAAO,CAAC,CAAA;QACnB,QAAQ,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM;IACrC;IAEA,sEAAsE;IACtE,MAAM,YAAY,UAAU,GAAG,CAAC,CAAA,QAAS,CAAC;YACxC,OAAO,YAAY;YACnB,QAAQ,QAAQ,GAAG,CAAC,SAAS,QAAQ,GAAG,CAAC,SAAS;YAClD,UAAU;QACZ,CAAC;IAED,wDAAwD;IACxD,MAAM,YAAY,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IAC/D,sDAAsD;IACtD,MAAM,oBAAoB,CAAC;QACzB,IAAI,YAAY,GAAG,OAAO,GAAG,qBAAqB;QAClD,IAAI,YAAY,GAAG,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,mCAAmC;QACxF,IAAI,YAAY,IAAI,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,oCAAoC;QAC1F,OAAO,KAAK,IAAI,CAAC,WAAW,OAAO,kCAAkC;IACvE;IACA,MAAM,WAAW,kBAAkB;IAMnC,+DAA+D;IAC/D,MAAM,kBAAkB,CAAC;QACvB,OAAO,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,KAAK,CAAC;IAC5C;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,gBAAgB,SAAS;IAC3B;IAEA,yCAAyC;IACzC,MAAM,mBAAmB;QACvB,MAAM,uBAAuB;eAAI;SAAe,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACpE,MAAM,eAAe,qBAAqB,OAAO,CAAC;QAClD,IAAI,eAAe,GAAG;YACpB,gBAAgB,oBAAoB,CAAC,eAAe,EAAE;QACxD;IACF;IAEA,qCAAqC;IACrC,MAAM,eAAe;QACnB,MAAM,uBAAuB;eAAI;SAAe,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACpE,MAAM,eAAe,qBAAqB,OAAO,CAAC;QAClD,IAAI,eAAe,qBAAqB,MAAM,GAAG,GAAG;YAClD,gBAAgB,oBAAoB,CAAC,eAAe,EAAE;QACxD;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,mDAAmD;IACnD,MAAM,gBAAgB,aAAa,YAAY,aAAa,SAAS,aAAa;IAElF,+EAA+E;IAC/E,IAAI,CAAC,eAAe;QAClB,qBACE,6LAAC,kMAAA,CAAA,UAAkB;YACjB,OAAM;YACN,aAAY;;;;;;IAGlB;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA4E;;;;;;sCAG1F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,eAAe,OAAO,CAAC,kBAAkB;4BACnD,WAAU;;8CAEV,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAW,WAAW,YAAY;8CAAQ;;;;;;;;;;;;sCAGlD,6LAAC,8HAAA,CAAA,SAAM;4BAAC,OAAO,aAAa,QAAQ;4BAAI,eAAe;;8CACrD,6LAAC,8HAAA,CAAA,gBAAa;oCAAC,WAAW,WAAW,cAAc;8CACjD,cAAA,6LAAC,8HAAA,CAAA,cAAW;wCAAC,aAAY;;;;;;;;;;;8CAE3B,6LAAC,8HAAA,CAAA,gBAAa;8CACX,eAAe,GAAG,CAAC,CAAC,qBACnB,6LAAC,8HAAA,CAAA,aAAU;4CAAY,OAAO,KAAK,QAAQ;sDACxC;2CADc;;;;;;;;;;;;;;;;sCAOvB,6LAAC,8HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,UAAU,eAAe,OAAO,CAAC,kBAAkB,eAAe,MAAM,GAAG;4BAC3E,WAAU;;8CAEV,6LAAC;oCAAK,WAAW,WAAW,YAAY;8CAAQ;;;;;;8CAChD,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,iBAAc;oBAAC,QAAQ;oBAAa,WAAU;8BAC7C,cAAA,6LAAC,wJAAA,CAAA,YAAS;wBACR,MAAM;wBACN,QAAQ,WACJ;4BAAE,KAAK;4BAAG,OAAO;4BAAG,MAAM;4BAAG,QAAQ;wBAAE,IACvC;4BAAE,KAAK;4BAAI,OAAO;4BAAI,MAAM;4BAAG,QAAQ;wBAAE;;0CAG7C,6LAAC;0CACC,cAAA,6LAAC;oCAAe,IAAG;oCAAa,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAI,IAAG;;sDACtD,6LAAC;4CACC,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;sDAEf,6LAAC;4CACC,QAAO;4CACP,WAAU;4CACV,aAAa;;;;;;;;;;;;;;;;;0CAInB,6LAAC,gKAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,UAAU;;;;;;0CAC/C,6LAAC,wJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,UAAU;gCACV,UAAU;gCACV,YAAY;gCACZ,YAAY;;;;;;0CAEd,6LAAC,wJAAA,CAAA,QAAK;gCACJ,UAAU;gCACV,UAAU;gCACV,QAAQ;oCAAC;oCAAG;iCAAS;gCACrB,eAAe;gCACf,eAAe;;;;;;0CAEjB,6LAAC,6HAAA,CAAA,eAAY;gCACX,uBACE,6LAAC,6HAAA,CAAA,sBAAmB;oCAClB,gBAAgB,CAAC,QAAU,CAAC,OAAO,EAAE,OAAO;oCAC5C,WAAW,CAAC,QAAU;4CACpB,CAAA,GAAA,+GAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO;4CAC/B;yCACD;;;;;;;;;;;0CAIP,6LAAC,uJAAA,CAAA,OAAI;gCACH,SAAQ;gCACR,MAAK;gCACL,MAAK;gCACL,QAAO;gCACP,aAAa,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;GA9OwB;;QAKL,yHAAA,CAAA,cAAW;;;KALN", "debugId": null}}, {"offset": {"line": 2569, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/analytics/components/EnhancedAnalyticsPageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n/**\r\n * Business Analytics Page Client Component\r\n *\r\n * This component displays real-time analytics for a business profile, including:\r\n * - Visitor metrics (total, today, yesterday, 7-day, 30-day, monthly)\r\n * - Engagement metrics (likes, subscriptions, ratings)\r\n * - Visit trends (daily, hourly, and monthly)\r\n *\r\n * Database Implementation:\r\n *\r\n * 1. Database Tables:\r\n *    - card_visits: Records individual card visits with visitor_identifier and business_profile_id\r\n *    - business_profiles: Stores pre-aggregated metrics (total_visits, today_visits, etc.)\r\n *    - monthly_visit_metrics: Stores monthly unique visits data with year and month\r\n *\r\n * 2. Timezone Handling:\r\n *    - All visit metrics are calculated based on IST (Indian Standard Time, UTC+5:30)\r\n *    - The visited_at column in card_visits stores timestamps in UTC\r\n *    - All database functions convert these timestamps to IST using 'visited_at AT TIME ZONE 'Asia/Kolkata''\r\n *    - The update_visit_counts() function uses the most recent visit date in IST as \"today\"\r\n *    - This ensures accurate metrics regardless of when the functions run\r\n *\r\n * 3. Database Functions:\r\n *    - update_visit_counts(): Updates all visit metrics when a new visit is recorded\r\n *      This function calculates total_visits, today_visits, yesterday_visits, visits_7_days,\r\n *      and visits_30_days based on the card_visits table, using the most recent visit date in IST as \"today\".\r\n *\r\n *    - reset_daily_visit_counts(): Resets today's counts and shifts today's to yesterday's at midnight IST\r\n *      This function is called by a scheduled cron job at midnight IST.\r\n *\r\n *    - update_period_visit_counts(): Updates the 7-day and 30-day visit counts\r\n *      This function is called by a scheduled cron job at midnight IST.\r\n *\r\n *    - reset_all_visit_counts(): Wrapper function that calls both reset_daily_visit_counts()\r\n *      and update_period_visit_counts()\r\n *\r\n *    - update_monthly_visit_counts(): Updates monthly visit metrics when a new visit is recorded\r\n *      This function calculates unique visits for the current month based on the card_visits table.\r\n *\r\n *    - update_all_monthly_visit_metrics(): Updates monthly visit metrics for all businesses\r\n *      This function is called by a scheduled cron job on the 1st day of each month.\r\n *\r\n *    - populate_historical_monthly_data(): Populates historical monthly data from existing card_visits records\r\n *      This function is used for initial data migration and can be run manually.\r\n *\r\n *    - get_total_unique_visits(): Gets the total number of unique visitors for a business\r\n *    - get_daily_unique_visit_trend(): Gets the daily trend of unique visitors for a date range based on IST dates\r\n *    - get_hourly_unique_visit_trend(): Gets the hourly trend of unique visitors for a specific date based on IST hours\r\n *    - get_monthly_unique_visits(): Gets monthly unique visits for a specific business, month, and year\r\n *    - get_monthly_unique_visit_trend(): Gets monthly trend data for a date range\r\n *    - get_available_years_for_monthly_metrics(): Gets available years from monthly_visit_metrics table\r\n *\r\n * 4. Database Triggers:\r\n *    - handle_new_visit: Trigger on card_visits table that calls update_visit_counts()\r\n *      when a new visit is recorded\r\n *    - handle_new_visit_monthly: Trigger on card_visits table that calls update_monthly_visit_counts()\r\n *      when a new visit is recorded\r\n *\r\n * 5. Scheduled Jobs:\r\n *    - reset-visit-counts: A cron job runs reset_all_visit_counts() at midnight IST (6:30 PM UTC)\r\n *      Schedule: '30 18 * * *' (6:30 PM UTC daily)\r\n *    - clean-card-visits: A cron job runs clean_old_card_visits() at 1 AM IST (7:30 PM UTC)\r\n *      Schedule: '30 19 * * *' (7:30 PM UTC daily)\r\n *      This job deletes card_visits records older than 31 days to maintain database performance\r\n *    - update-monthly-visit-metrics: A cron job runs update_all_monthly_visit_metrics() at midnight IST on the 1st day of each month\r\n *      Schedule: '30 18 1 * *' (6:30 PM UTC on the 1st day of each month)\r\n *\r\n * 6. Visit Tracking:\r\n *    - Visits are tracked using a VisitTracker component on the public card page\r\n *    - Each visitor gets a unique identifier stored in localStorage\r\n *    - Only cards with \"online\" status are tracked\r\n *    - Both authenticated and non-authenticated users are tracked\r\n *\r\n * 7. Row-Level Security:\r\n *    - All tables have RLS policies that allow users to perform CRUD operations only on their own data\r\n *    - The business_profile_id field is used to match against auth.uid() for access control\r\n *\r\n * Real-time Updates:\r\n * - The component uses realtime database for all metrics:\r\n *   - Total unique visits, today's visits, yesterday's visits, 7-day visits, and 30-day visits from business_profiles table\r\n *   - Total likes, total subscriptions, and average rating from business_profiles table\r\n *   - Monthly unique visits from monthly_visit_metrics table\r\n * - When changes occur in any of these tables, the UI updates automatically with animations\r\n * - Note: card_visits table does not have realtime enabled; instead, we rely on the business_profiles table\r\n *   which has pre-aggregated metrics that are updated by triggers when new visits are recorded\r\n */\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { BarChart3 } from \"lucide-react\";\r\nimport { realtimeService } from \"@/lib/services/realtimeService\";\r\nimport EnhancedEngagementMetricsSection from \"./EnhancedEngagementMetricsSection\";\r\nimport EnhancedVisitMetricsSection from \"./EnhancedVisitMetricsSection\";\r\nimport DailyVisitTrendChart from \"./DailyVisitTrendChart\";\r\nimport HourlyVisitTrendChart from \"./HourlyVisitTrendChart\";\r\nimport MonthlyVisitTrendChart from \"./MonthlyVisitTrendChart\";\r\n// Removed unused import: AnalyticsBackground\r\n\r\ninterface AnalyticsData {\r\n  totalUniqueVisits: number;\r\n  todayUniqueVisits: number;\r\n  yesterdayUniqueVisits: number;\r\n  visits7Days: number;\r\n  visits30Days: number;\r\n  currentMonthUniqueVisits: number;\r\n  previousMonthUniqueVisits: number;\r\n  currentYear: number;\r\n  currentMonth: number;\r\n  dailyTrend7Days: { date: string; visits: number }[];\r\n  dailyTrend30Days: { date: string; visits: number }[];\r\n  hourlyTrendToday: { hour: number; visits: number }[];\r\n  monthlyTrend: { year: number; month: number; visits: number }[];\r\n  availableYears: number[];\r\n}\r\n\r\ninterface BusinessProfile {\r\n  total_likes: number;\r\n  total_subscriptions: number;\r\n  average_rating: number;\r\n  total_visits: number;\r\n  today_visits: number;\r\n  yesterday_visits: number;\r\n  visits_7_days: number;\r\n  visits_30_days: number;\r\n}\r\n\r\ninterface EnhancedAnalyticsPageClientProps {\r\n  analyticsData: AnalyticsData;\r\n  userId: string;\r\n  initialProfile: BusinessProfile;\r\n  userPlan: string | null;\r\n}\r\n\r\nexport default function EnhancedAnalyticsPageClient({\r\n  analyticsData,\r\n  userId,\r\n  initialProfile,\r\n  userPlan,\r\n}: EnhancedAnalyticsPageClientProps) {\r\n  // State for real-time profile data\r\n  const [profile, setProfile] = useState<BusinessProfile>(initialProfile);\r\n\r\n  // Ref to store current profile values for comparison without triggering re-renders\r\n  const profileRef = useRef<BusinessProfile>(initialProfile);\r\n\r\n  // Sync profileRef with initialProfile when it changes\r\n  useEffect(() => {\r\n    profileRef.current = initialProfile;\r\n    setProfile(initialProfile);\r\n  }, [initialProfile]);\r\n\r\n  // State for animated counters\r\n  const [isVisitUpdated, setIsVisitUpdated] = useState(false);\r\n\r\n  // Animation variants\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n      },\r\n    },\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { opacity: 0, y: 20 },\r\n    visible: {\r\n      opacity: 1,\r\n      y: 0,\r\n      transition: {\r\n        type: \"spring\",\r\n        stiffness: 300,\r\n        damping: 24,\r\n      }\r\n    },\r\n  };\r\n\r\n  /**\r\n   * Set up Supabase real-time subscriptions\r\n   *\r\n   * This effect sets up real-time subscriptions to:\r\n   * 1. The business_profiles table for all general metrics:\r\n   *    - total_likes, total_subscriptions, average_rating (engagement metrics)\r\n   *    - total_visits, today_visits, yesterday_visits, visits_7_days, visits_30_days (visit metrics)\r\n   * 2. The monthly_visit_metrics table for monthly visit data\r\n   *\r\n   * Note: We don't subscribe to card_visits table as it doesn't have realtime enabled.\r\n   * Instead, we rely on the business_profiles table which has pre-aggregated metrics\r\n   * that are updated by triggers when new visits are recorded.\r\n   *\r\n   * When changes are detected:\r\n   * 1. The UI is updated with the new values\r\n   * 2. Animations are triggered to highlight the changes\r\n   */\r\n  useEffect(() => {\r\n    console.log(\"Setting up real-time subscriptions for user:\", userId);\r\n    console.log(\"Initial profile data:\", initialProfile);\r\n\r\n    // Set up real-time subscription to business_profiles table\r\n    const profileSubscription = realtimeService.subscribeToBusinessProfile(\r\n      userId,\r\n      (payload) => {\r\n        console.log(\"Real-time update received:\", payload);\r\n        const newData = payload.new as BusinessProfile;\r\n        if (newData) {\r\n          console.log(\"Current profile:\", profileRef.current);\r\n          console.log(\"New profile data:\", newData);\r\n\r\n          // Always create a new profile object to ensure React detects the change\r\n          const updatedProfile = {\r\n            total_likes: newData.total_likes ?? profileRef.current.total_likes ?? 0,\r\n            total_subscriptions: newData.total_subscriptions ?? profileRef.current.total_subscriptions ?? 0,\r\n            average_rating: newData.average_rating ?? profileRef.current.average_rating ?? 0,\r\n            total_visits: newData.total_visits ?? profileRef.current.total_visits ?? 0,\r\n            today_visits: newData.today_visits ?? profileRef.current.today_visits ?? 0,\r\n            yesterday_visits: newData.yesterday_visits ?? profileRef.current.yesterday_visits ?? 0,\r\n            visits_7_days: newData.visits_7_days ?? profileRef.current.visits_7_days ?? 0,\r\n            visits_30_days: newData.visits_30_days ?? profileRef.current.visits_30_days ?? 0,\r\n          };\r\n\r\n          // Check if visit metrics have changed\r\n          const visitMetricsChanged =\r\n            newData.total_visits !== profileRef.current.total_visits ||\r\n            newData.today_visits !== profileRef.current.today_visits ||\r\n            newData.yesterday_visits !== profileRef.current.yesterday_visits ||\r\n            newData.visits_7_days !== profileRef.current.visits_7_days ||\r\n            newData.visits_30_days !== profileRef.current.visits_30_days;\r\n\r\n          // Trigger animation for visit updates\r\n          if (visitMetricsChanged) {\r\n            console.log(\"Visit metrics changed, triggering animation\");\r\n            setIsVisitUpdated(true);\r\n            setTimeout(() => setIsVisitUpdated(false), 500);\r\n          }\r\n\r\n          // Always update both the state and the ref\r\n          console.log(\"Updating profile state:\", updatedProfile);\r\n          setProfile(updatedProfile);\r\n          profileRef.current = updatedProfile;\r\n        }\r\n      },\r\n      'analytics'\r\n    );\r\n\r\n    // Set up real-time subscription to monthly_visit_metrics table\r\n    const monthlyMetricsSubscription = realtimeService.subscribeToMonthlyMetrics(\r\n      userId,\r\n      (payload) => {\r\n        console.log(\"Monthly metrics update received:\", payload);\r\n\r\n        // When monthly metrics change, trigger visit update animation\r\n        setIsVisitUpdated(true);\r\n        setTimeout(() => setIsVisitUpdated(false), 500);\r\n\r\n        // Refresh analytics data (this would ideally be implemented with a server action)\r\n        // For now, we'll rely on the business_profiles subscription for updates\r\n      },\r\n      'analytics'\r\n    );\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      profileSubscription.unsubscribe();\r\n      monthlyMetricsSubscription.unsubscribe();\r\n    };\r\n  }, [userId, initialProfile]);\r\n\r\n  return (\r\n    <motion.div\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      variants={containerVariants}\r\n      className=\"space-y-8\"\r\n    >\r\n      {/* Analytics Header - Full Width */}\r\n      <div className=\"flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6\">\r\n        <div className=\"p-3 rounded-xl bg-muted hidden sm:block\">\r\n          <BarChart3 className=\"w-6 h-6 text-foreground\" />\r\n        </div>\r\n        <div className=\"flex-1\">\r\n          <h1 className=\"text-2xl font-bold text-foreground\">\r\n            Business Analytics\r\n          </h1>\r\n          <p className=\"text-muted-foreground mt-1\">\r\n            Comprehensive insights into your business card performance\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Engagement Metrics Section - Full Width */}\r\n      <motion.div variants={itemVariants}>\r\n        <EnhancedEngagementMetricsSection\r\n          profile={profile}\r\n          initialProfile={initialProfile}\r\n        />\r\n      </motion.div>\r\n\r\n      {/* Visitor Metrics Section - Full Width */}\r\n      <motion.div variants={itemVariants}>\r\n        <EnhancedVisitMetricsSection\r\n          totalUniqueVisits={profile.total_visits}\r\n          todayUniqueVisits={profile.today_visits}\r\n          yesterdayUniqueVisits={profile.yesterday_visits}\r\n          currentMonthUniqueVisits={analyticsData.currentMonthUniqueVisits}\r\n          isVisitUpdated={isVisitUpdated}\r\n        />\r\n      </motion.div>\r\n\r\n      {/* Charts Section - Full Width */}\r\n      <motion.div variants={itemVariants}>\r\n        <div className=\"grid gap-6 grid-cols-1 lg:grid-cols-2\">\r\n          {/* Daily Visit Trend Chart */}\r\n          <DailyVisitTrendChart\r\n            trend7Days={analyticsData.dailyTrend7Days || []}\r\n            trend30Days={analyticsData.dailyTrend30Days || []}\r\n            userPlan={userPlan}\r\n          />\r\n\r\n          {/* Hourly Visit Trend Chart */}\r\n          <HourlyVisitTrendChart\r\n            data={analyticsData.hourlyTrendToday || []}\r\n            userPlan={userPlan}\r\n          />\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Monthly Trend Chart - Full Width */}\r\n      <motion.div variants={itemVariants}>\r\n        <MonthlyVisitTrendChart\r\n          monthlyTrend={analyticsData.monthlyTrend || []}\r\n          availableYears={analyticsData.availableYears || []}\r\n          userPlan={userPlan}\r\n        />\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAqFC,GAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjGA;;;;;;;;;;AAuIe,SAAS,4BAA4B,EAClD,aAAa,EACb,MAAM,EACN,cAAc,EACd,QAAQ,EACyB;;IACjC,mCAAmC;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAExD,mFAAmF;IACnF,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IAE3C,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iDAAE;YACR,WAAW,OAAO,GAAG;YACrB,WAAW;QACb;gDAAG;QAAC;KAAe;IAEnB,8BAA8B;IAC9B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBAAqB;IACrB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA;;;;;;;;;;;;;;;;GAgBC,GACD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iDAAE;YACR,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,QAAQ,GAAG,CAAC,yBAAyB;YAErC,2DAA2D;YAC3D,MAAM,sBAAsB,qIAAA,CAAA,kBAAe,CAAC,0BAA0B,CACpE;6EACA,CAAC;oBACC,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,MAAM,UAAU,QAAQ,GAAG;oBAC3B,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC,oBAAoB,WAAW,OAAO;wBAClD,QAAQ,GAAG,CAAC,qBAAqB;wBAEjC,wEAAwE;wBACxE,MAAM,iBAAiB;4BACrB,aAAa,QAAQ,WAAW,IAAI,WAAW,OAAO,CAAC,WAAW,IAAI;4BACtE,qBAAqB,QAAQ,mBAAmB,IAAI,WAAW,OAAO,CAAC,mBAAmB,IAAI;4BAC9F,gBAAgB,QAAQ,cAAc,IAAI,WAAW,OAAO,CAAC,cAAc,IAAI;4BAC/E,cAAc,QAAQ,YAAY,IAAI,WAAW,OAAO,CAAC,YAAY,IAAI;4BACzE,cAAc,QAAQ,YAAY,IAAI,WAAW,OAAO,CAAC,YAAY,IAAI;4BACzE,kBAAkB,QAAQ,gBAAgB,IAAI,WAAW,OAAO,CAAC,gBAAgB,IAAI;4BACrF,eAAe,QAAQ,aAAa,IAAI,WAAW,OAAO,CAAC,aAAa,IAAI;4BAC5E,gBAAgB,QAAQ,cAAc,IAAI,WAAW,OAAO,CAAC,cAAc,IAAI;wBACjF;wBAEA,sCAAsC;wBACtC,MAAM,sBACJ,QAAQ,YAAY,KAAK,WAAW,OAAO,CAAC,YAAY,IACxD,QAAQ,YAAY,KAAK,WAAW,OAAO,CAAC,YAAY,IACxD,QAAQ,gBAAgB,KAAK,WAAW,OAAO,CAAC,gBAAgB,IAChE,QAAQ,aAAa,KAAK,WAAW,OAAO,CAAC,aAAa,IAC1D,QAAQ,cAAc,KAAK,WAAW,OAAO,CAAC,cAAc;wBAE9D,sCAAsC;wBACtC,IAAI,qBAAqB;4BACvB,QAAQ,GAAG,CAAC;4BACZ,kBAAkB;4BAClB;6FAAW,IAAM,kBAAkB;4FAAQ;wBAC7C;wBAEA,2CAA2C;wBAC3C,QAAQ,GAAG,CAAC,2BAA2B;wBACvC,WAAW;wBACX,WAAW,OAAO,GAAG;oBACvB;gBACF;4EACA;YAGF,+DAA+D;YAC/D,MAAM,6BAA6B,qIAAA,CAAA,kBAAe,CAAC,yBAAyB,CAC1E;oFACA,CAAC;oBACC,QAAQ,GAAG,CAAC,oCAAoC;oBAEhD,8DAA8D;oBAC9D,kBAAkB;oBAClB;4FAAW,IAAM,kBAAkB;2FAAQ;gBAE3C,kFAAkF;gBAClF,wEAAwE;gBAC1E;mFACA;YAGF,mBAAmB;YACnB;yDAAO;oBACL,oBAAoB,WAAW;oBAC/B,2BAA2B,WAAW;gBACxC;;QACF;gDAAG;QAAC;QAAQ;KAAe;IAE3B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAQ;QACR,SAAQ;QACR,UAAU;QACV,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CAGnD,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,6LAAC,gNAAA,CAAA,UAAgC;oBAC/B,SAAS;oBACT,gBAAgB;;;;;;;;;;;0BAKpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,6LAAC,2MAAA,CAAA,UAA2B;oBAC1B,mBAAmB,QAAQ,YAAY;oBACvC,mBAAmB,QAAQ,YAAY;oBACvC,uBAAuB,QAAQ,gBAAgB;oBAC/C,0BAA0B,cAAc,wBAAwB;oBAChE,gBAAgB;;;;;;;;;;;0BAKpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,oMAAA,CAAA,UAAoB;4BACnB,YAAY,cAAc,eAAe,IAAI,EAAE;4BAC/C,aAAa,cAAc,gBAAgB,IAAI,EAAE;4BACjD,UAAU;;;;;;sCAIZ,6LAAC,qMAAA,CAAA,UAAqB;4BACpB,MAAM,cAAc,gBAAgB,IAAI,EAAE;4BAC1C,UAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,6LAAC,sMAAA,CAAA,UAAsB;oBACrB,cAAc,cAAc,YAAY,IAAI,EAAE;oBAC9C,gBAAgB,cAAc,cAAc,IAAI,EAAE;oBAClD,UAAU;;;;;;;;;;;;;;;;;AAKpB;GA5MwB;KAAA", "debugId": null}}]}