module.exports = {

"[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Subscription status constants - IMMUTABLE SINGLE SOURCE OF TRUTH
__turbopack_context__.s({
    "PLAN_IDS": (()=>PLAN_IDS),
    "SUBSCRIPTION_STATUS": (()=>SUBSCRIPTION_STATUS)
});
const SUBSCRIPTION_STATUS = {
    ACTIVE: 'active',
    AUTHENTICATED: 'authenticated',
    TRIAL: 'trial',
    PENDING: 'pending',
    HALTED: 'halted',
    CANCELLED: 'cancelled',
    EXPIRED: 'expired',
    COMPLETED: 'completed',
    PAYMENT_FAILED: 'payment_failed',
    CANCELLATION_SCHEDULED: 'cancellation_scheduled'
};
const PLAN_IDS = {
    FREE: 'free',
    BASIC: 'basic',
    GROWTH: 'growth',
    PRO: 'pro',
    ENTERPRISE: 'enterprise'
};
}}),
"[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "SubscriptionStateManager": (()=>SubscriptionStateManager),
    "isActivePaidSubscription": (()=>isActivePaidSubscription),
    "isFreeStatus": (()=>isFreeStatus),
    "isTerminalStatus": (()=>isTerminalStatus),
    "isTrialStatus": (()=>isTrialStatus),
    "shouldHaveActiveSubscription": (()=>shouldHaveActiveSubscription),
    "shouldHaveActiveSubscriptionByPlan": (()=>shouldHaveActiveSubscriptionByPlan)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-rsc] (ecmascript)");
;
class SubscriptionStateManager {
    /**
   * MASTER FUNCTION: Determines if a user should have active subscription access
   * This is the ONLY function that should be used for subscription access control
   *
   * @param status The subscription status
   * @param planId The plan ID (required for accurate determination)
   * @returns true if the user should have active subscription features
   */ static shouldHaveActiveSubscription(status, planId = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
        // Free plan users NEVER have "active subscription" - they're on free tier
        if (planId === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
            return false;
        }
        // Trial users NEVER have "active subscription" - they're testing, not paying
        if (status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL) {
            return false;
        }
        // Only ACTIVE status on PAID plans counts as "active subscription"
        // Authenticated users have selected a plan but haven't paid yet
        const activeStatuses = [
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].ACTIVE
        ];
        return activeStatuses.includes(status);
    }
    /**
   * Determines if a subscription status is considered terminal (final state)
   * Terminal states cannot transition to active states without creating new subscription
   */ static isTerminalStatus(status) {
        const terminalStatuses = [
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].CANCELLED,
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].EXPIRED,
            __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].COMPLETED
        ];
        return terminalStatuses.includes(status);
    }
    /**
   * Determines if a subscription status indicates trial period
   */ static isTrialStatus(status) {
        return status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL;
    }
    /**
   * Determines if a plan is a free plan
   */ static isFreeStatus(status, planId) {
        return planId === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE || status === 'free';
    }
    /**
   * Get user's access level based on subscription state
   */ static getAccessLevel(status, planId = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
        if (planId === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) return 'free';
        if (status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL) return 'trial';
        if (this.shouldHaveActiveSubscription(status, planId)) return 'paid';
        return 'free';
    }
    /**
   * Determines if a subscription status indicates an active paid subscription
   */ static isActivePaidSubscription(status, planId = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE) {
        return this.shouldHaveActiveSubscription(status, planId);
    }
    /**
   * Validates if a status transition is allowed
   */ static isValidStatusTransition(fromStatus, toStatus) {
        // Terminal states cannot transition to non-terminal states
        if (this.isTerminalStatus(fromStatus) && !this.isTerminalStatus(toStatus)) {
            return false;
        }
        // All other transitions are allowed
        return true;
    }
}
function shouldHaveActiveSubscription(status) {
    console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');
    return SubscriptionStateManager.shouldHaveActiveSubscription(status, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE);
}
function isTerminalStatus(status) {
    return SubscriptionStateManager.isTerminalStatus(status);
}
function isActivePaidSubscription(status) {
    console.warn('[DEPRECATED] Use SubscriptionStateManager.isActivePaidSubscription(status, planId) instead');
    return SubscriptionStateManager.isActivePaidSubscription(status, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"].FREE);
}
function isTrialStatus(status) {
    return SubscriptionStateManager.isTrialStatus(status);
}
function isFreeStatus(status, planId) {
    return SubscriptionStateManager.isFreeStatus(status, planId);
}
function shouldHaveActiveSubscriptionByPlan(status, planId) {
    console.warn('[DEPRECATED] Use SubscriptionStateManager.shouldHaveActiveSubscription(status, planId) instead');
    return SubscriptionStateManager.shouldHaveActiveSubscription(status, planId);
}
}}),
"[project]/lib/razorpay/webhooks/handlers/subscription-state-validator.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "validateAndFixSubscriptionState": (()=>validateAndFixSubscriptionState),
    "validateSubscriptionState": (()=>validateSubscriptionState)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
;
;
;
function validateSubscriptionState(businessProfile, paymentSubscription) {
    const warnings = [];
    const errors = [];
    // Determine expected state based on payment subscription using centralized manager
    let expectedHasActiveSubscription = false;
    let accessLevel = 'free';
    if (paymentSubscription) {
        expectedHasActiveSubscription = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SubscriptionStateManager"].shouldHaveActiveSubscription(paymentSubscription.subscription_status, paymentSubscription.plan_id);
        accessLevel = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SubscriptionStateManager"].getAccessLevel(paymentSubscription.subscription_status, paymentSubscription.plan_id);
    }
    // Check for inconsistencies
    if (businessProfile.has_active_subscription !== expectedHasActiveSubscription) {
        errors.push(`has_active_subscription mismatch: business_profiles=${businessProfile.has_active_subscription}, expected=${expectedHasActiveSubscription}`);
    }
    // Check trial state consistency
    if (paymentSubscription?.subscription_status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL) {
        if (!businessProfile.trial_end_date) {
            warnings.push('Trial status but no trial_end_date set');
        } else {
            const trialEnd = new Date(businessProfile.trial_end_date);
            const now = new Date();
            if (trialEnd <= now) {
                warnings.push('Trial status but trial period has expired');
            }
        }
    }
    return {
        isValid: errors.length === 0,
        hasActiveSubscription: expectedHasActiveSubscription,
        accessLevel,
        warnings,
        errors
    };
}
async function validateAndFixSubscriptionState(businessProfileId) {
    try {
        const adminClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Get current state from both tables
        const [profileResult, subscriptionResult] = await Promise.all([
            adminClient.from('business_profiles').select('has_active_subscription, trial_end_date').eq('id', businessProfileId).maybeSingle(),
            adminClient.from('payment_subscriptions').select('subscription_status, plan_id').eq('business_profile_id', businessProfileId).order('created_at', {
                ascending: false
            }).limit(1).maybeSingle()
        ]);
        if (profileResult.error) {
            return {
                success: false,
                message: `Profile fetch error: ${profileResult.error.message}`,
                fixed: false
            };
        }
        if (!profileResult.data) {
            return {
                success: false,
                message: 'Business profile not found',
                fixed: false
            };
        }
        // Use centralized validation
        const validation = validateSubscriptionState(profileResult.data, subscriptionResult.data);
        if (validation.isValid) {
            return {
                success: true,
                message: 'Subscription state is consistent',
                fixed: false
            };
        }
        // Fix inconsistencies
        const expectedHasActiveSubscription = validation.hasActiveSubscription;
        if (profileResult.data.has_active_subscription !== expectedHasActiveSubscription) {
            const { error: fixError } = await adminClient.from('business_profiles').update({
                has_active_subscription: expectedHasActiveSubscription,
                updated_at: new Date().toISOString()
            }).eq('id', businessProfileId);
            if (fixError) {
                return {
                    success: false,
                    message: `Failed to fix inconsistency: ${fixError.message}`,
                    fixed: false
                };
            }
            console.log(`[STATE_VALIDATOR] Fixed has_active_subscription for ${businessProfileId}: ${profileResult.data.has_active_subscription} -> ${expectedHasActiveSubscription}`);
            return {
                success: true,
                message: `Fixed subscription state inconsistency`,
                fixed: true
            };
        }
        return {
            success: true,
            message: 'No fixes needed',
            fixed: false
        };
    } catch (error) {
        console.error(`[STATE_VALIDATOR] Exception:`, error);
        return {
            success: false,
            message: `Validation exception: ${error instanceof Error ? error.message : String(error)}`,
            fixed: false
        };
    }
}
}}),
"[project]/lib/razorpay/webhooks/types.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Razorpay Subscription Webhook Event Types
 *
 * This file contains the types for Razorpay subscription webhook events.
 */ // Subscription webhook event types
__turbopack_context__.s({
    "RazorpayPaymentStatus": (()=>RazorpayPaymentStatus),
    "RazorpayRefundStatus": (()=>RazorpayRefundStatus),
    "RazorpaySubscriptionEventType": (()=>RazorpaySubscriptionEventType),
    "RazorpaySubscriptionStatus": (()=>RazorpaySubscriptionStatus),
    "SupabaseSubscriptionStatus": (()=>SupabaseSubscriptionStatus),
    "mapRazorpayStatusToSupabase": (()=>mapRazorpayStatusToSupabase)
});
var RazorpaySubscriptionEventType = /*#__PURE__*/ function(RazorpaySubscriptionEventType) {
    // Subscription events
    RazorpaySubscriptionEventType["_SUBSCRIPTION_AUTHENTICATED"] = "subscription.authenticated";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_ACTIVATED"] = "subscription.activated";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_CHARGED"] = "subscription.charged";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_PENDING"] = "subscription.pending";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_HALTED"] = "subscription.halted";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_CANCELLED"] = "subscription.cancelled";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_COMPLETED"] = "subscription.completed";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_EXPIRED"] = "subscription.expired";
    RazorpaySubscriptionEventType["_SUBSCRIPTION_UPDATED"] = "subscription.updated";
    // Payment events
    RazorpaySubscriptionEventType["_PAYMENT_AUTHORIZED"] = "payment.authorized";
    RazorpaySubscriptionEventType["_PAYMENT_CAPTURED"] = "payment.captured";
    RazorpaySubscriptionEventType["_PAYMENT_FAILED"] = "payment.failed";
    // Invoice events
    RazorpaySubscriptionEventType["_INVOICE_PAID"] = "invoice.paid";
    // Refund events
    RazorpaySubscriptionEventType["_REFUND_CREATED"] = "refund.created";
    RazorpaySubscriptionEventType["_REFUND_PROCESSED"] = "refund.processed";
    RazorpaySubscriptionEventType["_REFUND_FAILED"] = "refund.failed";
    return RazorpaySubscriptionEventType;
}({});
var RazorpaySubscriptionStatus = /*#__PURE__*/ function(RazorpaySubscriptionStatus) {
    RazorpaySubscriptionStatus["_CREATED"] = "created";
    RazorpaySubscriptionStatus["_AUTHENTICATED"] = "authenticated";
    RazorpaySubscriptionStatus["_ACTIVE"] = "active";
    RazorpaySubscriptionStatus["_PENDING"] = "pending";
    RazorpaySubscriptionStatus["_HALTED"] = "halted";
    RazorpaySubscriptionStatus["_CANCELLED"] = "cancelled";
    RazorpaySubscriptionStatus["_COMPLETED"] = "completed";
    RazorpaySubscriptionStatus["_EXPIRED"] = "expired";
    return RazorpaySubscriptionStatus;
}({});
var RazorpayPaymentStatus = /*#__PURE__*/ function(RazorpayPaymentStatus) {
    RazorpayPaymentStatus["_CREATED"] = "created";
    RazorpayPaymentStatus["_AUTHORIZED"] = "authorized";
    RazorpayPaymentStatus["_CAPTURED"] = "captured";
    RazorpayPaymentStatus["_REFUNDED"] = "refunded";
    RazorpayPaymentStatus["_FAILED"] = "failed";
    return RazorpayPaymentStatus;
}({});
var RazorpayRefundStatus = /*#__PURE__*/ function(RazorpayRefundStatus) {
    RazorpayRefundStatus["_CREATED"] = "created";
    RazorpayRefundStatus["_PROCESSED"] = "processed";
    RazorpayRefundStatus["_FAILED"] = "failed";
    return RazorpayRefundStatus;
}({});
var SupabaseSubscriptionStatus = /*#__PURE__*/ function(SupabaseSubscriptionStatus) {
    SupabaseSubscriptionStatus["_ACTIVE"] = "active";
    SupabaseSubscriptionStatus["_PENDING"] = "pending";
    SupabaseSubscriptionStatus["_HALTED"] = "halted";
    SupabaseSubscriptionStatus["_CANCELLED"] = "cancelled";
    SupabaseSubscriptionStatus["_COMPLETED"] = "completed";
    SupabaseSubscriptionStatus["_EXPIRED"] = "expired";
    SupabaseSubscriptionStatus["_PAYMENT_FAILED"] = "payment_failed";
    SupabaseSubscriptionStatus["_AUTHENTICATED"] = "authenticated";
    return SupabaseSubscriptionStatus;
}({});
function mapRazorpayStatusToSupabase(razorpayStatus) {
    switch(razorpayStatus){
        case "active":
            return "active";
        case "pending":
            return "pending";
        case "halted":
            return "halted";
        case "cancelled":
            return "cancelled";
        case "completed":
            return "completed";
        case "authenticated":
            return "authenticated";
        case "expired":
            return "expired";
        default:
            // Default to pending for unknown statuses
            console.warn(`Unknown Razorpay status: ${razorpayStatus}, defaulting to pending`);
            return "pending";
    }
}
}}),
"[project]/lib/razorpay/webhooks/handlers/subscription-db-updater.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "updateSubscription": (()=>updateSubscription),
    "updateSubscriptionWithBusinessProfile": (()=>updateSubscriptionWithBusinessProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/admin.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-rsc] (ecmascript)");
;
;
;
async function updateSubscription(_supabase, subscriptionId, status, additionalData = {}) {
    try {
        // Get admin client to bypass RLS
        const adminClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        // Get subscription details from Razorpay to find the business_profile_id and other details
        const { getSubscription } = await __turbopack_context__.r("[project]/lib/razorpay/services/subscription/index.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        const subscriptionDetails = await getSubscription(subscriptionId);
        if (!subscriptionDetails.success || !subscriptionDetails.data) {
            console.error(`[RAZORPAY_WEBHOOK] Failed to get subscription details from Razorpay for ${subscriptionId}`);
            // ENHANCED: Try to get business_profile_id from our database instead
            const { data: localSubscription, error: localError } = await adminClient.from('payment_subscriptions').select('business_profile_id, plan_id, plan_cycle').eq('razorpay_subscription_id', subscriptionId).maybeSingle();
            if (localError || !localSubscription) {
                console.error(`[RAZORPAY_WEBHOOK] Also failed to get subscription from local database for ${subscriptionId}:`, localError);
                return {
                    success: false,
                    message: `Failed to get subscription details from both Razorpay and local database`
                };
            }
            console.log(`[RAZORPAY_WEBHOOK] Using local subscription data for ${subscriptionId} since Razorpay API failed`);
            // Create a mock subscription details object using local data
            const mockSubscriptionDetails = {
                success: true,
                data: {
                    id: subscriptionId,
                    plan_id: `${localSubscription.plan_id}_${localSubscription.plan_cycle}`,
                    customer_id: null,
                    current_start: null,
                    current_end: null,
                    charge_at: null,
                    start_at: null,
                    notes: {
                        business_profile_id: localSubscription.business_profile_id,
                        plan_type: localSubscription.plan_id,
                        plan_cycle: localSubscription.plan_cycle
                    }
                }
            };
            // Use the mock data for the rest of the function
            const subscriptionDetailsToUse = mockSubscriptionDetails;
            return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetailsToUse.data);
        }
        // Continue with normal processing using Razorpay data
        return await processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionDetails.data);
    } catch (error) {
        console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);
        return {
            success: false,
            message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
/**
 * Process subscription update with given subscription data
 * This function contains the main logic extracted from updateSubscription
 */ async function processSubscriptionUpdate(adminClient, subscriptionId, status, additionalData, subscriptionData) {
    try {
        // Extract business_profile_id from notes
        const businessProfileId = subscriptionData.notes?.business_profile_id || subscriptionData.notes?.user_id;
        if (!businessProfileId) {
            console.error(`[RAZORPAY_WEBHOOK] No business_profile_id found in subscription notes for ${subscriptionId}`);
            return {
                success: false,
                message: `No business_profile_id found in subscription notes`
            };
        }
        // Extract plan details from notes or plan_id
        let planType = subscriptionData.notes?.plan_type;
        let planCycle = subscriptionData.notes?.plan_cycle;
        // If plan type and cycle are not in notes, try to determine from plan_id
        if (!planType || !planCycle) {
            console.log(`[RAZORPAY_WEBHOOK] Plan type or cycle not found in notes, determining from plan_id: ${subscriptionData.plan_id}`);
            // Use centralized plan configuration to map Razorpay plan ID to plan details
            const { getPlanByRazorpayPlanId } = await __turbopack_context__.r("[project]/lib/config/plans.ts [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
            const planDetails = getPlanByRazorpayPlanId(subscriptionData.plan_id);
            if (planDetails) {
                planType = planDetails.id;
                // Determine cycle by checking which Razorpay plan ID matches
                planCycle = planDetails.razorpayPlanIds.monthly === subscriptionData.plan_id ? "monthly" : "yearly";
                console.log(`[RAZORPAY_WEBHOOK] Determined plan type: ${planType}, cycle: ${planCycle} from plan_id using centralized config`);
            } else {
                // Default to basic monthly if we can't determine
                planType = "basic";
                planCycle = "monthly";
                console.log(`[RAZORPAY_WEBHOOK] Could not determine plan type and cycle from plan_id: ${subscriptionData.plan_id}, defaulting to basic monthly`);
            }
        }
        // CENTRALIZED LOGIC: Use SubscriptionStateManager to determine has_active_subscription
        const hasActiveSubscription = __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SubscriptionStateManager"].shouldHaveActiveSubscription(status, planType || 'free');
        // Create a copy of additionalData to avoid modifying the original
        const additionalDataCopy = {
            ...additionalData
        };
        // Remove has_active_subscription from additionalData if it exists
        // This ensures we always set it based on the status, not what's passed in
        if ('has_active_subscription' in additionalDataCopy) {
            console.log(`[RAZORPAY_WEBHOOK] Removing has_active_subscription from additionalData for subscription with status ${status}`);
            delete additionalDataCopy.has_active_subscription;
        }
        // Extract subscription dates from Razorpay subscription details
        let subscriptionStartDate = subscriptionData.current_start ? new Date(subscriptionData.current_start * 1000).toISOString() : null;
        const subscriptionExpiryTime = subscriptionData.current_end ? new Date(subscriptionData.current_end * 1000).toISOString() : null;
        const subscriptionChargeTime = subscriptionData.charge_at ? new Date(subscriptionData.charge_at * 1000).toISOString() : null;
        // Check if the status is authenticated or active
        const isValidStatus = status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SupabaseSubscriptionStatus"]._AUTHENTICATED || status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SupabaseSubscriptionStatus"]._ACTIVE;
        if (!isValidStatus) {
            console.log(`[RAZORPAY_WEBHOOK] Skipping creation/update of subscription record for ${subscriptionId} with status ${status} - only handling authenticated or active statuses`);
            return {
                success: true,
                message: `Skipped creation/update of subscription record with status ${status}`
            };
        }
        // For authenticated subscriptions, ensure we're setting the correct dates
        // This is especially important for trial users where start_at is in the future
        if (status === __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SupabaseSubscriptionStatus"]._AUTHENTICATED) {
            console.log(`[RAZORPAY_WEBHOOK] Authenticated subscription detected, ensuring dates are set correctly`);
            // For authenticated subscriptions, we need to check if start_at is set
            // If it is, we should use that for subscription_start_date instead of current_start
            if (subscriptionData.start_at) {
                const startAt = new Date(subscriptionData.start_at * 1000).toISOString();
                console.log(`[RAZORPAY_WEBHOOK] Using start_at (${startAt}) for subscription_start_date`);
                // Override the subscription_start_date with start_at
                subscriptionStartDate = startAt;
            }
        }
        // Find existing subscription record
        const { data: subscription, error: findError } = await adminClient.from('payment_subscriptions').select('id, business_profile_id, razorpay_subscription_id').eq('razorpay_subscription_id', subscriptionId).maybeSingle();
        if (findError) {
            console.error(`[RAZORPAY_WEBHOOK] Error finding subscription ${subscriptionId}:`, findError);
            return {
                success: false,
                message: `Error finding subscription: ${findError.message}`
            };
        }
        // If no subscription record exists for this Razorpay subscription ID, check if there's an existing record for the business
        if (!subscription) {
            console.log(`[RAZORPAY_WEBHOOK] No subscription found with ID ${subscriptionId}, checking for existing subscription for business ${businessProfileId}`);
            // Check if there's an existing subscription for this business
            const { data: existingSubscriptions, error: existingError } = await adminClient.from('payment_subscriptions').select('id, razorpay_subscription_id, subscription_status').eq('business_profile_id', businessProfileId);
            if (existingError) {
                console.error(`[RAZORPAY_WEBHOOK] Error checking for existing subscriptions for business ${businessProfileId}:`, existingError);
                return {
                    success: false,
                    message: `Error checking for existing subscriptions: ${existingError.message}`
                };
            }
            // If there's an existing subscription for this business, update it instead of creating a new one
            if (existingSubscriptions && existingSubscriptions.length > 0) {
                console.log(`[RAZORPAY_WEBHOOK] Found existing subscription for business ${businessProfileId}, updating instead of creating new one`);
                const existingSubscription = existingSubscriptions[0];
                // Create the update data object
                const updateData = {
                    razorpay_subscription_id: subscriptionId,
                    razorpay_customer_id: subscriptionData.customer_id || null,
                    subscription_status: status,
                    plan_id: planType,
                    plan_cycle: planCycle,
                    subscription_start_date: subscriptionStartDate,
                    subscription_expiry_time: subscriptionExpiryTime,
                    subscription_charge_time: subscriptionChargeTime,
                    updated_at: new Date().toISOString(),
                    ...additionalDataCopy
                };
                // Update the existing subscription record
                const { error: updateError } = await adminClient.from('payment_subscriptions').update(updateData).eq('id', existingSubscription.id);
                if (updateError) {
                    console.error(`[RAZORPAY_WEBHOOK] Error updating existing subscription ${existingSubscription.id}:`, updateError);
                    return {
                        success: false,
                        message: `Error updating existing subscription: ${updateError.message}`
                    };
                }
                console.log(`[RAZORPAY_WEBHOOK] Updated existing subscription ${existingSubscription.id} with new Razorpay ID ${subscriptionId} and status ${status}`);
                // Use transaction utility to ensure consistency
                const transactionResult = await updateSubscriptionWithBusinessProfile({
                    subscription_id: subscriptionId,
                    business_profile_id: businessProfileId,
                    subscription_status: status,
                    has_active_subscription: hasActiveSubscription,
                    additional_data: updateData
                });
                if (!transactionResult.success) {
                    console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);
                    return {
                        success: false,
                        message: `Transaction failed: ${transactionResult.message}`
                    };
                }
                return {
                    success: true,
                    message: `Updated existing subscription with new Razorpay ID and status ${status}`
                };
            }
            // If no existing subscription for this business, create a new one
            console.log(`[RAZORPAY_WEBHOOK] No existing subscription found for business ${businessProfileId}, creating new one`);
            const insertData = {
                business_profile_id: businessProfileId,
                razorpay_subscription_id: subscriptionId,
                razorpay_customer_id: subscriptionData.customer_id || null,
                subscription_status: status,
                plan_id: planType,
                plan_cycle: planCycle,
                subscription_start_date: subscriptionStartDate,
                subscription_expiry_time: subscriptionExpiryTime,
                subscription_charge_time: subscriptionChargeTime,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                ...additionalDataCopy
            };
            const { data: _newSubscription, error: insertError } = await adminClient.from('payment_subscriptions').insert(insertData).select('id').single();
            if (insertError) {
                console.error(`[RAZORPAY_WEBHOOK] Error creating subscription record for ${subscriptionId}:`, insertError);
                return {
                    success: false,
                    message: `Error creating subscription record: ${insertError.message}`
                };
            }
            console.log(`[RAZORPAY_WEBHOOK] Created new subscription record for ${subscriptionId}`);
            // Use transaction utility to ensure consistency
            const transactionResult = await updateSubscriptionWithBusinessProfile({
                subscription_id: subscriptionId,
                business_profile_id: businessProfileId,
                subscription_status: status,
                has_active_subscription: hasActiveSubscription,
                additional_data: insertData
            });
            if (!transactionResult.success) {
                console.error(`[RAZORPAY_WEBHOOK] Transaction failed for new subscription ${subscriptionId}:`, transactionResult.message);
                return {
                    success: false,
                    message: `Transaction failed: ${transactionResult.message}`
                };
            }
            console.log(`[RAZORPAY_WEBHOOK] Created subscription for ${subscriptionId} with status ${status}`);
            return {
                success: true,
                message: `Created subscription with status ${status}`
            };
        }
        // If subscription exists, update it
        const updateData = {
            subscription_status: status,
            subscription_start_date: subscriptionStartDate,
            subscription_expiry_time: subscriptionExpiryTime,
            subscription_charge_time: subscriptionChargeTime,
            updated_at: new Date().toISOString(),
            ...additionalDataCopy
        };
        // ENHANCED: Use atomic RPC function for transaction safety
        const transactionResult = await updateSubscriptionWithBusinessProfile({
            subscription_id: subscriptionId,
            business_profile_id: businessProfileId,
            subscription_status: status,
            has_active_subscription: hasActiveSubscription,
            additional_data: updateData
        });
        if (!transactionResult.success) {
            console.error(`[RAZORPAY_WEBHOOK] Transaction failed for subscription ${subscriptionId}:`, transactionResult.message);
            return {
                success: false,
                message: `Transaction failed: ${transactionResult.message}`
            };
        }
        console.log(`[RAZORPAY_WEBHOOK] Updated subscription ${subscription.id} with status ${status}`);
        return {
            success: true,
            message: `Updated subscription with status ${status}`
        };
    } catch (error) {
        console.error(`[RAZORPAY_WEBHOOK] Exception updating subscription ${subscriptionId}:`, error);
        return {
            success: false,
            message: `Exception updating subscription: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function updateSubscriptionWithBusinessProfile(params) {
    try {
        const adminClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$admin$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createAdminClient"])();
        console.log(`[ATOMIC_TRANSACTION] Using atomic RPC for subscription ${params.subscription_id}`);
        // ENHANCED: Use atomic RPC function for true transaction safety
        const { data: result, error } = await adminClient.rpc('update_subscription_atomic', {
            p_subscription_id: params.subscription_id,
            p_new_status: params.subscription_status,
            p_business_profile_id: params.business_profile_id,
            p_has_active_subscription: params.has_active_subscription,
            p_additional_data: params.additional_data || {},
            p_webhook_timestamp: params.additional_data?.last_webhook_timestamp || null
        });
        if (error) {
            console.error(`[ATOMIC_TRANSACTION] RPC error for ${params.subscription_id}:`, error);
            return {
                success: false,
                message: `RPC error: ${error.message}`
            };
        }
        if (!result?.success) {
            console.error(`[ATOMIC_TRANSACTION] RPC function returned error for ${params.subscription_id}:`, result);
            return {
                success: false,
                message: result?.error || 'Unknown RPC error'
            };
        }
        console.log(`[ATOMIC_TRANSACTION] Successfully updated subscription ${params.subscription_id} atomically`);
        return {
            success: true,
            message: 'Atomic transaction completed successfully via RPC'
        };
    } catch (error) {
        console.error(`[ATOMIC_TRANSACTION] Exception in updateSubscriptionWithBusinessProfile:`, error);
        return {
            success: false,
            message: `Atomic transaction exception: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
}}),
"[project]/lib/razorpay/webhooks/handlers/webhook-utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractWebhookTimestamp": (()=>extractWebhookTimestamp),
    "getAppropriateSubscriptionStatus": (()=>getAppropriateSubscriptionStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-rsc] (ecmascript)");
;
function extractWebhookTimestamp(payload) {
    try {
        // Priority order for timestamp extraction (most reliable first)
        const timestampSources = [
            // 1. Main event timestamp (most reliable)
            ()=>payload.created_at,
            // 2. Subscription entity timestamps
            ()=>payload.payload?.subscription?.entity?.created_at,
            ()=>payload.payload?.subscription?.entity?.updated_at,
            // 3. Payment entity timestamps
            ()=>payload.payload?.payment?.entity?.created_at,
            ()=>payload.payload?.payment?.entity?.updated_at,
            // 4. Invoice entity timestamps
            ()=>payload.payload?.invoice?.entity?.created_at,
            ()=>payload.payload?.invoice?.entity?.updated_at,
            // 5. Generic entity timestamps
            ()=>payload.payload?.entity?.created_at,
            ()=>payload.payload?.entity?.updated_at,
            // 6. Event-specific timestamps
            ()=>payload.event_timestamp,
            ()=>payload.timestamp
        ];
        // Try each timestamp source in order
        for (const getTimestamp of timestampSources){
            try {
                const timestamp = getTimestamp();
                if (timestamp && typeof timestamp === 'number' && timestamp > 0) {
                    // Validate timestamp is reasonable (not too far in past/future)
                    const now = Math.floor(Date.now() / 1000);
                    const maxAge = 24 * 60 * 60; // 24 hours
                    const maxFuture = 5 * 60; // 5 minutes
                    if (timestamp >= now - maxAge && timestamp <= now + maxFuture) {
                        return timestamp;
                    } else {
                        console.warn(`[WEBHOOK_TIMESTAMP] Timestamp ${timestamp} outside reasonable range, trying next source`);
                    }
                }
            } catch (_sourceError) {
                continue;
            }
        }
        // Fallback to current time with warning
        console.warn('[WEBHOOK_TIMESTAMP] Could not extract valid timestamp from payload, using current time');
        console.warn('[WEBHOOK_TIMESTAMP] Payload structure:', JSON.stringify(payload, null, 2));
        return Math.floor(Date.now() / 1000);
    } catch (error) {
        console.error('[WEBHOOK_TIMESTAMP] Error extracting timestamp from payload:', error);
        return Math.floor(Date.now() / 1000);
    }
}
function getAppropriateSubscriptionStatus(hasActiveSubscription, trialEndDate, planId) {
    // If on free plan, always active status but has_active_subscription should be false
    if (planId === 'free') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].ACTIVE;
    }
    // Check if user is in trial period
    if (trialEndDate) {
        const trialEnd = new Date(trialEndDate);
        const now = new Date();
        if (trialEnd > now) {
            // User is in trial period - status is trial, has_active_subscription should be false
            return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].TRIAL;
        }
    }
    // If has active subscription flag, it means they have a paid subscription
    if (hasActiveSubscription) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].ACTIVE;
    }
    // Default to pending if no active subscription and not in trial
    return __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"].PENDING;
}
}}),
"[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$validator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-validator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$db$2d$updater$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-db-updater.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$webhook$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/webhook-utils.ts [app-rsc] (ecmascript)");
;
;
;
;
;
}}),
"[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$validator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-validator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$db$2d$updater$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-db-updater.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$webhook$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/webhook-utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PLAN_IDS": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PLAN_IDS"]),
    "SUBSCRIPTION_STATUS": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SUBSCRIPTION_STATUS"]),
    "SubscriptionStateManager": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SubscriptionStateManager"]),
    "extractWebhookTimestamp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$webhook$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["extractWebhookTimestamp"]),
    "getAppropriateSubscriptionStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$webhook$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getAppropriateSubscriptionStatus"]),
    "isActivePaidSubscription": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isActivePaidSubscription"]),
    "isFreeStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isFreeStatus"]),
    "isTerminalStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTerminalStatus"]),
    "isTrialStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["isTrialStatus"]),
    "shouldHaveActiveSubscription": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shouldHaveActiveSubscription"]),
    "shouldHaveActiveSubscriptionByPlan": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["shouldHaveActiveSubscriptionByPlan"]),
    "updateSubscription": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$db$2d$updater$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateSubscription"]),
    "updateSubscriptionWithBusinessProfile": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$db$2d$updater$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["updateSubscriptionWithBusinessProfile"]),
    "validateAndFixSubscriptionState": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$validator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateAndFixSubscriptionState"]),
    "validateSubscriptionState": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$validator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["validateSubscriptionState"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$manager$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-manager.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$state$2d$validator$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-state-validator.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$subscription$2d$db$2d$updater$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/subscription-db-updater.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$webhook$2d$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/webhook-utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PLAN_IDS": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["PLAN_IDS"]),
    "SUBSCRIPTION_STATUS": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SUBSCRIPTION_STATUS"]),
    "SubscriptionStateManager": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["SubscriptionStateManager"]),
    "extractWebhookTimestamp": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["extractWebhookTimestamp"]),
    "getAppropriateSubscriptionStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["getAppropriateSubscriptionStatus"]),
    "isActivePaidSubscription": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["isActivePaidSubscription"]),
    "isFreeStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["isFreeStatus"]),
    "isTerminalStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["isTerminalStatus"]),
    "isTrialStatus": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["isTrialStatus"]),
    "shouldHaveActiveSubscription": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["shouldHaveActiveSubscription"]),
    "shouldHaveActiveSubscriptionByPlan": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["shouldHaveActiveSubscriptionByPlan"]),
    "updateSubscription": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updateSubscription"]),
    "updateSubscriptionWithBusinessProfile": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["updateSubscriptionWithBusinessProfile"]),
    "validateAndFixSubscriptionState": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["validateAndFixSubscriptionState"]),
    "validateSubscriptionState": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__["validateSubscriptionState"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$razorpay$2f$webhooks$2f$handlers$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i("[project]/lib/razorpay/webhooks/handlers/utils.ts [app-rsc] (ecmascript) <exports>");
}}),

};

//# sourceMappingURL=lib_razorpay_webhooks_eec17b82._.js.map