{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/auth/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { redirect } from \"next/navigation\";\r\n// Removed unused headers import\r\n\r\nexport async function signOutUser() {\r\n  const supabase = await createClient();\r\n\r\n  try {\r\n    const { error: _error } = await supabase.auth.signOut();\r\n    // Note: Sign out errors are typically not critical for user experience\r\n    // The user will be redirected to login regardless\r\n\r\n    // Explicitly clear auth cookies to ensure logout\r\n    const cookieStore = await import(\"next/headers\").then((m) => m.cookies());\r\n    const cookiesToClear = [\"sb-access-token\", \"sb-refresh-token\"];\r\n\r\n    for (const cookieName of cookiesToClear) {\r\n      try {\r\n        cookieStore.set(cookieName, \"\", {\r\n          expires: new Date(0),\r\n          maxAge: -1,\r\n        });\r\n      } catch {\r\n        // Cookie clearing errors are not critical for sign out\r\n        // Continue with the sign out process\r\n      }\r\n    }\r\n  } catch {\r\n    // Even if sign out fails, redirect to login for security\r\n    // User will be treated as logged out\r\n  }\r\n\r\n  // Redirect to login with a flag to prevent middleware redirect loop\r\n  return redirect(\"/login?logged_out=true\");\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;;;;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,OAAO,MAAM,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACrD,uEAAuE;QACvE,kDAAkD;QAElD,iDAAiD;QACjD,MAAM,cAAc,MAAM,gIAAuB,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO;QACtE,MAAM,iBAAiB;YAAC;YAAmB;SAAmB;QAE9D,KAAK,MAAM,cAAc,eAAgB;YACvC,IAAI;gBACF,YAAY,GAAG,CAAC,YAAY,IAAI;oBAC9B,SAAS,IAAI,KAAK;oBAClB,QAAQ,CAAC;gBACX;YACF,EAAE,OAAM;YACN,uDAAuD;YACvD,qCAAqC;YACvC;QACF;IACF,EAAE,OAAM;IACN,yDAAyD;IACzD,qCAAqC;IACvC;IAEA,oEAAoE;IACpE,OAAO,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;AAClB;;;IA9BsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/admin.ts"], "sourcesContent": ["import { createClient as createSupabaseClient } from \"@supabase/supabase-js\";\r\n\r\n/**\r\n * Creates a Supabase admin client with the service role key.\r\n * This client has admin privileges and should only be used on the server.\r\n * Never expose your service_role key in the browser.\r\n */\r\nexport function createAdminClient() {\r\n  return createSupabaseClient(\r\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\r\n    process.env.SUPABASE_SERVICE_ROLE_KEY!\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAOO,SAAS;IACd,OAAO,CAAA,GAAA,uLAAA,CAAA,eAAoB,AAAD,gFAExB,QAAQ,GAAG,CAAC,yBAAyB;AAEzC", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/activities.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\n/**\r\n * Database Triggers Documentation\r\n *\r\n * The following triggers are set up in Supabase to automatically track activities:\r\n *\r\n * 1. add_like_activity() - Trigger function for likes\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'like',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is added\r\n * CREATE TRIGGER trigger_add_like_activity\r\n * AFTER INSERT ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_like_activity();\r\n * ```\r\n *\r\n * 1a. delete_like_activity() - Trigger function for removing like activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_like_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'like';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a like is deleted\r\n * CREATE TRIGGER trigger_delete_like_activity\r\n * AFTER DELETE ON likes\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_like_activity();\r\n * ```\r\n *\r\n * 2. add_subscription_activity() - Trigger function for subscriptions\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'subscribe',\r\n *     NEW.created_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is added\r\n * CREATE TRIGGER trigger_add_subscription_activity\r\n * AFTER INSERT ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_subscription_activity();\r\n * ```\r\n *\r\n * 2a. delete_subscription_activity() - Trigger function for removing subscription activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_subscription_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'subscribe';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a subscription is deleted\r\n * CREATE TRIGGER trigger_delete_subscription_activity\r\n * AFTER DELETE ON subscriptions\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_subscription_activity();\r\n * ```\r\n *\r\n * 3. add_rating_activity() - Trigger function for ratings\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION add_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Check if this is an update or insert\r\n *   IF TG_OP = 'UPDATE' THEN\r\n *     -- For updates, only add activity if rating changed\r\n *     IF NEW.rating = OLD.rating THEN\r\n *       RETURN NEW;\r\n *     END IF;\r\n *   END IF;\r\n *\r\n *   -- Insert a new activity record\r\n *   INSERT INTO business_activities (\r\n *     business_profile_id,\r\n *     user_id,\r\n *     activity_type,\r\n *     rating_value,\r\n *     created_at\r\n *   ) VALUES (\r\n *     NEW.business_profile_id,\r\n *     NEW.user_id,\r\n *     'rating',\r\n *     NEW.rating,\r\n *     NEW.updated_at\r\n *   );\r\n *\r\n *   RETURN NEW;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is added or updated\r\n * CREATE TRIGGER trigger_add_rating_activity\r\n * AFTER INSERT OR UPDATE OF rating ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION add_rating_activity();\r\n * ```\r\n *\r\n * 3a. delete_rating_activity() - Trigger function for removing rating activities\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION delete_rating_activity()\r\n * RETURNS TRIGGER AS $$\r\n * BEGIN\r\n *   -- Delete the activity record\r\n *   DELETE FROM business_activities\r\n *   WHERE business_profile_id = OLD.business_profile_id\r\n *   AND user_id = OLD.user_id\r\n *   AND activity_type = 'rating';\r\n *\r\n *   RETURN OLD;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n *\r\n * -- Trigger to call the function when a rating is deleted\r\n * CREATE TRIGGER trigger_delete_rating_activity\r\n * AFTER DELETE ON ratings_reviews\r\n * FOR EACH ROW\r\n * EXECUTE FUNCTION delete_rating_activity();\r\n * ```\r\n */\r\n\r\n/**\r\n * Table Structure\r\n *\r\n * The business_activities table is structured as follows:\r\n * ```sql\r\n * CREATE TABLE business_activities (\r\n *   id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\r\n *   business_profile_id UUID NOT NULL REFERENCES business_profiles(id) ON DELETE CASCADE,\r\n *   user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,\r\n *   activity_type TEXT NOT NULL CHECK (activity_type IN ('like', 'subscribe', 'rating')),\r\n *   rating_value INTEGER,\r\n *   created_at TIMESTAMPTZ NOT NULL DEFAULT now(),\r\n *   is_read BOOLEAN NOT NULL DEFAULT false,\r\n *\r\n *   -- Add constraint to ensure rating_value is only set for rating activities\r\n *   CONSTRAINT rating_value_only_for_ratings CHECK (\r\n *     (activity_type = 'rating' AND rating_value IS NOT NULL) OR\r\n *     (activity_type != 'rating' AND rating_value IS NULL)\r\n *   )\r\n * );\r\n *\r\n * -- Indexes for better performance\r\n * CREATE INDEX idx_business_activities_business_profile_id ON business_activities(business_profile_id);\r\n * CREATE INDEX idx_business_activities_user_id ON business_activities(user_id);\r\n * CREATE INDEX idx_business_activities_is_read ON business_activities(is_read);\r\n * CREATE INDEX idx_business_activities_activity_type ON business_activities(activity_type);\r\n * CREATE INDEX idx_business_activities_created_at ON business_activities(created_at);\r\n * ```\r\n */\r\n\r\n/**\r\n * Row Level Security (RLS) Policies\r\n *\r\n * The following RLS policies are set up in Supabase to secure the business_activities table:\r\n *\r\n * 1. Select Policy - Allows business owners to read their own activities\r\n * ```sql\r\n * CREATE POLICY business_activities_select_policy ON business_activities\r\n *   FOR SELECT\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n *\r\n * 2. Update Policy - Allows business owners to update their own activities (for marking as read)\r\n * ```sql\r\n * CREATE POLICY business_activities_update_policy ON business_activities\r\n *   FOR UPDATE\r\n *   USING (auth.uid() = business_profile_id);\r\n * ```\r\n */\r\n\r\n// Define types for activities\r\nexport interface BusinessActivity {\r\n  id: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  activity_type: \"like\" | \"subscribe\" | \"rating\";\r\n  rating_value: number | null;\r\n  created_at: string;\r\n  is_read: boolean;\r\n  user_profile?: {\r\n    name?: string | null;\r\n    avatar_url?: string | null;\r\n    email?: string | null;\r\n    is_business?: boolean;\r\n    business_name?: string | null;\r\n    business_slug?: string | null;\r\n    logo_url?: string | null;\r\n  };\r\n}\r\n\r\nexport type ActivitySortBy = \"newest\" | \"oldest\" | \"unread_first\";\r\n\r\n/**\r\n * Fetches activities for a business with pagination and sorting\r\n * Optionally marks fetched activities as read automatically\r\n */\r\nexport async function getBusinessActivities({\r\n  businessProfileId,\r\n  page = 1,\r\n  pageSize = 15,\r\n  sortBy = \"newest\",\r\n  filterBy = \"all\",\r\n  autoMarkAsRead = true, // New parameter to control auto-marking as read\r\n}: {\r\n  businessProfileId: string;\r\n  page?: number;\r\n  pageSize?: number;\r\n  sortBy?: ActivitySortBy;\r\n  filterBy?: \"all\" | \"like\" | \"subscribe\" | \"rating\" | \"unread\";\r\n  autoMarkAsRead?: boolean;\r\n}) {\r\n  const supabase = await createClient();\r\n  const supabaseAdmin = createAdminClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { activities: [], count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { activities: [], count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // Calculate pagination\r\n    const from = (page - 1) * pageSize;\r\n    const to = from + pageSize - 1;\r\n\r\n    // Build the query\r\n    let query = supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(\"business_profile_id\", businessProfileId);\r\n\r\n    // Apply filter\r\n    if (filterBy === \"like\") {\r\n      query = query.eq(\"activity_type\", \"like\");\r\n    } else if (filterBy === \"subscribe\") {\r\n      query = query.eq(\"activity_type\", \"subscribe\");\r\n    } else if (filterBy === \"rating\") {\r\n      query = query.eq(\"activity_type\", \"rating\");\r\n    } else if (filterBy === \"unread\") {\r\n      query = query.eq(\"is_read\", false);\r\n    }\r\n\r\n    // Apply sorting\r\n    switch (sortBy) {\r\n      case \"oldest\":\r\n        query = query.order(\"created_at\", { ascending: true });\r\n        break;\r\n      case \"unread_first\":\r\n        query = query.order(\"is_read\", { ascending: true }).order(\"created_at\", { ascending: false });\r\n        break;\r\n      case \"newest\":\r\n      default:\r\n        query = query.order(\"created_at\", { ascending: false });\r\n        break;\r\n    }\r\n\r\n    // Apply pagination\r\n    query = query.range(from, to);\r\n\r\n    // Execute the query\r\n    const { data: activities, error, count } = await query;\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching business activities:\", error);\r\n      return { activities: [], count: 0, error: error.message };\r\n    }\r\n\r\n    // Get user profiles for the activities\r\n    const userIds = activities.map((activity) => activity.user_id);\r\n\r\n    // Fetch both customer and business profiles\r\n    const [customerProfiles, businessProfiles] = await Promise.all([\r\n      supabaseAdmin\r\n        .from(\"customer_profiles\")\r\n        .select(\"id, name, avatar_url, email\")\r\n        .in(\"id\", userIds),\r\n      supabaseAdmin\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_name, business_slug, logo_url\")\r\n        .in(\"id\", userIds),\r\n    ]);\r\n\r\n    // Combine the profiles\r\n    const userProfiles = new Map();\r\n\r\n    // Add customer profiles to the map\r\n    customerProfiles.data?.forEach((profile) => {\r\n      userProfiles.set(profile.id, {\r\n        name: profile.name,\r\n        avatar_url: profile.avatar_url,\r\n        email: profile.email,\r\n        is_business: false,\r\n      });\r\n    });\r\n\r\n    // Add business profiles to the map, overriding customer profiles if both exist\r\n    businessProfiles.data?.forEach((profile) => {\r\n      const existingProfile = userProfiles.get(profile.id) || {};\r\n      userProfiles.set(profile.id, {\r\n        ...existingProfile,\r\n        business_name: profile.business_name,\r\n        business_slug: profile.business_slug,\r\n        logo_url: profile.logo_url,\r\n        is_business: true,\r\n      });\r\n    });\r\n\r\n    // Attach user profiles to activities\r\n    const activitiesWithProfiles = activities.map((activity) => ({\r\n      ...activity,\r\n      user_profile: userProfiles.get(activity.user_id) || {},\r\n    }));\r\n\r\n    // Auto-mark fetched activities as read if enabled\r\n    if (autoMarkAsRead && activities.length > 0) {\r\n      // Get IDs of unread activities\r\n      const unreadActivityIds = activities\r\n        .filter(activity => !activity.is_read)\r\n        .map(activity => activity.id);\r\n\r\n      // Only proceed if there are unread activities\r\n      if (unreadActivityIds.length > 0) {\r\n        // Mark these activities as read\r\n        const { error: markError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", unreadActivityIds);\r\n\r\n        if (markError) {\r\n          console.error(\"Error auto-marking activities as read:\", markError);\r\n        } else {\r\n          // Update the activities in our result to reflect they're now read\r\n          activitiesWithProfiles.forEach(activity => {\r\n            if (unreadActivityIds.includes(activity.id)) {\r\n              activity.is_read = true;\r\n            }\r\n          });\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      activities: activitiesWithProfiles,\r\n      count: count || 0,\r\n      error: null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching business activities:\", error);\r\n    return {\r\n      activities: [],\r\n      count: 0,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Marks activities as read\r\n * Handles pagination for large numbers of activities to work around Supabase's 1000 row limit\r\n */\r\nexport async function markActivitiesAsRead({\r\n  businessProfileId,\r\n  activityIds,\r\n}: {\r\n  businessProfileId: string;\r\n  activityIds: string[] | \"all\";\r\n}) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { success: false, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { success: false, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    // If marking specific activities as read\r\n    if (activityIds !== \"all\") {\r\n      // Handle case where we have specific activity IDs\r\n      const { error } = await supabase\r\n        .from(\"business_activities\")\r\n        .update({ is_read: true })\r\n        .eq(\"business_profile_id\", businessProfileId)\r\n        .in(\"id\", activityIds);\r\n\r\n      if (error) {\r\n        console.error(\"Error marking specific activities as read:\", error);\r\n        return { success: false, error: error.message };\r\n      }\r\n    } else {\r\n      // Handle \"mark all as read\" with pagination to work around Supabase's 1000 row limit\r\n      const BATCH_SIZE = 1000; // Maximum number of rows to update at once\r\n      let hasMore = true;\r\n      let processedCount = 0;\r\n\r\n      while (hasMore) {\r\n        // Get a batch of unread activity IDs\r\n        const { data: unreadActivities, error: fetchError } = await supabase\r\n          .from(\"business_activities\")\r\n          .select(\"id\")\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .eq(\"is_read\", false)\r\n          .limit(BATCH_SIZE);\r\n\r\n        if (fetchError) {\r\n          console.error(\"Error fetching unread activities:\", fetchError);\r\n          return { success: false, error: fetchError.message };\r\n        }\r\n\r\n        // If no more unread activities, we're done\r\n        if (!unreadActivities || unreadActivities.length === 0) {\r\n          hasMore = false;\r\n          break;\r\n        }\r\n\r\n        // Extract IDs from the batch\r\n        const batchIds = unreadActivities.map(activity => activity.id);\r\n\r\n        // Mark this batch as read\r\n        const { error: updateError } = await supabase\r\n          .from(\"business_activities\")\r\n          .update({ is_read: true })\r\n          .eq(\"business_profile_id\", businessProfileId)\r\n          .in(\"id\", batchIds);\r\n\r\n        if (updateError) {\r\n          console.error(\"Error marking batch as read:\", updateError);\r\n          return { success: false, error: updateError.message };\r\n        }\r\n\r\n        // Update processed count and check if we need to continue\r\n        processedCount += batchIds.length;\r\n        hasMore = batchIds.length === BATCH_SIZE; // If we got a full batch, there might be more\r\n      }\r\n\r\n      console.log(`Marked ${processedCount} activities as read`);\r\n    }\r\n\r\n    // Revalidate the activities page\r\n    revalidatePath(\"/dashboard/business/activities\");\r\n\r\n    return { success: true, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error marking activities as read:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the count of unread activities\r\n */\r\nexport async function getUnreadActivitiesCount(businessProfileId: string) {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (!user) {\r\n    return { count: 0, error: \"Not authenticated\" };\r\n  }\r\n\r\n  // Verify the user is the owner of the business\r\n  if (user.id !== businessProfileId) {\r\n    return { count: 0, error: \"Unauthorized\" };\r\n  }\r\n\r\n  try {\r\n    const { count, error } = await supabase\r\n      .from(\"business_activities\")\r\n      .select(\"*\", { count: \"exact\", head: true })\r\n      .eq(\"business_profile_id\", businessProfileId)\r\n      .eq(\"is_read\", false);\r\n\r\n    if (error) {\r\n      console.error(\"Error getting unread activities count:\", error);\r\n      return { count: 0, error: error.message };\r\n    }\r\n\r\n    return { count: count || 0, error: null };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error getting unread activities count:\", error);\r\n    return { count: 0, error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;;;;;AAuPO,eAAe,sBAAsB,EAC1C,iBAAiB,EACjB,OAAO,CAAC,EACR,WAAW,EAAE,EACb,SAAS,QAAQ,EACjB,WAAW,KAAK,EAChB,iBAAiB,IAAI,EAQtB;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IAEtC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,YAAY,EAAE;YAAE,OAAO;YAAG,OAAO;QAAoB;IAChE;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,YAAY,EAAE;YAAE,OAAO;YAAG,OAAO;QAAe;IAC3D;IAEA,IAAI;QACF,uBAAuB;QACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,WAAW;QAE7B,kBAAkB;QAClB,IAAI,QAAQ,SACT,IAAI,CAAC,uBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ,GAC7B,EAAE,CAAC,uBAAuB;QAE7B,eAAe;QACf,IAAI,aAAa,QAAQ;YACvB,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,aAAa;YACnC,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,UAAU;YAChC,QAAQ,MAAM,EAAE,CAAC,iBAAiB;QACpC,OAAO,IAAI,aAAa,UAAU;YAChC,QAAQ,MAAM,EAAE,CAAC,WAAW;QAC9B;QAEA,gBAAgB;QAChB,OAAQ;YACN,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBACpD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,WAAW;oBAAE,WAAW;gBAAK,GAAG,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAC3F;YACF,KAAK;YACL;gBACE,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBACrD;QACJ;QAEA,mBAAmB;QACnB,QAAQ,MAAM,KAAK,CAAC,MAAM;QAE1B,oBAAoB;QACpB,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAEjD,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;gBAAE,YAAY,EAAE;gBAAE,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1D;QAEA,uCAAuC;QACvC,MAAM,UAAU,WAAW,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO;QAE7D,4CAA4C;QAC5C,MAAM,CAAC,kBAAkB,iBAAiB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC7D,cACG,IAAI,CAAC,qBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM;YACZ,cACG,IAAI,CAAC,qBACL,MAAM,CAAC,8CACP,EAAE,CAAC,MAAM;SACb;QAED,uBAAuB;QACvB,MAAM,eAAe,IAAI;QAEzB,mCAAmC;QACnC,iBAAiB,IAAI,EAAE,QAAQ,CAAC;YAC9B,aAAa,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC3B,MAAM,QAAQ,IAAI;gBAClB,YAAY,QAAQ,UAAU;gBAC9B,OAAO,QAAQ,KAAK;gBACpB,aAAa;YACf;QACF;QAEA,+EAA+E;QAC/E,iBAAiB,IAAI,EAAE,QAAQ,CAAC;YAC9B,MAAM,kBAAkB,aAAa,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;YACzD,aAAa,GAAG,CAAC,QAAQ,EAAE,EAAE;gBAC3B,GAAG,eAAe;gBAClB,eAAe,QAAQ,aAAa;gBACpC,eAAe,QAAQ,aAAa;gBACpC,UAAU,QAAQ,QAAQ;gBAC1B,aAAa;YACf;QACF;QAEA,qCAAqC;QACrC,MAAM,yBAAyB,WAAW,GAAG,CAAC,CAAC,WAAa,CAAC;gBAC3D,GAAG,QAAQ;gBACX,cAAc,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC;YACvD,CAAC;QAED,kDAAkD;QAClD,IAAI,kBAAkB,WAAW,MAAM,GAAG,GAAG;YAC3C,+BAA+B;YAC/B,MAAM,oBAAoB,WACvB,MAAM,CAAC,CAAA,WAAY,CAAC,SAAS,OAAO,EACpC,GAAG,CAAC,CAAA,WAAY,SAAS,EAAE;YAE9B,8CAA8C;YAC9C,IAAI,kBAAkB,MAAM,GAAG,GAAG;gBAChC,gCAAgC;gBAChC,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChC,IAAI,CAAC,uBACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;gBAEZ,IAAI,WAAW;oBACb,QAAQ,KAAK,CAAC,0CAA0C;gBAC1D,OAAO;oBACL,kEAAkE;oBAClE,uBAAuB,OAAO,CAAC,CAAA;wBAC7B,IAAI,kBAAkB,QAAQ,CAAC,SAAS,EAAE,GAAG;4BAC3C,SAAS,OAAO,GAAG;wBACrB;oBACF;gBACF;YACF;QACF;QAEA,OAAO;YACL,YAAY;YACZ,OAAO,SAAS;YAChB,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO;YACL,YAAY,EAAE;YACd,OAAO;YACP,OAAO;QACT;IACF;AACF;AAMO,eAAe,qBAAqB,EACzC,iBAAiB,EACjB,WAAW,EAIZ;IACC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoB;IACtD;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAe;IACjD;IAEA,IAAI;QACF,yCAAyC;QACzC,IAAI,gBAAgB,OAAO;YACzB,kDAAkD;YAClD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC;gBAAE,SAAS;YAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;YAEZ,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,8CAA8C;gBAC5D,OAAO;oBAAE,SAAS;oBAAO,OAAO,MAAM,OAAO;gBAAC;YAChD;QACF,OAAO;YACL,qFAAqF;YACrF,MAAM,aAAa,MAAM,2CAA2C;YACpE,IAAI,UAAU;YACd,IAAI,iBAAiB;YAErB,MAAO,QAAS;gBACd,qCAAqC;gBACrC,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACzD,IAAI,CAAC,uBACL,MAAM,CAAC,MACP,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,WAAW,OACd,KAAK,CAAC;gBAET,IAAI,YAAY;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,OAAO;wBAAE,SAAS;wBAAO,OAAO,WAAW,OAAO;oBAAC;gBACrD;gBAEA,2CAA2C;gBAC3C,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;oBACtD,UAAU;oBACV;gBACF;gBAEA,6BAA6B;gBAC7B,MAAM,WAAW,iBAAiB,GAAG,CAAC,CAAA,WAAY,SAAS,EAAE;gBAE7D,0BAA0B;gBAC1B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,uBACL,MAAM,CAAC;oBAAE,SAAS;gBAAK,GACvB,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,MAAM;gBAEZ,IAAI,aAAa;oBACf,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,OAAO;wBAAE,SAAS;wBAAO,OAAO,YAAY,OAAO;oBAAC;gBACtD;gBAEA,0DAA0D;gBAC1D,kBAAkB,SAAS,MAAM;gBACjC,UAAU,SAAS,MAAM,KAAK,YAAY,8CAA8C;YAC1F;YAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,eAAe,mBAAmB,CAAC;QAC3D;QAEA,iCAAiC;QACjC,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,OAAO;YAAE,SAAS;YAAM,OAAO;QAAK;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAKO,eAAe,yBAAyB,iBAAyB;IACtE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;YAAG,OAAO;QAAoB;IAChD;IAEA,+CAA+C;IAC/C,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,OAAO;YAAG,OAAO;QAAe;IAC3C;IAEA,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,SAC5B,IAAI,CAAC,uBACL,MAAM,CAAC,KAAK;YAAE,OAAO;YAAS,MAAM;QAAK,GACzC,EAAE,CAAC,uBAAuB,mBAC1B,EAAE,CAAC,WAAW;QAEjB,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;gBAAE,OAAO;gBAAG,OAAO,MAAM,OAAO;YAAC;QAC1C;QAEA,OAAO;YAAE,OAAO,SAAS;YAAG,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YAAE,OAAO;YAAG,OAAO;QAA+B;IAC3D;AACF;;;IAlTsB;IA4KA;IAqGA;;AAjRA,+OAAA;AA4KA,+OAAA;AAqGA,+OAAA", "debugId": null}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/authSchemas.ts"], "sourcesContent": ["import { z } from \"zod\";\r\n\r\n// Reusable base schema for password complexity\r\nexport const PasswordComplexitySchema = z\r\n  .string()\r\n  .min(6, { message: \"Password must be at least 6 characters\" })\r\n  .regex(/[A-Z]/, { message: \"Password must contain at least one capital letter\" })\r\n  .regex(/[a-z]/, { message: \"Password must contain at least one lowercase letter.\" }) // Added lowercase check for consistency\r\n  .regex(/[0-9]/, { message: \"Password must contain at least one number\" })\r\n  .regex(/[^A-Za-z0-9]/, { message: \"Password must contain at least one symbol\" });\r\n\r\n// Reusable schema for mobile number validation\r\nexport const IndianMobileSchema = z\r\n  .string()\r\n  .min(10, { message: \"Mobile number must be at least 10 digits\" })\r\n  .max(10, { message: \"Mobile number must be exactly 10 digits\" })\r\n  .regex(/^\\d{10}$/, {\r\n    message: \"Please enter a valid 10-digit mobile number\"\r\n  });\r\n\r\n// Schema for forms requiring password confirmation (e.g., registration, change password)\r\nexport const PasswordConfirmationSchema = z\r\n  .object({\r\n    password: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Passwords don't match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for forms requiring new password confirmation (used in settings/reset)\r\n// Renaming fields for clarity in those contexts\r\nexport const NewPasswordConfirmationSchema = z\r\n  .object({\r\n    newPassword: PasswordComplexitySchema,\r\n    confirmPassword: z.string(),\r\n  })\r\n  .refine((data) => data.newPassword === data.confirmPassword, {\r\n    message: \"Passwords do not match.\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\n// Schema for just the password field (e.g., for login or single field validation)\r\nexport const SinglePasswordSchema = z.object({\r\n    password: PasswordComplexitySchema,\r\n});\r\n\r\n// Schema for just the new password field (used in reset password action)\r\nexport const SingleNewPasswordSchema = z.object({\r\n    password: PasswordComplexitySchema, // Action receives it as 'password'\r\n});\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CACtC,MAAM,GACN,GAAG,CAAC,GAAG;IAAE,SAAS;AAAyC,GAC3D,KAAK,CAAC,SAAS;IAAE,SAAS;AAAoD,GAC9E,KAAK,CAAC,SAAS;IAAE,SAAS;AAAuD,GAAG,wCAAwC;CAC5H,KAAK,CAAC,SAAS;IAAE,SAAS;AAA4C,GACtE,KAAK,CAAC,gBAAgB;IAAE,SAAS;AAA4C;AAGzE,MAAM,qBAAqB,oIAAA,CAAA,IAAC,CAChC,MAAM,GACN,GAAG,CAAC,IAAI;IAAE,SAAS;AAA2C,GAC9D,GAAG,CAAC,IAAI;IAAE,SAAS;AAA0C,GAC7D,KAAK,CAAC,YAAY;IACjB,SAAS;AACX;AAGK,MAAM,6BAA6B,oIAAA,CAAA,IAAC,CACxC,MAAM,CAAC;IACN,UAAU;IACV,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAIK,MAAM,gCAAgC,oIAAA,CAAA,IAAC,CAC3C,MAAM,CAAC;IACN,aAAa;IACb,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;AAC3B,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,WAAW,KAAK,KAAK,eAAe,EAAE;IAC3D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGK,MAAM,uBAAuB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,UAAU;AACd;AAGO,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,UAAU;AACd", "debugId": null}}, {"offset": {"line": 428, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/schema.ts"], "sourcesContent": ["import * as z from \"zod\";\r\nimport { IndianMobileSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Regular expression for validating hex color codes (e.g., #RRGGBB, #RGB)\r\n// const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/; // Removed as theme_color is removed\r\n\r\n// Zod schema for business card data validation (Phase 1)\r\nexport const businessCardSchema = z.object({\r\n  // Optional fields first\r\n  logo_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for logo/profile photo.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .nullable(), // Allow empty string, null, or valid URL\r\n  established_year: z\r\n    .number()\r\n    .int({ message: \"Established year must be a whole number.\" })\r\n    .min(1800, { message: \"Established year must be after 1800.\" })\r\n    .max(new Date().getFullYear(), { message: \"Established year cannot be in the future.\" })\r\n    .optional()\r\n    .nullable(),\r\n  // Address broken down - NOW REQUIRED (from onboarding)\r\n  address_line: z\r\n    .string()\r\n    .min(1, { message: \"Address line is required.\" })\r\n    .max(100, { message: \"Address line cannot exceed 100 characters.\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality/area is required.\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required.\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required.\" }),\r\n  pincode: z\r\n    .string()\r\n    .min(6, { message: \"Pincode must be 6 digits.\" })\r\n    .max(6, { message: \"Pincode must be 6 digits.\" })\r\n    .regex(/^\\d+$/, { message: \"Pincode must contain only digits.\" }),\r\n  phone: IndianMobileSchema, // Primary display phone - NOW REQUIRED\r\n  // timing_info removed\r\n  // delivery_info removed\r\n  // website_url removed\r\n  instagram_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Instagram.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  facebook_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Facebook.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // linkedin_url removed\r\n  // twitter_url removed\r\n  // youtube_url removed\r\n  whatsapp_number: IndianMobileSchema // For wa.me link\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // call_number removed\r\n  about_bio: z\r\n    .string()\r\n    .max(100, { message: \"Bio cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  theme_color: z // Added for Growth plan\r\n    .string()\r\n    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {\r\n      message: \"Invalid hex color format (e.g., #RRGGBB or #RGB).\",\r\n    })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // card_texture field removed as it doesn't exist in the database\r\n  business_hours: z.any().optional().nullable(), // Added for Growth plan - Using z.any() for now, refine if specific structure needed\r\n  delivery_info: z // Added for Growth plan\r\n    .string()\r\n    .max(100, { message: \"Delivery info cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  business_category: z\r\n    .string()\r\n    .min(1, { message: \"Business category is required.\" }),\r\n  google_maps_url: z\r\n    .string()\r\n    .url({ message: \"Please enter a valid Google Maps URL.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .refine((url) => {\r\n      if (!url || url === \"\") return true; // Allow empty\r\n      // Validate Google Maps URL patterns\r\n      const googleMapsPatterns = [\r\n        /^https:\\/\\/maps\\.app\\.goo\\.gl\\/[a-zA-Z0-9]+$/,\r\n        /^https:\\/\\/www\\.google\\.com\\/maps\\//,\r\n        /^https:\\/\\/goo\\.gl\\/maps\\//,\r\n        /^https:\\/\\/maps\\.google\\.com\\//\r\n      ];\r\n      return googleMapsPatterns.some(pattern => pattern.test(url));\r\n    }, {\r\n      message: \"Please enter a valid Google Maps URL (e.g., https://maps.app.goo.gl/... or https://www.google.com/maps/...)\"\r\n    }),\r\n  status: z.enum([\"online\", \"offline\"]).default(\"offline\"),\r\n  // Custom branding fields for Pro/Enterprise users\r\n  custom_branding: z.object({\r\n    custom_header_text: z.string().max(50).optional().or(z.literal(\"\")),\r\n    custom_header_image_url: z.string().url().optional().or(z.literal(\"\")), // Legacy field\r\n    custom_header_image_light_url: z.string().url().optional().or(z.literal(\"\")), // Light theme\r\n    custom_header_image_dark_url: z.string().url().optional().or(z.literal(\"\")), // Dark theme\r\n    hide_dukancard_branding: z.boolean().optional(),\r\n    // File objects for pending uploads (not saved to database)\r\n    pending_light_header_file: z.any().optional(), // File object for light theme\r\n    pending_dark_header_file: z.any().optional(), // File object for dark theme\r\n  }).optional()\r\n  .refine((data) => {\r\n    // Only require custom_header_text OR any header image if hide_dukancard_branding is explicitly true\r\n    if (data?.hide_dukancard_branding === true) {\r\n      const hasText = data?.custom_header_text && data.custom_header_text.trim() !== \"\";\r\n      const hasLegacyImage = data?.custom_header_image_url && data.custom_header_image_url.trim() !== \"\";\r\n      const hasLightImage = data?.custom_header_image_light_url && data.custom_header_image_light_url.trim() !== \"\";\r\n      const hasDarkImage = data?.custom_header_image_dark_url && data.custom_header_image_dark_url.trim() !== \"\";\r\n\r\n      if (!hasText && !hasLegacyImage && !hasLightImage && !hasDarkImage) {\r\n        return false;\r\n      }\r\n    }\r\n    return true;\r\n  }, {\r\n    message: \"Custom header text or image is required when hiding Dukancard branding\",\r\n    path: [\"custom_header_text\"]\r\n  }),\r\n  // Custom ads for Pro/Enterprise users\r\n  custom_ads: z.object({\r\n    enabled: z.boolean().optional(),\r\n    image_url: z.string().url().optional().or(z.literal(\"\")),\r\n    link_url: z.string().url().optional().or(z.literal(\"\")),\r\n    uploaded_at: z.string().optional().or(z.literal(\"\")).nullable(),\r\n  }).optional(),\r\n  business_slug: z\r\n    .string()\r\n    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {\r\n      message:\r\n        \"Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen.\",\r\n    })\r\n    .min(3, { message: \"Slug must be at least 3 characters long.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n\r\n  // Required fields\r\n  member_name: z\r\n    .string()\r\n    .min(1, { message: \"Member name is required.\" })\r\n    .max(50, { message: \"Name cannot exceed 50 characters.\" }),\r\n  title: z\r\n    .string()\r\n    .min(1, { message: \"Title/Designation is required.\" })\r\n    .max(50, { message: \"Title cannot exceed 50 characters.\" }),\r\n  business_name: z\r\n    .string()\r\n    .min(1, { message: \"Business name is required.\" })\r\n    .max(100, { message: \"Business name cannot exceed 100 characters.\" }),\r\n\r\n  // Read-only/managed fields (keep for type safety if needed)\r\n  id: z.string().uuid().optional(),\r\n  contact_email: z.string().email({ message: \"Please enter a valid email address\" }).min(1, { message: \"Contact email is required\" }),\r\n  has_active_subscription: z.boolean().optional(),\r\n  trial_end_date: z.string().optional().nullable(), // Database returns string, not Date\r\n  created_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n  updated_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n\r\n  // Interaction fields (added in Phase 2) - make optional as they might not always be fetched\r\n  total_likes: z.number().int().nonnegative().optional(),\r\n  total_subscriptions: z.number().int().nonnegative().optional(),\r\n  average_rating: z.number().nonnegative().optional(),\r\n  total_visits: z.number().int().nonnegative().optional(),\r\n});\r\n\r\n// TypeScript type inferred from the Zod schema\r\nexport type BusinessCardData = z.infer<typeof businessCardSchema>;\r\n\r\n// Default values for initializing the form or preview (Phase 1)\r\nexport const defaultBusinessCardData: Partial<BusinessCardData> = {\r\n  member_name: \"\",\r\n  title: \"\",\r\n  business_name: \"\",\r\n  logo_url: null,\r\n  established_year: null,\r\n  address_line: \"\",\r\n  locality: \"\",\r\n  city: \"\",\r\n  state: \"\",\r\n  pincode: \"\",\r\n  phone: \"\",\r\n  instagram_url: \"\",\r\n  facebook_url: \"\",\r\n  whatsapp_number: \"\",\r\n  about_bio: \"\",\r\n  theme_color: \"\",\r\n  business_hours: null,\r\n  delivery_info: \"\",\r\n  business_category: \"\",\r\n  google_maps_url: \"\",\r\n  status: \"offline\",\r\n  business_slug: \"\",\r\n  contact_email: \"\", // Added contact_email field\r\n  custom_branding: {\r\n    custom_header_text: \"\",\r\n    custom_header_image_url: \"\", // Legacy field\r\n    custom_header_image_light_url: \"\", // Light theme\r\n    custom_header_image_dark_url: \"\", // Dark theme\r\n    hide_dukancard_branding: false,\r\n    pending_light_header_file: null, // File object for light theme\r\n    pending_dark_header_file: null, // File object for dark theme\r\n  },\r\n  custom_ads: {\r\n    enabled: false,\r\n    image_url: \"\",\r\n    link_url: \"\",\r\n    uploaded_at: null,\r\n  },\r\n};\r\n\r\n// Define which fields are strictly required to go online\r\nexport const requiredFieldsForOnline: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\",\r\n  \"contact_email\", // Added contact_email as required for online status\r\n  \"business_category\" // Added business_category as required for online status\r\n];\r\n\r\n// Define which fields are required for saving regardless of status (all onboarding fields except plan)\r\nexport const requiredFieldsForSaving: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"contact_email\",\r\n  \"business_category\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\"\r\n];\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAMO,MAAM,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACzC,wBAAwB;IACxB,UAAU,CAAA,GAAA,oIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA6C,GAC5D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE,KACb,QAAQ;IACX,kBAAkB,CAAA,GAAA,oIAAA,CAAA,SACT,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA2C,GAC1D,GAAG,CAAC,MAAM;QAAE,SAAS;IAAuC,GAC5D,GAAG,CAAC,IAAI,OAAO,WAAW,IAAI;QAAE,SAAS;IAA4C,GACrF,QAAQ,GACR,QAAQ;IACX,uDAAuD;IACvD,cAAc,CAAA,GAAA,oIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA6C;IACpE,UAAU,CAAA,GAAA,oIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;IAClD,MAAM,CAAA,GAAA,oIAAA,CAAA,SACG,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB;IACzC,OAAO,CAAA,GAAA,oIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAqB;IAC1C,SAAS,CAAA,GAAA,oIAAA,CAAA,SACA,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,KAAK,CAAC,SAAS;QAAE,SAAS;IAAoC;IACjE,OAAO,6HAAA,CAAA,qBAAkB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,sBAAsB;IACtB,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAoC,GACnD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,CAAA,GAAA,oIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAmC,GAClD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,iBAAiB,8HAAmB,iBAAiB;IAApC,CAAA,qBAAkB,CAChC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,sBAAsB;IACtB,WAAW,CAAA,GAAA,oIAAA,CAAA,SACF,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAAoC,GACxD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,aAAa,CAAA,GAAA,oIAAA,CAAA,SACJ,AAAD,IACL,KAAK,CAAC,sCAAsC;QAC3C,SAAS;IACX,GACC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,iEAAiE;IACjE,gBAAgB,CAAA,GAAA,oIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C,GAClE,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,mBAAmB,CAAA,GAAA,oIAAA,CAAA,SACV,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC;IACtD,iBAAiB,CAAA,GAAA,oIAAA,CAAA,SACR,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAwC,GACvD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE,KACb,MAAM,CAAC,CAAC;QACP,IAAI,CAAC,OAAO,QAAQ,IAAI,OAAO,MAAM,cAAc;QACnD,oCAAoC;QACpC,MAAM,qBAAqB;YACzB;YACA;YACA;YACA;SACD;QACD,OAAO,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;IACzD,GAAG;QACD,SAAS;IACX;IACF,QAAQ,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAU;KAAU,EAAE,OAAO,CAAC;IAC9C,kDAAkD;IAClD,iBAAiB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;QACxB,oBAAoB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QAC/D,yBAAyB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QAClE,+BAA+B,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACxE,8BAA8B,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACvE,yBAAyB,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7C,2DAA2D;QAC3D,2BAA2B,CAAA,GAAA,oIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;QAC3C,0BAA0B,CAAA,GAAA,oIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ;IAC5C,GAAG,QAAQ,GACV,MAAM,CAAC,CAAC;QACP,oGAAoG;QACpG,IAAI,MAAM,4BAA4B,MAAM;YAC1C,MAAM,UAAU,MAAM,sBAAsB,KAAK,kBAAkB,CAAC,IAAI,OAAO;YAC/E,MAAM,iBAAiB,MAAM,2BAA2B,KAAK,uBAAuB,CAAC,IAAI,OAAO;YAChG,MAAM,gBAAgB,MAAM,iCAAiC,KAAK,6BAA6B,CAAC,IAAI,OAAO;YAC3G,MAAM,eAAe,MAAM,gCAAgC,KAAK,4BAA4B,CAAC,IAAI,OAAO;YAExG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,cAAc;gBAClE,OAAO;YACT;QACF;QACA,OAAO;IACT,GAAG;QACD,SAAS;QACT,MAAM;YAAC;SAAqB;IAC9B;IACA,sCAAsC;IACtC,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;QACnB,SAAS,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;QAC7B,WAAW,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACpD,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;QACnD,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE,KAAK,QAAQ;IAC/D,GAAG,QAAQ;IACX,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,KAAK,CAAC,8BAA8B;QACnC,SACE;IACJ,GACC,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAEhB,kBAAkB;IAClB,aAAa,CAAA,GAAA,oIAAA,CAAA,SACJ,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAC7C,GAAG,CAAC,IAAI;QAAE,SAAS;IAAoC;IAC1D,OAAO,CAAA,GAAA,oIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC,GACnD,GAAG,CAAC,IAAI;QAAE,SAAS;IAAqC;IAC3D,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B,GAC/C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C;IAErE,4DAA4D;IAC5D,IAAI,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,GAAG,QAAQ;IAC9B,eAAe,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAAqC,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B;IACjI,yBAAyB,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ;IAC7C,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC9C,YAAY,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IACA,YAAY,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IAEA,4FAA4F;IAC5F,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IACpD,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IAC5D,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,WAAW,GAAG,QAAQ;IACjD,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;AACvD;AAMO,MAAM,0BAAqD;IAChE,aAAa;IACb,OAAO;IACP,eAAe;IACf,UAAU;IACV,kBAAkB;IAClB,cAAc;IACd,UAAU;IACV,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IACnB,iBAAiB;IACjB,QAAQ;IACR,eAAe;IACf,eAAe;IACf,iBAAiB;QACf,oBAAoB;QACpB,yBAAyB;QACzB,+BAA+B;QAC/B,8BAA8B;QAC9B,yBAAyB;QACzB,2BAA2B;QAC3B,0BAA0B;IAC5B;IACA,YAAY;QACV,SAAS;QACT,WAAW;QACX,UAAU;QACV,aAAa;IACf;AACF;AAGO,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB,wDAAwD;CAC7E;AAGM,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/data/businessCardMapper.ts"], "sourcesContent": ["import { BusinessCardData } from \"../schema\";\r\nimport { ProductServiceData } from \"../../products/actions\";\r\n\r\n// Type for raw database data\r\ntype RawBusinessCardData = Record<string, unknown> & {\r\n  id?: string;\r\n  business_name?: string | null;\r\n  contact_email?: string | null;\r\n  logo_url?: string | null;\r\n  member_name?: string | null;\r\n  title?: string | null;\r\n  address_line?: string | null;\r\n  city?: string | null;\r\n  state?: string | null;\r\n  pincode?: string | null;\r\n  locality?: string | null;\r\n  phone?: string | null;\r\n  instagram_url?: string | null;\r\n  facebook_url?: string | null;\r\n  whatsapp_number?: string | null;\r\n  about_bio?: string | null;\r\n  status?: string | null;\r\n  business_slug?: string | null;\r\n  theme_color?: string | null;\r\n  delivery_info?: string | null;\r\n  business_category?: string | null;\r\n  google_maps_url?: string | null;\r\n  established_year?: number | null;\r\n  total_likes?: number | null;\r\n  total_subscriptions?: number | null;\r\n  average_rating?: number | null;\r\n  business_hours?: unknown;\r\n  trial_end_date?: string | null;\r\n  created_at?: string | null;\r\n  updated_at?: string | null;\r\n  has_active_subscription?: boolean | null;\r\n  custom_branding?: Record<string, unknown>;\r\n  custom_ads?: Record<string, unknown>;\r\n  products_services?: unknown[];\r\n};\r\n\r\n/**\r\n * Maps raw database data to BusinessCardData type with proper defaults\r\n * @param data - Raw data from database\r\n * @returns Properly mapped BusinessCardData\r\n */\r\nexport function mapBusinessCardData(data: RawBusinessCardData): BusinessCardData {\r\n  return {\r\n    id: data.id,\r\n    business_name: data.business_name ?? \"\",\r\n    contact_email: data.contact_email ?? \"\",\r\n    has_active_subscription: Boolean(data.has_active_subscription) ?? false,\r\n    trial_end_date: (typeof data.trial_end_date === 'string' ? data.trial_end_date : null),\r\n    created_at: (typeof data.created_at === 'string' ? data.created_at : undefined),\r\n    updated_at: (typeof data.updated_at === 'string' ? data.updated_at : undefined),\r\n    logo_url: (typeof data.logo_url === 'string' ? data.logo_url : null),\r\n    member_name: (typeof data.member_name === 'string' ? data.member_name : \"\"),\r\n    title: (typeof data.title === 'string' ? data.title : \"\"),\r\n    address_line: (typeof data.address_line === 'string' ? data.address_line : \"\"),\r\n    city: (typeof data.city === 'string' ? data.city : \"\"),\r\n    state: (typeof data.state === 'string' ? data.state : \"\"),\r\n    pincode: (typeof data.pincode === 'string' ? data.pincode : \"\"),\r\n    locality: (typeof data.locality === 'string' ? data.locality : \"\"),\r\n    phone: (typeof data.phone === 'string' ? data.phone : \"\"),\r\n    instagram_url: (typeof data.instagram_url === 'string' ? data.instagram_url : \"\"),\r\n    facebook_url: (typeof data.facebook_url === 'string' ? data.facebook_url : \"\"),\r\n    whatsapp_number: (typeof data.whatsapp_number === 'string' ? data.whatsapp_number : \"\"),\r\n    about_bio: (typeof data.about_bio === 'string' ? data.about_bio : \"\"),\r\n    status: (typeof data.status === 'string' && (data.status === 'online' || data.status === 'offline') ? data.status : \"offline\"),\r\n    business_slug: (typeof data.business_slug === 'string' ? data.business_slug : \"\"),\r\n    // Include metrics data\r\n    total_likes: (typeof data.total_likes === 'number' ? data.total_likes : 0),\r\n    total_subscriptions: (typeof data.total_subscriptions === 'number' ? data.total_subscriptions : 0),\r\n    average_rating: (typeof data.average_rating === 'number' ? data.average_rating : 0),\r\n    // Include additional fields\r\n    theme_color: (typeof data.theme_color === 'string' ? data.theme_color : \"\"),\r\n    delivery_info: (typeof data.delivery_info === 'string' ? data.delivery_info : \"\"),\r\n    business_hours: data.business_hours ?? null,\r\n    business_category: (typeof data.business_category === 'string' ? data.business_category : \"\"),\r\n    google_maps_url: (typeof data.google_maps_url === 'string' ? data.google_maps_url : \"\"),\r\n    established_year: (typeof data.established_year === 'number' ? data.established_year : null),\r\n    custom_branding: {\r\n      custom_header_text: (typeof data.custom_branding?.custom_header_text === 'string' ? data.custom_branding.custom_header_text : \"\"),\r\n      custom_header_image_url: (typeof data.custom_branding?.custom_header_image_url === 'string' ? data.custom_branding.custom_header_image_url : \"\"),\r\n      custom_header_image_light_url: (typeof data.custom_branding?.custom_header_image_light_url === 'string' ? data.custom_branding.custom_header_image_light_url : \"\"),\r\n      custom_header_image_dark_url: (typeof data.custom_branding?.custom_header_image_dark_url === 'string' ? data.custom_branding.custom_header_image_dark_url : \"\"),\r\n      hide_dukancard_branding: (typeof data.custom_branding?.hide_dukancard_branding === 'boolean' ? data.custom_branding.hide_dukancard_branding : false),\r\n      pending_light_header_file: null, // Always null when loading from database\r\n      pending_dark_header_file: null, // Always null when loading from database\r\n    },\r\n    custom_ads: (data.custom_ads && typeof data.custom_ads === 'object') ? {\r\n      enabled: (typeof data.custom_ads.enabled === 'boolean' ? data.custom_ads.enabled : false),\r\n      image_url: (typeof data.custom_ads.image_url === 'string' ? data.custom_ads.image_url : \"\"),\r\n      link_url: (typeof data.custom_ads.link_url === 'string' ? data.custom_ads.link_url : \"\"),\r\n      uploaded_at: (typeof data.custom_ads.uploaded_at === 'string' ? data.custom_ads.uploaded_at : \"\"),\r\n    } : {\r\n      enabled: false,\r\n      image_url: \"\",\r\n      link_url: \"\",\r\n      uploaded_at: \"\",\r\n    },\r\n  };\r\n}\r\n\r\n/**\r\n * Maps public card data with products/services\r\n * @param data - Raw data from database\r\n * @returns Mapped data for public card display\r\n */\r\nexport function mapPublicCardData(data: RawBusinessCardData): BusinessCardData & { products_services?: ProductServiceData[] } {\r\n  // Sort products (display_order removed, sort by created_at desc as fallback)\r\n  const sortedProducts =\r\n    (data.products_services as Partial<ProductServiceData>[])\r\n      ?.sort(\r\n        (a, b) =>\r\n          new Date(b.created_at ?? 0).getTime() -\r\n          new Date(a.created_at ?? 0).getTime()\r\n      )\r\n      .slice(0, 10) ?? [];\r\n\r\n  return {\r\n    id: data.id,\r\n    business_name: data.business_name ?? \"\",\r\n    contact_email: data.contact_email ?? \"\",\r\n    has_active_subscription: Boolean(data.has_active_subscription) ?? false,\r\n    trial_end_date: (typeof data.trial_end_date === 'string' ? data.trial_end_date : null),\r\n    created_at: (typeof data.created_at === 'string' ? data.created_at : undefined),\r\n    updated_at: (typeof data.updated_at === 'string' ? data.updated_at : undefined),\r\n    logo_url: (typeof data.logo_url === 'string' ? data.logo_url : null),\r\n    member_name: (typeof data.member_name === 'string' ? data.member_name : \"\"),\r\n    title: (typeof data.title === 'string' ? data.title : \"\"),\r\n    address_line: (typeof data.address_line === 'string' ? data.address_line : \"\"),\r\n    city: (typeof data.city === 'string' ? data.city : \"\"),\r\n    state: (typeof data.state === 'string' ? data.state : \"\"),\r\n    pincode: (typeof data.pincode === 'string' ? data.pincode : \"\"),\r\n    locality: (typeof data.locality === 'string' ? data.locality : \"\"),\r\n    phone: (typeof data.phone === 'string' ? data.phone : \"\"),\r\n    business_category: (typeof data.business_category === 'string' ? data.business_category : \"\"),\r\n    business_hours: data.business_hours ?? null,\r\n    delivery_info: (typeof data.delivery_info === 'string' ? data.delivery_info : \"\"),\r\n    google_maps_url: (typeof data.google_maps_url === 'string' ? data.google_maps_url : \"\"),\r\n    established_year: (typeof data.established_year === 'number' ? data.established_year : null),\r\n    instagram_url: (typeof data.instagram_url === 'string' ? data.instagram_url : \"\"),\r\n    facebook_url: (typeof data.facebook_url === 'string' ? data.facebook_url : \"\"),\r\n    whatsapp_number: (typeof data.whatsapp_number === 'string' ? data.whatsapp_number : \"\"),\r\n    about_bio: (typeof data.about_bio === 'string' ? data.about_bio : \"\"),\r\n    status: (typeof data.status === 'string' && (data.status === 'online' || data.status === 'offline') ? data.status : \"offline\"),\r\n    business_slug: (typeof data.business_slug === 'string' ? data.business_slug : \"\"),\r\n    products_services: sortedProducts as ProductServiceData[],\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AA8CO,SAAS,oBAAoB,IAAyB;IAC3D,OAAO;QACL,IAAI,KAAK,EAAE;QACX,eAAe,KAAK,aAAa,IAAI;QACrC,eAAe,KAAK,aAAa,IAAI;QACrC,yBAAyB,QAAQ,KAAK,uBAAuB,KAAK;QAClE,gBAAiB,OAAO,KAAK,cAAc,KAAK,WAAW,KAAK,cAAc,GAAG;QACjF,YAAa,OAAO,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,GAAG;QACrE,YAAa,OAAO,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,GAAG;QACrE,UAAW,OAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,GAAG;QAC/D,aAAc,OAAO,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,GAAG;QACxE,OAAQ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;QACtD,cAAe,OAAO,KAAK,YAAY,KAAK,WAAW,KAAK,YAAY,GAAG;QAC3E,MAAO,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAG;QACnD,OAAQ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;QACtD,SAAU,OAAO,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,GAAG;QAC5D,UAAW,OAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,GAAG;QAC/D,OAAQ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;QACtD,eAAgB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,aAAa,GAAG;QAC9E,cAAe,OAAO,KAAK,YAAY,KAAK,WAAW,KAAK,YAAY,GAAG;QAC3E,iBAAkB,OAAO,KAAK,eAAe,KAAK,WAAW,KAAK,eAAe,GAAG;QACpF,WAAY,OAAO,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS,GAAG;QAClE,QAAS,OAAO,KAAK,MAAM,KAAK,YAAY,CAAC,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK,MAAM,GAAG;QACpH,eAAgB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,aAAa,GAAG;QAC9E,uBAAuB;QACvB,aAAc,OAAO,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,GAAG;QACxE,qBAAsB,OAAO,KAAK,mBAAmB,KAAK,WAAW,KAAK,mBAAmB,GAAG;QAChG,gBAAiB,OAAO,KAAK,cAAc,KAAK,WAAW,KAAK,cAAc,GAAG;QACjF,4BAA4B;QAC5B,aAAc,OAAO,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,GAAG;QACxE,eAAgB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,aAAa,GAAG;QAC9E,gBAAgB,KAAK,cAAc,IAAI;QACvC,mBAAoB,OAAO,KAAK,iBAAiB,KAAK,WAAW,KAAK,iBAAiB,GAAG;QAC1F,iBAAkB,OAAO,KAAK,eAAe,KAAK,WAAW,KAAK,eAAe,GAAG;QACpF,kBAAmB,OAAO,KAAK,gBAAgB,KAAK,WAAW,KAAK,gBAAgB,GAAG;QACvF,iBAAiB;YACf,oBAAqB,OAAO,KAAK,eAAe,EAAE,uBAAuB,WAAW,KAAK,eAAe,CAAC,kBAAkB,GAAG;YAC9H,yBAA0B,OAAO,KAAK,eAAe,EAAE,4BAA4B,WAAW,KAAK,eAAe,CAAC,uBAAuB,GAAG;YAC7I,+BAAgC,OAAO,KAAK,eAAe,EAAE,kCAAkC,WAAW,KAAK,eAAe,CAAC,6BAA6B,GAAG;YAC/J,8BAA+B,OAAO,KAAK,eAAe,EAAE,iCAAiC,WAAW,KAAK,eAAe,CAAC,4BAA4B,GAAG;YAC5J,yBAA0B,OAAO,KAAK,eAAe,EAAE,4BAA4B,YAAY,KAAK,eAAe,CAAC,uBAAuB,GAAG;YAC9I,2BAA2B;YAC3B,0BAA0B;QAC5B;QACA,YAAY,AAAC,KAAK,UAAU,IAAI,OAAO,KAAK,UAAU,KAAK,WAAY;YACrE,SAAU,OAAO,KAAK,UAAU,CAAC,OAAO,KAAK,YAAY,KAAK,UAAU,CAAC,OAAO,GAAG;YACnF,WAAY,OAAO,KAAK,UAAU,CAAC,SAAS,KAAK,WAAW,KAAK,UAAU,CAAC,SAAS,GAAG;YACxF,UAAW,OAAO,KAAK,UAAU,CAAC,QAAQ,KAAK,WAAW,KAAK,UAAU,CAAC,QAAQ,GAAG;YACrF,aAAc,OAAO,KAAK,UAAU,CAAC,WAAW,KAAK,WAAW,KAAK,UAAU,CAAC,WAAW,GAAG;QAChG,IAAI;YACF,SAAS;YACT,WAAW;YACX,UAAU;YACV,aAAa;QACf;IACF;AACF;AAOO,SAAS,kBAAkB,IAAyB;IACzD,6EAA6E;IAC7E,MAAM,iBACJ,AAAC,KAAK,iBAAiB,EACnB,KACA,CAAC,GAAG,IACF,IAAI,KAAK,EAAE,UAAU,IAAI,GAAG,OAAO,KACnC,IAAI,KAAK,EAAE,UAAU,IAAI,GAAG,OAAO,IAEtC,MAAM,GAAG,OAAO,EAAE;IAEvB,OAAO;QACL,IAAI,KAAK,EAAE;QACX,eAAe,KAAK,aAAa,IAAI;QACrC,eAAe,KAAK,aAAa,IAAI;QACrC,yBAAyB,QAAQ,KAAK,uBAAuB,KAAK;QAClE,gBAAiB,OAAO,KAAK,cAAc,KAAK,WAAW,KAAK,cAAc,GAAG;QACjF,YAAa,OAAO,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,GAAG;QACrE,YAAa,OAAO,KAAK,UAAU,KAAK,WAAW,KAAK,UAAU,GAAG;QACrE,UAAW,OAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,GAAG;QAC/D,aAAc,OAAO,KAAK,WAAW,KAAK,WAAW,KAAK,WAAW,GAAG;QACxE,OAAQ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;QACtD,cAAe,OAAO,KAAK,YAAY,KAAK,WAAW,KAAK,YAAY,GAAG;QAC3E,MAAO,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,GAAG;QACnD,OAAQ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;QACtD,SAAU,OAAO,KAAK,OAAO,KAAK,WAAW,KAAK,OAAO,GAAG;QAC5D,UAAW,OAAO,KAAK,QAAQ,KAAK,WAAW,KAAK,QAAQ,GAAG;QAC/D,OAAQ,OAAO,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,GAAG;QACtD,mBAAoB,OAAO,KAAK,iBAAiB,KAAK,WAAW,KAAK,iBAAiB,GAAG;QAC1F,gBAAgB,KAAK,cAAc,IAAI;QACvC,eAAgB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,aAAa,GAAG;QAC9E,iBAAkB,OAAO,KAAK,eAAe,KAAK,WAAW,KAAK,eAAe,GAAG;QACpF,kBAAmB,OAAO,KAAK,gBAAgB,KAAK,WAAW,KAAK,gBAAgB,GAAG;QACvF,eAAgB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,aAAa,GAAG;QAC9E,cAAe,OAAO,KAAK,YAAY,KAAK,WAAW,KAAK,YAAY,GAAG;QAC3E,iBAAkB,OAAO,KAAK,eAAe,KAAK,WAAW,KAAK,eAAe,GAAG;QACpF,WAAY,OAAO,KAAK,SAAS,KAAK,WAAW,KAAK,SAAS,GAAG;QAClE,QAAS,OAAO,KAAK,MAAM,KAAK,YAAY,CAAC,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,KAAK,SAAS,IAAI,KAAK,MAAM,GAAG;QACpH,eAAgB,OAAO,KAAK,aAAa,KAAK,WAAW,KAAK,aAAa,GAAG;QAC9E,mBAAmB;IACrB;AACF", "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/data/subscriptionChecker.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\n/**\r\n * Checks if a user's subscription allows them to go online\r\n * @param userId - The user ID to check\r\n * @returns Object indicating if user can go online and any error message\r\n */\r\nexport async function checkSubscriptionStatus(userId: string): Promise<{\r\n  canGoOnline: boolean;\r\n  error?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  // Fetch subscription status from payment_subscriptions\r\n  const { data: subscription, error: subscriptionError } = await supabase\r\n    .from(\"payment_subscriptions\")\r\n    .select(\"subscription_status\")\r\n    .eq(\"business_profile_id\", userId)\r\n    .order(\"created_at\", { ascending: false })\r\n    .limit(1)\r\n    .maybeSingle();\r\n\r\n  if (subscriptionError) {\r\n    console.error(\"Error fetching subscription data:\", subscriptionError);\r\n    // Continue with caution - allow going online if we can't check\r\n    return { canGoOnline: true };\r\n  }\r\n\r\n  // If subscription is halted, prevent going online\r\n  if (subscription?.subscription_status === \"halted\") {\r\n    console.log(`User ${userId} attempted to set card online with halted subscription`);\r\n    return {\r\n      canGoOnline: false,\r\n      error: \"Cannot set card to online status while your subscription is paused. Please resume your subscription first.\"\r\n    };\r\n  }\r\n\r\n  return { canGoOnline: true };\r\n}\r\n\r\n/**\r\n * Checks subscription status for forcing offline during data fetch\r\n * @param userId - The user ID to check\r\n * @param supabase - Supabase client instance\r\n * @returns Object indicating if card should be forced offline\r\n */\r\nexport async function checkForceOfflineStatus(userId: string, supabase: Awaited<ReturnType<typeof import(\"@/utils/supabase/server\").createClient>>): Promise<{\r\n  shouldForceOffline: boolean;\r\n  reason?: string;\r\n}> {\r\n  // Fetch subscription status from payment_subscriptions\r\n  const { data: subscription, error: subscriptionError } = await supabase\r\n    .from(\"payment_subscriptions\")\r\n    .select(\"subscription_status\")\r\n    .eq(\"business_profile_id\", userId)\r\n    .order(\"created_at\", { ascending: false })\r\n    .limit(1)\r\n    .maybeSingle();\r\n\r\n  if (subscriptionError) {\r\n    console.error(\"Error fetching subscription data:\", subscriptionError);\r\n    return { shouldForceOffline: false };\r\n  }\r\n\r\n  // Import centralized manager for consistent logic\r\n  const { SUBSCRIPTION_STATUS } = await import('@/lib/razorpay/webhooks/handlers/utils');\r\n\r\n  // Check if subscription is halted - force offline if it is\r\n  if (subscription?.subscription_status === SUBSCRIPTION_STATUS.HALTED) {\r\n    return {\r\n      shouldForceOffline: true,\r\n      reason: \"subscription is paused\"\r\n    };\r\n  }\r\n\r\n  return { shouldForceOffline: false };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;AAOO,eAAe,wBAAwB,MAAc;IAI1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uDAAuD;IACvD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,yBACL,MAAM,CAAC,uBACP,EAAE,CAAC,uBAAuB,QAC1B,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC,GACN,WAAW;IAEd,IAAI,mBAAmB;QACrB,QAAQ,KAAK,CAAC,qCAAqC;QACnD,+DAA+D;QAC/D,OAAO;YAAE,aAAa;QAAK;IAC7B;IAEA,kDAAkD;IAClD,IAAI,cAAc,wBAAwB,UAAU;QAClD,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,OAAO,sDAAsD,CAAC;QAClF,OAAO;YACL,aAAa;YACb,OAAO;QACT;IACF;IAEA,OAAO;QAAE,aAAa;IAAK;AAC7B;AAQO,eAAe,wBAAwB,MAAc,EAAE,QAAoF;IAIhJ,uDAAuD;IACvD,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,yBACL,MAAM,CAAC,uBACP,EAAE,CAAC,uBAAuB,QAC1B,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC,GACN,WAAW;IAEd,IAAI,mBAAmB;QACrB,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;YAAE,oBAAoB;QAAM;IACrC;IAEA,kDAAkD;IAClD,MAAM,EAAE,mBAAmB,EAAE,GAAG;IAEhC,2DAA2D;IAC3D,IAAI,cAAc,wBAAwB,oBAAoB,MAAM,EAAE;QACpE,OAAO;YACL,oBAAoB;YACpB,QAAQ;QACV;IACF;IAEA,OAAO;QAAE,oBAAoB;IAAM;AACrC;;;IArEsB;IAuCA;;AAvCA,+OAAA;AAuCA,+OAAA", "debugId": null}}, {"offset": {"line": 852, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/business-card/getBusinessCardData.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { BusinessCardData, requiredFieldsForOnline } from \"../schema\";\r\nimport { mapBusinessCardData } from \"../data/businessCardMapper\";\r\nimport { checkForceOfflineStatus } from \"../data/subscriptionChecker\";\r\n\r\n/**\r\n * Fetches business card data for the authenticated user\r\n * @returns Business card data or error\r\n */\r\nexport async function getBusinessCardData(): Promise<{\r\n  data?: BusinessCardData;\r\n  error?: string;\r\n}> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Select columns including metrics data\r\n  const { data, error } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\r\n      `\r\n      id, business_name, contact_email, has_active_subscription,\r\n      trial_end_date, created_at, updated_at, logo_url, member_name, title,\r\n      address_line, city, state, pincode, locality, phone, instagram_url,\r\n      facebook_url, whatsapp_number, about_bio, status, business_slug,\r\n      total_likes, total_subscriptions, average_rating, theme_color, delivery_info, business_hours,\r\n      business_category, custom_branding, custom_ads, established_year, google_maps_url\r\n    `\r\n    )\r\n    .eq(\"id\", user.id)\r\n    .single(); // Use single as profile must exist for editing\r\n\r\n  if (error) {\r\n    if (error.code === \"PGRST116\") {\r\n      return { data: undefined };\r\n    }\r\n    console.error(\"Supabase Fetch Error:\", error);\r\n    return { error: `Failed to fetch profile: ${error.message}` };\r\n  }\r\n\r\n  // Server-side check: Force offline if online but subscription is halted or missing required fields\r\n  if (data.status === \"online\") {\r\n    let shouldForceOffline = false;\r\n    let reason = \"\";\r\n\r\n    // Check subscription status\r\n    const subscriptionCheck = await checkForceOfflineStatus(user.id, supabase);\r\n    if (subscriptionCheck.shouldForceOffline) {\r\n      shouldForceOffline = true;\r\n      reason = subscriptionCheck.reason || \"subscription issue\";\r\n    }\r\n\r\n    // Check if all required fields are present\r\n    const missingRequiredFields = requiredFieldsForOnline.filter(\r\n      (field) => {\r\n        const value = (data as Record<string, unknown>)[field];\r\n        return !value || String(value).trim() === \"\";\r\n      }\r\n    );\r\n\r\n    if (missingRequiredFields.length > 0) {\r\n      shouldForceOffline = true;\r\n      reason = `missing required fields: ${missingRequiredFields.join(\", \")}`;\r\n    }\r\n\r\n    // Force offline if needed\r\n    if (shouldForceOffline) {\r\n      console.log(\r\n        `User ${user.id} card forced offline due to ${reason}.`\r\n      );\r\n      const { error: updateError } = await supabase\r\n        .from(\"business_profiles\")\r\n        .update({ status: \"offline\" })\r\n        .eq(\"id\", user.id);\r\n\r\n      if (updateError) {\r\n        console.error(\"Error forcing card offline:\", updateError.message);\r\n      } else {\r\n        data.status = \"offline\";\r\n      }\r\n    }\r\n  }\r\n\r\n  // Map data using the shared mapper\r\n  const mappedData = mapBusinessCardData(data);\r\n  return { data: mappedData };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;;;;;;AAMO,eAAe;IAIpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,OAAO;QAA0B;IAC5C;IAEA,wCAAwC;IACxC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CACL,CAAC;;;;;;;IAOH,CAAC,EAEA,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM,IAAI,+CAA+C;IAE5D,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY;YAC7B,OAAO;gBAAE,MAAM;YAAU;QAC3B;QACA,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YAAE,OAAO,CAAC,yBAAyB,EAAE,MAAM,OAAO,EAAE;QAAC;IAC9D;IAEA,mGAAmG;IACnG,IAAI,KAAK,MAAM,KAAK,UAAU;QAC5B,IAAI,qBAAqB;QACzB,IAAI,SAAS;QAEb,4BAA4B;QAC5B,MAAM,oBAAoB,MAAM,CAAA,GAAA,oLAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,EAAE,EAAE;QACjE,IAAI,kBAAkB,kBAAkB,EAAE;YACxC,qBAAqB;YACrB,SAAS,kBAAkB,MAAM,IAAI;QACvC;QAEA,2CAA2C;QAC3C,MAAM,wBAAwB,+JAAA,CAAA,0BAAuB,CAAC,MAAM,CAC1D,CAAC;YACC,MAAM,QAAQ,AAAC,IAAgC,CAAC,MAAM;YACtD,OAAO,CAAC,SAAS,OAAO,OAAO,IAAI,OAAO;QAC5C;QAGF,IAAI,sBAAsB,MAAM,GAAG,GAAG;YACpC,qBAAqB;YACrB,SAAS,CAAC,yBAAyB,EAAE,sBAAsB,IAAI,CAAC,OAAO;QACzE;QAEA,0BAA0B;QAC1B,IAAI,oBAAoB;YACtB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;YAEzD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;gBAAE,QAAQ;YAAU,GAC3B,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,+BAA+B,YAAY,OAAO;YAClE,OAAO;gBACL,KAAK,MAAM,GAAG;YAChB;QACF;IACF;IAEA,mCAAmC;IACnC,MAAM,aAAa,CAAA,GAAA,mLAAA,CAAA,sBAAmB,AAAD,EAAE;IACvC,OAAO;QAAE,MAAM;IAAW;AAC5B;;;IArFsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/validation/businessCardValidation.ts"], "sourcesContent": ["import * as z from \"zod\";\r\nimport {\r\n  businessCardSchema,\r\n  type BusinessCardData,\r\n  requiredFieldsForOnline,\r\n  requiredFieldsForSaving,\r\n} from \"../schema\";\r\n\r\n/**\r\n * Validates business card data with different requirements based on status\r\n * @param formData - The business card data to validate\r\n * @returns Validation result with success/error information\r\n */\r\nexport function validateBusinessCardData(formData: BusinessCardData) {\r\n  // First, validate required fields for saving regardless of status\r\n  const baseSchema = businessCardSchema.refine(\r\n    (data) => {\r\n      return requiredFieldsForSaving.every(\r\n        (field) => data[field] && String(data[field]).trim() !== \"\"\r\n      );\r\n    },\r\n    {\r\n      message: `Required fields missing: ${requiredFieldsForSaving.join(\", \")}.`,\r\n      path: [\"member_name\"], // Point to the first required field\r\n    }\r\n  );\r\n\r\n  // Then, if status is online, also validate online-specific required fields\r\n  const schemaToUse =\r\n    formData.status === \"online\"\r\n      ? baseSchema.refine(\r\n          (data) => {\r\n            return requiredFieldsForOnline.every(\r\n              (field) => data[field] && String(data[field]).trim() !== \"\"\r\n            );\r\n          },\r\n          {\r\n            message: `Cannot set status to online. Required fields missing: ${requiredFieldsForOnline.join(\r\n              \", \"\r\n            )}.`,\r\n            path: [\"status\"],\r\n          }\r\n        )\r\n      : baseSchema;\r\n\r\n  return schemaToUse.safeParse(formData);\r\n}\r\n\r\n/**\r\n * Validates slug format\r\n * @param slug - The slug to validate\r\n * @returns Validation result\r\n */\r\nexport function validateSlugFormat(slug: string) {\r\n  const slugValidation = z\r\n    .string()\r\n    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)\r\n    .min(3)\r\n    .safeParse(slug);\r\n  \r\n  return slugValidation;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAYO,SAAS,yBAAyB,QAA0B;IACjE,kEAAkE;IAClE,MAAM,aAAa,+JAAA,CAAA,qBAAkB,CAAC,MAAM,CAC1C,CAAC;QACC,OAAO,+JAAA,CAAA,0BAAuB,CAAC,KAAK,CAClC,CAAC,QAAU,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO;IAE7D,GACA;QACE,SAAS,CAAC,yBAAyB,EAAE,+JAAA,CAAA,0BAAuB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1E,MAAM;YAAC;SAAc;IACvB;IAGF,2EAA2E;IAC3E,MAAM,cACJ,SAAS,MAAM,KAAK,WAChB,WAAW,MAAM,CACf,CAAC;QACC,OAAO,+JAA<PERSON>,CAAA,0BAAuB,CAAC,KAAK,CAClC,CAAC,QAAU,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO;IAE7D,GACA;QACE,SAAS,CAAC,sDAAsD,EAAE,+JAAA,CAAA,0BAAuB,CAAC,IAAI,CAC5F,MACA,CAAC,CAAC;QACJ,MAAM;YAAC;SAAS;IAClB,KAEF;IAEN,OAAO,YAAY,SAAS,CAAC;AAC/B;AAOO,SAAS,mBAAmB,IAAY;IAC7C,MAAM,iBAAiB,CAAA,GAAA,oIAAA,CAAA,SACd,AAAD,IACL,KAAK,CAAC,8BACN,GAAG,CAAC,GACJ,SAAS,CAAC;IAEb,OAAO;AACT", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/slugUtils.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { unstable_noStore as noStore } from \"next/cache\";\r\nimport * as z from \"zod\";\r\n\r\n/**\r\n * Shared utility function to check if a business slug is available\r\n * Used by both onboarding and dashboard business card edit\r\n */\r\nexport async function checkBusinessSlugAvailability(\r\n  slug: string,\r\n  userId?: string | null\r\n): Promise<{ available: boolean; error?: string }> {\r\n  noStore(); // Ensure this check always hits the database\r\n\r\n  // Basic validation\r\n  if (!slug || slug.length < 3) {\r\n    return { available: false, error: \"Slug must be at least 3 characters.\" };\r\n  }\r\n\r\n  // Format validation\r\n  const slugFormatCheck = z\r\n    .string()\r\n    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)\r\n    .safeParse(slug);\r\n\r\n  if (!slugFormatCheck.success) {\r\n    return {\r\n      available: false,\r\n      error: \"Invalid format (lowercase, numbers, hyphens only).\",\r\n    };\r\n  }\r\n\r\n  // Create a fresh Supabase client for each check to avoid any caching issues\r\n  const supabase = await createClient();\r\n  // Create admin client to bypass RLS for slug checks\r\n  const supabaseAdmin = createAdminClient();\r\n\r\n  // Get current user if not provided\r\n  let currentUserId = userId;\r\n  if (!currentUserId) {\r\n    const { data: { user } } = await supabase.auth.getUser();\r\n    currentUserId = user?.id;\r\n  }\r\n\r\n  try {\r\n    // Use admin client to check if the slug exists (excluding current user)\r\n    const { data: existingProfile, error } = await supabaseAdmin\r\n      .from(\"business_profiles\")\r\n      .select(\"id, business_slug\")\r\n      .ilike(\"business_slug\", slug)\r\n      .neq(\"id\", currentUserId ?? \"\")\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      return { available: false, error: \"Database error checking slug.\" };\r\n    }\r\n\r\n    const isAvailable = !existingProfile;\r\n    return { available: isAvailable };\r\n  } catch (_e) {\r\n    return { available: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;;;;;;AAMO,eAAe,8BACpB,IAAY,EACZ,MAAsB;IAEtB,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD,KAAK,6CAA6C;IAExD,mBAAmB;IACnB,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;QAC5B,OAAO;YAAE,WAAW;YAAO,OAAO;QAAsC;IAC1E;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAA,GAAA,oIAAA,CAAA,SACf,AAAD,IACL,KAAK,CAAC,8BACN,SAAS,CAAC;IAEb,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,OAAO;YACL,WAAW;YACX,OAAO;QACT;IACF;IAEA,4EAA4E;IAC5E,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,oDAAoD;IACpD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IAEtC,mCAAmC;IACnC,IAAI,gBAAgB;IACpB,IAAI,CAAC,eAAe;QAClB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,gBAAgB,MAAM;IACxB;IAEA,IAAI;QACF,wEAAwE;QACxE,MAAM,EAAE,MAAM,eAAe,EAAE,KAAK,EAAE,GAAG,MAAM,cAC5C,IAAI,CAAC,qBACL,MAAM,CAAC,qBACP,KAAK,CAAC,iBAAiB,MACvB,GAAG,CAAC,MAAM,iBAAiB,IAC3B,WAAW;QAEd,IAAI,OAAO;YACT,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAAgC;QACpE;QAEA,MAAM,cAAc,CAAC;QACrB,OAAO;YAAE,WAAW;QAAY;IAClC,EAAE,OAAO,IAAI;QACX,OAAO;YAAE,WAAW;YAAO,OAAO;QAAgC;IACpE;AACF;;;IAtDsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1058, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/utils/slugGenerator.ts"], "sourcesContent": ["/**\r\n * Generates a URL-friendly slug from a business name\r\n * @param name - The business name to convert to a slug\r\n * @returns A clean, URL-friendly slug\r\n */\r\nexport function generateSlug(name: string): string {\r\n  const baseSlug = name\r\n    .toLowerCase()\r\n    .trim()\r\n    .replace(/[^\\w\\s-]/g, \"\")\r\n    .replace(/[\\s_-]+/g, \"-\")\r\n    .replace(/^-+|-+$/g, \"\");\r\n  return baseSlug || \"business\";\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACM,SAAS,aAAa,IAAY;IACvC,MAAM,WAAW,KACd,WAAW,GACX,IAAI,GACJ,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;IACvB,OAAO,YAAY;AACrB", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/utils/constants.ts"], "sourcesContent": ["import { customAlphabet } from \"nanoid\";\r\n\r\n// Nanoid generator for unique slugs\r\nexport const nanoid = customAlphabet(\"1234567890abcdef\", 6);\r\n\r\n// Maximum attempts for slug generation\r\nexport const MAX_SLUG_ATTEMPTS = 5;\r\n\r\n// File upload constants\r\nexport const LOGO_MAX_SIZE_MB = 15;\r\nexport const ALLOWED_IMAGE_TYPES = [\"image/png\", \"image/jpeg\", \"image/gif\", \"image/webp\"];\r\n\r\n// Logo compression is now handled client-side\r\n\r\n// Storage bucket name\r\nexport const STORAGE_BUCKET = \"business\";\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGO,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD,EAAE,oBAAoB;AAGlD,MAAM,oBAAoB;AAG1B,MAAM,mBAAmB;AACzB,MAAM,sBAAsB;IAAC;IAAa;IAAc;IAAa;CAAa;AAKlF,MAAM,iBAAiB", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/slug/slugUtils.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { checkBusinessSlugAvailability } from \"@/lib/utils/slugUtils\";\r\nimport { generateSlug } from \"../utils/slugGenerator\";\r\nimport { validateSlugFormat } from \"../validation/businessCardValidation\";\r\nimport { nanoid, MAX_SLUG_ATTEMPTS } from \"../utils/constants\";\r\n\r\n/**\r\n * Generates a unique slug for a business\r\n * @param businessName - The business name to generate slug from\r\n * @param currentSlug - Current slug if any\r\n * @param userId - User ID for availability checking\r\n * @returns Object with success status and final slug or error\r\n */\r\nexport async function generateUniqueSlug(\r\n  businessName: string,\r\n  currentSlug: string,\r\n  userId: string\r\n): Promise<{ success: boolean; slug?: string; error?: string }> {\r\n  const desiredSlug = currentSlug || generateSlug(businessName);\r\n\r\n  let isUnique = false;\r\n  let checkSlug = desiredSlug;\r\n  let attempts = 0;\r\n\r\n  while (!isUnique && attempts < MAX_SLUG_ATTEMPTS) {\r\n    // Use the shared slug availability check\r\n    const { available, error: slugCheckError } = await checkBusinessSlugAvailability(checkSlug, userId);\r\n\r\n    if (slugCheckError) {\r\n      console.error(\"Slug Check Error:\", slugCheckError);\r\n      return { success: false, error: \"Error checking slug availability.\" };\r\n    }\r\n\r\n    if (available) {\r\n      isUnique = true;\r\n      const finalSlug = checkSlug;\r\n      \r\n      // Validate the final slug format\r\n      const slugValidation = validateSlugFormat(finalSlug);\r\n      if (!slugValidation.success) {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid business slug format generated. Please set one manually.\",\r\n        };\r\n      }\r\n      \r\n      return { success: true, slug: finalSlug };\r\n    } else {\r\n      attempts++;\r\n      checkSlug = `${desiredSlug}-${nanoid()}`;\r\n      if (attempts === MAX_SLUG_ATTEMPTS) {\r\n        return {\r\n          success: false,\r\n          error: `Could not generate a unique slug for '${desiredSlug}'. Please try setting one manually.`,\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  return { success: false, error: \"Failed to generate unique slug.\" };\r\n}\r\n\r\n/**\r\n * Action to check slug availability (wrapper for shared utility)\r\n * @param slug - The slug to check\r\n * @returns Object with availability status\r\n */\r\nexport async function checkSlugAvailability(\r\n  slug: string\r\n): Promise<{ available: boolean; error?: string }> {\r\n  return checkBusinessSlugAvailability(slug);\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;;;;;;;;AASO,eAAe,mBACpB,YAAoB,EACpB,WAAmB,EACnB,MAAc;IAEd,MAAM,cAAc,eAAe,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE;IAEhD,IAAI,WAAW;IACf,IAAI,YAAY;IAChB,IAAI,WAAW;IAEf,MAAO,CAAC,YAAY,WAAW,2KAAA,CAAA,oBAAiB,CAAE;QAChD,yCAAyC;QACzC,MAAM,EAAE,SAAS,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,CAAA,GAAA,yHAAA,CAAA,gCAA6B,AAAD,EAAE,WAAW;QAE5F,IAAI,gBAAgB;YAClB,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAoC;QACtE;QAEA,IAAI,WAAW;YACb,WAAW;YACX,MAAM,YAAY;YAElB,iCAAiC;YACjC,MAAM,iBAAiB,CAAA,GAAA,6LAAA,CAAA,qBAAkB,AAAD,EAAE;YAC1C,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;YAEA,OAAO;gBAAE,SAAS;gBAAM,MAAM;YAAU;QAC1C,OAAO;YACL;YACA,YAAY,GAAG,YAAY,CAAC,EAAE,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,KAAK;YACxC,IAAI,aAAa,2KAAA,CAAA,oBAAiB,EAAE;gBAClC,OAAO;oBACL,SAAS;oBACT,OAAO,CAAC,sCAAsC,EAAE,YAAY,mCAAmC,CAAC;gBAClG;YACF;QACF;IACF;IAEA,OAAO;QAAE,SAAS;QAAO,OAAO;IAAkC;AACpE;AAOO,eAAe,sBACpB,IAAY;IAEZ,OAAO,CAAA,GAAA,yHAAA,CAAA,gCAA6B,AAAD,EAAE;AACvC;;;IA1DsB;IAsDA;;AAtDA,+OAAA;AAsDA,+OAAA", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/utils/businessHoursProcessor.ts"], "sourcesContent": ["/**\r\n * Processes business hours data for database storage\r\n * Ensures proper JSONB formatting and validation\r\n */\r\nexport function processBusinessHours(businessHours: unknown): Record<string, unknown> | null {\r\n  if (!businessHours) {\r\n    return null;\r\n  }\r\n\r\n  try {\r\n    let businessHoursData: unknown = null;\r\n\r\n    // If it's already a string, parse it to ensure it's valid JSON\r\n    if (typeof businessHours === 'string') {\r\n      businessHoursData = JSON.parse(businessHours);\r\n    } else {\r\n      // If it's an object, use it directly\r\n      businessHoursData = businessHours;\r\n    }\r\n\r\n    // Validate that it has the expected structure\r\n    if (typeof businessHoursData !== 'object' || businessHoursData === null) {\r\n      console.warn(\"Invalid business_hours format, setting to null\");\r\n      return null;\r\n    }\r\n\r\n    return businessHoursData as Record<string, unknown>;\r\n  } catch (error) {\r\n    console.error(\"Error processing business_hours data:\", error);\r\n    return null;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,qBAAqB,aAAsB;IACzD,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IAEA,IAAI;QACF,IAAI,oBAA6B;QAEjC,+DAA+D;QAC/D,IAAI,OAAO,kBAAkB,UAAU;YACrC,oBAAoB,KAAK,KAAK,CAAC;QACjC,OAAO;YACL,qCAAqC;YACrC,oBAAoB;QACtB;QAEA,8CAA8C;QAC9C,IAAI,OAAO,sBAAsB,YAAY,sBAAsB,MAAM;YACvE,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/utils/storage-paths.ts"], "sourcesContent": ["/**\r\n * Scalable Storage Path Utilities\r\n *\r\n * This module provides utilities for generating scalable storage paths\r\n * that can handle billions of users efficiently using hash-based distribution.\r\n */\r\n\r\n/**\r\n * Generate scalable user path using hash-based distribution\r\n *\r\n * @param userId - The user's UUID\r\n * @returns Scalable path: users/{prefix}/{midfix}/{userId}\r\n *\r\n * Example:\r\n * - Input: \"a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n * - Output: \"users/a1/b2/a1b2c3d4-e5f6-7890-abcd-ef1234567890\"\r\n */\r\nexport function getScalableUserPath(userId: string): string {\r\n  if (!userId || typeof userId !== 'string') {\r\n    throw new Error(`Invalid userId: expected string, got ${typeof userId}. Value: ${userId}`);\r\n  }\r\n\r\n  if (userId.length < 4) {\r\n    throw new Error(`Invalid userId: must be at least 4 characters long. Got: ${userId}`);\r\n  }\r\n\r\n  const prefix = userId.substring(0, 2).toLowerCase();\r\n  const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n  return `users/${prefix}/${midfix}/${userId}`;\r\n}\r\n\r\n/**\r\n * Generate profile image path\r\n */\r\nexport function getProfileImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/profile/logo_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product image path (legacy - for backward compatibility)\r\n * @deprecated Use getProductBaseImagePath or getProductVariantImagePath instead\r\n */\r\nexport function getProductImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate base product image path\r\n */\r\nexport function getProductBaseImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/base/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate product variant image path\r\n */\r\nexport function getProductVariantImagePath(\r\n  userId: string,\r\n  productId: string,\r\n  variantId: string,\r\n  imageIndex: number,\r\n  timestamp: number\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/products/${productId}/${variantId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate gallery image path\r\n */\r\nexport function getGalleryImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/gallery/gallery_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post image path\r\n */\r\nexport function getPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate post folder path for deletion\r\n */\r\nexport function getPostFolderPath(userId: string, postId: string, createdAt: string): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  const postDate = new Date(createdAt);\r\n  const year = postDate.getFullYear();\r\n  const month = String(postDate.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}`;\r\n}\r\n\r\n/**\r\n * Generate customer avatar image path\r\n */\r\nexport function getCustomerAvatarPath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/avatar/avatar_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate customer post image path\r\n */\r\nexport function getCustomerPostImagePath(\r\n  userId: string,\r\n  postId: string,\r\n  imageIndex: number,\r\n  timestamp: number,\r\n  createdAt?: string\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n\r\n  // Use post creation date if provided, otherwise use current date (for backward compatibility)\r\n  const dateToUse = createdAt ? new Date(createdAt) : new Date();\r\n  const year = dateToUse.getFullYear();\r\n  const month = String(dateToUse.getMonth() + 1).padStart(2, '0');\r\n\r\n  return `${userPath}/posts/${year}/${month}/${postId}/image_${imageIndex}_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom ad image path\r\n */\r\nexport function getCustomAdImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/ads/custom_ad_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate custom header image path\r\n */\r\nexport function getCustomHeaderImagePath(userId: string, timestamp: number): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${timestamp}.webp`;\r\n}\r\n\r\n/**\r\n * Generate theme-specific custom header image path\r\n */\r\nexport function getThemeSpecificHeaderImagePath(\r\n  userId: string,\r\n  timestamp: number,\r\n  theme: 'light' | 'dark'\r\n): string {\r\n  const userPath = getScalableUserPath(userId);\r\n  return `${userPath}/branding/header_${theme}_${timestamp}.webp`;\r\n}\r\n\r\n// Legacy utilities removed since migration is complete\r\n\r\n/**\r\n * Path validation utilities\r\n */\r\nexport class PathValidator {\r\n  /**\r\n   * Validate if a path follows the new scalable structure\r\n   */\r\n  static isScalablePath(path: string): boolean {\r\n    return path.startsWith('users/') && path.split('/').length >= 4;\r\n  }\r\n\r\n  /**\r\n   * Extract user ID from scalable path\r\n   */\r\n  static extractUserIdFromPath(path: string): string | null {\r\n    if (!this.isScalablePath(path)) {\r\n      return null;\r\n    }\r\n\r\n    const parts = path.split('/');\r\n    return parts[3]; // users/{prefix}/{midfix}/{userId}/...\r\n  }\r\n\r\n  /**\r\n   * Validate path structure integrity\r\n   */\r\n  static validatePathStructure(userId: string, path: string): boolean {\r\n    const expectedUserPath = getScalableUserPath(userId);\r\n    return path.startsWith(expectedUserPath);\r\n  }\r\n}\r\n\r\n/**\r\n * Storage analytics utilities\r\n */\r\nexport class StorageAnalytics {\r\n  /**\r\n   * Get storage distribution info for monitoring\r\n   */\r\n  static getDistributionInfo(userId: string): {\r\n    prefix: string;\r\n    midfix: string;\r\n    bucket: string;\r\n    estimatedPeers: number;\r\n  } {\r\n    const prefix = userId.substring(0, 2).toLowerCase();\r\n    const midfix = userId.substring(2, 4).toLowerCase();\r\n\r\n    // Estimate number of users in same bucket (assuming even distribution)\r\n    const totalBuckets = 16 * 16 * 16 * 16; // 65,536 buckets\r\n    const estimatedPeers = Math.floor(1000000 / totalBuckets); // Estimate for 1M users\r\n\r\n    return {\r\n      prefix,\r\n      midfix,\r\n      bucket: `${prefix}/${midfix}`,\r\n      estimatedPeers\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED;;;;;;;;;CASC;;;;;;;;;;;;;;;;;AACM,SAAS,oBAAoB,MAAc;IAChD,IAAI,CAAC,UAAU,OAAO,WAAW,UAAU;QACzC,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,OAAO,OAAO,SAAS,EAAE,QAAQ;IAC3F;IAEA,IAAI,OAAO,MAAM,GAAG,GAAG;QACrB,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,QAAQ;IACtF;IAEA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;IAEjD,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ;AAC9C;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,cAAc,EAAE,UAAU,KAAK,CAAC;AACrD;AAMO,SAAS,oBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAClF;AAKO,SAAS,wBACd,MAAc,EACd,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,YAAY,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AACvF;AAKO,SAAS,2BACd,MAAc,EACd,SAAiB,EACjB,SAAiB,EACjB,UAAkB,EAClB,SAAiB;IAEjB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,UAAU,EAAE,UAAU,CAAC,EAAE,UAAU,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC/F;AAKO,SAAS,oBAAoB,MAAc,EAAE,SAAiB;IACnE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,iBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,kBAAkB,MAAc,EAAE,MAAc,EAAE,SAAiB;IACjF,MAAM,WAAW,oBAAoB;IACrC,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,OAAO,SAAS,WAAW;IACjC,MAAM,QAAQ,OAAO,SAAS,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE1D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ;AACvD;AAKO,SAAS,sBAAsB,MAAc,EAAE,SAAiB;IACrE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBACd,MAAc,EACd,MAAc,EACd,UAAkB,EAClB,SAAiB,EACjB,SAAkB;IAElB,MAAM,WAAW,oBAAoB;IAErC,8FAA8F;IAC9F,MAAM,YAAY,YAAY,IAAI,KAAK,aAAa,IAAI;IACxD,MAAM,OAAO,UAAU,WAAW;IAClC,MAAM,QAAQ,OAAO,UAAU,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IAE3D,OAAO,GAAG,SAAS,OAAO,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,OAAO,OAAO,EAAE,WAAW,CAAC,EAAE,UAAU,KAAK,CAAC;AAC7F;AAKO,SAAS,qBAAqB,MAAc,EAAE,SAAiB;IACpE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,eAAe,EAAE,UAAU,KAAK,CAAC;AACtD;AAKO,SAAS,yBAAyB,MAAc,EAAE,SAAiB;IACxE,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,UAAU,KAAK,CAAC;AACxD;AAKO,SAAS,gCACd,MAAc,EACd,SAAiB,EACjB,KAAuB;IAEvB,MAAM,WAAW,oBAAoB;IACrC,OAAO,GAAG,SAAS,iBAAiB,EAAE,MAAM,CAAC,EAAE,UAAU,KAAK,CAAC;AACjE;AAOO,MAAM;IACX;;GAEC,GACD,OAAO,eAAe,IAAY,EAAW;QAC3C,OAAO,KAAK,UAAU,CAAC,aAAa,KAAK,KAAK,CAAC,KAAK,MAAM,IAAI;IAChE;IAEA;;GAEC,GACD,OAAO,sBAAsB,IAAY,EAAiB;QACxD,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO;YAC9B,OAAO;QACT;QAEA,MAAM,QAAQ,KAAK,KAAK,CAAC;QACzB,OAAO,KAAK,CAAC,EAAE,EAAE,uCAAuC;IAC1D;IAEA;;GAEC,GACD,OAAO,sBAAsB,MAAc,EAAE,IAAY,EAAW;QAClE,MAAM,mBAAmB,oBAAoB;QAC7C,OAAO,KAAK,UAAU,CAAC;IACzB;AACF;AAKO,MAAM;IACX;;GAEC,GACD,OAAO,oBAAoB,MAAc,EAKvC;QACA,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QACjD,MAAM,SAAS,OAAO,SAAS,CAAC,GAAG,GAAG,WAAW;QAEjD,uEAAuE;QACvE,MAAM,eAAe,KAAK,KAAK,KAAK,IAAI,iBAAiB;QACzD,MAAM,iBAAiB,KAAK,KAAK,CAAC,UAAU,eAAe,wBAAwB;QAEnF,OAAO;YACL;YACA;YACA,QAAQ,GAAG,OAAO,CAAC,EAAE,QAAQ;YAC7B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 1361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions/themeHeaderActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getThemeSpecificHeaderImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface ThemeHeaderUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface ThemeHeaderDeleteResult {\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload theme-specific custom header image with compression\r\n * This function only uploads to storage and returns the URL\r\n * Database update happens in the main save action\r\n */\r\nexport async function uploadThemeHeaderImage(\r\n  imageFile: File,\r\n  theme: 'light' | 'dark'\r\n): Promise<ThemeHeaderUploadResult> {\r\n  try {\r\n    // Create admin client for storage operations\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Validate file type\r\n    if (!imageFile.type.startsWith(\"image/\")) {\r\n      return {\r\n        success: false,\r\n        error: \"Invalid file type. Please upload an image file.\",\r\n      };\r\n    }\r\n\r\n    // Validate file size (5MB limit)\r\n    const maxSizeBytes = 5 * 1024 * 1024; // 5MB\r\n    if (imageFile.size > maxSizeBytes) {\r\n      const fileSizeMB = (imageFile.size / (1024 * 1024)).toFixed(1);\r\n      return {\r\n        success: false,\r\n        error: `Image size (${fileSizeMB}MB) is too large. Please choose an image smaller than 5MB.`,\r\n      };\r\n    }\r\n\r\n    const bucketName = \"business\";\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const imagePath = getThemeSpecificHeaderImagePath(user.id, timestamp, theme);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Theme Header Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Theme header upload error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred during upload.\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete theme-specific custom header image from storage\r\n * This function only deletes from storage\r\n * Database update happens in the main save action\r\n */\r\nexport async function deleteThemeHeaderImage(\r\n  imageUrl: string\r\n): Promise<ThemeHeaderDeleteResult> {\r\n  try {\r\n    // Create admin client for storage operations\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    if (!imageUrl || imageUrl.trim() === \"\") {\r\n      return {\r\n        success: true, // Nothing to delete\r\n      };\r\n    }\r\n\r\n    try {\r\n      // Extract the storage path from the URL\r\n      const url = new URL(imageUrl);\r\n      const pathParts = url.pathname.split('/');\r\n\r\n      // The path will be in format like /storage/v1/object/public/business/userId/branding/header_theme_timestamp.webp\r\n      const businessIndex = pathParts.findIndex(part => part === 'business');\r\n\r\n      if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {\r\n        // Extract the path after 'business/'\r\n        const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];\r\n\r\n        // Delete the file from storage using admin client\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(\"business\")\r\n          .remove([storagePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting theme header from storage:\", deleteError);\r\n          return {\r\n            success: false,\r\n            error: `Failed to delete image: ${deleteError.message}`,\r\n          };\r\n        }\r\n      }\r\n    } catch (urlError) {\r\n      console.error(\"Error processing image URL for deletion:\", urlError);\r\n      return {\r\n        success: false,\r\n        error: \"Invalid image URL format.\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Theme header deletion error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred during deletion.\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Clean up old theme-specific header images for a user\r\n * This is used to remove old images when new ones are uploaded\r\n */\r\nexport async function cleanupOldThemeHeaderImages(\r\n  userId: string,\r\n  theme: 'light' | 'dark',\r\n  keepUrl?: string\r\n): Promise<void> {\r\n  try {\r\n    const adminSupabase = createAdminClient();\r\n    const bucketName = \"business\";\r\n    \r\n    // Get the user's branding folder path\r\n    const userPath = userId.slice(0, 2) + '/' + userId.slice(2, 4) + '/' + userId;\r\n    const brandingFolderPath = `${userPath}/branding/`;\r\n\r\n    // List all files in the branding folder\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .list(brandingFolderPath, { limit: 20 });\r\n\r\n    if (listError || !existingFiles) {\r\n      console.error(\"Error listing branding files:\", listError);\r\n      return;\r\n    }\r\n\r\n    // Filter for theme-specific header files\r\n    const themeHeaderFiles = existingFiles.filter(file => \r\n      file.name.startsWith(`header_${theme}_`) && file.name.endsWith('.webp')\r\n    );\r\n\r\n    // If we have a URL to keep, extract its filename\r\n    let keepFilename: string | undefined;\r\n    if (keepUrl) {\r\n      try {\r\n        const url = new URL(keepUrl);\r\n        const pathParts = url.pathname.split('/');\r\n        keepFilename = pathParts[pathParts.length - 1].split('?')[0];\r\n      } catch (error) {\r\n        console.error(\"Error extracting filename from keep URL:\", error);\r\n      }\r\n    }\r\n\r\n    // Delete old files (keep the current one if specified)\r\n    const filesToDelete = themeHeaderFiles\r\n      .filter(file => !keepFilename || file.name !== keepFilename)\r\n      .map(file => `${brandingFolderPath}${file.name}`);\r\n\r\n    if (filesToDelete.length > 0) {\r\n      const { error: deleteError } = await adminSupabase.storage\r\n        .from(bucketName)\r\n        .remove(filesToDelete);\r\n\r\n      if (deleteError) {\r\n        console.error(\"Error cleaning up old theme header files:\", deleteError);\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error(\"Error in cleanup function:\", error);\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;;;;;AAkBO,eAAe,uBACpB,SAAe,EACf,KAAuB;IAEvB,IAAI;QACF,6CAA6C;QAC7C,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;YACxC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,iCAAiC;QACjC,MAAM,eAAe,IAAI,OAAO,MAAM,MAAM;QAC5C,IAAI,UAAU,IAAI,GAAG,cAAc;YACjC,MAAM,aAAa,CAAC,UAAU,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;YAC5D,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,YAAY,EAAE,WAAW,0DAA0D,CAAC;YAC9F;QACF;QAEA,MAAM,aAAa;QACnB,MAAM,YAAY,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC1D,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,kCAA+B,AAAD,EAAE,KAAK,EAAE,EAAE,WAAW;QAEtE,4DAA4D;QAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,UAAU,WAAW;QAE1D,gDAAgD;QAChD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC,WAAW,YAAY;YAC7B,aAAa,UAAU,IAAI;YAC3B,QAAQ;QACV;QAEF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wBAAwB,EAAE,YAAY,OAAO,EAAE;YACzD;QACF;QAEA,qBAAqB;QACrB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,cAAc,OAAO,CAC5C,IAAI,CAAC,YACL,YAAY,CAAC;QAEhB,IAAI,CAAC,SAAS,WAAW;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;YACT,KAAK,QAAQ,SAAS;QACxB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAOO,eAAe,uBACpB,QAAgB;IAEhB,IAAI;QACF,6CAA6C;QAC7C,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;YACvC,OAAO;gBACL,SAAS;YACX;QACF;QAEA,IAAI;YACF,wCAAwC;YACxC,MAAM,MAAM,IAAI,IAAI;YACpB,MAAM,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC;YAErC,iHAAiH;YACjH,MAAM,gBAAgB,UAAU,SAAS,CAAC,CAAA,OAAQ,SAAS;YAE3D,IAAI,kBAAkB,CAAC,KAAK,gBAAgB,UAAU,MAAM,GAAG,GAAG;gBAChE,qCAAqC;gBACrC,MAAM,cAAc,UAAU,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;gBAE9E,kDAAkD;gBAClD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC;oBAAC;iBAAY;gBAEvB,IAAI,eAAe,YAAY,OAAO,KAAK,8BAA8B;oBACvE,QAAQ,KAAK,CAAC,6CAA6C;oBAC3D,OAAO;wBACL,SAAS;wBACT,OAAO,CAAC,wBAAwB,EAAE,YAAY,OAAO,EAAE;oBACzD;gBACF;YACF;QACF,EAAE,OAAO,UAAU;YACjB,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YACL,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAMO,eAAe,4BACpB,MAAc,EACd,KAAuB,EACvB,OAAgB;IAEhB,IAAI;QACF,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QACtC,MAAM,aAAa;QAEnB,sCAAsC;QACtC,MAAM,WAAW,OAAO,KAAK,CAAC,GAAG,KAAK,MAAM,OAAO,KAAK,CAAC,GAAG,KAAK,MAAM;QACvE,MAAM,qBAAqB,GAAG,SAAS,UAAU,CAAC;QAElD,wCAAwC;QACxC,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAAc,OAAO,CAC1E,IAAI,CAAC,YACL,IAAI,CAAC,oBAAoB;YAAE,OAAO;QAAG;QAExC,IAAI,aAAa,CAAC,eAAe;YAC/B,QAAQ,KAAK,CAAC,iCAAiC;YAC/C;QACF;QAEA,yCAAyC;QACzC,MAAM,mBAAmB,cAAc,MAAM,CAAC,CAAA,OAC5C,KAAK,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC;QAGjE,iDAAiD;QACjD,IAAI;QACJ,IAAI,SAAS;YACX,IAAI;gBACF,MAAM,MAAM,IAAI,IAAI;gBACpB,MAAM,YAAY,IAAI,QAAQ,CAAC,KAAK,CAAC;gBACrC,eAAe,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC9D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4CAA4C;YAC5D;QACF;QAEA,uDAAuD;QACvD,MAAM,gBAAgB,iBACnB,MAAM,CAAC,CAAA,OAAQ,CAAC,gBAAgB,KAAK,IAAI,KAAK,cAC9C,GAAG,CAAC,CAAA,OAAQ,GAAG,qBAAqB,KAAK,IAAI,EAAE;QAElD,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC;YAEV,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;IAC9C;AACF;;;IA9NsB;IA2FA;IA0EA;;AArKA,+OAAA;AA2FA,+OAAA;AA0EA,+OAAA", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/business-card/updateBusinessCard.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { BusinessCardData } from \"../schema\";\r\nimport { validateBusinessCardData } from \"../validation/businessCardValidation\";\r\nimport { checkSubscriptionStatus } from \"../data/subscriptionChecker\";\r\nimport { generateUniqueSlug } from \"../slug/slugUtils\";\r\nimport { processBusinessHours } from \"../utils/businessHoursProcessor\";\r\nimport { uploadThemeHeaderImage, deleteThemeHeaderImage, cleanupOldThemeHeaderImages } from \"../actions/themeHeaderActions\";\r\n\r\n/**\r\n * Updates business card data with validation and processing\r\n * @param formData - The business card data to update\r\n * @returns Success/error response with updated data\r\n */\r\nexport async function updateBusinessCard(\r\n  formData: BusinessCardData\r\n): Promise<{ success: boolean; error?: string; data?: BusinessCardData }> {\r\n  const supabase = await createClient();\r\n\r\n  // 1. Validate the incoming data\r\n  const validatedFields = validateBusinessCardData(formData);\r\n\r\n  if (!validatedFields.success) {\r\n    console.error(\r\n      \"Validation Error:\",\r\n      validatedFields.error.flatten().fieldErrors\r\n    );\r\n    return {\r\n      success: false,\r\n      error: \"Invalid data provided. Please check the form fields.\",\r\n    };\r\n  }\r\n\r\n  // 2. Get the authenticated user\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    console.error(\"Auth Error:\", authError);\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Get existing profile to compare phone numbers\r\n  const { data: existingProfile, error: profileError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"phone\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (profileError) {\r\n    console.error(\"Profile fetch error:\", profileError);\r\n    return { success: false, error: \"Failed to fetch existing profile.\" };\r\n  }\r\n\r\n  // 3. Check subscription status if going online\r\n  if (validatedFields.data.status === \"online\") {\r\n    const subscriptionCheck = await checkSubscriptionStatus(user.id);\r\n    if (!subscriptionCheck.canGoOnline) {\r\n      return {\r\n        success: false,\r\n        error: subscriptionCheck.error || \"Cannot set card to online status.\"\r\n      };\r\n    }\r\n  }\r\n\r\n  // 4. Handle Slug Logic if going online\r\n  let finalSlug = validatedFields.data.business_slug;\r\n\r\n  if (validatedFields.data.status === \"online\") {\r\n    const slugResult = await generateUniqueSlug(\r\n      validatedFields.data.business_name,\r\n      finalSlug || \"\",\r\n      user.id\r\n    );\r\n\r\n    if (!slugResult.success) {\r\n      return {\r\n        success: false,\r\n        error: slugResult.error || \"Failed to generate unique slug.\"\r\n      };\r\n    }\r\n\r\n    finalSlug = slugResult.slug;\r\n  } else {\r\n    finalSlug = validatedFields.data.business_slug;\r\n  }\r\n\r\n  // 5. Handle theme-specific header image uploads\r\n  const updatedCustomBranding = { ...validatedFields.data.custom_branding };\r\n\r\n  // Handle light theme header upload\r\n  if (validatedFields.data.custom_branding?.pending_light_header_file) {\r\n    const lightFile = validatedFields.data.custom_branding.pending_light_header_file as File;\r\n    const lightUploadResult = await uploadThemeHeaderImage(lightFile, 'light');\r\n\r\n    if (lightUploadResult.success && lightUploadResult.url) {\r\n      // Clean up old light theme images\r\n      if (updatedCustomBranding.custom_header_image_light_url) {\r\n        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_light_url);\r\n      }\r\n      await cleanupOldThemeHeaderImages(user.id, 'light', lightUploadResult.url);\r\n\r\n      updatedCustomBranding.custom_header_image_light_url = lightUploadResult.url;\r\n    } else {\r\n      console.error(\"Light theme header upload failed:\", lightUploadResult.error);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload light theme header: ${lightUploadResult.error}`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // Handle dark theme header upload\r\n  if (validatedFields.data.custom_branding?.pending_dark_header_file) {\r\n    const darkFile = validatedFields.data.custom_branding.pending_dark_header_file as File;\r\n    const darkUploadResult = await uploadThemeHeaderImage(darkFile, 'dark');\r\n\r\n    if (darkUploadResult.success && darkUploadResult.url) {\r\n      // Clean up old dark theme images\r\n      if (updatedCustomBranding.custom_header_image_dark_url) {\r\n        await deleteThemeHeaderImage(updatedCustomBranding.custom_header_image_dark_url);\r\n      }\r\n      await cleanupOldThemeHeaderImages(user.id, 'dark', darkUploadResult.url);\r\n\r\n      updatedCustomBranding.custom_header_image_dark_url = darkUploadResult.url;\r\n    } else {\r\n      console.error(\"Dark theme header upload failed:\", darkUploadResult.error);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload dark theme header: ${darkUploadResult.error}`,\r\n      };\r\n    }\r\n  }\r\n\r\n  // Handle deletion of theme-specific headers (when URL is empty but no new file)\r\n  if (validatedFields.data.custom_branding?.custom_header_image_light_url === \"\" &&\r\n      !validatedFields.data.custom_branding?.pending_light_header_file) {\r\n    // Get current light URL from database to delete\r\n    const { data: currentProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_branding\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (currentProfile?.custom_branding?.custom_header_image_light_url) {\r\n      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_light_url);\r\n    }\r\n    updatedCustomBranding.custom_header_image_light_url = \"\";\r\n  }\r\n\r\n  if (validatedFields.data.custom_branding?.custom_header_image_dark_url === \"\" &&\r\n      !validatedFields.data.custom_branding?.pending_dark_header_file) {\r\n    // Get current dark URL from database to delete\r\n    const { data: currentProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_branding\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (currentProfile?.custom_branding?.custom_header_image_dark_url) {\r\n      await deleteThemeHeaderImage(currentProfile.custom_branding.custom_header_image_dark_url);\r\n    }\r\n    updatedCustomBranding.custom_header_image_dark_url = \"\";\r\n  }\r\n\r\n  // Remove pending file fields from the data to be saved to database\r\n  delete updatedCustomBranding.pending_light_header_file;\r\n  delete updatedCustomBranding.pending_dark_header_file;\r\n\r\n  // Note: Phone uniqueness check removed as multiple businesses/customers can share the same number\r\n\r\n  // 7. Prepare data for Supabase update\r\n  const businessHoursData = processBusinessHours(validatedFields.data.business_hours);\r\n\r\n  const dataToUpdate: Partial<BusinessCardData> = {\r\n    business_name: validatedFields.data.business_name,\r\n    member_name: validatedFields.data.member_name,\r\n    title: validatedFields.data.title,\r\n    logo_url: validatedFields.data.logo_url,\r\n    established_year: validatedFields.data.established_year,\r\n    address_line: validatedFields.data.address_line,\r\n    city: validatedFields.data.city,\r\n    state: validatedFields.data.state,\r\n    pincode: validatedFields.data.pincode,\r\n    phone: validatedFields.data.phone,\r\n    delivery_info: validatedFields.data.delivery_info,\r\n    google_maps_url: validatedFields.data.google_maps_url,\r\n    instagram_url: validatedFields.data.instagram_url,\r\n    facebook_url: validatedFields.data.facebook_url,\r\n    whatsapp_number: validatedFields.data.whatsapp_number,\r\n    about_bio: validatedFields.data.about_bio,\r\n    locality: validatedFields.data.locality,\r\n    theme_color: validatedFields.data.theme_color,\r\n    business_hours: businessHoursData,\r\n    status: validatedFields.data.status,\r\n    business_slug: finalSlug,\r\n    contact_email: validatedFields.data.contact_email,\r\n    business_category: validatedFields.data.business_category,\r\n    custom_branding: updatedCustomBranding,\r\n    custom_ads: validatedFields.data.custom_ads,\r\n  };\r\n\r\n  // 7. Update the business profile in Supabase\r\n  const { data: updatedProfile, error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update(dataToUpdate)\r\n    .eq(\"id\", user.id)\r\n    .select(\r\n      `\r\n      id, business_name, member_name, title, logo_url, address_line, city, state, pincode, locality,\r\n      phone, instagram_url, facebook_url, whatsapp_number, about_bio, status, business_slug,\r\n      theme_color, delivery_info, business_hours, contact_email, has_active_subscription,\r\n      trial_end_date, created_at, updated_at, total_likes, total_subscriptions, average_rating,\r\n      business_category, custom_branding, custom_ads, google_maps_url, established_year\r\n    `\r\n    )\r\n    .single();\r\n\r\n  if (updateError) {\r\n    console.error(\"Supabase Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  if (!updatedProfile) {\r\n    return {\r\n      success: false,\r\n      error: \"Failed to update profile. Profile not found after update.\",\r\n    };\r\n  }\r\n\r\n  // 8. Update phone in Supabase auth.users table if phone was changed\r\n  if (\r\n    validatedFields.data.phone &&\r\n    validatedFields.data.phone !== existingProfile.phone\r\n  ) {\r\n    const { error: authUpdateError } = await supabase.auth.updateUser({\r\n      phone: `+91${validatedFields.data.phone}`,\r\n    });\r\n\r\n    if (authUpdateError) {\r\n      console.warn('Failed to update auth phone field:', authUpdateError.message);\r\n      // Don't fail the operation for this, just log the warning\r\n      // The business_profiles table is updated successfully\r\n    }\r\n  }\r\n\r\n  // 9. Revalidate paths\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  if (dataToUpdate.status === \"online\" && dataToUpdate.business_slug) {\r\n    revalidatePath(`/(main)/card/${dataToUpdate.business_slug}`, \"page\");\r\n  }\r\n\r\n  // 10. Return success response with the updated data\r\n  return { success: true, data: updatedProfile as BusinessCardData };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAOO,eAAe,mBACpB,QAA0B;IAE1B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,gCAAgC;IAChC,MAAM,kBAAkB,CAAA,GAAA,6LAAA,CAAA,2BAAwB,AAAD,EAAE;IAEjD,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC5B,QAAQ,KAAK,CACX,qBACA,gBAAgB,KAAK,CAAC,OAAO,GAAG,WAAW;QAE7C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,gCAAgC;IAChC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,gDAAgD;IAChD,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAC1D,IAAI,CAAC,qBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,cAAc;QAChB,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YAAE,SAAS;YAAO,OAAO;QAAoC;IACtE;IAEA,+CAA+C;IAC/C,IAAI,gBAAgB,IAAI,CAAC,MAAM,KAAK,UAAU;QAC5C,MAAM,oBAAoB,MAAM,CAAA,GAAA,oLAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,EAAE;QAC/D,IAAI,CAAC,kBAAkB,WAAW,EAAE;YAClC,OAAO;gBACL,SAAS;gBACT,OAAO,kBAAkB,KAAK,IAAI;YACpC;QACF;IACF;IAEA,uCAAuC;IACvC,IAAI,YAAY,gBAAgB,IAAI,CAAC,aAAa;IAElD,IAAI,gBAAgB,IAAI,CAAC,MAAM,KAAK,UAAU;QAC5C,MAAM,aAAa,MAAM,CAAA,GAAA,0KAAA,CAAA,qBAAkB,AAAD,EACxC,gBAAgB,IAAI,CAAC,aAAa,EAClC,aAAa,IACb,KAAK,EAAE;QAGT,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO,WAAW,KAAK,IAAI;YAC7B;QACF;QAEA,YAAY,WAAW,IAAI;IAC7B,OAAO;QACL,YAAY,gBAAgB,IAAI,CAAC,aAAa;IAChD;IAEA,gDAAgD;IAChD,MAAM,wBAAwB;QAAE,GAAG,gBAAgB,IAAI,CAAC,eAAe;IAAC;IAExE,mCAAmC;IACnC,IAAI,gBAAgB,IAAI,CAAC,eAAe,EAAE,2BAA2B;QACnE,MAAM,YAAY,gBAAgB,IAAI,CAAC,eAAe,CAAC,yBAAyB;QAChF,MAAM,oBAAoB,MAAM,CAAA,GAAA,sLAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW;QAElE,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,GAAG,EAAE;YACtD,kCAAkC;YAClC,IAAI,sBAAsB,6BAA6B,EAAE;gBACvD,MAAM,CAAA,GAAA,sLAAA,CAAA,yBAAsB,AAAD,EAAE,sBAAsB,6BAA6B;YAClF;YACA,MAAM,CAAA,GAAA,sLAAA,CAAA,8BAA2B,AAAD,EAAE,KAAK,EAAE,EAAE,SAAS,kBAAkB,GAAG;YAEzE,sBAAsB,6BAA6B,GAAG,kBAAkB,GAAG;QAC7E,OAAO;YACL,QAAQ,KAAK,CAAC,qCAAqC,kBAAkB,KAAK;YAC1E,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,qCAAqC,EAAE,kBAAkB,KAAK,EAAE;YAC1E;QACF;IACF;IAEA,kCAAkC;IAClC,IAAI,gBAAgB,IAAI,CAAC,eAAe,EAAE,0BAA0B;QAClE,MAAM,WAAW,gBAAgB,IAAI,CAAC,eAAe,CAAC,wBAAwB;QAC9E,MAAM,mBAAmB,MAAM,CAAA,GAAA,sLAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU;QAEhE,IAAI,iBAAiB,OAAO,IAAI,iBAAiB,GAAG,EAAE;YACpD,iCAAiC;YACjC,IAAI,sBAAsB,4BAA4B,EAAE;gBACtD,MAAM,CAAA,GAAA,sLAAA,CAAA,yBAAsB,AAAD,EAAE,sBAAsB,4BAA4B;YACjF;YACA,MAAM,CAAA,GAAA,sLAAA,CAAA,8BAA2B,AAAD,EAAE,KAAK,EAAE,EAAE,QAAQ,iBAAiB,GAAG;YAEvE,sBAAsB,4BAA4B,GAAG,iBAAiB,GAAG;QAC3E,OAAO;YACL,QAAQ,KAAK,CAAC,oCAAoC,iBAAiB,KAAK;YACxE,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,oCAAoC,EAAE,iBAAiB,KAAK,EAAE;YACxE;QACF;IACF;IAEA,gFAAgF;IAChF,IAAI,gBAAgB,IAAI,CAAC,eAAe,EAAE,kCAAkC,MACxE,CAAC,gBAAgB,IAAI,CAAC,eAAe,EAAE,2BAA2B;QACpE,gDAAgD;QAChD,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,mBACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,gBAAgB,iBAAiB,+BAA+B;YAClE,MAAM,CAAA,GAAA,sLAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,eAAe,CAAC,6BAA6B;QAC3F;QACA,sBAAsB,6BAA6B,GAAG;IACxD;IAEA,IAAI,gBAAgB,IAAI,CAAC,eAAe,EAAE,iCAAiC,MACvE,CAAC,gBAAgB,IAAI,CAAC,eAAe,EAAE,0BAA0B;QACnE,+CAA+C;QAC/C,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC,mBACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,gBAAgB,iBAAiB,8BAA8B;YACjE,MAAM,CAAA,GAAA,sLAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,eAAe,CAAC,4BAA4B;QAC1F;QACA,sBAAsB,4BAA4B,GAAG;IACvD;IAEA,mEAAmE;IACnE,OAAO,sBAAsB,yBAAyB;IACtD,OAAO,sBAAsB,wBAAwB;IAErD,kGAAkG;IAElG,sCAAsC;IACtC,MAAM,oBAAoB,CAAA,GAAA,wLAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,IAAI,CAAC,cAAc;IAElF,MAAM,eAA0C;QAC9C,eAAe,gBAAgB,IAAI,CAAC,aAAa;QACjD,aAAa,gBAAgB,IAAI,CAAC,WAAW;QAC7C,OAAO,gBAAgB,IAAI,CAAC,KAAK;QACjC,UAAU,gBAAgB,IAAI,CAAC,QAAQ;QACvC,kBAAkB,gBAAgB,IAAI,CAAC,gBAAgB;QACvD,cAAc,gBAAgB,IAAI,CAAC,YAAY;QAC/C,MAAM,gBAAgB,IAAI,CAAC,IAAI;QAC/B,OAAO,gBAAgB,IAAI,CAAC,KAAK;QACjC,SAAS,gBAAgB,IAAI,CAAC,OAAO;QACrC,OAAO,gBAAgB,IAAI,CAAC,KAAK;QACjC,eAAe,gBAAgB,IAAI,CAAC,aAAa;QACjD,iBAAiB,gBAAgB,IAAI,CAAC,eAAe;QACrD,eAAe,gBAAgB,IAAI,CAAC,aAAa;QACjD,cAAc,gBAAgB,IAAI,CAAC,YAAY;QAC/C,iBAAiB,gBAAgB,IAAI,CAAC,eAAe;QACrD,WAAW,gBAAgB,IAAI,CAAC,SAAS;QACzC,UAAU,gBAAgB,IAAI,CAAC,QAAQ;QACvC,aAAa,gBAAgB,IAAI,CAAC,WAAW;QAC7C,gBAAgB;QAChB,QAAQ,gBAAgB,IAAI,CAAC,MAAM;QACnC,eAAe;QACf,eAAe,gBAAgB,IAAI,CAAC,aAAa;QACjD,mBAAmB,gBAAgB,IAAI,CAAC,iBAAiB;QACzD,iBAAiB;QACjB,YAAY,gBAAgB,IAAI,CAAC,UAAU;IAC7C;IAEA,6CAA6C;IAC7C,MAAM,EAAE,MAAM,cAAc,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,qBACL,MAAM,CAAC,cACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM,CACL,CAAC;;;;;;IAMH,CAAC,EAEA,MAAM;IAET,IAAI,aAAa;QACf,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,OAAO,CAAC,0BAA0B,EAAE,YAAY,OAAO,EAAE;QAC3D;IACF;IAEA,IAAI,CAAC,gBAAgB;QACnB,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,oEAAoE;IACpE,IACE,gBAAgB,IAAI,CAAC,KAAK,IAC1B,gBAAgB,IAAI,CAAC,KAAK,KAAK,gBAAgB,KAAK,EACpD;QACA,MAAM,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;YAChE,OAAO,CAAC,GAAG,EAAE,gBAAgB,IAAI,CAAC,KAAK,EAAE;QAC3C;QAEA,IAAI,iBAAiB;YACnB,QAAQ,IAAI,CAAC,sCAAsC,gBAAgB,OAAO;QAC1E,0DAA0D;QAC1D,sDAAsD;QACxD;IACF;IAEA,sBAAsB;IACtB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,IAAI,aAAa,MAAM,KAAK,YAAY,aAAa,aAAa,EAAE;QAClE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,aAAa,EAAE,aAAa,aAAa,EAAE,EAAE;IAC/D;IAEA,oDAAoD;IACpD,OAAO;QAAE,SAAS;QAAM,MAAM;IAAmC;AACnE;;;IArPsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1776, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/logo/logoActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { revalidatePath } from \"next/cache\";\r\nimport { getProfileImagePath, getScalableUserPath } from \"@/lib/utils/storage-paths\";\r\nimport {\r\n  LOGO_MAX_SIZE_MB,\r\n  ALLOWED_IMAGE_TYPES,\r\n  STORAGE_BUCKET\r\n} from \"../utils/constants\";\r\n\r\n/**\r\n * Updates only the logo URL in the database\r\n * @param logoUrl - The new logo URL\r\n * @returns Success/error response\r\n */\r\nexport async function updateLogoUrl(\r\n  logoUrl: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: logoUrl, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Logo URL Update Error:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update logo URL: ${updateError.message}`,\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Deletes logo from storage and updates the database\r\n * @returns Success/error response\r\n */\r\nexport async function deleteLogoUrl(): Promise<{ success: boolean; error?: string }> {\r\n  const supabase = await createClient();\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // First, get the current logo URL to extract the path\r\n  const { data: profile, error: fetchError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"logo_url\")\r\n    .eq(\"id\", user.id)\r\n    .single();\r\n\r\n  if (fetchError) {\r\n    console.error(\"Error fetching profile for logo deletion:\", fetchError);\r\n    return { success: false, error: \"Failed to fetch profile information.\" };\r\n  }\r\n\r\n  // If there's a logo URL, delete the file from storage\r\n  if (profile?.logo_url) {\r\n    try {\r\n      // Extract the file path from the URL\r\n      const urlParts = profile.logo_url.split('/storage/v1/object/public/business/');\r\n      if (urlParts.length === 2) {\r\n        const filePath = urlParts[1].split('?')[0]; // Remove any query parameters\r\n\r\n        // Use admin client to delete from storage (required to bypass RLS)\r\n        const adminSupabase = createAdminClient();\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove([filePath]);\r\n\r\n        if (deleteError && deleteError.message !== \"The resource was not found\") {\r\n          console.error(\"Error deleting logo from storage:\", deleteError);\r\n          // Continue with database update even if storage deletion fails\r\n        } else {\r\n          console.log(\"Successfully deleted logo from storage:\", filePath);\r\n        }\r\n      } else {\r\n        console.warn(\"Could not parse logo URL for storage deletion:\", profile.logo_url);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error processing logo URL for deletion:\", error);\r\n      // Continue with database update even if storage deletion fails\r\n    }\r\n  }\r\n\r\n  // Update the database to remove the logo URL\r\n  const { error: updateError } = await supabase\r\n    .from(\"business_profiles\")\r\n    .update({ logo_url: null, updated_at: new Date().toISOString() })\r\n    .eq(\"id\", user.id);\r\n\r\n  if (updateError) {\r\n    console.error(\"Error updating profile after logo deletion:\", updateError);\r\n    return {\r\n      success: false,\r\n      error: `Failed to update profile after logo deletion: ${updateError.message}`\r\n    };\r\n  }\r\n\r\n  revalidatePath(\"/dashboard/business/card\");\r\n  return { success: true };\r\n}\r\n\r\n/**\r\n * Uploads logo file and returns public URL\r\n * @param formData - Form data containing the logo file\r\n * @returns Success/error response with URL\r\n */\r\nexport async function uploadLogoAndGetUrl(\r\n  formData: FormData\r\n): Promise<{ success: boolean; url?: string; error?: string }> {\r\n  const supabase = await createClient();\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n  const userId = user.id;\r\n\r\n  const file = formData.get(\"logoFile\") as File | null;\r\n  if (!file) {\r\n    return { success: false, error: \"No logo file provided.\" };\r\n  }\r\n\r\n  if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {\r\n    return { success: false, error: \"Invalid file type.\" };\r\n  }\r\n\r\n  // Server-side file size validation\r\n  if (file.size > LOGO_MAX_SIZE_MB * 1024 * 1024) {\r\n    return { success: false, error: `File size must be less than ${LOGO_MAX_SIZE_MB}MB.` };\r\n  }\r\n\r\n  const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);\r\n  const fullPath = getProfileImagePath(userId, timestamp);\r\n\r\n  try {\r\n    // Use admin client for cleanup operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Clean up existing logos in the profile folder\r\n    const userPath = getScalableUserPath(userId);\r\n    const profileFolderPath = `${userPath}/profile/`;\r\n\r\n    const { data: existingFiles, error: listError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .list(profileFolderPath, { limit: 10 });\r\n\r\n    if (!listError && existingFiles && existingFiles.length > 0) {\r\n      const filesToDelete = existingFiles\r\n        .filter(f => f.name.startsWith('logo_'))\r\n        .map(f => `${profileFolderPath}${f.name}`);\r\n\r\n      if (filesToDelete.length > 0) {\r\n        const { error: deleteError } = await adminSupabase.storage\r\n          .from(STORAGE_BUCKET)\r\n          .remove(filesToDelete);\r\n        if (deleteError) {\r\n          console.warn(`Error deleting existing logos:`, deleteError.message);\r\n        }\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.warn(\"Exception during logo deletion check:\", e);\r\n  }\r\n\r\n  try {\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await file.arrayBuffer());\r\n\r\n    // Use admin client for storage operations to bypass RLS\r\n    const adminSupabase = createAdminClient();\r\n\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .upload(fullPath, fileBuffer, {\r\n        contentType: file.type, // Use the file's original type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Logo Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload logo: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(STORAGE_BUCKET)\r\n      .getPublicUrl(fullPath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      console.error(\r\n        \"Get Public URL Error: URL data is null or missing publicUrl property for path:\",\r\n        fullPath\r\n      );\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    return { success: true, url: urlData.publicUrl };\r\n  } catch (processingError) {\r\n    console.error(\"Image Processing/Upload Error:\", processingError);\r\n    return { success: false, error: \"Failed to process or upload image.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;AAWO,eAAe,cACpB,OAAe;IAEf,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;QAAE,UAAU;QAAS,YAAY,IAAI,OAAO,WAAW;IAAG,GACjE,EAAE,CAAC,MAAM,KAAK,EAAE;IAEnB,IAAI,aAAa;QACf,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YACL,SAAS;YACT,OAAO,CAAC,2BAA2B,EAAE,YAAY,OAAO,EAAE;QAC5D;IACF;IAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,OAAO;QAAE,SAAS;IAAK;AACzB;AAMO,eAAe;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,sDAAsD;IACtD,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,qBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;IAET,IAAI,YAAY;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO;YAAE,SAAS;YAAO,OAAO;QAAuC;IACzE;IAEA,sDAAsD;IACtD,IAAI,SAAS,UAAU;QACrB,IAAI;YACF,qCAAqC;YACrC,MAAM,WAAW,QAAQ,QAAQ,CAAC,KAAK,CAAC;YACxC,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,8BAA8B;gBAE1E,mEAAmE;gBACnE,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;gBACtC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,2KAAA,CAAA,iBAAc,EACnB,MAAM,CAAC;oBAAC;iBAAS;gBAEpB,IAAI,eAAe,YAAY,OAAO,KAAK,8BAA8B;oBACvE,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,+DAA+D;gBACjE,OAAO;oBACL,QAAQ,GAAG,CAAC,2CAA2C;gBACzD;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,kDAAkD,QAAQ,QAAQ;YACjF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,+DAA+D;QACjE;IACF;IAEA,6CAA6C;IAC7C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;QAAE,UAAU;QAAM,YAAY,IAAI,OAAO,WAAW;IAAG,GAC9D,EAAE,CAAC,MAAM,KAAK,EAAE;IAEnB,IAAI,aAAa;QACf,QAAQ,KAAK,CAAC,+CAA+C;QAC7D,OAAO;YACL,SAAS;YACT,OAAO,CAAC,8CAA8C,EAAE,YAAY,OAAO,EAAE;QAC/E;IACF;IAEA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;IACf,OAAO;QAAE,SAAS;IAAK;AACzB;AAOO,eAAe,oBACpB,QAAkB;IAElB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IACA,MAAM,SAAS,KAAK,EAAE;IAEtB,MAAM,OAAO,SAAS,GAAG,CAAC;IAC1B,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,SAAS;YAAO,OAAO;QAAyB;IAC3D;IAEA,IAAI,CAAC,2KAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;QAC5C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqB;IACvD;IAEA,mCAAmC;IACnC,IAAI,KAAK,IAAI,GAAG,2KAAA,CAAA,mBAAgB,GAAG,OAAO,MAAM;QAC9C,OAAO;YAAE,SAAS;YAAO,OAAO,CAAC,4BAA4B,EAAE,2KAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC;QAAC;IACvF;IAEA,MAAM,YAAY,IAAI,OAAO,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;IACpE,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ;IAE7C,IAAI;QACF,wDAAwD;QACxD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,gDAAgD;QAChD,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE;QACrC,MAAM,oBAAoB,GAAG,SAAS,SAAS,CAAC;QAEhD,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAAc,OAAO,CAC1E,IAAI,CAAC,2KAAA,CAAA,iBAAc,EACnB,IAAI,CAAC,mBAAmB;YAAE,OAAO;QAAG;QAEvC,IAAI,CAAC,aAAa,iBAAiB,cAAc,MAAM,GAAG,GAAG;YAC3D,MAAM,gBAAgB,cACnB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,UAAU,CAAC,UAC9B,GAAG,CAAC,CAAA,IAAK,GAAG,oBAAoB,EAAE,IAAI,EAAE;YAE3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,2KAAA,CAAA,iBAAc,EACnB,MAAM,CAAC;gBACV,IAAI,aAAa;oBACf,QAAQ,IAAI,CAAC,CAAC,8BAA8B,CAAC,EAAE,YAAY,OAAO;gBACpE;YACF;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,IAAI,CAAC,yCAAyC;IACxD;IAEA,IAAI;QACF,4DAA4D;QAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QAErD,wDAAwD;QACxD,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,2KAAA,CAAA,iBAAc,EACnB,MAAM,CAAC,UAAU,YAAY;YAC5B,aAAa,KAAK,IAAI;YACtB,QAAQ;QACV;QAEF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,uBAAuB,EAAE,YAAY,OAAO,EAAE;YACxD;QACF;QAEA,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,cAAc,OAAO,CAC5C,IAAI,CAAC,2KAAA,CAAA,iBAAc,EACnB,YAAY,CAAC;QAEhB,IAAI,CAAC,SAAS,WAAW;YACvB,QAAQ,KAAK,CACX,kFACA;YAEF,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;YAAM,KAAK,QAAQ,SAAS;QAAC;IACjD,EAAE,OAAO,iBAAiB;QACxB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YAAE,SAAS;YAAO,OAAO;QAAqC;IACvE;AACF;;;IArNsB;IAkCA;IA2EA;;AA7GA,+OAAA;AAkCA,+OAAA;AA2EA,+OAAA", "debugId": null}}, {"offset": {"line": 1986, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/public/publicCardActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { BusinessCardData } from \"../schema\";\r\nimport { ProductServiceData } from \"../../products/actions\";\r\nimport { mapPublicCardData } from \"../data/businessCardMapper\";\r\n\r\n/**\r\n * Fetches public card data by business slug\r\n * @param slug - The business slug to fetch data for\r\n * @returns Public card data with products/services or error\r\n */\r\nexport async function getPublicCardDataBySlug(slug: string): Promise<{\r\n  data?: BusinessCardData & { products_services?: ProductServiceData[] };\r\n  error?: string;\r\n}> {\r\n  if (!slug) {\r\n    return { error: \"Business slug is required.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n\r\n  // Fetch profile and related products, selecting only Phase 1 columns\r\n  const { data, error } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\r\n      `\r\n      id, business_name, contact_email, has_active_subscription,\r\n      trial_end_date, created_at, updated_at, logo_url, member_name, title,\r\n      address_line, city, state, pincode, locality, phone, instagram_url,\r\n      facebook_url, whatsapp_number, about_bio, status, business_slug,\r\n      business_category, business_hours, delivery_info, established_year, google_maps_url,\r\n      products_services (\r\n        id, name, description, base_price, is_available, image_url, created_at, updated_at\r\n      )\r\n    `\r\n    )\r\n    .eq(\"business_slug\", slug)\r\n    .eq(\"status\", \"online\")\r\n    .maybeSingle();\r\n\r\n  if (error) {\r\n    console.error(\"Public Fetch Error:\", error);\r\n    return { error: `Failed to fetch public profile: ${error.message}` };\r\n  }\r\n\r\n  if (!data) {\r\n    return { error: \"Profile not found or is not online.\" };\r\n  }\r\n\r\n  // Map data using the shared mapper\r\n  const mappedData = mapPublicCardData(data);\r\n  return { data: mappedData };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;;;;;;AAOO,eAAe,wBAAwB,IAAY;IAIxD,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;QAA6B;IAC/C;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,qEAAqE;IACrE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CACL,CAAC;;;;;;;;;IASH,CAAC,EAEA,EAAE,CAAC,iBAAiB,MACpB,EAAE,CAAC,UAAU,UACb,WAAW;IAEd,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YAAE,OAAO,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;QAAC;IACrE;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;QAAsC;IACxD;IAEA,mCAAmC;IACnC,MAAM,aAAa,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE;IACrC,OAAO;QAAE,MAAM;IAAW;AAC5B;;;IAzCsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 2044, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/location.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\n\r\n// --- Pincode Lookup Action ---\r\nexport async function getPincodeDetails(pincode: string): Promise<{\r\n  data?: {\r\n    city: string;\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  city?: string;\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!pincode || !/^\\d{6}$/.test(pincode)) {\r\n    return { error: \"Invalid Pincode format.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // First get city and state from pincodes table\r\n    const { data: pincodeData, error: pincodeError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"OfficeName, DivisionName, StateName\")\r\n      .eq(\"Pincode\", pincode) // Updated column name to match database\r\n      .order(\"OfficeName\");\r\n\r\n    if (pincodeError) {\r\n      console.error(\"Pincode Fetch Error:\", pincodeError);\r\n      return { error: \"Database error fetching pincode details.\" };\r\n    }\r\n\r\n    if (!pincodeData || pincodeData.length === 0) {\r\n      return { error: \"Pincode not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = pincodeData[0].StateName;\r\n\r\n    // Use DivisionName as the city (already cleaned)\r\n    const city = pincodeData[0].DivisionName;\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(pincodeData.map((item) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { city, state, localities },\r\n      city,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Pincode Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during pincode lookup.\" };\r\n  }\r\n}\r\n// --- End Pincode Lookup ---\r\n\r\n// --- City Lookup Action ---\r\nexport async function getCityDetails(city: string): Promise<{\r\n  data?: {\r\n    pincodes: string[];\r\n    state: string;\r\n    localities: string[];\r\n  };\r\n  pincodes?: string[];\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!city || city.length < 2) {\r\n    return { error: \"City name must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabase = await createClient();\r\n  try {\r\n    // Get pincodes and state for the city - DivisionName is the city column\r\n    const { data: cityData, error: cityError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"Pincode, OfficeName, StateName, DivisionName\")\r\n      .ilike(\"DivisionName\", `%${city}%`)\r\n      .order(\"Pincode\");\r\n\r\n    if (cityError) {\r\n      console.error(\"City Fetch Error:\", cityError);\r\n      return { error: \"Database error fetching city details.\" };\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { error: \"City not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = cityData[0].StateName;\r\n\r\n    // Get unique pincodes\r\n    const pincodes = [...new Set(cityData.map((item) => item.Pincode))] as string[];\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(cityData.map((item) => item.OfficeName)),\r\n    ] as string[];\r\n\r\n    return {\r\n      data: { pincodes, state, localities },\r\n      pincodes,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during city lookup.\" };\r\n  }\r\n}\r\n// --- End City Lookup ---\r\n\r\n// --- City Autocomplete Action ---\r\n/**\r\n * Get city suggestions based on a search query\r\n *\r\n * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.\r\n * The PostgreSQL function is defined as:\r\n *\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)\r\n * RETURNS TABLE(city TEXT) AS $$\r\n * BEGIN\r\n *   RETURN QUERY\r\n *   SELECT DISTINCT \"DivisionName\" as city\r\n *   FROM pincodes\r\n *   WHERE \"DivisionName\" ILIKE search_query\r\n *   ORDER BY \"DivisionName\"\r\n *   LIMIT result_limit;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n * ```\r\n *\r\n * @param query The search query (minimum 2 characters)\r\n * @returns Array of up to 5 unique city suggestions\r\n */\r\nexport async function getCitySuggestions(query: string): Promise<{\r\n  data?: {\r\n    cities: string[];\r\n  };\r\n  cities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!query || query.length < 2) {\r\n    return { error: \"Query must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabaseAdmin = createAdminClient();\r\n  try {\r\n    // Use the PostgreSQL function to get distinct cities (up to 5)\r\n    const { data: cityData, error: cityError } = await supabaseAdmin\r\n      .rpc('get_distinct_cities', {\r\n        search_query: `%${query}%`,\r\n        result_limit: 5\r\n      });\r\n\r\n    if (cityError) {\r\n      console.error(\"City Suggestions Error:\", cityError);\r\n\r\n      // Fallback to regular query if RPC fails\r\n      try {\r\n        // Use a regular query as fallback\r\n        const { data: fallbackData, error: fallbackError } = await supabaseAdmin\r\n          .from(\"pincodes\")\r\n          .select(\"DivisionName\")\r\n          .ilike(\"DivisionName\", `%${query}%`)\r\n          .order(\"DivisionName\")\r\n          .limit(100);\r\n\r\n        if (fallbackError) {\r\n          throw fallbackError;\r\n        }\r\n\r\n        if (!fallbackData || fallbackData.length === 0) {\r\n          return { data: { cities: [] }, cities: [] };\r\n        }\r\n\r\n        // Get unique cities and format them\r\n        const cities = [...new Set(fallbackData.map((item) =>\r\n          item.DivisionName.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n        ))] as string[];\r\n\r\n        const topCities = cities.slice(0, 5);\r\n\r\n        return {\r\n          data: { cities: topCities },\r\n          cities: topCities\r\n        };\r\n      } catch (fallbackErr) {\r\n        console.error(\"Fallback City Query Error:\", fallbackErr);\r\n        return { error: \"Database error fetching city suggestions.\" };\r\n      }\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { data: { cities: [] }, cities: [] };\r\n    }\r\n\r\n    // Format the city names to Title Case\r\n    const cities = cityData.map((item: { city: string }) =>\r\n      item.city.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n    );\r\n\r\n    return {\r\n      data: { cities },\r\n      cities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Suggestions Exception:\", e);\r\n    return { error: \"An unexpected error occurred while fetching city suggestions.\" };\r\n  }\r\n}\r\n// --- End City Autocomplete ---\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;;;;AAGO,eAAe,kBAAkB,OAAe;IAWrD,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,UAAU;QACxC,OAAO;YAAE,OAAO;QAA0B;IAC5C;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,IAAI;QACF,+CAA+C;QAC/C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,uCACP,EAAE,CAAC,WAAW,SAAS,wCAAwC;SAC/D,KAAK,CAAC;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE,OAAO;YAA2C;QAC7D;QAEA,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;YAC5C,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,WAAW,CAAC,EAAE,CAAC,SAAS;QAEtC,iDAAiD;QACjD,MAAM,OAAO,WAAW,CAAC,EAAE,CAAC,YAAY;QAExC,+CAA+C;QAC/C,MAAM,aAAa;eACd,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC,OAAS,KAAK,UAAU;SACrD;QAED,OAAO;YACL,MAAM;gBAAE;gBAAM;gBAAO;YAAW;YAChC;YACA;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,OAAO;QAAsD;IACxE;AACF;AAIO,eAAe,eAAe,IAAY;IAW/C,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;QAC5B,OAAO;YAAE,OAAO;QAA2C;IAC7D;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,IAAI;QACF,wEAAwE;QACxE,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,YACL,MAAM,CAAC,gDACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EACjC,KAAK,CAAC;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAwC;QAC1D;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,OAAO;YAAkB;QACpC;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,QAAQ,CAAC,EAAE,CAAC,SAAS;QAEnC,sBAAsB;QACtB,MAAM,WAAW;eAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,OAAS,KAAK,OAAO;SAAG;QAEnE,+CAA+C;QAC/C,MAAM,aAAa;eACd,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,OAAS,KAAK,UAAU;SAClD;QAED,OAAO;YACL,MAAM;gBAAE;gBAAU;gBAAO;YAAW;YACpC;YACA;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,OAAO;QAAmD;IACrE;AACF;AA2BO,eAAe,mBAAmB,KAAa;IAOpD,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO;YAAE,OAAO;QAAuC;IACzD;IAEA,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;IACtC,IAAI;QACF,+DAA+D;QAC/D,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,cAChD,GAAG,CAAC,uBAAuB;YAC1B,cAAc,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,cAAc;QAChB;QAEF,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,yCAAyC;YACzC,IAAI;gBACF,kCAAkC;gBAClC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,cACxD,IAAI,CAAC,YACL,MAAM,CAAC,gBACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAClC,KAAK,CAAC,gBACN,KAAK,CAAC;gBAET,IAAI,eAAe;oBACjB,MAAM;gBACR;gBAEA,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;oBAC9C,OAAO;wBAAE,MAAM;4BAAE,QAAQ,EAAE;wBAAC;wBAAG,QAAQ,EAAE;oBAAC;gBAC5C;gBAEA,oCAAoC;gBACpC,MAAM,SAAS;uBAAI,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,OAC3C,KAAK,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;iBAClF;gBAEH,MAAM,YAAY,OAAO,KAAK,CAAC,GAAG;gBAElC,OAAO;oBACL,MAAM;wBAAE,QAAQ;oBAAU;oBAC1B,QAAQ;gBACV;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,OAAO;oBAAE,OAAO;gBAA4C;YAC9D;QACF;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,MAAM;oBAAE,QAAQ,EAAE;gBAAC;gBAAG,QAAQ,EAAE;YAAC;QAC5C;QAEA,sCAAsC;QACtC,MAAM,SAAS,SAAS,GAAG,CAAC,CAAC,OAC3B,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;QAG7E,OAAO;YACL,MAAM;gBAAE;YAAO;YACf;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;QAAgE;IAClF;AACF,EACA,gCAAgC;;;IAvNV;IA0DA;IAiFA;;AA3IA,+OAAA;AA0DA,+OAAA;AAiFA,+OAAA", "debugId": null}}, {"offset": {"line": 2239, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions/customAdUpload.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { createAdminClient } from \"@/utils/supabase/admin\";\r\nimport { getCustomAdImagePath } from \"@/lib/utils/storage-paths\";\r\n\r\nexport interface CustomAdUploadResult {\r\n  success: boolean;\r\n  url?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface CustomAdUpdateResult {\r\n  success: boolean;\r\n  error?: string;\r\n}\r\n\r\n/**\r\n * Upload custom ad image with compression and auto-save to database\r\n */\r\nexport async function uploadCustomAdImage(\r\n  formData: FormData\r\n): Promise<CustomAdUploadResult> {\r\n  try {\r\n    // Create admin client for storage operations\r\n    const adminSupabase = createAdminClient();\r\n\r\n    // Get authenticated user\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Extract the cropped image file from FormData\r\n    const imageFile = formData.get(\"image\") as File;\r\n    if (!imageFile) {\r\n      return {\r\n        success: false,\r\n        error: \"No image file provided\",\r\n      };\r\n    }\r\n\r\n    // Validate file type\r\n    if (!imageFile.type.startsWith(\"image/\")) {\r\n      return {\r\n        success: false,\r\n        error: \"Invalid file type. Please upload an image.\",\r\n      };\r\n    }\r\n\r\n    // Validate file size (max 15MB before compression)\r\n    if (imageFile.size > 15 * 1024 * 1024) {\r\n      return {\r\n        success: false,\r\n        error: \"File too large. Maximum size is 15MB.\",\r\n      };\r\n    }\r\n\r\n    const bucketName = \"business\";\r\n    const timestamp = Date.now() + Math.floor(Math.random() * 1000);\r\n    const imagePath = getCustomAdImagePath(user.id, timestamp);\r\n\r\n    // File is already compressed on client-side, just upload it\r\n    const fileBuffer = Buffer.from(await imageFile.arrayBuffer());\r\n\r\n    // Upload to Supabase Storage using admin client\r\n    const { error: uploadError } = await adminSupabase.storage\r\n      .from(bucketName)\r\n      .upload(imagePath, fileBuffer, {\r\n        contentType: imageFile.type, // Use original file type (already compressed)\r\n        upsert: true\r\n      });\r\n\r\n    if (uploadError) {\r\n      console.error(\"Custom Ad Upload Error:\", uploadError);\r\n      return {\r\n        success: false,\r\n        error: `Failed to upload image: ${uploadError.message}`,\r\n      };\r\n    }\r\n\r\n    // Get the public URL\r\n    const { data: urlData } = adminSupabase.storage\r\n      .from(bucketName)\r\n      .getPublicUrl(imagePath);\r\n\r\n    if (!urlData?.publicUrl) {\r\n      return {\r\n        success: false,\r\n        error: \"Could not retrieve public URL after upload.\",\r\n      };\r\n    }\r\n\r\n    // Auto-save to database - update custom_ads field\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: true,\r\n          image_url: urlData.publicUrl,\r\n          link_url: \"\", // Will be updated separately\r\n          uploaded_at: new Date().toISOString(),\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      console.error(\"Database update error:\", updateError);\r\n      // Image uploaded successfully but database update failed\r\n      // We could delete the image here, but let's keep it and return success\r\n      // The user can try again\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      url: urlData.publicUrl,\r\n    };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad upload error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred during upload.\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Update custom ad link URL\r\n */\r\nexport async function updateCustomAdLink(linkUrl: string): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Validate URL if provided\r\n    if (linkUrl && linkUrl.trim()) {\r\n      try {\r\n        new URL(linkUrl);\r\n      } catch {\r\n        return {\r\n          success: false,\r\n          error: \"Invalid URL format\",\r\n        };\r\n      }\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the link_url field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      link_url: linkUrl.trim(),\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to update ad link\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad link update error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Toggle custom ad enabled/disabled state\r\n */\r\nexport async function toggleCustomAd(enabled: boolean): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // Get current custom_ads data\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    // Update only the enabled field\r\n    const updatedCustomAds = {\r\n      ...profile.custom_ads,\r\n      enabled,\r\n    };\r\n\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({ custom_ads: updatedCustomAds })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to toggle ad state\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad toggle error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Delete custom ad image and reset data\r\n */\r\nexport async function deleteCustomAd(): Promise<CustomAdUpdateResult> {\r\n  try {\r\n    const supabase = await createClient();\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return {\r\n        success: false,\r\n        error: \"Authentication required\",\r\n      };\r\n    }\r\n\r\n    // First, get the current custom ad data to extract the image URL\r\n    const { data: profile, error: fetchError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"custom_ads\")\r\n      .eq(\"id\", user.id)\r\n      .single();\r\n\r\n    if (fetchError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to fetch current ad data\",\r\n      };\r\n    }\r\n\r\n    const currentCustomAds = profile?.custom_ads;\r\n    const imageUrl = currentCustomAds?.image_url;\r\n\r\n    // Delete the image from storage if it exists\r\n    if (imageUrl) {\r\n      try {\r\n        // Extract the file path from the URL\r\n        // URL format: https://domain.supabase.co/storage/v1/object/public/business/users/xx/xx/userId/ads/custom_ad_timestamp.webp\r\n        const urlParts = imageUrl.split('/storage/v1/object/public/business/');\r\n        if (urlParts.length === 2) {\r\n          const filePath = urlParts[1];\r\n\r\n          // Use admin client to delete from storage\r\n          const adminSupabase = createAdminClient();\r\n          const { error: deleteError } = await adminSupabase.storage\r\n            .from(\"business\")\r\n            .remove([filePath]);\r\n\r\n          if (deleteError) {\r\n            console.error(\"Storage deletion error:\", deleteError);\r\n            // Continue with database update even if storage deletion fails\r\n          }\r\n        }\r\n      } catch (storageError) {\r\n        console.error(\"Error deleting custom ad from storage:\", storageError);\r\n        // Continue with database update even if storage deletion fails\r\n      }\r\n    }\r\n\r\n    // Reset custom_ads data in database\r\n    const { error: updateError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .update({\r\n        custom_ads: {\r\n          enabled: false,\r\n          image_url: \"\",\r\n          link_url: \"\",\r\n          uploaded_at: null,\r\n        }\r\n      })\r\n      .eq(\"id\", user.id);\r\n\r\n    if (updateError) {\r\n      return {\r\n        success: false,\r\n        error: \"Failed to delete custom ad\",\r\n      };\r\n    }\r\n\r\n    return { success: true };\r\n\r\n  } catch (error) {\r\n    console.error(\"Custom ad delete error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"An unexpected error occurred\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;;;;;;;AAgBO,eAAe,oBACpB,QAAkB;IAElB,IAAI;QACF,6CAA6C;QAC7C,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;QAEtC,yBAAyB;QACzB,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,+CAA+C;QAC/C,MAAM,YAAY,SAAS,GAAG,CAAC;QAC/B,IAAI,CAAC,WAAW;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,qBAAqB;QACrB,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;YACxC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,mDAAmD;QACnD,IAAI,UAAU,IAAI,GAAG,KAAK,OAAO,MAAM;YACrC,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,aAAa;QACnB,MAAM,YAAY,KAAK,GAAG,KAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;QAC1D,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,EAAE,EAAE;QAEhD,4DAA4D;QAC5D,MAAM,aAAa,OAAO,IAAI,CAAC,MAAM,UAAU,WAAW;QAE1D,gDAAgD;QAChD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC,WAAW,YAAY;YAC7B,aAAa,UAAU,IAAI;YAC3B,QAAQ;QACV;QAEF,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,SAAS;gBACT,OAAO,CAAC,wBAAwB,EAAE,YAAY,OAAO,EAAE;YACzD;QACF;QAEA,qBAAqB;QACrB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,cAAc,OAAO,CAC5C,IAAI,CAAC,YACL,YAAY,CAAC;QAEhB,IAAI,CAAC,SAAS,WAAW;YACvB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,kDAAkD;QAClD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;YACN,YAAY;gBACV,SAAS;gBACT,WAAW,QAAQ,SAAS;gBAC5B,UAAU;gBACV,aAAa,IAAI,OAAO,WAAW;YACrC;QACF,GACC,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;QACxC,yDAAyD;QACzD,uEAAuE;QACvE,yBAAyB;QAC3B;QAEA,OAAO;YACL,SAAS;YACT,KAAK,QAAQ,SAAS;QACxB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAKO,eAAe,mBAAmB,OAAe;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,2BAA2B;QAC3B,IAAI,WAAW,QAAQ,IAAI,IAAI;YAC7B,IAAI;gBACF,IAAI,IAAI;YACV,EAAE,OAAM;gBACN,OAAO;oBACL,SAAS;oBACT,OAAO;gBACT;YACF;QACF;QAEA,8BAA8B;QAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,qBACL,MAAM,CAAC,cACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,YAAY;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,iCAAiC;QACjC,MAAM,mBAAmB;YACvB,GAAG,QAAQ,UAAU;YACrB,UAAU,QAAQ,IAAI;QACxB;QAEA,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAE,YAAY;QAAiB,GACtC,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,aAAa;YACf,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAKO,eAAe,eAAe,OAAgB;IACnD,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,8BAA8B;QAC9B,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,qBACL,MAAM,CAAC,cACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,YAAY;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,gCAAgC;QAChC,MAAM,mBAAmB;YACvB,GAAG,QAAQ,UAAU;YACrB;QACF;QAEA,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;YAAE,YAAY;QAAiB,GACtC,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,aAAa;YACf,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAClC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,iEAAiE;QACjE,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,qBACL,MAAM,CAAC,cACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,YAAY;YACd,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,MAAM,mBAAmB,SAAS;QAClC,MAAM,WAAW,kBAAkB;QAEnC,6CAA6C;QAC7C,IAAI,UAAU;YACZ,IAAI;gBACF,qCAAqC;gBACrC,2HAA2H;gBAC3H,MAAM,WAAW,SAAS,KAAK,CAAC;gBAChC,IAAI,SAAS,MAAM,KAAK,GAAG;oBACzB,MAAM,WAAW,QAAQ,CAAC,EAAE;oBAE5B,0CAA0C;oBAC1C,MAAM,gBAAgB,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD;oBACtC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,cAAc,OAAO,CACvD,IAAI,CAAC,YACL,MAAM,CAAC;wBAAC;qBAAS;oBAEpB,IAAI,aAAa;wBACf,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,+DAA+D;oBACjE;gBACF;YACF,EAAE,OAAO,cAAc;gBACrB,QAAQ,KAAK,CAAC,0CAA0C;YACxD,+DAA+D;YACjE;QACF;QAEA,oCAAoC;QACpC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,qBACL,MAAM,CAAC;YACN,YAAY;gBACV,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,aAAa;YACf;QACF,GACC,EAAE,CAAC,MAAM,KAAK,EAAE;QAEnB,IAAI,aAAa;YACf,OAAO;gBACL,SAAS;gBACT,OAAO;YACT;QACF;QAEA,OAAO;YAAE,SAAS;QAAK;IAEzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;;;IAvUsB;IAmHA;IAsEA;IA0DA;;AAnPA,+OAAA;AAmHA,+OAAA;AAsEA,+OAAA;AA0DA,+OAAA", "debugId": null}}, {"offset": {"line": 2526, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28dashboard%29/dashboard/business/card/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {signOutUser as '00a78b43259bdfa35946a0918da66b9382dcd7b4dc'} from 'ACTIONS_MODULE0'\nexport {getUnreadActivitiesCount as '40f389eb27483c521497eadb1dbe197d2328544a4a'} from 'ACTIONS_MODULE1'\nexport {getBusinessCardData as '00ac7b36660fe8e3f55a6671e6373a6903a6fb6aed'} from 'ACTIONS_MODULE2'\nexport {updateBusinessCard as '406dbae2f14f62e28feaeaaecbeb3f49836ad493bc'} from 'ACTIONS_MODULE3'\nexport {deleteLogoUrl as '0010ba48a44bea2492c723f7a28a67c55d1e8f63b8'} from 'ACTIONS_MODULE4'\nexport {updateLogoUrl as '40a53098aa3aff2d9433261f3dce0d79c7deb7b8e0'} from 'ACTIONS_MODULE4'\nexport {uploadLogoAndGetUrl as '40b3f4cad8ec7ec71d71e14eeeab4c4cd7146e79fa'} from 'ACTIONS_MODULE4'\nexport {checkSlugAvailability as '40e9edb9705da2d3e0389c65de0aeb61801ba6e517'} from 'ACTIONS_MODULE5'\nexport {generateUniqueSlug as '7074d9d655e48683e0bce7cb659290e26c8094d647'} from 'ACTIONS_MODULE5'\nexport {getPublicCardDataBySlug as '40f538859af26572d875b423879f2fed9959117c93'} from 'ACTIONS_MODULE6'\nexport {checkSubscriptionStatus as '40a8fccdb6dd2a312c1917e2d71355df793eca8c32'} from 'ACTIONS_MODULE7'\nexport {checkForceOfflineStatus as '60dbffc1d7d7264f8d03c75d9a045e7f1e23096c1a'} from 'ACTIONS_MODULE7'\nexport {deleteThemeHeaderImage as '40543e589e80edd41205f56511270624acdc957338'} from 'ACTIONS_MODULE8'\nexport {uploadThemeHeaderImage as '60e1f8e9a0ac5e32065b7560cca87e2845d6630769'} from 'ACTIONS_MODULE8'\nexport {cleanupOldThemeHeaderImages as '706e8267af5a406a0b5f9b6ecbd06be583b42114f6'} from 'ACTIONS_MODULE8'\nexport {checkBusinessSlugAvailability as '60fd6a4ee95871b119d8aca3e04dcc02ae2e00fe71'} from 'ACTIONS_MODULE9'\nexport {getPincodeDetails as '40ab9ff6341449bb46121f282a1e253cc89e3417db'} from 'ACTIONS_MODULE10'\nexport {uploadCustomAdImage as '40556d14bf65b21618bc0581c9b6251092db839d8d'} from 'ACTIONS_MODULE11'\nexport {deleteCustomAd as '003233efd32ad6c3e758adfd5e429f545129249016'} from 'ACTIONS_MODULE11'\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AAEA;AAGA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2677, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/actions.ts"], "sourcesContent": ["// Re-export all actions from organized modules\r\nexport * from \"./business-card/updateBusinessCard\";\r\nexport * from \"./business-card/getBusinessCardData\";\r\nexport * from \"./logo/logoActions\";\r\nexport * from \"./slug/slugUtils\";\r\nexport * from \"./public/publicCardActions\";\r\n"], "names": [], "mappings": "AAAA,+CAA+C;;AAC/C;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2708, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/CardEditorClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/card/CardEditorClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/card/CardEditorClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgU,GAC7V,8FACA", "debugId": null}}, {"offset": {"line": 2722, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/CardEditorClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(dashboard)/dashboard/business/card/CardEditorClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(dashboard)/dashboard/business/card/CardEditorClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 2736, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2746, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\r\nimport { getBusinessCardData } from \"./actions\";\r\nimport { defaultBusinessCardData } from \"./schema\";\r\nimport CardEditorClient, { UserPlanStatus } from \"./CardEditorClient\"; // Import UserPlanStatus type\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Edit Business Card\",\r\n  description: \"Manage and customize your digital business card.\",\r\n  robots: \"noindex, nofollow\", // Added robots meta tag\r\n};\r\n\r\nexport default async function BusinessCardPage() {\r\n  // Fetch initial data for the card\r\n  const { data: initialData, error } = await getBusinessCardData();\r\n\r\n  if (error) {\r\n    // Handle error fetching data (e.g., show an error message)\r\n    // For now, we'll proceed with defaults, but log the error\r\n    console.error(\"Error fetching business card data:\", error);\r\n    // You might want to render an error component here\r\n  }\r\n\r\n  // Use fetched data or defaults if no profile exists yet\r\n  const cardData = initialData ?? defaultBusinessCardData;\r\n\r\n  // Fetch subscription data to get the plan_id and subscription_status\r\n  const supabase = await createClient();\r\n  const { data: subscription, error: subscriptionError } = await supabase\r\n    .from(\"payment_subscriptions\")\r\n    .select(\"plan_id, subscription_status\")\r\n    .eq(\"business_profile_id\", cardData.id || \"\")\r\n    .order(\"created_at\", { ascending: false })\r\n    .limit(1)\r\n    .maybeSingle();\r\n\r\n  if (subscriptionError) {\r\n    console.error(\"Error fetching subscription data:\", subscriptionError);\r\n  }\r\n\r\n  const planId = subscription?.plan_id || \"free\";\r\n  const subscriptionStatus = subscription?.subscription_status || null;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <CardEditorClient\r\n        initialData={cardData}\r\n        currentUserPlan={planId as UserPlanStatus}\r\n        subscriptionStatus={subscriptionStatus}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA,2SAAuE,6BAA6B;AACpG;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,QAAQ;AACV;AAEe,eAAe;IAC5B,kCAAkC;IAClC,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,CAAA,GAAA,gMAAA,CAAA,sBAAmB,AAAD;IAE7D,IAAI,OAAO;QACT,2DAA2D;QAC3D,0DAA0D;QAC1D,QAAQ,KAAK,CAAC,sCAAsC;IACpD,mDAAmD;IACrD;IAEA,wDAAwD;IACxD,MAAM,WAAW,eAAe,+JAAA,CAAA,0BAAuB;IAEvD,qEAAqE;IACrE,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,yBACL,MAAM,CAAC,gCACP,EAAE,CAAC,uBAAuB,SAAS,EAAE,IAAI,IACzC,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC,GACN,WAAW;IAEd,IAAI,mBAAmB;QACrB,QAAQ,KAAK,CAAC,qCAAqC;IACrD;IAEA,MAAM,SAAS,cAAc,WAAW;IACxC,MAAM,qBAAqB,cAAc,uBAAuB;IAEhE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0KAAA,CAAA,UAAgB;YACf,aAAa;YACb,iBAAiB;YACjB,oBAAoB;;;;;;;;;;;AAI5B", "debugId": null}}]}